<script setup>
import { onMounted, onBeforeUnmount, ref } from "vue";
import videojs from "video.js";
import "video.js/dist/video-js.css";

const videoPlayer = ref(null);
let player = null;

onMounted(() => {
  if (videoPlayer.value) {
  player = videojs(videoPlayer.value, {
  controls: true,
  fluid: true,
  html5: {
    vhs: {
      overrideNative: true, // 强制使用 video.js 处理 HLS，而不是浏览器原生支持
      enableLowInitialPlaylist: true // 让 HLS 播放更稳定
    }
  },
  sources: [{
    src: 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8',
    type: 'application/x-mpegURL'
  }]
});

  }
});

onBeforeUnmount(() => {
  if (player) {
    player.dispose();
  }
});
</script>

<template>
  <div class="video-container">
    <video
      ref="videoPlayer"
      class="video-js vjs-default-skin vjs-big-play-centered"
    />
    <iframe class="vii" src="http://127.0.0.1:5501/SH-*************-SFTianjinUniversityH5/src/public/test.html" frameborder="0"></iframe>
  </div>
</template>

<style scoped>
.video-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}
.vii{
  width: 200px;
  height: 200px;
}
</style>
