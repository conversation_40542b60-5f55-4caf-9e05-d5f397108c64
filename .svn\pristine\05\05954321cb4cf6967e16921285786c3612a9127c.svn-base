<template>
  <div ref="chartRef" :style="{ width: '100%', height: '300px' }"></div>
</template>

<script>
import * as echarts from "echarts";
import { onMounted, onUnmounted, ref, watch } from "vue";

export default {
  name: "<PERSON><PERSON><PERSON><PERSON>",
  props: {
    chartData: {
      type: Array,
      required: true,
      default: () => [],
    },
    unit: {
      type: String,
      default: "",
    },
    title: {
      type: String,
      default: "",
    },
  },
  setup(props) {
    const chartRef = ref(null);
    let chart = null;

    const initChart = () => {
      if (chart) {
        chart.dispose();
      }
      chart = echarts.init(chartRef.value);
    };

    const updateChart = () => {
      if (!chart) {
        initChart();
      }

      const times = props.chartData.map((item) => item.time);
      const values = props.chartData.map((item) => item.value);

      const option = {
        title: {
          text: props.title,
          textStyle: {
            color: "#fff",
            fontSize: 26, // 放大标题字体
          },
        },
        tooltip: {
          trigger: "axis",
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: times,
          axisLabel: {
            color: "#fff",
            fontSize: 24, // 放大 x 轴刻度字体
          },
          axisLine: {
            lineStyle: {
              color: "#fff",
            },
          },
        },
        yAxis: {
          type: "value",
          name: props.unit,
          nameTextStyle: {
            color: "#fff",
            fontSize: 26, // 放大 y 轴单位字体
            fontWeight: "bold",
          },
          axisLabel: {
            color: "#fff",
            fontSize: 24, // 放大 y 轴刻度字体
            formatter: "{value}",
          },
          axisLine: {
            lineStyle: {
              color: "#fff",
            },
          },
        },
        series: [
          {
            name: props.title,
            type: "line",
            data: values,
            smooth: true,
            showSymbol: false,
            lineStyle: {
              width: 2,
              color: "#37a2b6",
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "rgba(55, 162, 182, 0.5)",
                },
                {
                  offset: 1,
                  color: "rgba(55, 162, 182, 0.1)",
                },
              ]),
            },
          },
        ],
      };

      chart.setOption(option);
    };

    watch(
      () => props.chartData,
      () => {
        updateChart();
      },
      { deep: true }
    );

    onMounted(() => {
      initChart();
      updateChart();
      window.addEventListener("resize", updateChart);
    });

    onUnmounted(() => {
      if (chart) {
        chart.dispose();
        chart = null;
      }
      window.removeEventListener("resize", updateChart);
    });

    return {
      chartRef,
    };
  },
};
</script>
