<template>
  <div>
    <div class="echart" ref="echart">
    </div>


  </div>
</template>
  
<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",

  data() {

    return {};
  },
  props: ["echartData"],

  watch: {
    echartData(newVal) {
      this.init();
    },
  },
  mounted() {
    this.init();
  },

  methods: {

    initData() {

    },
    init() {
      var title = this.echartData.title
      var color = this.echartData.color
      var color1 = this.echartData.color1
      const myChart = echarts.init(this.$refs.echart);
      var value = this.echartData.value;
      var data = [value, value];

      const option = {

        title: {
          text: (value * 100).toFixed(2) + '{a|%}',
          textStyle: {
            fontSize: 40,
            fontFamily: 'PangMenZhengDao',
            fontWeight: '400',
            color: color,
            rich: {
              a: {
                fontSize: 40,
              }
            }
          },
          x: 'center',
          y: '32%'
        },
        graphic: [{
          type: 'group',
          left: 'center',
          top: '52%',
          children: [{
            type: 'text',
            z: 100,
            left: '20',
            top: 'middle',
            style: {
              fill: '#fff',
              text: title,
              // font: '1px  Source Han Sans CN;'
              fontSize: 18,
              fontFamily: 'Source Han Sans CN',
            }
          }]
        },
          ,],
        series: [{
          type: 'liquidFill',
          radius: '80%',
          center: ['50%', '50%'],
          data: data,
          backgroundStyle: {
            borderWidth: 0.1,
            borderColor: '#0EB8C9',
            color: 'rgba( 14,183,199,.2) '
          },
          outline: {
            show: false
          },
          color: [color, color1],
          label: {
            normal: {
              formatter: '',
            }
          }
        }]
      };


      myChart.setOption(option);
    },
  },
};
</script>
  
<style lang="less" scoped>
.echart {
  width: 400px;
  height: 280px;
}

@media (max-height: 1080px) {
  .echart {
    width: 400px;
    height: 280px !important;
  }
}
</style>