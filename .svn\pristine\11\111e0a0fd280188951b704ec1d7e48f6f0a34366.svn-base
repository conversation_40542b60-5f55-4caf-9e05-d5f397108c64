<template>
  <div class="echart" ref="echart"></div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  methods: {
    // 获取最近7天的日期
    getLast7Days() {
      const dates = [];
      const today = new Date();
      
      // 往前推7天，包含今天
      for (let i = 6; i >= 0; i--) {
        const date = new Date(today);
        date.setDate(today.getDate() - i);
        
        // 格式化日期为 MM.DD
        const formattedDate = `${(date.getMonth() + 1).toString().padStart(2, '0')}.${date.getDate().toString().padStart(2, '0')}`;
        dates.push(formattedDate);
      }
      
      return dates;
    },

    init() {
      const myChart = echarts.init(this.$refs.echart);
      const last7Days = this.getLast7Days(); // 获取最近7天的日期

      const option = {
        color: ["#3398DB"],
        title: {
          text: "单位：台",
          x: "6%",
          y: "2%",
          textStyle: {
            color: "#fff",
            fontSize: 16,
          },
        },
        legend: {
          itemGap: 16,
          itemWidth: 13,
          itemHeight: 13,
          top: 0,
          right: "1%",
          textStyle: { fontSize: 16, color: "#fff" },
          data: ["门控故障", "门位超限", "风速异常"],
        },
        grid: {
          top: "18%",
          bottom: "0%",
          left: "6%",
          right: "6%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: last7Days,  // 使用动态生成的日期数组

            axisLabel: {
              show: true,
              textStyle: {
                color: "#fff", // 将 X 轴标签字体颜色设置为白色
                fontSize: 16,
              },
            },
          },
        ],
        yAxis: [
          {
            axisTick: {
              alignWithLabel: true,
            },

            axisLabel: {
              show: true,
              textStyle: {
                color: "#fff", // 将 Y 轴标签字体颜色设置为白色
                fontSize: 16,
              },
            },
          },
        ],

        series: [
          {
            name: "门控故障",
            type: "bar",
            barWidth: "20%",
            data: [2, 1, 2, 2, 1, 1, 0],  // 示例数据，根据实际情况修改
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#FF88C2",
                  },
                  {
                    offset: 1,
                    color: "#FF44AA",
                  },
                ]),
                shadowColor: "rgba(0, 0, 0, 0.1)",
                shadowBlur: 10,
              },
            },
          },
          {
            name: "风速异常",
            type: "bar",
            barWidth: "20%",
            data: [2, 1, 2, 2, 4, 1, 2],  // 示例数据，根据实际情况修改
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#F5921C",
                  },
                  {
                    offset: 1,
                    color: "#F5921C",
                  },
                ]),
                shadowColor: "rgba(0, 0, 0, 0.1)",
                shadowBlur: 10,
              },
            },
          },
          {
            name: "门位超限",
            type: "bar",
            barWidth: "20%",
            data: [1, 1, 2, 1, 1, 1, 1],  // 示例数据，根据实际情况修改
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#FFFF77",
                  },
                  {
                    offset: 1,
                    color: "#FFFF77",
                  },
                ]),
                shadowColor: "rgba(0, 0, 0, 0.1)",
                shadowBlur: 10,
              },
            },
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>

<style lang="less" scoped>
.echart {
  width: 455px;
  height: 320px;
}
</style>
