import { createApp } from "vue";
import App from "./App.vue";
import router from "./router";
import store from "./store";
import "lib-flexible/flexible";
import "echarts-liquidfill/src/liquidFill.js";
import "../mock/mock.js";
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import zhCN from "element-plus/dist/locale/zh-cn.mjs";
import Title from "@/components/common/Title.vue";
import Title1 from "@/components/common/Title1.vue";
import Title2 from "@/components/common/Title2.vue";
import Title3 from "@/components/common/Title3.vue";
import FitScreen from "@fit-screen/vue";
// main.js
import VScaleScreen from "v-scale-screen";
import axios from "axios";

// // 将 axios 挂载到全局属性上，使其在所有组件中可以通过 this.$axios 使用
// app.config.globalProperties.$axios = axios;
createApp(App)
  .use(store)
  .use(router)
  .use(ElementPlus, { locale: zhCN })
  .use(FitScreen)
  .use(ElementPlus)
  .use(VScaleScreen)
  .component("Title", Title)
  .component("Title1", Title1)
  .component("Title2", Title2)
  .component("Title3", Title3)
  .mount("#app");
