<template>
  <div class="echart" ref="echart"></div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  props: {
    chartData: {
      type: Array,
      default: () => []
    },
    unit: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      chart: null
    };
  },
  watch: {
    chartData: {
      handler(newData) {
        this.updateChart();
      },
      deep: true
    }
  },
  mounted() {
    this.initChart();
  },
  beforeUnmount() {
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.echart);
      this.updateChart();
      
      // 添加窗口大小改变时的自适应
      window.addEventListener('resize', () => {
        this.chart && this.chart.resize();
      });
    },
    update<PERSON>hart() {
      if (!this.chart) return;

      const option = {
        title: {
          text: `${this.title} (${this.unit})`,
          x: "3%",
          y: "0%",
          textStyle: {
            color: "#fff",
            fontSize: 16,
          },
        },
        tooltip: {
          trigger: 'axis',
          formatter: (params) => {
            const data = params[0];
            return `${data.name}<br/>${data.marker}${data.value} ${this.unit}`;
          }
        },
        grid: {
          top: "16%",
          bottom: "2%",
          left: "2%",
          right: "2%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            margin: 2,
            textStyle: {
              fontSize: 14,
              color: "#fff",
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "#939ab6",
              opacity: 0.15,
            },
          },
          data: this.chartData.map(item => item.time)
        },
        yAxis: {
          type: "value",
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            margin: 2,
            textStyle: {
              fontSize: 14,
              color: "#fff",
            },
          },
          splitLine: {
            show: false,
          },
        },
        series: [
          {
            name: this.title,
            type: "line",
            z: 3,
            showSymbol: false,
            smoothMonotone: "x",
            lineStyle: {
              smooth: false,
              width: 3,
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(59,102,246)",
                  },
                  {
                    offset: 1,
                    color: "rgba(118,237,252)",
                  },
                ],
              },
            },
            data: this.chartData.map(item => item.value)
          },
        ],
      };

      this.chart.setOption(option);
    },
    setOption(option) {
      if (this.chart) {
        this.chart.setOption(option);
      }
    }
  },
};
</script>

<style lang="less" scoped>
.echart {
  width: 350px !important;
  height: 232px;
}
</style>
