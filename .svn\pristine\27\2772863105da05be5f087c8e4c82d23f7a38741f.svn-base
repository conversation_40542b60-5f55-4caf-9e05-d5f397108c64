<template>
  <div class="echart" ref="echart"></div>
</template>
  
<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  methods: {
    initData() {},
    init() {
      const myChart = echarts.init(this.$refs.echart);

      // app.title = "未优化用电量";

      const option = {
        //     legend: {
        //   textStyle: {
        //                 color:"#fff",
        //                 fontSize:14
        //             },
        //         top: '1%',
        //     right:"1%",
        //         data: ['未优化用电量', 'AI优化用电量'],
        //     },

        color: ["#3398DB"],
        title: {
          text: "K*wh",
          x: "6%",
          y: "2%",
          textStyle: {
            color: "#fff",
            fontSize: 14,
          },
        },
        legend: {
          data: ["历史用电监测"],
          top: "8%",
          right: "40px",
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            color: "#fff",
          },
        },
        grid: {
          top: "18%",
          bottom: "6%",
          left: "6%",
          right: "6%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: [
              "2/16",
              "2/17",
              "2/18",
              "2/19",
              "2/20",
              "2/21",
              "2/22",
              "2/23",
              "2/24",
              "2/25",
              "2/26",
            ],

            axisLabel: {
              show: true,
              textStyle: {
                color: "#fff", // 将 X 轴标签字体颜色设置为白色
              },
            },
          },
        ],
        yAxis: [
          {
            axisTick: {
              alignWithLabel: true,
            },

            axisLabel: {
              show: true,
              textStyle: {
                color: "#fff", // 将 Y 轴标签字体颜色设置为白色
              },
            },
          },
        ],

        series: [
          {
            name: "历史用电监测",
            type: "bar",
            barWidth: "20%",
            data: [380, 380, 380, 380, 380, 380, 380, 380, 380, 380, 380],
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#66C4FC",
                  },
                  {
                    offset: 1,
                    color: "#66C4FC",
                  },
                ]),
                shadowColor: "rgba(0, 0, 0, 0.1)",
                shadowBlur: 10,
              },
            },
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>
  
<style lang="less" scoped>
.echart {
  margin-left: 20px;
  width: 90%;
  margin-top: 20px;
  height: 180px;
}
</style>