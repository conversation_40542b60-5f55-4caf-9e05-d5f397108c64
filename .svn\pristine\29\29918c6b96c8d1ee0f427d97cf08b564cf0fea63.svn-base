<template>
  <div class="table-home">
    <div class="box">
      <div class="title">
        <div class="img">{{ selectedItem.name }}</div>
        <p class="local">位置：{{ selectedItem.location }}</p>
        <div class="wenzhixuanz">
          <!-- <div class="left">
            <div style="color: #08f9f9">使用中</div>
            /
            <div style="color: #a2abb0">停用</div>
          </div> -->
          <div class="right">
            <div>状态:</div>
            <div class="item">{{ selectedItem.status }}</div>
          </div>
        </div>
        <!-- this.selectedItem = item; -->
        <img class="x" src="../../assets/image/table-x.png" alt="" @click="close()" />
      </div>
      <div class="content">
        <div class="iframe">
          <!-- <div class="tupianimg" v-if="!isshowiframe"> <img :src="selectedItem.imgurl" alt="" /></div> -->
          <img class="tupianimg" v-if="!isshowiframe" :src="selectedItem.imgurl" alt="" />
          <!-- <img class="tupianimg" :src="zengtiimg" alt="" /> -->
          <iframe v-if="isshowiframe" :src="`https://qiye.3dzhanting.cn/share-model.html?ids=${ids}&res=1&isshow=1`"
            frameborder="0"></iframe>
        </div>
        <div class="rigth">
          <div>
            <div>
              <div class="titles">
                <div class="item" @click="qiehuna(1)" :class="{ item1: currentView === 1 }">
                  预约详情
                </div>
                <div class="item" @click="qiehuna(2)" :class="{ item1: currentView === 2 }">
                  设备详情

                </div>

              </div>

              <div v-if="currentView === 2">

                <div class="biaot" v-for="item in selectedItem.details" :key="item">
                  <img src="../../assets/image/table-qiu.png" alt="" />
                  <div class="name">{{ item.name }}</div>

                  <div class="value">{{ item.value }}</div>
                </div>
                <hr class="hr" />
                <div class="xiabox">
                  <div class="biaotss">
                    <!-- <img src="../../assets/image/table-qiu.png" alt="" /> -->
                    <div class="name">维护记录:</div>
                  </div>
                  <div class="biaotss">
                    <span class="name1">时间:</span>
                    <span class="name2">{{
                      selectedItem.maintenance_records.date
                    }}</span>
                  </div>
                  <div class="biaotss">
                    <span class="name1">维护内容:</span>
                    <span class="name2">{{
                      selectedItem.maintenance_records.maintenance_content
                    }}</span>
                  </div>
                  <div class="biaotss">
                    <span class="name1">下次维护时间:</span>
                    <span class="name2">{{
                      selectedItem.maintenance_records.next_maintenance_date
                    }}</span>
                  </div>
                  <!-- <echarts1> </echarts1> -->
                </div>
                <hr class="hr" />

                <div class="local1">
                  <div class="">
                    <span class="sp1"> 资产处管理人员：</span>
                    <span class="sp2">{{ selectedItem.management_name }}</span>
                  </div>
                  <div class="local11">
                    <span class="sp1">联系方式：</span>
                    <span class="sp2">{{
                      selectedItem.management_contact_info
                    }}</span>
                  </div>
                </div>
              </div>
              <div v-else>
                <div class="titless">
                  <div>预约人</div>
                  <div>预约时间</div>
                  <div>预约时长</div>
                  <!-- <div>门禁编号</div> -->
                  <div>预约状态</div>
                </div>
                <div class="contents" v-for="item in tableDatass" :key="item">
                  <div>{{ item.name }}</div>
                  <div>{{ item.date }}</div>
                  <div>{{ item.duration }}</div>
                  <!-- <div>{{ item.roomNumber }}</div> -->
                  <div>{{ item.status }}</div>
                </div>
                <!-- <div class="anniu" @click="yuyue">点击立即预约</div> -->

              </div>
            </div>

          </div>

        </div>
      </div>
    </div>
  </div>
</template>

<script>
import echarts1 from "@/components/echarts/bingjifang/echarts4.vue";
import axios from "axios";
export default {
  components: {
    echarts1,
  },
  props: ["selectedItem", "zengtiimg"],

  data() {
    return {
      // isshowiframe: true,
      tableDatass: [
        // {
        //   name: "张三",
        //   date: "2024-09-01",
        //   duration: "2H (13:00-15:00)",
        //   roomNumber: "001",
        //   status: "已成功",
        // },

      ],
      currentView: 1,
      dist: false,
      items: ["T1", "T2"],
      backgroundClasses: ["bg-image-1", "bg-image-2", "bg-image-3"],
      // Array of background images using require
      backgrounds: [
        { backgroundImage: `url(${require("../../assets/image/image1.jpg")})` },
        { backgroundImage: `url(${require("../../assets/image/image2.jpg")})` },
        { backgroundImage: `url(${require("../../assets/image/image3.jpg")})` },
      ],
      currentIndex: 0, // Initial index for class and image

      selectedIndex: 0,
      inputs: [
        {
          name: "温度",
          value: "4℃",
          type: "温度",
          ztaqi: "4℃",
        },

        {
          name: "湿度",
          value: "56%",
          type: "门状态",
          ztaqi: "已开启",
        },
      ],
    };
  },
  watch: {
    selectedItem(newValue, oldValue) {
      // 当 selectedItem 发生变化时执行的函数
      console.log("selectedItem has changed:", newValue);
      this.getyiqiyuyue()
    }
  },
  mounted() {
    this.getyiqiyuyue()
  },
  computed: {

    isshowiframe() {
      const itemName = this.selectedItem && this.selectedItem.name;
      return itemName == "场发射透射电子显微镜JEM-F200" || itemName == "环境扫描电子显微镜" || itemName == "极高分辨场发射扫描电镜" || itemName.includes("核磁共振谱仪") || itemName.includes("冷冻切片") || itemName.includes("离子减薄仪");
    },
    ids() {
      const itemName = this.selectedItem && this.selectedItem.name;
      // 创建一个映射对象，根据 itemName 返回对应的值
      const itemMapping = {
        "场发射透射电子显微镜JEM-F200": 'UoBAeT3ZLfVje-pMOizgww==',
        "环境扫描电子显微镜": 'feQYGPfQDz5OogPPP9LQmA==',
        "极高分辨场发射扫描电镜": 'GgKPe3Rl3YzjfHk3NY8n9Q==',
        "核磁共振谱仪": 'dwPEr0uLrqp2DRB7nRW3XA==',
        "冷冻切片": 'nOkagGZ9DwKsphP9ZKq-fQ==',
        "离子减薄仪": 'ZKFmtO8ttmydzvkpBcfdmw=='
      };

      // 遍历映射对象的键，检查 itemName 是否包含这些键
      for (const key in itemMapping) {
        if (itemName && itemName.includes(key)) {
          return itemMapping[key]; // 如果包含，返回对应的值
        }
      }

      // 如果没有匹配的字段，返回默认值
      return '';
    },
    // Get the current class name
    currentClass() {
      return this.backgroundClasses[this.currentIndex];
    },
    // Get the current background style
    currentBackground() {
      return this.backgrounds[this.currentIndex];
    },
  },
  methods: {
    getyiqiyuyue() {
      console.log(this.selectedItem, '预约');
      const headers = {
        clientid: '5a298e93-158d-4e22-83cf-6ceb62e9b4f1',
        clientsecret: '2c8ec39e-9887-482a-b28b-e64c496b601c'
      };
      const body = {
        "method": "gpui/eq_reserv/reservList",
        "params": {
          "dtstart": 0,
          "dtend": 0,
          "params": {
            "equipment_id": 1175,
            "limit": [
              0,
              10
            ]
          }
        }
      }
      axios.post('http://yiqi.tju.edu.cn/lims/api', body, { headers })
        .then(response => {
          console.log(response.data, 535);

          // 调用函数进行转换
          this.tableDatass = transformData(response.data.response);

          function transformData(responseData) {
            return responseData.map(item => {
              // 处理时间：转换为 Date 对象
              const startTime = new Date(item.start * 1000);  // start: Unix 时间戳转日期
              const endTime = new Date(item.end * 1000);      // end: Unix 时间戳转日期

              // 获取开始时间和结束时间的具体日期和时间（yyyy-mm-dd HH:mm:ss）
              const formatTime = (time) => {
                const year = time.getFullYear();
                const month = String(time.getMonth() + 1).padStart(2, '0'); // 月份从 0 开始，补齐为两位数
                const day = String(time.getDate()).padStart(2, '0');
                const hours = String(time.getHours()).padStart(2, '0');
                const minutes = String(time.getMinutes()).padStart(2, '0');
                const seconds = String(time.getSeconds()).padStart(2, '0');
                return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
              };

              const startFormatted = formatTime(startTime);
              const endFormatted = formatTime(endTime);

              // 计算时间差：差值（单位是秒）
              const durationInSeconds = item.end - item.start;
              const durationHours = Math.floor(durationInSeconds / 3600); // 小时
              const durationMinutes = Math.floor((durationInSeconds % 3600) / 60); // 分钟

              // 格式化 duration 为 "X小时X分钟"
              const duration = `${durationHours}H ${durationMinutes}min`;

              // 处理状态，status 为 0 -> 未成功, 1 -> 已成功
              const status = item.status === "0" ? "已成功" : "已成功";

              return {
                name: item.user_name,  // user_name -> name
                date: `${startFormatted} - ${endFormatted}`,  // start -> date
                duration: duration,    // 计算的时间差
                status: status         // 根据 status 设置状态
              };
            });
          }
        })
        .catch(error => {
          console.error('Error:', error);
        });
    },
    qiehuna(view) {
      // 切换显示的视图，view可以是1或2
      this.currentView = view;
    },
    switchBackground() {
      this.currentIndex = (this.currentIndex + 1) % this.backgrounds.length;
    },
    toggleSelection(index) {
      if (index == 0) {
        this.inputs = [
          {
            name: "温度",
            value: "4℃",
          },
          // {
          //   name: "分辨率",
          //   value: "0.01℃",
          // },
          // {
          //   name: "精度",
          //   value: "±0.5℃(-100℃~100℃);±1.0℃(其他范围);",
          // },
          {
            name: "门状态",
            value: "关",
          },
        ];
      } else if (index == 1) {
        this.inputs = [
          {
            name: "温度",
            value: "4.1℃",
          },
          // {
          //   name: "分辨率",
          //   value: "0.01℃",
          // },
          // {
          //   name: "精度",
          //   value: "±0.5℃(-100℃~100℃);±1.0℃(其他范围);",
          // },
          {
            name: "门状态",
            value: "关",
          },
        ];
      } else if (index == 2) {
        this.inputs = [];
      } else if (index == 3) {
        this.inputs = [];
      }

      this.selectedIndex = index; // 否则选中当前的
    },
    close() {
      this.$emit("hidedetails");
    },
  },
};
</script>

<style lang="less" scoped>
.wenzhixuanz {
  display: flex;
  align-items: center;

  .left {
    display: flex;
    font-family: Source Han Sans SC;
    font-weight: 400;
    font-size: 19px;
  }

  .right {
    width: 151px;
    margin-left: 35px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-family: Source Han Sans SC;
    font-weight: bold;
    font-size: 19px;
    color: #fff;

    .item {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 90px;
      height: 35px;
      background: url("../../assets/image/lanse.png");
      font-family: Source Han Sans SC;
      font-weight: bold;
      font-size: 15px;
      color: #64dbfa;
      // cursor: pointer;
      display: flex;
      align-items: center;
    }

    .item1 {
      width: 90px;
      height: 35px;
    }
  }
}

.local {
  font-family: Source Han Sans SC;
  font-weight: bold;
  font-size: 18px;
  color: #ffffff;

  // width: 200px;
  //  display: flex;
  //  flex-direction: row;
  //  justify-content: space-between;
  .lianxi {
    margin-left: 118px;
  }
}

.local1 {
  font-family: Source Han Sans SC;
  font-weight: bold;
  font-size: 18px;
  color: #ffffff;
  text-align: left;
  width: 600px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;

  .local11 {
    margin-left: 90px;
  }

  .sp1 {
    color: #b1f2f2;
  }
}

.table-home {
  .box {
    color: #fff;
    background: url("../../assets/image/table-beijing.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 1221px;
    height: 810px;
    padding-top: 34px;

    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-right: 28px;
      margin-left: 31px;

      .img {
        letter-spacing: 0.5px;
        background: url("../../assets/image/title.png");
        background-size: 100% 100%;
        width: 354px;
        height: 34px;
        font-family: Alibaba PuHuiTi;
        font-weight: 500;
        font-style: italic;
        font-size: 22px;
        color: #ffffff;
        text-align: left;
        line-height: 21px;
        padding-left: 38px;
        white-space: nowrap;
        /* 防止文本换行 */
        overflow: hidden;
        /* 隐藏超出容器的内容 */
        text-overflow: ellipsis;
        /* 超出部分显示为省略号 */
        display: flex;
        // padding-top: 10px;

        justify-content: space-between;
      }

      .x {
        cursor: pointer;
      }
    }

    .content {
      display: flex;

      justify-content: space-between;
      margin: 23px 29px 0 67px;

      .iframe {

        display: flex;
        align-items: center;
        width: 429px;
        height: 654px;

        iframe {
          width: 100%;
          height: 100%;
        }
      }

      .rigth {
        margin-left: 33px;
        display: flex;
        align-items: center;
        flex-direction: column;
        // justify-content: center;
        // gap: 62px;

        .qiehuan {
          display: flex;
          align-items: center;
          // justify-content: space-between;
          width: 291px;
          height: 57px;
          // margin-left: 15px;
          margin-right: 15px;

          .xuanze {
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            background: url("../../assets/image/xiaoqiehuan.png");
            background-repeat: no-repeat;
            background-size: 100% 100%;
            width: 63px;
            height: 48px;
            font-family: Source Han Sans SC;
            font-weight: 400;
            font-size: 17px;
            color: #ffffff;
          }

          .selected {
            padding-bottom: 8px !important;
            margin-top: 9px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            background: url("../../assets/image/xiaoxuanzhong.png");
            background-repeat: no-repeat;
            background-size: 100% 100%;
            width: 63px;
            height: 57px;
            font-family: Source Han Sans SC;
            font-weight: 400;
            font-size: 17px;
            color: #ffffff;
          }
        }

        .biaot {
          width: 607px;
          height: 47px;
          margin-top: 3px;
          display: flex;
          align-items: center;

          .name {
            width: 125px;
            font-family: "Roboto", sans-serif;

            font-weight: bold;
            font-size: 17px;
            color: #b1f2f2;
            margin-left: 6px;
          }

          .value {
            background: url("../../assets/image/tableinptubox.png");
            background-repeat: no-repeat;
            background-size: 100% 100%;
            // 418px x 47px
            width: 418px;
            height: 47px;
            font-family: "Roboto", sans-serif;
            font-weight: 400;
            font-size: 17px;
            color: #ffffff;
            line-height: 47px;
            padding-left: 22px;
            margin-left: 24px;
          }
        }
      }

      .hr {
        margin-top: 10px;
        margin-bottom: 10px;
        width: 100%;
        background-color: rgba(36, 101, 138, 1);
        color: rgba(36, 101, 138, 1);
      }
    }

    .biaotss {
      height: 37px;
      // margin-top: 12px;
      display: flex;
      align-items: center;

      .name {
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 19px;
        color: #b1f2f2;
        margin-left: 6px;
      }

      .name1 {
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 17px;
        color: #b1f2f2;
        margin-left: 40px;
      }

      .name2 {
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 17px;
        color: #fff;
        margin-left: 8px;
      }
    }
  }
}

.xiabox {
  width: 100% !important;
}

.bg-image-1 {
  transition: background 0.3s ease-in-out;
}

.bg-image-2 {
  transition: background 0.3s ease-in-out;
}

.bg-image-3 {
  transition: background 0.3s ease-in-out;
}

.tupianimg {
  width: 429px;
  height: 416px;
}

.boxsttt {
  margin-top: 6px;
  padding-top: 5px;
  margin-bottom: 0.225rem;

  // height: 800px;

  overflow-y: scroll;

  /* 设置垂直滚动条 */
  /* 设置滚动条的样式 */
  &::-webkit-scrollbar {
    width: 0.1px;
    /* 设置滚动条的宽度 */
  }

  /* 设置滚动条轨道的样式 */
  &::-webkit-scrollbar-track {
    background-color: #f1f1f1;
    /* 设置滚动条轨道的背景色 */
  }

  /* 设置滚动条滑块的样式 */
  &::-webkit-scrollbar-thumb {
    background-color: #334f6e;
    /* 设置滚动条滑块的背景色 */
  }

  /* 鼠标悬停在滚动条上时的样式 */
  &::-webkit-scrollbar-thumb:hover {
    background-color: #555;
    /* 设置鼠标悬停时滚动条滑块的背景色 */
  }

  .el-input {
    width: 305px;
    height: 34px;
    color: #fff !important;

    ::v-deep .el-input__wrapper {
      background: url("../../assets/image/inputss.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
      box-shadow: none !important;
    }
  }

  .suosuo {
    position: absolute;
    top: 66px;
    left: 285px;
  }

  .xiaobox {
    margin-top: 20px;
    display: flex;
    align-items: center;

    .siqiu {
      width: 16px;
      height: 16px;
    }

    .shuru {
      display: flex;
      align-items: center;
      padding-left: 10px;
      background: url("../../assets/image/shebei12.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 396px;
      height: 37px;
      font-family: Source Han Sans SC;
      font-weight: bold;
      font-size: 12px;
      color: #ffffff;
    }
  }

  .xiaoboxs {
    cursor: pointer;
    margin-top: 16px;
    display: flex;
    margin-left: 5px;
    align-items: center;

    .nihaowo {
      width: 167px;
      font-family: Source Han Sans SC;
      font-weight: bold;
      font-size: 18px;
      color: #ffffff;

      display: flex;
    }

    .siqiu {
      width: 16px;
      height: 16px;
      margin-left: 10px;
      margin-right: 7px;
    }

    .shuru {
      display: flex;
      align-items: center;
      padding-left: 10px;
      background: url("../../assets/image/shebei12.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 396px;
      height: 37px;
      font-family: Source Han Sans SC;
      font-weight: bold;
      font-size: 12px;
      color: #ffffff;
    }
  }
}

.rigth {
  margin-left: 660px;
  display: flex;

  .titles {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 11px;

    .item {
      color: #fff;
      font-size: 19px;

      background: url("../../assets/image/weixuanzhogn.png");

      width: 305px;
      height: 57px;
      background-repeat: no-repeat;
      background-size: 100% 100%;
      // align-items: center;
      // justify-content: center;
      text-align: center;
      font-family: Source Han Sans SC;
      font-weight: bold;
      font-size: 20px;
      line-height: 56px;
      color: #a09a97;
      cursor: pointer;
    }

    .item1 {
      background: url("../../assets/image/tablexuanzhong.png");
      cursor: pointer;
      width: 305px;
      height: 66px;
      line-height: 56px !important;
      background-repeat: no-repeat;
      background-size: 100% 100%;

      font-family: Source Han Sans SC;
      font-weight: bold;
      font-size: 20px;
      color: #ffffff;
      margin-top: 9px;

      text-align: center;
    }
  }
}

.titless {
  margin-right: 10px;
  width: 100%;
  background: rgba(44, 59, 100, 0.5);
  height: 51px;
  margin-top: 8px;
  display: flex;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  font-size: 17px;
  color: #40d7ff;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;

}

.titless div:nth-child(1) {
  flex: 1.2;
}

.titless div:nth-child(2) {
  flex: 7;
}

.titless div:nth-child(3) {
  flex: 2;
}

.titless div:nth-child(4) {
  flex: 1.6;
}

.contents {
  border-bottom: 1px solid #293e5e;
  margin-right: 10px;
  width: 100%;
  background: rgba(56, 78, 115, 0.2);
  height: 51px;

  display: flex;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  font-size: 17px;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.contents div:nth-child(1) {
  flex: 1.2;
}

.contents div:nth-child(2) {
  flex: 7;
}

.contents div:nth-child(3) {
  flex: 2;
}

.contents div:nth-child(4) {
  flex: 1.6;
}

.contents:nth-child(odd) {
  background: rgba(52, 76, 116, 0.4);
  /* 深色背景 */
}

.contents:nth-child(even) {
  background: rgba(55, 76, 117, 0.2);
  /* 浅色背景 */
}

.anniu {
  cursor: pointer;
  background: url("../../assets/image/dianjianniu.png");
  width: 159px;
  height: 48px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  font-family: Source Han Sans CN;
  font-weight: 400;
  font-size: 20px;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  bottom: 46px;
  right: 44px;
  z-index: 1;
}
</style>
