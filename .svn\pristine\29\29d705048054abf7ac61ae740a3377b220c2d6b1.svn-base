<template>
  <div>
    <component :is="componentTag" @fatherMethoddd="fatherMethoddd" v-if="isshowwhat"></component>
    <div class="botbtn">
      <div
        v-for="(item, index) in changeTitle"
        :key="index"
        :class="titactive == index ? 'btt1' : 'btt'"
        @click="changetit(index)"
      >
        {{ item }}
      </div>
    </div>
    <tedai
      :ids="ids"
      :selectedItem="selectedItem"
      class="sbdetails"
      :zengtiimg="zengtiimg"
      v-if="isshowsss"
      @hidedetails="hidedetailsss"
    ></tedai>
    <biaoGe
      :Title="Title"
      @xuanze-dialog="xuanzedialog"
      v-if="isshow"
      @hidedetails="hidedetails"
      :tableTitle="tableTitle"
      :tableDataItem="devicedata"
    ></biaoGe>
    <div class="container" v-if="!isshowwhat">
      <div
        class="left-panel"
        :class="{
          'left-panel-active': showdh,
          'no-animation': noAnimation,
          'left-panel-active1': showdh1,
        }"
      >
        <Title2
          @open-dialog="opendialog"
          class="ltitle1"
          tit="大仪管理"
          title="查看详情"
        >
          <div class="box">
            <div>
              <el-input
                class="el-input"
                v-model="input"
                placeholder="请输入内容"
              ></el-input>
            </div>
            <!-- <div class="xiaobox">
              <img class="siqiu" src="../assets/image/shixinqiu.png" alt="" />
              <div class="shuru">全部设备</div>
            </div> -->
            <div class="menu-container">
              <!-- 动态生成菜单 -->
              <div class="menu">
                <div
                  v-for="(menu, index) in devicedata"
                  :key="index"
                  class="menu-group"
                >
                  <div class="qiuqiu">
                    <img
                      class="siqiu"
                      src="../assets/image/shixinqiu.png"
                      alt=""
                    />
                    <div class="menu-item" @click="toggleSubMenu(menu.id)">
                      {{ menu.category }}
                    </div>
                    <!-- <div class="listtypes" @click="showdetails(menu)">详情</div> -->
                  </div>

                  <div v-show="activeSubmenu === menu.id" class="submenu">
                    <div
                      v-for="(item, subIndex) in menu.items"
                      :key="subIndex"
                      class="submenu-item"
                      @click="setContent(item)"
                    >
                      <p class="ellipsis" :title="item.name">{{ item.name }}</p>

                      <!-- <div class="listtype">{{ item.type }}</div> -->
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- shebei12.png -->
        </Title2>
      </div>
      <!-- shebeibgc.png -->
      <!-- 右侧内容 -->
      <!-- 右侧内容 -->

      <div
        class="right-panel"
        :class="{
          'right-panel-active': showdh,
          'no-animation': noAnimation,
          'right-panel-active1': showdh1,
        }"
      ></div>
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import component0 from "@/views/dayi/zichan.vue";
import huanxing from "@/components/echarts/huanxing.vue";
import zhexian from "@/components/echarts/zhexian.vue";
import zhexian1 from "@/components/echarts/zhexian1.vue";
import SystemDete from "@/components/echarts/SystemDete.vue";
import echarts1 from "@/components/echarts/bingjifang/echarts3.vue";
import tedai from "@/components/common/cl_details.vue";
import biaoGe from "@/components/common/biaoGes.vue";
import shuangxiang from "@/components/echarts/shuangxiang.vue";

// import details from "@/components/common/details.vue";
export default {
  // import引入的组件需要注入到对象中才能使用
  components: {
    tedai,
    huanxing,
    zhexian,
    zhexian1,
    SystemDete,
    echarts1,
    shuangxiang,
    biaoGe,
    component0
  },
  props: ["tabledata", "zengtiimg"],
 
  data() {
    // 这里存放数据
    return {
           isshowwhat: true,
      isshowsss: false,
      titactive: 0,
  
      changeTitle: ["数据统计", "数据列表"],
      activeSubmenu: null, // 当前激活的子菜单
      activeContent: null, // 当前显示的内容
      newArr: [],
      isshow: false,
      xxxx: false,
      cgqlist: [],
      listtable: [],
      devicedata: [
        {
          id: "menu1",
          category: "5F",
          items: [
            {
              name: "离子溅射仪", //产品名称
              imgurl:
                "https://3d.dddtask.cn/enginner-xufeng/image/tianjin/lizi.png",
              location: "北洋园校区54楼, E105", //位置
              status: "已领用", //仪器状态  可领用

              details: [
                {
                  name: "设备名称",
                  value: "场发射扫描电子显微镜 （S4800）",
                },
                {
                  name: "今日预约",
                  value: "已预约",
                },
                {
                  name: "原值",
                  value: "--",
                },
                {
                  name: "购入时间",
                  value: "2020/09/02",
                },
                {
                  name: "品牌",
                  value: "--",
                },
                {
                  name: "生产厂家",
                  value: "--",
                },
                {
                  name: "供应商名称",
                  value: "--",
                },
                {
                  name: "供应商联系信息",
                  value: "--",
                },
              ],
              maintenance_records: {
                maintenance_content: "校准和系统升级", //维护内容
                date: "2024-01-10", //维护时间
                next_maintenance_date: "2022-01-10", //下次维护时间
              },
              management_name: "王工",
              management_contact_info: "15698567542",
            },
            {
              name: "场发射扫描电子显微镜 （S4800）", //产品名称
              imgurl:
                "https://3d.dddtask.cn/enginner-xufeng/image/tianjin/xwj.png",
              location: "北洋园校区54楼, E105", //位置
              status: "已领用", //仪器状态  可领用

              details: [
                {
                  name: "产品名称",
                  value: "场发射扫描电子显微镜（Apreo S LoVac）",
                },
                {
                  name: "原值",
                  value: "--",
                },
                {
                  name: "购入时间",
                  value: "2009/12/07",
                },
                {
                  name: "品牌",
                  value: "日立 ",
                },
                {
                  name: "生产厂家",
                  value: "日立",
                },
                {
                  name: "供应商名称",
                  value: "--",
                },
                {
                  name: "供应商联系信息",
                  value: "--",
                },
              ],
              maintenance_records: {
                maintenance_content: "--", //维护内容
                date: "--", //维护时间
                next_maintenance_date: "--", //下次维护时间
              },
              management_name: "王工",
              management_contact_info: "15698567542",
            },
            {
              name: "热重及同步热分析仪", //产品名称
              imgurl:
                "https://3d.dddtask.cn/enginner-xufeng/image/tianjin/fxy.png",
              location: "卫津路校区结晶中心 , 3306", //位置
              status: "已领用", //仪器状态  可领用

              details: [
                {
                  name: "产品名称",
                  value: "热重及同步热分析仪",
                },
                {
                  name: "原值",
                  value: "--",
                },
                {
                  name: "购入时间",
                  value: "2011/03/04",
                },
                {
                  name: "品牌",
                  value: "梅特勒 ",
                },
                {
                  name: "生产厂家",
                  value: "梅特勒 ",
                },
                {
                  name: "供应商名称",
                  value: "--",
                },
                {
                  name: "供应商联系信息",
                  value: "--",
                },
              ],
              maintenance_records: {
                maintenance_content: "--", //维护内容
                date: "--", //维护时间
                next_maintenance_date: "--", //下次维护时间
              },
              management_name: "王工",
              management_contact_info: "15698567542",
            },
          ],
        },
        {
          id: "menu2",
          category: "4F",
          items: [
            {
              name: "离子溅射仪", //产品名称
              imgurl:
                "https://3d.dddtask.cn/enginner-xufeng/image/tianjin/lizi.png",
              location: "北洋园校区54楼, E105", //位置
              status: "已领用", //仪器状态  可领用

              details: [
                {
                  name: "设备名称",
                  value: "场发射扫描电子显微镜 （S4800）",
                },
                {
                  name: "今日预约",
                  value: "已预约",
                },
                {
                  name: "原值",
                  value: "--",
                },
                {
                  name: "购入时间",
                  value: "2020/09/02",
                },
                {
                  name: "品牌",
                  value: "--",
                },
                {
                  name: "生产厂家",
                  value: "--",
                },
                {
                  name: "供应商名称",
                  value: "--",
                },
                {
                  name: "供应商联系信息",
                  value: "--",
                },
              ],
              maintenance_records: {
                maintenance_content: "校准和系统升级", //维护内容
                date: "2024-01-10", //维护时间
                next_maintenance_date: "2022-01-10", //下次维护时间
              },
              management_name: "王工",
              management_contact_info: "15698567542",
            },
            {
              name: "场发射扫描电子显微镜 （S4800）", //产品名称
              imgurl:
                "https://3d.dddtask.cn/enginner-xufeng/image/tianjin/xwj.png",
              location: "北洋园校区54楼, E105", //位置
              status: "已领用", //仪器状态  可领用

              details: [
                {
                  name: "产品名称",
                  value: "场发射扫描电子显微镜（Apreo S LoVac）",
                },
                {
                  name: "原值",
                  value: "--",
                },
                {
                  name: "购入时间",
                  value: "2009/12/07",
                },
                {
                  name: "品牌",
                  value: "日立 ",
                },
                {
                  name: "生产厂家",
                  value: "日立",
                },
                {
                  name: "供应商名称",
                  value: "--",
                },
                {
                  name: "供应商联系信息",
                  value: "--",
                },
              ],
              maintenance_records: {
                maintenance_content: "--", //维护内容
                date: "--", //维护时间
                next_maintenance_date: "--", //下次维护时间
              },
              management_name: "王工",
              management_contact_info: "15698567542",
            },
            {
              name: "热重及同步热分析仪", //产品名称
              imgurl:
                "https://3d.dddtask.cn/enginner-xufeng/image/tianjin/fxy.png",
              location: "卫津路校区结晶中心 , 3306", //位置
              status: "已领用", //仪器状态  可领用

              details: [
                {
                  name: "产品名称",
                  value: "热重及同步热分析仪",
                },
                {
                  name: "原值",
                  value: "--",
                },
                {
                  name: "购入时间",
                  value: "2011/03/04",
                },
                {
                  name: "品牌",
                  value: "梅特勒 ",
                },
                {
                  name: "生产厂家",
                  value: "梅特勒 ",
                },
                {
                  name: "供应商名称",
                  value: "--",
                },
                {
                  name: "供应商联系信息",
                  value: "--",
                },
              ],
              maintenance_records: {
                maintenance_content: "--", //维护内容
                date: "--", //维护时间
                next_maintenance_date: "--", //下次维护时间
              },
              management_name: "王工",
              management_contact_info: "15698567542",
            },
          ],
        },
        // {
        //   id: "menu3",
        //   category: "3F",
        //   items: [
        //     {
        //       name: "台式扫描电子显微镜",
        //       imgurl: "",
        //       location: "研发中心",
        //       status: "已领用",
        //       details: [
        //         { name: "产品名称", value: "电脑" },
        //         { name: "原值", value: "12000" },
        //         { name: "购入时间", value: "2023-05-15" },
        //         { name: "品牌", value: "戴尔" },
        //         { name: "生产厂家", value: "戴尔科技有限公司" },
        //         { name: "供应商名称", value: "广州科技设备有限公司" },
        //         { name: "供应商联系信息", value: "电话: +86-20-87654321" },
        //       ],
        //       maintenance_records: {
        //         maintenance_content: "系统升级和硬盘检测",
        //         date: "2024-03-01",
        //         next_maintenance_date: "2025-03-01",
        //       },
        //       management_name: "李工",
        //       management_contact_info: "13987654321",
        //     },
        //     {
        //       name: "打印机-DYJ-021",
        //       imgurl: "",
        //       location: "办公地点",
        //       status: "可领用",
        //       details: [
        //         { name: "产品名称", value: "打印机" },
        //         { name: "原值", value: "3000" },
        //         { name: "购入时间", value: "2022-10-05" },
        //         { name: "品牌", value: "惠普" },
        //         { name: "生产厂家", value: "惠普（HP）有限公司" },
        //         { name: "供应商名称", value: "北京办公设备有限公司" },
        //         { name: "供应商联系信息", value: "电话: +86-10-12345678" },
        //       ],
        //       maintenance_records: {
        //         maintenance_content: "墨盒更换与清洁",
        //         date: "2024-02-12",
        //         next_maintenance_date: "2024-08-12",
        //       },
        //       management_name: "王工",
        //       management_contact_info: "15698567542",
        //     },
        //     {
        //       name: "智慧屏-ZHP-101",
        //       imgurl: "",
        //       location: "会议室",
        //       status: "已领用",
        //       details: [
        //         { name: "产品名称", value: "智慧屏" },
        //         { name: "原值", value: "18000" },
        //         { name: "购入时间", value: "2021-12-20" },
        //         { name: "品牌", value: "华为" },
        //         { name: "生产厂家", value: "华为技术有限公司" },
        //         { name: "供应商名称", value: "深圳电子设备有限公司" },
        //         { name: "供应商联系信息", value: "电话: +86-755-12345678" },
        //       ],
        //       maintenance_records: {
        //         maintenance_content: "屏幕校准与系统更新",
        //         date: "2023-11-10",
        //         next_maintenance_date: "2024-11-10",
        //       },
        //       management_name: "陈工",
        //       management_contact_info: "13876543210",
        //     },
        //   ],
        // }, {
        //   id: "menu4",
        //   category: "2F",
        //   items: [
        //     {
        //       name: "台式扫描电子显微镜",
        //       imgurl: "",
        //       location: "研发中心",
        //       status: "已领用",
        //       details: [
        //         { name: "产品名称", value: "电脑" },
        //         { name: "原值", value: "12000" },
        //         { name: "购入时间", value: "2023-05-15" },
        //         { name: "品牌", value: "戴尔" },
        //         { name: "生产厂家", value: "戴尔科技有限公司" },
        //         { name: "供应商名称", value: "广州科技设备有限公司" },
        //         { name: "供应商联系信息", value: "电话: +86-20-87654321" },
        //       ],
        //       maintenance_records: {
        //         maintenance_content: "系统升级和硬盘检测",
        //         date: "2024-03-01",
        //         next_maintenance_date: "2025-03-01",
        //       },
        //       management_name: "李工",
        //       management_contact_info: "13987654321",
        //     },
        //     {
        //       name: "打印机-DYJ-021",
        //       imgurl: "",
        //       location: "办公地点",
        //       status: "可领用",
        //       details: [
        //         { name: "产品名称", value: "打印机" },
        //         { name: "原值", value: "3000" },
        //         { name: "购入时间", value: "2022-10-05" },
        //         { name: "品牌", value: "惠普" },
        //         { name: "生产厂家", value: "惠普（HP）有限公司" },
        //         { name: "供应商名称", value: "北京办公设备有限公司" },
        //         { name: "供应商联系信息", value: "电话: +86-10-12345678" },
        //       ],
        //       maintenance_records: {
        //         maintenance_content: "墨盒更换与清洁",
        //         date: "2024-02-12",
        //         next_maintenance_date: "2024-08-12",
        //       },
        //       management_name: "王工",
        //       management_contact_info: "15698567542",
        //     },
        //     {
        //       name: "智慧屏-ZHP-101",
        //       imgurl: "",
        //       location: "会议室",
        //       status: "已领用",
        //       details: [
        //         { name: "产品名称", value: "智慧屏" },
        //         { name: "原值", value: "18000" },
        //         { name: "购入时间", value: "2021-12-20" },
        //         { name: "品牌", value: "华为" },
        //         { name: "生产厂家", value: "华为技术有限公司" },
        //         { name: "供应商名称", value: "深圳电子设备有限公司" },
        //         { name: "供应商联系信息", value: "电话: +86-755-12345678" },
        //       ],
        //       maintenance_records: {
        //         maintenance_content: "屏幕校准与系统更新",
        //         date: "2023-11-10",
        //         next_maintenance_date: "2024-11-10",
        //       },
        //       management_name: "陈工",
        //       management_contact_info: "13876543210",
        //     },
        //   ],
        // }, {
        //   id: "menu6",
        //   category: "1F",
        //   items: [
        //     {
        //       name: "台式扫描电子显微镜",
        //       imgurl: "",
        //       location: "研发中心",
        //       status: "已领用",
        //       details: [
        //         { name: "产品名称", value: "电脑" },
        //         { name: "原值", value: "12000" },
        //         { name: "购入时间", value: "2023-05-15" },
        //         { name: "品牌", value: "戴尔" },
        //         { name: "生产厂家", value: "戴尔科技有限公司" },
        //         { name: "供应商名称", value: "广州科技设备有限公司" },
        //         { name: "供应商联系信息", value: "电话: +86-20-87654321" },
        //       ],
        //       maintenance_records: {
        //         maintenance_content: "系统升级和硬盘检测",
        //         date: "2024-03-01",
        //         next_maintenance_date: "2025-03-01",
        //       },
        //       management_name: "李工",
        //       management_contact_info: "13987654321",
        //     },
        //     {
        //       name: "打印机-DYJ-021",
        //       imgurl: "",
        //       location: "办公地点",
        //       status: "可领用",
        //       details: [
        //         { name: "产品名称", value: "打印机" },
        //         { name: "原值", value: "3000" },
        //         { name: "购入时间", value: "2022-10-05" },
        //         { name: "品牌", value: "惠普" },
        //         { name: "生产厂家", value: "惠普（HP）有限公司" },
        //         { name: "供应商名称", value: "北京办公设备有限公司" },
        //         { name: "供应商联系信息", value: "电话: +86-10-12345678" },
        //       ],
        //       maintenance_records: {
        //         maintenance_content: "墨盒更换与清洁",
        //         date: "2024-02-12",
        //         next_maintenance_date: "2024-08-12",
        //       },
        //       management_name: "王工",
        //       management_contact_info: "15698567542",
        //     },
        //     {
        //       name: "智慧屏-ZHP-101",
        //       imgurl: "",
        //       location: "会议室",
        //       status: "已领用",
        //       details: [
        //         { name: "产品名称", value: "智慧屏" },
        //         { name: "原值", value: "18000" },
        //         { name: "购入时间", value: "2021-12-20" },
        //         { name: "品牌", value: "华为" },
        //         { name: "生产厂家", value: "华为技术有限公司" },
        //         { name: "供应商名称", value: "深圳电子设备有限公司" },
        //         { name: "供应商联系信息", value: "电话: +86-755-12345678" },
        //       ],
        //       maintenance_records: {
        //         maintenance_content: "屏幕校准与系统更新",
        //         date: "2023-11-10",
        //         next_maintenance_date: "2024-11-10",
        //       },
        //       management_name: "陈工",
        //       management_contact_info: "13876543210",
        //     },
        //   ],
        // }, {
        //   id: "menu5",
        //   category: "B1F",
        //   items: [
        //     {
        //       name: "台式扫描电子显微镜",
        //       imgurl: "",
        //       location: "研发中心",
        //       status: "已领用",
        //       details: [
        //         { name: "产品名称", value: "电脑" },
        //         { name: "原值", value: "12000" },
        //         { name: "购入时间", value: "2023-05-15" },
        //         { name: "品牌", value: "戴尔" },
        //         { name: "生产厂家", value: "戴尔科技有限公司" },
        //         { name: "供应商名称", value: "广州科技设备有限公司" },
        //         { name: "供应商联系信息", value: "电话: +86-20-87654321" },
        //       ],
        //       maintenance_records: {
        //         maintenance_content: "系统升级和硬盘检测",
        //         date: "2024-03-01",
        //         next_maintenance_date: "2025-03-01",
        //       },
        //       management_name: "李工",
        //       management_contact_info: "13987654321",
        //     },
        //     {
        //       name: "打印机-DYJ-021",
        //       imgurl: "",
        //       location: "办公地点",
        //       status: "可领用",
        //       details: [
        //         { name: "产品名称", value: "打印机" },
        //         { name: "原值", value: "3000" },
        //         { name: "购入时间", value: "2022-10-05" },
        //         { name: "品牌", value: "惠普" },
        //         { name: "生产厂家", value: "惠普（HP）有限公司" },
        //         { name: "供应商名称", value: "北京办公设备有限公司" },
        //         { name: "供应商联系信息", value: "电话: +86-10-12345678" },
        //       ],
        //       maintenance_records: {
        //         maintenance_content: "墨盒更换与清洁",
        //         date: "2024-02-12",
        //         next_maintenance_date: "2024-08-12",
        //       },
        //       management_name: "王工",
        //       management_contact_info: "15698567542",
        //     },
        //     {
        //       name: "智慧屏-ZHP-101",
        //       imgurl: "",
        //       location: "会议室",
        //       status: "已领用",
        //       details: [
        //         { name: "产品名称", value: "智慧屏" },
        //         { name: "原值", value: "18000" },
        //         { name: "购入时间", value: "2021-12-20" },
        //         { name: "品牌", value: "华为" },
        //         { name: "生产厂家", value: "华为技术有限公司" },
        //         { name: "供应商名称", value: "深圳电子设备有限公司" },
        //         { name: "供应商联系信息", value: "电话: +86-755-12345678" },
        //       ],
        //       maintenance_records: {
        //         maintenance_content: "屏幕校准与系统更新",
        //         date: "2023-11-10",
        //         next_maintenance_date: "2024-11-10",
        //       },
        //       management_name: "陈工",
        //       management_contact_info: "13876543210",
        //     },
        //   ],
        // },
      ],
      input: "",
      activeTab: "today",
      listst: [
        {
          name: "广东质检中诚认证有限公司到中广...",
        },
        { name: "材料科学、化学工程及医药研发成..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
        { name: "植酸检测方法及作用" },
        { name: "兔拉检测：行业中芯片造假技术..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
      ],
      showdh: true,
      showdh1: false,
      noAnimation: false,
      selectedItem: null,
      tableDataItem: [],
      Title: "资产管理",
      tableTitle: [
        { key: "楼层" },
        { key: "设备编号" },
        { key: "设备名称" },
        { key: "房间号" },
        { key: "模型" },
        { key: "设备状态" },
        { key: "状态说明" },
      ],
      ids: null,
      nhlist: [
        {
          title: "供气压力",
          status: "0.3Mpa",
          unit: "℃",
        },

        {
          title: "供气流量",
          status: "6M3/min",
          unit: "㎡",
        },
        {
          title: "露点温度",
          status: "6℃",
          unit: "℃",
        },
        {
          title: "含氧量",
          status: "6PPM",
          unit: "㎡",
        },
      ],
      warnlist1: [
        {
          type: 1,
          name: "检测到烟雾，可能有着火灾的发生...",
          value: "",
          time: "09:13:59  2023-06-07",
        },
        {
          type: 2,
          name: "检测到烟雾，可能预示着火灾的发生..",
          value: "",
          time: "10:13:18  2023-06-12",
        },
        {
          type: 3,
          name: "实验室内检测到漏水，可能来自冷凝水..",
          value: "",
          time: "10:13:18  2023-06-12",
        },
      ],
      isButton2Active: false,
      status: "巡检中",
      status1: "已完成",
      status2: "待巡检",
      selectedIndex: 0,
      componentTag: "component0",
    };
  },
  // 计算属性类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {},
  // 方法集合
  methods: {
     changetit(index) {
      this.titactive = index
      this.isshowwhat=!index
      if (index == 1) {
        this.showdh = false;
        this.showdh1 = true;
        this.noAnimation = true;
      }else{
        this.showdh = true;
        this.showdh1 = false;
      this.noAnimation = false;
      }
 
    },
    hidedetailsss() {
      this.isshowsss = false;
    },
    opendialog(payload) {
      if (payload == 1) {
        this.isshow = true;

        this.data.forEach((item) => {
          if (item.category == "电子显微镜") {
            this.isshow = true;
            this.tableDataItem = item.items;
            console.log(this.tableDataItem);
          }
        });
      }

      // 在这里处理事件
    },
    toggleSubMenu(menuId) {
      this.activeSubmenu = this.activeSubmenu === menuId ? null : menuId;
    },
    // 设置内容
    setContent(content) {
      this.isshowsss = true;
      this.selectedItem = content;
      console.log(this.selectedItem);

      this.activeContent = content;
    },
    showdetails(item) {
      // item.items.forEach((item) => {
      //   this.newArr.push({ name: item.name });
      // });
      // console.log(this.newArr);

      this.isshow = true;
      this.tableDataItem = item.items;
    },
    hidedetails() {
      this.isshow = false;
    },

    oc(value) {
      console.log(value, "收到的值");
      this.showdh = value;
    },
    xuanzedialog(value) {
      const optionMapping = {
        选项1: 0,
        选项2: 1,
        选项3: 2,
        选项4: 3,
        选项5: 4,
        选项6: 5,
        选项7: 6,
        选项8: 7,
        选项9: 8,
        选项10: 9,
        选项11: 10,
        选项12: 11,
        选项13: 12,
      };

      const index = optionMapping[value];
      if (index !== undefined) {
        this.tableDataItem = this.data[index].items;
      } else {
        console.error("无效的选项: ", value);
      }
    },
  },
  // 生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.showdh1 = true;
    setTimeout(() => {
      this.showdh1 = false;
      this.noAnimation = false;
    }, 1000); // 动画持续时间为1秒
    console.log(1222);
  },
  beforeCreate() {}, // 生命周期 - 创建之前
  beforeMount() {}, // 生命周期 - 挂载之前
  beforeUpdate() {}, // 生命周期 - 更新之前
  updated() {}, // 生命周期 - 更新之后
  beforeUnmount() {
    // 在组件销毁之前清除定时器
    console.log(1111);
  },

  unmounted() {
    console.log(2222);
  }, // 生命周期 - 销毁之前
  destroyed() {
    console.log(1221);
  }, // 生命周期 - 销毁完成
  activated() {}, // 如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="less" scoped>
.container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: stretch;
  height: 1080px;
  text-align: center;

  .sbdetails {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
  }

  .box {
    margin-top: 6px;
    padding-top: 5px;
    margin-bottom: 0.225rem;
    height: 800px;
    width: 330px;
    // height: 800px;

    overflow-y: scroll;

    /* 设置垂直滚动条 */
    /* 设置滚动条的样式 */
    &::-webkit-scrollbar {
      width: 0.1px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条轨道的样式 */
    &::-webkit-scrollbar-track {
      background-color: #f1f1f1;
      /* 设置滚动条轨道的背景色 */
    }

    /* 设置滚动条滑块的样式 */
    &::-webkit-scrollbar-thumb {
      background-color: #334f6e;
      /* 设置滚动条滑块的背景色 */
    }

    /* 鼠标悬停在滚动条上时的样式 */
    &::-webkit-scrollbar-thumb:hover {
      background-color: #555;
      /* 设置鼠标悬停时滚动条滑块的背景色 */
    }

    .el-input {
      width: 305px;
      height: 34px;
      color: #fff !important;

      ::v-deep .el-input__wrapper {
        background: url("../assets/image/inputss.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        box-shadow: none !important;
      }
    }

    .suosuo {
      position: absolute;
      top: 66px;
      left: 285px;
    }

    .xiaobox {
      margin-top: 20px;
      display: flex;
      align-items: center;

      .siqiu {
        width: 16px;
        height: 16px;
      }

      .shuru {
        display: flex;
        align-items: center;
        padding-left: 10px;
        background: url("../assets/image/shebei12.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 261px;
        height: 32px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 17px;
        color: #ffffff;
      }
    }

    .xiaoboxs {
      cursor: pointer;
      margin-top: 14px;
      display: flex;
      margin-left: 5px;
      align-items: center;

      .nihaowo {
        width: 78px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 10px;
        color: #ffffff;
        display: flex;
        white-space: nowrap;
        /* 不换行 */
        overflow: hidden;
        /* 超出部分隐藏 */
        text-overflow: ellipsis;
        /* 超出部分显示省略号 */
      }

      .siqiu {
        width: 16px;
        height: 16px;
        margin-left: 10px;
        margin-right: 7px;
      }

      .shuru {
        display: flex;
        align-items: center;
        padding-left: 10px;
        background: url("../assets/image/shebei12.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 261px;
        height: 32px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 17px;
        color: #ffffff;
        white-space: nowrap;
        /* 不换行 */
        overflow: hidden;
        /* 超出部分隐藏 */
        text-overflow: ellipsis;
        /* 超出部分显示省略号 */
      }
    }
  }

  .left-panel {
    position: fixed;
    z-index: 1;
    top: 100px;
    left: 22px;
    width: 330px;
    height: 937px;

    background-size: 100% 100%;
    transform: translate(-122%);
    transition: transform 0.5s ease-in-out;
  }

  .left-panel-active {
    transform: translate(0%);
  }

  .left-panel-active1 {
    // transform: translate(0%);
    animation: slideOut 1s ease-in-out forwards;
  }

  @keyframes slideOut {
    100% {
      transform: translateX(0%);
    }

    // 85% {
    //   transform: translateX(-25%);
    // }

    // 65% {
    //   transform: translateX(-15%);
    // }

    // 40% {
    //   transform: translateX(-55%);
    // }

    // 30% {
    //   transform: translateX(-40%);
    // }

    0% {
      transform: translateX(-100%);
    }
  }

  .rtitle {
    margin-top: 16px;
  }

  @keyframes slideIn {
    0% {
      transform: translateX(100%);
    }

    // 30% {
    //   transform: translateX(65%);
    // }

    // 40% {
    //   transform: translateX(40%);
    // }

    // 65% {
    //   transform: translateX(15%);
    // }

    // 85% {
    //   transform: translateX(25%);
    // }

    100% {
      transform: translateX(0%);
    }
  }

  .right-panel {
    position: fixed;
    z-index: 1;
    right: 83px;
    width: 330px;
    top: 100px;
    height: 937px;

    background-size: 100% 100%;
    transform: translate(122%);
    transition: transform 0.5s ease-in-out;

    .jk {
      margin-top: 12px;
      width: 90%;
      height: 200px;
    }

    .box {
      margin-top: 6px;
      margin-bottom: 18px;
      // background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      width: 94%;
      // height: 428px;

      .loudong {
        margin-top: 21px;
        margin-bottom: 25px;
      }

      .wenzi {
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 10px;
        color: #bdecf9;
        text-align: left;
        margin-left: 20px;
        margin-right: 20px;

        .h2biaoti {
          margin-bottom: 15px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 12px;
          color: #00ffff;
        }
      }

      .p {
        text-indent: 2em;
        margin-bottom: 1em;
        letter-spacing: 0.05em;
      }
    }

    .boxxx {
      margin-top: 6px;
      margin-bottom: 18px;
      background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      width: 333px;
      height: 420px;
      overflow-y: scroll;
      /* 设置垂直滚动条 */
      // overflow: hidden;
      /* 设置滚动条的样式 */

      .zengti {
        margin: 10px 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 315px;
        height: 38px;
        gap: 5px;

        .left {
          display: flex;
          align-items: center;
          justify-content: space-evenly;
          width: 84px;
          height: 27px;

          .yuan {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #08f7f7;
          }

          .wenziss {
            display: flex;
            align-items: flex-start;
            flex-direction: column;

            .p1 {
              font-family: Source Han Sans SC;
              font-weight: 500;
              font-size: 10px;
              color: #ffffff;
            }

            .p2 {
              font-family: Source Han Sans SC;
              font-weight: 500;
              font-size: 10px;
              color: #55cff9;
            }
          }
        }

        .right {
          background: url("../assets/image/rightbeij.png");
          background-size: 100% 100%;
          background-repeat: no-repeat;

          width: 217px;
          height: 38px;
          font-family: Source Han Sans SC;
          font-weight: 500;
          font-size: 11px;
          color: #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    .boxxx::-webkit-scrollbar {
      width: 1px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条轨道的样式 */
    .boxxx::-webkit-scrollbar-track {
      background-color: #f1f1f1;
      /* 设置滚动条轨道的背景色 */
    }

    /* 设置滚动条滑块的样式 */
    .boxxx::-webkit-scrollbar-thumb {
      background-color: #013363;
      /* 设置滚动条滑块的背景色 */
    }
  }

  .no-animation {
    transition: none;
  }

  .right-panel-active {
    transform: translate(0%);
    // animation: slideIn 1s ease-in-out ;
  }

  .right-panel-active1 {
    // transform: translate(0%);
    animation: slideIn 1s ease-in-out forwards;
  }

  @keyframes slideIn {
    0% {
      transform: translateX(100%);
    }

    // 30% {
    //   transform: translateX(65%);
    // }

    // 40% {
    //   transform: translateX(40%);
    // }

    // 65% {
    //   transform: translateX(15%);
    // }

    // 85% {
    //   transform: translateX(25%);
    // }

    100% {
      transform: translateX(0%);
    }
  }

  .completed {
    background: #7ad0ff;
  }

  .incomplete {
    background: #ff6041;
  }

  .warning {
    background: #00ffc0;
  }

  .completeds {
    color: #7ad0ff;
  }

  .incompletes {
    color: #ff6041;
  }

  .warnings {
    color: #00ffc0;
  }
}

.ql-center {
  display: flex;
  margin-top: 20px;
  justify-content: space-around;
  margin-top: 14px;

  .ql-Box {
    width: 75px;
    height: 49px;
    border: 1px solid #7ad0ff;
    // opacity: 0.6;
    border-radius: 2px;

    .ql-box1 {
      font-family: Alibaba PuHuiTi;
      font-weight: bold;
      font-size: 16px;
      color: #7ad0ff;
      margin-top: -10px;
    }

    .ql-box {
      display: flex;
      padding-left: 8px;
      padding-right: 9px;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 34px;

      .left_ql {
        width: 49px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #ffffff;

        .yuan {
          width: 7px;
          height: 7px;
          border-radius: 50%;
        }

        .pp {
          color: #fff;
          font-size: 12px;
        }
      }

      img {
        height: 12px;
        width: 7px;
      }
    }
  }
}

.warn1 {
  background: url("../assets/image/warnred.png");
}

.warn2 {
  background: url("../assets/image/warnyellow.png");
}

.warn3 {
  background: url("../assets/image/warngreen.png");
}

.warning12 {
  background-size: 100% 100%;
  // width: 365px;
  height: 47px;

  .info {
    margin-top: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: left;

    margin-left: 50px;

    .info1 {
      display: flex;
      flex-direction: column;

      p:nth-of-type(1) {
        font-size: 13px;
        font-family: Alibaba PuHuiTi;
        font-weight: bold;
      }

      .red {
        color: #ff0000;
      }

      .green {
        color: #00ffcc;
      }

      .yellow {
        color: #ffff00;
      }

      p:nth-of-type(2) {
        font-size: 16px;
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        color: #ffffff;
      }
    }

    .info2 {
      margin-right: 10px;
      font-size: 15px;
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      color: #cffff8;
    }
  }
}

.xxxx {
  position: absolute;
  top: 1%;
  right: 1%;
  width: 25px;
  height: 25px;
  z-index: 99999;
}

/* 菜单容器 */
.menu-container {
  display: flex;
}

/* 菜单样式 */
.menu {
  width: 100%;
  // background-color: #fff;
  // border: 1px solid #ddd;
  border-radius: 4px;
}

/* 菜单项样式 */
.menu-group {
  margin-top: 14px;
}

.menu-item {
  cursor: pointer;
  background: url("../assets/image/rightbeij.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;

  width: 100%;
  height: 32px;
  font-family: Source Han Sans SC;
  font-weight: 500;
  font-size: 19px;
  color: #ffffff;
  display: flex;
  align-items: center;
  padding-left: 10px;
  // justify-content: center;
}

.menu-item:hover {
  // background-color: #f0f0f0;
}

.submenu {
  // background-color: #f9f9f9;
  padding-left: 20px;
}

.submenu-item {
  padding: 3px;
  padding-left: 12px;
  margin: 8px;
  font-size: 16px;
  color: #ffffff;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #2c343f;
}

.submenu-item:hover {
  background-color: #163561;
}

.qiuqiu {
  display: flex;
  align-items: center;

  .siqiu {
    width: 16px;
    height: 16px;
    margin-left: 10px;
    margin-right: 7px;
  }
}

.listtype {
  background: url("../assets/image/xuanzhotiemmoon.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 80px;
  height: 26px;
  text-align: center;
  line-height: 26px;

  font-size: 13px;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  color: #fff;
}

.listtypes {
  background: url("../assets/image/xuanzhotiemmoon.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 100px;
  height: 32px;
  text-align: center;
  line-height: 32px;
  cursor: pointer;
  font-size: 13px;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  color: #fff;
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 190px;
  /* 你可以根据需要调整宽度 */
  font-size: 16px;
}
.botbtn {
  position: fixed;
  top: 978px;
  left: 370px;
  width: 200px;
  height: 43px;
  background: #022d56;
  border-radius: 8px;
  z-index: 20;
  display: flex;
  align-items: center;
  justify-content: center;

  .btt {
    color: #fff;
    font-size: 17px;
    line-height: 44px;
    width: 88px;
    text-align: center;
    height: 43px;
    cursor: pointer;
  }

  .btt1 {
    color: rgb(8, 207, 241);
    font-size: 17px;
    line-height: 44px;
    width: 88px;
    text-align: center;
    height: 43px;
    cursor: pointer;
  }
}
</style>
