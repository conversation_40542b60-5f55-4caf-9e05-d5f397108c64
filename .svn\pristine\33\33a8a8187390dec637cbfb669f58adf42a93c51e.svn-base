<template>
  <div class="echart" ref="echart"></div>
</template>
  
<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  methods: {
    initData() {},
    init() {
      const myChart = echarts.init(this.$refs.echart);

      const data = [
        {
          label: "使用中",
          value: 40,
        },
        {
          label: "预约中",
          value: 30,
        },
        {
          label: "空闲",
          value: 20,
        },
        {
          label: "报修",
          value: 10,
        },
      ];
      const colors = [
        "37, 171, 200",
        "214, 128, 120",
        "252, 182, 53",
        "47, 255, 242",
        "42,191,191",
      ];
      const option = {
        legend: {
          orient: "vertical",
          top: "30",
          right: "3%",
          data: data.map((it) => it.label),
          textStyle: {
            color: "#fff",
            fontSize: 10,
          },
          itemWidth: 10,
          itemHeight: 10,
        },
        tooltip: {
          trigger: "item",
          formatter: "{a} <br/>{b} : {c} ({d}%)",
          textStyle: {
            fontSize: 10,
          },
        },
        series: [
          {
            name: "仪器状态",
            type: "pie",
            radius: ["30%", "60%"],
            center: ["40%", "50%"],
            roseType: "radius",
            label: {
              show: true,
              normal: {
                position: "outside",
                fontSize: 10,
                formatter: "{d}%",
              },
            },
            labelLine: {
              length: 2,
              length2: 7,
            },
            data: data.map((it, i) => {
              return {
                value: it.value,
                name: it.label,
                itemStyle: {
                  color: `rgba(${colors[i]},0.7)`,
                  borderColor: `rgba(${colors[i]},1)`,
                  borderWidth: 1,
                },
              };
            }),
          },
        ],
      };
      myChart.setOption(option);
    },
  },
};
</script>
  
<style lang="less" scoped>
.echart {
  width: 100%;
  height: 100%;
}

@media (max-height: 1080px) {
  .echart {
    width: 100%;
    height: 100% !important;
  }
}
</style>