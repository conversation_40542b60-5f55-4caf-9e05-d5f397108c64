<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>HLS m3u8 视频播放器</title>

    <!-- 引入 Video.js CSS -->
    <link href="./video-js.min.css" rel="stylesheet" />

    <style>
      body {
        font-family: Arial, sans-serif;
        text-align: center;
        /* background-color: #6b2828; */
      }
      .video-container {
        width: 100%;
        overflow: hidden;
        /* margin: 20px auto; */
        /* background: #000; */
        /* padding: 10px; */
        /* border-radius: 10px; */
      }
    </style>
  </head>
  <body>
    <div class="video-container">
      <video
        id="my-video"
        class="video-js vjs-default-skin vjs-big-play-centered"
        controls
      ></video>
    </div>

    <!-- 引入 Video.js -->
    <script src="./video.min.js"></script>
    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/videojs-http-streaming/2.9.2/videojs-http-streaming.min.js"></script> -->

    <script>
      function getQueryParam(param) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(param);
      }

      document.addEventListener("DOMContentLoaded", function () {
        const defaultVideoUrl =
          "https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8"; // 默认视频
        const videoUrl = getQueryParam("url") || defaultVideoUrl; // 获取 URL 参数
        console.log(videoUrl, "videoUrl");

        const player = videojs("my-video", {
          controls: false,
          fluid: true,
          muted: true,
          autoplay: "play", // 自动播放
          loop: false,
          preload: "auto",
          language: "zh-CN",
          sources: [
            {
              src: videoUrl,
              type: "application/x-mpegURL",
            },
          ],
          html5: {
            vhs: {
              overrideNative: true,
            },
          },
        });

        // 销毁播放器，防止内存泄漏
        window.addEventListener("beforeunload", function () {
          if (player) {
            player.dispose();
          }
        });
      });
    </script>
  </body>
</html>
