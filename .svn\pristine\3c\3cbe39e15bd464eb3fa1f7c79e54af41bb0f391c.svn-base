<template>
  <div class="echart" ref="echart"></div>
</template>
  
<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  props: {
    chartData: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  methods: {
    initData() { },
    init() {
      const myChart = echarts.init(this.$refs.echart);
      let data = this.chartData ? this.chartData : {
        title: ["已领用", "未领用"],
        xAxisdata: ["20", "21", "22", "23", "25", "24",],
        yAxisdata1: [4, 7, 5, 9, 6, 5],
        yAxisdata2: [4, 7, 5, 9, 6, 5],
      }
      const option = {
        title: {
          text: "台",
          x: "6%",
          y: "-2%",
          textStyle: {
            color: "#fff",
            fontSize: 16,
          },
        },
        legend: {
          itemGap: 16,
          itemWidth: 12,
          itemHeight: 12,
          top: 0,
          right: "1%",
          textStyle: { fontSize: 18, color: "#fff" },
          data: data.title,
        },
        tooltip: {
          show: false,
        },
        grid: {
          top: "18%",
          bottom: "1%",
          left: "6%",
          right: "6%",
          containLabel: true,
        },

        xAxis: [
          {
            type: "category",
            boundaryGap: false,
            axisLine: {
              show: true ,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              margin: 10,
              textStyle: {
                fontSize: 16,
                color: "#fff",
              },
            },
            splitLine: {
              show: false,
              lineStyle: {
                color: "#fff",
                opacity: 0.5,
              },
            },
            data: data.xAxisdata,
          },
        ],
        yAxis: [
          {
            type: "value",
            axisLine: {
              show: true ,
            },
          
            axisLine: {
              show: false,
            },
            axisLabel: {
              margin: 10,
              textStyle: {
                fontSize: 16,
                color: "#fff",
              },
            },
            splitLine: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: data.title[0],
            type: "line",
            z: 3,
            smooth: true, // 启用平滑曲线
            showSymbol: false,
            smoothMonotone: "x",

            lineStyle: {
              smooth: false, // 确保线段是直线
              width: 3,
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(59,102,246)", // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: "rgba(118,237,252)", // 100% 处的颜色
                  },
                ],
              },
              shadowBlur: 4,
              shadowColor: "rgba(69,26,247,.2)",
              shadowOffsetY: 4,
            },
            areaStyle: {
              normal: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "#00FF00", // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "#00FF00", // 100% 处的颜色
                    },
                  ],
                },
              },
            },

            data: data.yAxisdata1,
          }, {
            name: data.title[1],
            smooth: true, // 启用平滑曲线
            type: "line",
            z: 3,
            showSymbol: false,
            smoothMonotone: "x",

            lineStyle: {
              smooth: false, // 确保线段是直线
              width: 1,
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(59,2,146)", // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: "rgba(18,237,152)", // 100% 处的颜色
                  },
                ],
              },
              shadowBlur: 4,
              shadowColor: "rgba(0,26,47,.5)",
              shadowOffsetY: 4,
            },
            areaStyle: {
              normal: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "#FFBB66", // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "#FF8800", // 100% 处的颜色
                    },
                  ],
                },
              },
            },

            data: data.yAxisdata2,
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>
  
<style lang="less" scoped>
.echart {
  margin-top: 15px;

  width: 667px;
  height: 210px;
}

// @media (max-height: 1080px) {
//   .echart {
//     width: 95%;
//     height: 180px !important;
//   }
// }
</style>