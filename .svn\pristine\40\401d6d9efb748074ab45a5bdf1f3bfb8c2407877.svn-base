<template>
  <div class="echart" ref="echart"></div>
</template>
  
<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  methods: {
    initData() { },
    init() {
      const myChart = echarts.init(this.$refs.echart);

      const option = {
        title: {
          text: "（℃）",
          x: "3%",
          y: "0%",
          textStyle: {
            color: "#fff",
            fontSize: 12,
          },
        },
        legend: {
          data: ["温度"],
          textStyle: {
            color: "#ffff", // 设置图例文字的颜色，例如橙色
            fontSize: 14, // 可选：设置图例文字的字体大小
          },
        },
        tooltip: {
          show: false,
        },
        grid: {
          top: "14%",
          bottom: "16%",
          left: "5%",
          right: "6%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            boundaryGap: false,
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              margin: 10,
              textStyle: {
                fontSize: 12,
                color: "#fff",
              },
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "#939ab6",
                opacity: 0.15,
              },
            },
            data: [
              "06:00",
              "09:00",
              "12:00",
              "15:00",
              "18:00",
              "21:00",
              "24:00",
            ],
          },
        ],
        yAxis: [
          {
            type: "value",
            max: 8,
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              margin: 10,
              textStyle: {
                fontSize: 12,
                color: "#fff",
              },
            },
            splitLine: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: "实时温度",

            type: "line",
            z: 3,
            showSymbol: false,
            smoothMonotone: "x",

            lineStyle: {
              smooth: false, // 确保线段是直线
              width: 3,
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(59,102,246)", // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: "rgba(118,237,252)", // 100% 处的颜色
                  },
                ],
              },
              // shadowBlur: 4,
              // shadowColor: "rgba(69,126,247,.2)",
              // shadowOffsetY: 4,
            },
            // areaStyle: {
            //   normal: {
            //     color: {
            //       type: "linear",
            //       x: 0,
            //       y: 0,
            //       x2: 0,
            //       y2: 1,
            //       colorStops: [
            //         {
            //           offset: 0,
            //           color: "#34dfb4", // 0% 处的颜色
            //         },
            //         {
            //           offset: 0.5,
            //           color: "#34dfb4",
            //         },
            //         {
            //           offset: 1,
            //           color: "rgba(19, 44, 57, 0.1)", // 下部透明
            //         },
            //       ],
            //     },
            //   },
            // },

            data: [4, 4, 4, 4.2, 4.1, 4.1, 4.2, 4, 4],
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>
  
<style lang="less" scoped>
.echart {
  width: 100%;
  height: 212px;
}

@media (max-height: 1080px) {
  .echart {
    width: 100%;
    height: 212px !important;
  }
}
</style>