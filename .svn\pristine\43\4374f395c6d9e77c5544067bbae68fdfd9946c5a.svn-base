<template>
  <div class="echart" ref="echart"></div>
</template>
    
<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  methods: {
    initData() { },
    init() {
      const myChart = echarts.init(this.$refs.echart);

      app.title = "";

      const option = {
        //     legend: {
        //   textStyle: {
        //                 color:"#fff",
        //                 fontSize:14
        //             },
        //         top: '1%',
        //     right:"1%",
        //         data: ['未优化用电量', 'AI优化用电量'],
        //     },

        color: ["#3398DB"],
        title: {
          text: "单位：台",
          x: "6%",
          y: "2%",
          textStyle: {
            color: "#fff",
            fontSize: 16,
          },
        },
        legend: {
          itemGap: 16,
          itemWidth: 13,
          itemHeight: 13,
          top: 0,
          right: "1%",
          textStyle: { fontSize: 16, color: "#fff" },
          data: ["停用总数", "运行总数"],
        },
        grid: {
          top: "18%",
          bottom: "0%",
          left: "6%",
          right: "6%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: [ "10.20", "10.21", "10.22", "10.23","10.24", "10.25"],

            axisLabel: {
              show: true,
              textStyle: {
                color: "#fff", // 将 X 轴标签字体颜色设置为白色
                fontSize: 16,
              },
            },
          },
        ],
        yAxis: [
          {
            axisTick: {
              alignWithLabel: true,
            },

            axisLabel: {       
              show: true,
              textStyle: {
                color: "#fff", // 将 Y 轴标签字体颜色设置为白色
                fontSize: 16,
              },
            },
          },
        ],

        series: [
        
          {
            name: "运行总数",
            type: "bar",
            barWidth: "20%",
            data: [45, 56, 62,55, 42, 50],
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#FF88C2",
                  },
                  {
                    offset: 1,
                    color: "#FF44AA",
                  },
                ]),
                shadowColor: "rgba(0, 0, 0, 0.1)",
                shadowBlur: 10,
              },
            },
          },
          {
            name: "停用总数",
            type: "bar",
            barWidth: "20%",
            data: [23, 12, 6, 13, 26, 18],
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#00DD00",
                  },
                  {
                    offset: 1,
                    color: "#00FF00",
                  },
                ]),
                shadowColor: "rgba(0, 0, 0, 0.1)",
                shadowBlur: 10,
              },
            },
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>
    
<style lang="less" scoped>
.echart {
  width: 455px;
  height: 320px;
}


</style>