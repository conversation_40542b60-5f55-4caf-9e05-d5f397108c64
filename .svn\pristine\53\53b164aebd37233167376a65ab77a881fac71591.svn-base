<template>
  <div>
    <!-- <component :is="componentTag" @fatherMethoddd="fatherMethoddd"></component> -->

    <!-- <tedai
      :ids="ids"
      :selectedItem="selectedItem"
      class="sbdetails"
      :zengtiimg="zengtiimg"
      v-if="isshow"
      @hidedetails="hidedetails"
    ></tedai> -->
    <biaoGe
      v-if="isshow"
      @hidedetails="hidedetails"
      :tableTitle="tableTitle"
      :tableDataItem="tableDataItem"
    ></biaoGe>
    <div class="container">
      <div
        class="left-panel"
        :class="{
          'left-panel-active': showdh,
          'no-animation': noAnimation,
          'left-panel-active1': showdh1,
        }"
      >
        <Title2 class="ltitle1" tit="大型仪器列表">
          <div class="box">
            <div>
              <el-input
                class="el-input"
                v-model="input"
                placeholder="请输入内容"
              ></el-input>
            </div>
            <!-- <div class="xiaobox">
              <img class="siqiu" src="../assets/image/shixinqiu.png" alt="" />
              <div class="shuru">全部设备</div>
            </div> -->
            <div
              class="xiaoboxs"
              v-for="item in data"
              :key="item"
              @click="showdetails(item)"
            >
              <img class="siqiu" src="../assets/image/kongxin.png" alt="" />
              <div class="shuru">{{ item.category }}</div>
            </div>
          </div>
          <!-- shebei12.png -->
        </Title2>
      </div>
      <!-- shebeibgc.png -->
      <!-- 右侧内容 -->
      <!-- 右侧内容 -->

      <div
        class="right-panel"
        :class="{
          'right-panel-active': showdh,
          'no-animation': noAnimation,
          'right-panel-active1': showdh1,
        }"
      >
        <Title3 tit="大型仪器详情">
          <div class="box">
            <div class="xiaoboxs" v-for="item in cgqlist" :key="item">
              <img class="siqiu" src="../assets/image/kongxin.png" alt="" />
              <div class="nihaowo">{{ item.name }}</div>
              <div class="shuru">{{ item.value }}</div>
            </div>
          </div>
          <!-- <img class="jk" src="../assets/jiankong.png" alt=""> -->
        </Title3>
      </div>
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import huanxing from "@/components/echarts/huanxing.vue";
import zhexian from "@/components/echarts/zhexian.vue";
import zhexian1 from "@/components/echarts/zhexian1.vue";
import SystemDete from "@/components/echarts/SystemDete.vue";
import echarts1 from "@/components/echarts/bingjifang/echarts3.vue";
import tedai from "@/components/common/tedai.vue";
import biaoGe from "@/components/common/biaoGe.vue";
import shuangxiang from "@/components/echarts/shuangxiang.vue";

// import details from "@/components/common/details.vue";
export default {
  // import引入的组件需要注入到对象中才能使用
  components: {
    tedai,
    huanxing,
    zhexian,
    zhexian1,
    SystemDete,
    echarts1,
    shuangxiang,
    biaoGe,
  },
  props: ["tabledata", "zengtiimg"],
  //  cgqlistss: [
  //       { name: "湿度范围：", value: "5%RH~100%RH（无结露）" },
  //       { name: "湿度分辨率：", value: "0.04%" },
  //       { name: "IP地址：", value: "±3%RH（20%RH~80%RH）" },
  //       { name: "湿度精度：", value: "-40℃~+125℃" },
  //       { name: "温度分辨率：", value: "0.01℃" },
  //       { name: "温度精度：", value: "±0.5℃（10℃~60℃）" },
  //     ],

  data() {
    // 这里存放数据
    return {
      isshow: false,
      xxxx: false,
      cgqlist: [],
      // data: [
      //   {
      //     name: "离子溅射仪",
      //   },
      //   {
      //     name: "热重及同步热分析仪",
      //   },
      //   {
      //     name: "场发射扫描电子显微镜 （S4800）",
      //   },
      //   {
      //     name: "场发射扫描电子显微镜（Apreo S LoVac）",
      //   },
      //   {
      //     name: "场发射扫描电子显微镜（Regulus 8100）",
      //   },
      //   {
      //     name: "场发射透射电子显微镜JEM-2100F",
      //   },
      //   {
      //     name: "场发射透射电子显微镜JEM-2100F能谱仪Ultim Max",
      //   },
      //   {
      //     name: "场发射透射电子显微镜JEM-F200",
      //   },
      //   {
      //     name: "超高效液相色谱-三重四极杆质谱联用仪",
      //   },
      //   {
      //     name: "动态热流示差示扫描量热仪",
      //   },
      //   {
      //     name: "傅里叶变换红外光谱仪（iS50 FT-IR）",
      //   },
      //   {
      //     name: "JEM-F200",
      //   },
      //   {
      //     name: "聚光镜球差校正电镜",
      //   },
      //   {
      //     name: "离子淌度高分辨液质联用仪",
      //   },
      //   {
      //     name: "纳米粒度和Zeta电位及分子量分析仪",
      //   },
      //   {
      //     name: "纳米粒度及zeta电位仪",
      //   },
      //   {
      //     name: "物镜球差校正电镜",
      //   },
      //   {
      //     name: "X射线光电子能谱分析仪XPS",
      //   },
      //   {
      //     name: "X射线光电子能谱仪",
      //   },
      //   {
      //     name: "X射线衍射仪（布鲁克铜靶）",
      //   },
      //   {
      //     name: "X射线衍射仪（Smartlab）",
      //   },
      //   {
      //     name: "X射线荧光光谱仪",
      //   },
      //   {
      //     name: "旋转流变力学测试仪",
      //   },
      //   {
      //     name: "原子力显微镜",
      //   },
      //   {
      //     name: "3D打印机",
      //   },
      //   {
      //     name: "程序升温化学吸附仪",
      //   },
      //   {
      //     name: "电子墨水显示器件关键参数测试仪",
      //   },
      //   {
      //     name: "动态机械分析仪",
      //   },
      //   {
      //     name: "多检测凝胶色谱仪",
      //   },
      //   {
      //     name: "多通道电化学综合测试系统",
      //   },
      //   {
      //     name: "傅立叶变换红外光谱仪",
      //   },
      //   {
      //     name: "高效液相色谱仪",
      //   },
      //   {
      //     name: "化学吸附仪",
      //   },
      //   {
      //     name: "接触角测量仪",
      //   },
      //   {
      //     name: "粒子图像速度场仪",
      //   },
      //   {
      //     name: "纳米力学测试系统",
      //   },
      //   {
      //     name: "气相色谱/质谱联用仪",
      //   },
      //   {
      //     name: "全自动比表面孔隙分析仪",
      //   },
      //   {
      //     name: "全自动智能倒置荧光显微镜",
      //   },
      //   {
      //     name: "示差扫描量热仪",
      //   },
      //   {
      //     name: "石英晶体微量分析仪",
      //   },
      //   {
      //     name: "同步热分析仪",
      //   },
      //   {
      //     name: "液相色谱-高分辨四极杆飞行时间串联质谱联用仪",
      //   },
      //   {
      //     name: "紫外-可见分光光度计",
      //   },
      //   {
      //     name: "超速离心机",
      //   },
      //   {
      //     name: "离子溅射仪",
      //   },
      //   {
      //     name: "热分析仪",
      //   },
      //   {
      //     name: "差热分析仪",
      //   },
      //   {
      //     name: "高分子热分析系统（热重分析仪）",
      //   },
      //   {
      //     name: "高级旋转流变仪",
      //   },
      //   {
      //     name: "高效液相色谱",
      //   },
      //   {
      //     name: "激光共聚焦拉曼光谱仪",
      //   },
      //   {
      //     name: "激光共聚焦扫描显微镜",
      //   },
      //   {
      //     name: "基质辅助激光解吸电离-飞行时间质谱",
      //   },
      //   {
      //     name: "基质辅助激光解吸附-串联飞行时间质谱",
      //   },
      //   {
      //     name: "凝胶渗透色谱仪",
      //   },
      //   {
      //     name: "热台显微镜系统",
      //   },
      //   {
      //     name: "深紫外激光共聚焦拉曼光谱仪",
      //   },
      //   {
      //     name: "实时在线颗粒分析仪",
      //   },
      //   {
      //     name: "台式扫描电子显微镜",
      //   },
      //   {
      //     name: "台式原位疲劳试验电镜系统",
      //   },
      //   {
      //     name: "透射电子显微镜JEM-1400Flash",
      //   },
      //   {
      //     name: "钨灯丝扫描电子显微镜",
      //   },
      //   {
      //     name: "x射线台式粉末衍射仪",
      //   },
      //   {
      //     name: "X射线吸收精细结构谱仪",
      //   },
      //   {
      //     name: "X射线衍射仪（帕纳科铜靶）",
      //   },
      //   {
      //     name: "圆二色光谱仪J-1700和Stop Flow快速动力学停流仪",
      //   },
      //   {
      //     name: "圆二色光谱仪J-810",
      //   },
      //   {
      //     name: "折射率测试仪",
      //   },
      //   {
      //     name: "中孔分析仪",
      //   },
      //   {
      //     name: "等离子体发射光谱仪",
      //   },
      //   {
      //     name: "X射线面探系统",
      //   },
      //   {
      //     name: "超高速智能粒度分析仪",
      //   },
      //   {
      //     name: "等离子体发射光谱仪(ICP-OES)",
      //   },
      //   {
      //     name: "等温滴定量热仪",
      //   },
      //   {
      //     name: "低温扫描隧道显微镜与原子力显微镜系统",
      //   },
      //   {
      //     name: "电化学综合测试仪",
      //   },
      //   {
      //     name: "电液伺服疲劳试验机",
      //   },
      //   {
      //     name: "动态蒸汽吸附分析仪",
      //   },
      //   {
      //     name: "粉体特性分析仪",
      //   },
      //   {
      //     name: "分子量测试仪器",
      //   },
      //   {
      //     name: "傅里叶变换红外光谱分析仪",
      //   },
      //   {
      //     name: "傅立叶变换红外光谱仪",
      //   },
      //   {
      //     name: "傅立叶变换红外光谱仪",
      //   },
      //   {
      //     name: "傅立叶红外变换光谱仪",
      //   },
      //   {
      //     name: "傅里叶红外光谱仪",
      //   },
      //   {
      //     name: "傅里叶红外光谱仪",
      //   },
      //   {
      //     name: "干法粒度和粒形分析仪",
      //   },
      //   {
      //     name: "高速高分辨显微共焦拉曼光谱仪",
      //   },
      //   {
      //     name: "固体表面Zeta电位测试仪",
      //   },
      //   {
      //     name: "光固化激光3D打印机",
      //   },
      //   {
      //     name: "迴转式粉体特性分析仪（含带离子放电组块）",
      //   },
      //   {
      //     name: "激光拉曼光谱仪",
      //   },
      //   {
      //     name: "激光粒度仪",
      //   },
      //   {
      //     name: "激光闪射法导热仪",
      //   },
      //   {
      //     name: "流变仪",
      //   },
      //   {
      //     name: "流式细胞仪-分析",
      //   },
      //   {
      //     name: "流式细胞仪-分选",
      //   },
      //   {
      //     name: "偏光显微镜",
      //   },
      //   {
      //     name: "气相色谱",
      //   },
      //   {
      //     name: "气相色谱-三重四极杆质谱联用仪",
      //   },
      //   {
      //     name: "气相色谱/质谱联用仪",
      //   },
      //   {
      //     name: "全自动实验室合成反应器",
      //   },
      //   {
      //     name: "热重分析仪",
      //   },
      //   {
      //     name: "生物分子相互作用分析仪",
      //   },
      //   {
      //     name: "实时荧光定量PCR仪",
      //   },
      //   {
      //     name: "实时原位反应分析系统",
      //   },
      //   {
      //     name: "台式电子显微镜",
      //   },
      //   {
      //     name: "太阳能电池测试系统",
      //   },
      //   {
      //     name: "小动物活体三维光学成像系统",
      //   },
      //   {
      //     name: "X射线三维显微成像系统（NanoVoxel 3502E）",
      //   },
      //   {
      //     name: "X射线衍射仪",
      //   },
      //   {
      //     name: "液质联用仪",
      //   },
      //   {
      //     name: "荧光定量基因扩增仪",
      //   },
      //   {
      //     name: "荧光分光光度计",
      //   },
      //   {
      //     name: "原子力显微镜",
      //   },
      //   {
      //     name: "在线拉曼光谱仪",
      //   },
      //   {
      //     name: "32位高通量平行生物反应系统",
      //   },
      //   {
      //     name: "比表面及孔径分析仪",
      //   },
      //   {
      //     name: "表面力仪分析系统",
      //   },
      //   {
      //     name: "超速离心机",
      //   },
      //   {
      //     name: "带红外高速的摄影仪",
      //   },
      //   {
      //     name: "低场核磁共振仪",
      //   },
      //   {
      //     name: "电感耦合等离子体发射光谱仪",
      //   },
      //   {
      //     name: "动态鼠胃消化系统",
      //   },
      //   {
      //     name: "发动机排放分析系统",
      //   },
      //   {
      //     name: "非接触式生物活体激光测振系统",
      //   },
      //   {
      //     name: "分光光度计",
      //   },
      //   {
      //     name: "傅立叶变换红外光谱仪",
      //   },
      //   {
      //     name: "傅里叶变换红外光谱仪",
      //   },
      //   {
      //     name: "高速运动分析系统",
      //   },
      //   {
      //     name: "高通量核酸合成仪",
      //   },
      //   {
      //     name: "高通量自动振荡培养箱",
      //   },
      //   {
      //     name: "高温裂解试验装置",
      //   },
      //   {
      //     name: "固体红外",
      //   },
      //   {
      //     name: "光谱型椭偏仪",
      //   },
      //   {
      //     name: "光谱仪",
      //   },
      //   {
      //     name: "红外光谱仪",
      //   },
      //   {
      //     name: "化学吸附仪",
      //   },
      //   {
      //     name: "化学吸附仪",
      //   },
      //   {
      //     name: "化学吸附仪",
      //   },
      //   {
      //     name: "激光拉曼光谱仪",
      //   },
      //   {
      //     name: "加速绝热量热仪",
      //   },
      //   {
      //     name: "拉/扭一体试验机",
      //   },
      //   {
      //     name: "模块化纳米结构小角度X光散射仪",
      //   },
      //   {
      //     name: "模块化微反应器系统",
      //   },
      //   {
      //     name: "PCM结晶监测系统",
      //   },
      //   {
      //     name: "气相色谱质谱联用仪",
      //   },
      //   {
      //     name: "气相色谱质谱联用仪",
      //   },
      //   {
      //     name: "气质三重四级杆质谱联用仪",
      //   },
      //   {
      //     name: "轻油加氢装置",
      //   },
      //   {
      //     name: "轻油加氢装置",
      //   },
      //   {
      //     name: "全自动程序升温化学吸附仪",
      //   },
      //   {
      //     name: "全自动程序升温化学吸附仪",
      //   },
      //   {
      //     name: "全自动倒置荧光显微镜",
      //   },
      //   {
      //     name: "全自动多级分子蒸馏设备",
      //   },
      //   {
      //     name: "全自动智能倒置荧光显微镜",
      //   },
      //   {
      //     name: "全自动智能倒置荧光显微镜",
      //   },
      //   {
      //     name: "燃料热氧化安定性测定仪",
      //   },
      //   {
      //     name: "热重-质谱联用仪",
      //   },
      //   {
      //     name: "三维流场粒子探测成像仪",
      //   },
      //   {
      //     name: "三站全自动比表微孔介孔分析仪",
      //   },
      //   {
      //     name: "扫描探针显微镜（纳米力学测试系统）",
      //   },
      //   {
      //     name: "色谱质谱联用仪",
      //   },
      //   {
      //     name: "实时原位反应分析系统",
      //   },
      //   {
      //     name: "实验室墨水研磨系统",
      //   },
      //   {
      //     name: "实验室三辊研磨机",
      //   },
      //   {
      //     name: "实验室砚磨机",
      //   },
      //   {
      //     name: "石英晶体微天平",
      //   },
      //   {
      //     name: "双套燃料高温加热试验装置",
      //   },
      //   {
      //     name: "同步热分析仪",
      //   },
      //   {
      //     name: "透射电镜原位系统",
      //   },
      //   {
      //     name: "微分电化学质谱",
      //   },
      //   {
      //     name: "微量热等温滴定量热仪",
      //   },
      //   {
      //     name: "微型激光诱导荧光测试系统",
      //   },
      //   {
      //     name: "稳态瞬态荧光光谱仪",
      //   },
      //   {
      //     name: "物理化学吸附分析仪",
      //   },
      //   {
      //     name: "显微高频粒子图像测速仪",
      //   },
      //   {
      //     name: "液相色谱-无机质谱联用设备",
      //   },
      //   {
      //     name: "液相色谱质谱联用仪",
      //   },
      //   {
      //     name: "荧光定量基因扩增仪",
      //   },
      //   {
      //     name: "原子力显微镜",
      //   },
      //   {
      //     name: "载流子扩散瞬态成像测试仪",
      //   },
      //   {
      //     name: "在线高清晰颗粒显微镜",
      //   },
      //   {
      //     name: "中试系统-复配装置",
      //   },
      //   {
      //     name: "自动化高速冷冻离心机",
      //   },
      //   {
      //     name: "自动化基因扩增仪",
      //   },
      //   {
      //     name: "自动荧光定量系统",
      //   },
      //   {
      //     name: "紫外可见分光光度计",
      //   },
      // ],

      data: [
        {
          category: "电子显微镜",
          items: [
            {
              name: "离子溅射仪",
              number: "2017016906(附件)",
              room: "A1002",
              loucheng: "1层",
              sbtypess: "使用中",
            },
            {
              name: "场发射扫描电子显微镜 （S4800）",
              number: "20096369",
              room: "A1003",
              loucheng: "1层",
              sbtypess: "报修",
            },
            {
              name: "场发射扫描电子显微镜（Apreo S LoVac）",
              number: "2017016906",
              room: "A1003",
              sbtypess: "空闲",
              loucheng: "1层",
            },
            {
              name: "场发射扫描电子显微镜（Regulus 8100）",
              number: "2020006966",
              room: "A1003",
              loucheng: "1层",
              sbtypess: "预约中",
            },
            {
              name: "场发射透射电子显微镜JEM-2100F",
              number: "20103150",
              room: "A1003",
              sbtypess: "使用中",
              loucheng: "1层",
            },
            {
              name: "场发射透射电子显微镜JEM-2100F能谱仪Ultim Max",
              number: "2022003642",
              room: "A1003",
              loucheng: "1层",
              sbtypess: "使用中",
            },
            {
              name: "场发射透射电子显微镜JEM-F200",
              number: "2023024322",
              room: "A1003",
              loucheng: "1层",
              sbtypess: "使用中",
            },
            {
              name: "JEM-F200",
              number: "22DK000394",
              room: "A1003",
              loucheng: "1层",
              sbtypess: "使用中",
            },
            {
              name: "聚光镜球差校正电镜",
              number: "22DK000392-1",
              room: "A1003",
              loucheng: "1层",
              sbtypess: "空闲",
            },
            {
              name: "物镜球差校正电镜",
              number: "22DK00390",
              room: "A1003",
              loucheng: "1层",
              sbtypess: "使用中",
            },
            {
              name: "台式扫描电子显微镜",
              number: "2012003231",
              room: "A1003",
              loucheng: "1层",
              sbtypess: "使用中",
            },
            {
              name: "台式原位疲劳试验电镜系统",
              number: "2024007404",
              room: "A1003",
              loucheng: "1层",
              sbtypess: "使用中",
            },
            {
              name: "透射电子显微镜JEM-1400Flash",
              number: "2023024323",
              sbtypess: "使用中",
              room: "A1003",
              loucheng: "1层",
            },
            {
              name: "钨灯丝扫描电子显微镜",
              number: "2024008065",
              room: "A1003",
              loucheng: "1层",
              sbtypess: "使用中",
            },
            { name: "台式电子显微镜", number: "2011990153", room: "A1003" },
            {
              name: "透射电镜原位系统",
              number: "暂未入账（2207000840）",
              room: "A1003",
              sbtypess: "使用中",
              loucheng: "1层",
            },
          ],
        },
        {
          category: "光学显微镜",
          items: [
            { name: "原子力显微镜", number: "2017017251" },
            { name: "原子力显微镜", number: "2012004559" },
            { name: "全自动智能倒置荧光显微镜", number: "2017011385" },
            { name: "激光共聚焦扫描显微镜", number: "2023031057" },
            { name: "偏光显微镜", number: "22DK000392" },
            { name: "全自动倒置荧光显微镜", number: "2020004180" },
            { name: "全自动智能倒置荧光显微镜", number: "2016008521" },
            { name: "热台显微镜系统", number: "2019006954" },
            { name: "小动物活体三维光学成像系统", number: "2024005676" },
            { name: "在线高清晰颗粒显微镜", number: "2013008017" },
          ],
        },
        {
          category: "光谱分析仪器",
          items: [
            {
              name: "傅里叶变换红外光谱仪（iS50 FT-IR）",
              number: "2007001934",
            },
            { name: "傅立叶变换红外光谱仪", number: "20111359" },
            { name: "傅立叶变换红外光谱仪", number: "20075294" },
            { name: "傅立叶红外变换光谱仪", number: "2016016654" },
            { name: "傅里叶红外光谱仪", number: "2011996331" },
            { name: "傅里叶红外光谱仪", number: "2011996334" },
            { name: "傅里叶变换红外光谱分析仪", number: "2017000468" },
            { name: "紫外-可见分光光度计", number: "20108211" },
            { name: "激光共聚焦拉曼光谱仪", number: "2019011812" },
            { name: "深紫外激光共聚焦拉曼光谱仪", number: "22DK100391" },
            { name: "激光拉曼光谱仪", number: "2016017627" },
            { name: "在线拉曼光谱仪", number: "2019011346" },
            { name: "高速高分辨显微共焦拉曼光谱仪", number: "2023023628" },
            {
              name: "圆二色光谱仪J-1700和Stop Flow快速动力学停流仪",
              number: "2024006852",
            },
            { name: "圆二色光谱仪J-810", number: "20054393" },
            { name: "光谱型椭偏仪", number: "2014004358" },
            { name: "光谱仪", number: "2012005138" },
            { name: "红外光谱仪", number: "2024012502" },
            { name: "分光光度计", number: "2016019071" },
            { name: "荧光分光光度计", number: "20094601" },
            { name: "微型激光诱导荧光测试系统", number: "2016017634" },
            { name: "稳态瞬态荧光光谱仪", number: "2015010590" },
            { name: "红外光谱仪", number: "2011993829" },
            { name: "紫外可见分光光度计", number: "2017007552" },
            {
              name: "迴转式粉体特性分析仪（含带离子放电组块）",
              number: "2017000470",
            },
          ],
        },
        {
          category: "色谱和质谱仪器",
          items: [
            {
              name: "超高效液相色谱-三重四极杆质谱联用仪",
              number: "2017015947",
            },
            { name: "高效液相色谱仪", number: "2022001993" },
            {
              name: "液相色谱-高分辨四极杆飞行时间串联质谱联用仪",
              number: "20096192",
            },
            { name: "气相色谱/质谱联用仪", number: "2014009415" },
            { name: "气相色谱-三重四极杆质谱联用仪", number: "2017017237" },
            { name: "气相色谱质谱联用仪", number: "2014006079" },
            { name: "气质三重四级杆质谱联用仪", number: "2021022611" },
            { name: "液质联用仪", number: "2011994007" },
            { name: "气相色谱", number: "2014008299" },
            { name: "多检测凝胶色谱仪", number: "20101760" },
            { name: "高效液相色谱", number: "2023033078" },
            { name: "基质辅助激光解吸电离-飞行时间质谱", number: "2024008063" },
            { name: "基质辅助激光解吸附-串联飞行时间质谱", number: "20096191" },
            { name: "凝胶渗透色谱仪", number: "2022003641" },
            { name: "液相色谱质谱联用仪", number: "2023028024" },
            { name: "液相色谱-无机质谱联用设备", number: "2024012460" },
            { name: "气相色谱质谱联用仪", number: "2016004969" },
            { name: "色谱质谱联用仪", number: "20075014" },
          ],
        },
        {
          category: "热分析仪器",
          items: [
            { name: "热重及同步热分析仪", number: "20111361" },
            { name: "动态热流示差示扫描量热仪", number: "20075223" },
            { name: "示差扫描量热仪", number: "20096406" },
            { name: "差热分析仪", number: "2017000469" },
            { name: "热重分析仪", number: "2012004045" },
            { name: "高分子热分析系统（热重分析仪）", number: "2023033104" },
            { name: "动态机械分析仪", number: "20070789" },
            { name: "热分析仪", number: "20111360" },
            { name: "同步热分析仪", number: "2023031915" },
            { name: "加速绝热量热仪", number: "2011993363" },
            { name: "热重-质谱联用仪", number: "2015004611" },
            { name: "等温滴定量热仪", number: "2020004013" },
            { name: "微量热等温滴定量热仪", number: "2016018072" },
          ],
        },
        {
          category: "物理性能测试仪器",
          items: [
            { name: "旋转流变力学测试仪", number: "2023033117" },
            { name: "高级旋转流变仪", number: "2016018068" },
            { name: "流变仪", number: "20104478" },
            { name: "拉/扭一体试验机", number: "2016005990" },
            { name: "纳米力学测试系统", number: "2016016729" },
            { name: "电液伺服疲劳试验机", number: "2023023123" },
            { name: "动态蒸汽吸附分析仪", number: "2013000227" },
            { name: "石英晶体微量分析仪", number: "2011990556" },
            { name: "石英晶体微天平", number: "2014003596" },
            { name: "电感耦合等离子体发射光谱仪", number: "2024007738" },
            { name: "等离子体发射光谱仪", number: "20033122" },
            {
              name: "等离子体发射光谱仪(ICP-OES)",
              number: "22DK101364（待验收）",
            },
            { name: "表面力仪分析系统", number: "2016017870" },
            { name: "折射率测试仪", number: "2017000471" },
            { name: "接触角测量仪", number: "2015007538" },
            { name: "粒子图像速度场仪", number: "20094129" },
            { name: "流式细胞仪-分析", number: "2023030987" },
            { name: "流式细胞仪-分选", number: "2017016902" },
            { name: "生物分子相互作用分析仪", number: "20101758" },
            { name: "物理化学吸附分析仪", number: "20041599" },
            { name: "固体表面Zeta电位测试仪", number: "2011990154" },
            { name: "动态鼠胃消化系统", number: "2023032619" },
            { name: "非接触式生物活体激光测振系统", number: "2022005578" },
            { name: "显微高频粒子图像测速仪", number: "2016017901" },
            { name: "程序升温化学吸附仪", number: "20002634" },
            { name: "化学吸附仪", number: "2020010892" },
            { name: "全自动程序升温化学吸附仪", number: "2023031069" },
          ],
        },
        {
          category: "电化学和质谱仪器",
          items: [
            { name: "多通道电化学综合测试系统", number: "2016017899" },
            { name: "电化学综合测试仪", number: "2022000641" },
            { name: "微分电化学质谱", number: "2024005693" },
          ],
        },
        {
          category: "粒度及表面分析仪器",
          items: [
            { name: "纳米粒度和Zeta电位及分子量分析仪", number: "2015009486" },
            { name: "纳米粒度及zeta电位仪", number: "20101759" },
            { name: "比表面及孔径分析仪", number: "2018002562" },
            { name: "全自动比表面孔隙分析仪", number: "2018015569" },
            { name: "三站全自动比表微孔介孔分析仪", number: "2024007755" },
            { name: "中孔分析仪", number: "20075019" },
            { name: "粉体特性分析仪", number: "2017000480" },
            { name: "干法粒度和粒形分析仪", number: "2013000273" },
            { name: "激光粒度仪", number: "2015009566" },
            { name: "实时在线颗粒分析仪", number: "2013008012" },
            { name: "超高速智能粒度分析仪", number: "2013000274" },
            { name: "高速运动分析系统", number: "2018017235" },
          ],
        },
        {
          category: "X射线仪器",
          items: [
            { name: "X射线光电子能谱分析仪XPS", number: "2022006207" },
            { name: "X射线光电子能谱仪", number: "2017016904" },
            { name: "X射线衍射仪（布鲁克铜靶）", number: "20096410" },
            { name: "X射线衍射仪（Smartlab）", number: "2017016905" },
            { name: "x射线台式粉末衍射仪", number: "2022006344" },
            { name: "X射线衍射仪（帕纳科铜靶）", number: "20054107" },
            { name: "X射线吸收精细结构谱仪", number: "22DK100458(待验收）" },
            { name: "模块化纳米结构小角度X光散射仪", number: "2016018883" },
            { name: "X射线面探系统", number: "20052861" },
            {
              name: "X射线三维显微成像系统（NanoVoxel 3502E）",
              number: "22DK100461",
            },
            { name: "X射线衍射仪", number: "20094131" },
          ],
        },
        {
          category: "扫描探针显微镜",
          items: [
            {
              name: "低温扫描隧道显微镜与原子力显微镜系统",
              number: "2021016798",
            },
            {
              name: "扫描探针显微镜（纳米力学测试系统）",
              number: "2014003597",
            },
            { name: "原子力显微镜", number: "2017014390" },
            { name: "原子力显微镜", number: "2017017251" },
            { name: "原子力显微镜", number: "2012004559" },
          ],
        },
        {
          category: "生物科学仪器",
          items: [
            { name: "实时荧光定量PCR仪", number: "2023031056" },
            { name: "荧光定量基因扩增仪", number: "20094149" },
            { name: "自动化基因扩增仪", number: "2022009429" },
            { name: "自动荧光定量系统", number: "2022004060" },
            { name: "高通量核酸合成仪", number: "2020008469" },
            { name: "高通量自动振荡培养箱", number: "2023007794" },
            { name: "32位高通量平行生物反应系统", number: "2021022660" },
            { name: "小动物活体三维光学成像系统", number: "2024005676" },
          ],
        },
        {
          category: "离心机",
          items: [
            { name: "超速离心机", number: "2024005677" },
            { name: "超速离心机", number: "2023024978" },
            { name: "自动化高速冷冻离心机", number: "2022009422" },
          ],
        },
        {
          category: "3D打印设备",
          items: [
            { name: "3D打印机", number: "2015001016" },
            { name: "光固化激光3D打印机", number: "2018017866" },
          ],
        },
        {
          category: "其他仪器",
          items: [
            { name: "高温裂解试验装置", number: "2013001329" },
            {
              name: "太阳能电池测试系统",
              number: "2013001512、20107738、20107741、2012000349",
            },
            { name: "发动机排放分析系统", number: "2022006393" },
            { name: "轻油加氢装置", number: "20111080" },
            { name: "轻油加氢装置", number: "20111082" },
            { name: "全自动实验室合成反应器", number: "20105218" },
            { name: "模块化微反应器系统", number: "2016017674" },
            { name: "PCM结晶监测系统", number: "2024004489" },
            { name: "燃料热氧化安定性测定仪", number: "2013003838" },
            { name: "实验室墨水研磨系统", number: "2012007083" },
            { name: "实验室三辊研磨机", number: "2012007081" },
            { name: "实验室砚磨机", number: "2012007082" },
            { name: "中试系统-复配装置", number: "2013007224" },
            { name: "带红外高速的摄影仪", number: "2024009875" },
            { name: "实时原位反应分析系统", number: "2013008016" },
            { name: "实时原位反应分析系统", number: "2012000051" },
          ],
        },
      ],
      input: "",
      activeTab: "today",
      listst: [
        {
          name: "广东质检中诚认证有限公司到中广...",
        },
        { name: "材料科学、化学工程及医药研发成..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
        { name: "植酸检测方法及作用" },
        { name: "兔拉检测：行业中芯片造假技术..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
      ],
      showdh: true,
      showdh1: false,
      noAnimation: false,
      selectedItem: null,
      tableDataItem: [],
      tableTitle: [
        { key: "楼层" },
        { key: "设备编号" },
        { key: "设备名称" },
        { key: "房间号" },
        { key: "模型" },
        { key: "设备状态" },
        { key: "状态说明" },
      ],
      ids: null,
      nhlist: [
        {
          title: "供气压力",
          status: "0.3Mpa",
          unit: "℃",
        },

        {
          title: "供气流量",
          status: "6M3/min",
          unit: "㎡",
        },
        {
          title: "露点温度",
          status: "6℃",
          unit: "℃",
        },
        {
          title: "含氧量",
          status: "6PPM",
          unit: "㎡",
        },
      ],
      warnlist1: [
        {
          type: 1,
          name: "检测到烟雾，可能有着火灾的发生...",
          value: "",
          time: "09:13:59  2023-06-07",
        },
        {
          type: 2,
          name: "检测到烟雾，可能预示着火灾的发生..",
          value: "",
          time: "10:13:18  2023-06-12",
        },
        {
          type: 3,
          name: "实验室内检测到漏水，可能来自冷凝水..",
          value: "",
          time: "10:13:18  2023-06-12",
        },
      ],
      isButton2Active: false,
      status: "巡检中",
      status1: "已完成",
      status2: "待巡检",
      selectedIndex: 0,
      componentTag: "component0",
    };
  },
  // 计算属性类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {},
  // 方法集合
  methods: {
    showdetails(item) {
      console.log(item.items);

      this.isshow = true;
      this.tableDataItem = item.items;
    },
    hidedetails() {
      this.isshow = false;
    },
    oc(value) {
      console.log(value, "收到的值");
      this.showdh = value;
    },
  },
  // 生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.showdh1 = true;
    setTimeout(() => {
      this.showdh1 = false;
      this.noAnimation = false;
    }, 1000); // 动画持续时间为1秒
    console.log(1222);
  },
  beforeCreate() {}, // 生命周期 - 创建之前
  beforeMount() {}, // 生命周期 - 挂载之前
  beforeUpdate() {}, // 生命周期 - 更新之前
  updated() {}, // 生命周期 - 更新之后
  beforeUnmount() {
    // 在组件销毁之前清除定时器
    console.log(1111);
  },

  unmounted() {
    console.log(2222);
  }, // 生命周期 - 销毁之前
  destroyed() {
    console.log(1221);
  }, // 生命周期 - 销毁完成
  activated() {}, // 如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="less" scoped>
.container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: stretch;
  height: 1080px;
  text-align: center;

  .sbdetails {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
  }

  .box {
    margin-top: 6px;
    padding-top: 5px;
    margin-bottom: 0.225rem;
    height: 800px;
    width: 330px;
    // height: 800px;

    overflow-y: scroll;

    /* 设置垂直滚动条 */
    /* 设置滚动条的样式 */
    &::-webkit-scrollbar {
      width: 0.1px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条轨道的样式 */
    &::-webkit-scrollbar-track {
      background-color: #f1f1f1;
      /* 设置滚动条轨道的背景色 */
    }

    /* 设置滚动条滑块的样式 */
    &::-webkit-scrollbar-thumb {
      background-color: #334f6e;
      /* 设置滚动条滑块的背景色 */
    }

    /* 鼠标悬停在滚动条上时的样式 */
    &::-webkit-scrollbar-thumb:hover {
      background-color: #555;
      /* 设置鼠标悬停时滚动条滑块的背景色 */
    }

    .el-input {
      width: 305px;
      height: 34px;
      color: #fff !important;

      ::v-deep .el-input__wrapper {
        background: url("../assets/image/inputss.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        box-shadow: none !important;
      }
    }

    .suosuo {
      position: absolute;
      top: 66px;
      left: 285px;
    }

    .xiaobox {
      margin-top: 20px;
      display: flex;
      align-items: center;

      .siqiu {
        width: 16px;
        height: 16px;
      }

      .shuru {
        display: flex;
        align-items: center;
        padding-left: 10px;
        background: url("../assets/image/shebei12.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 261px;
        height: 26px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 12px;
        color: #ffffff;
      }
    }

    .xiaoboxs {
      cursor: pointer;
      margin-top: 14px;
      display: flex;
      margin-left: 5px;
      align-items: center;

      .nihaowo {
        width: 78px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 10px;
        color: #ffffff;
        display: flex;
        white-space: nowrap; /* 不换行 */
        overflow: hidden; /* 超出部分隐藏 */
        text-overflow: ellipsis; /* 超出部分显示省略号 */
      }

      .siqiu {
        width: 16px;
        height: 16px;
        margin-left: 10px;
        margin-right: 7px;
      }

      .shuru {
        display: flex;
        align-items: center;
        padding-left: 10px;
        background: url("../assets/image/shebei12.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 261px;
        height: 26px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 12px;
        color: #ffffff;
        white-space: nowrap; /* 不换行 */
        overflow: hidden; /* 超出部分隐藏 */
        text-overflow: ellipsis; /* 超出部分显示省略号 */
      }
    }
  }

  .left-panel {
    position: fixed;
    z-index: 1;
    top: 100px;
    left: 22px;
    width: 330px;
    height: 937px;

    background-size: 100% 100%;
    transform: translate(-122%);
    transition: transform 0.5s ease-in-out;
  }

  .left-panel-active {
    transform: translate(0%);
  }

  .left-panel-active1 {
    // transform: translate(0%);
    animation: slideOut 1s ease-in-out forwards;
  }

  @keyframes slideOut {
    100% {
      transform: translateX(0%);
    }

    // 85% {
    //   transform: translateX(-25%);
    // }

    // 65% {
    //   transform: translateX(-15%);
    // }

    // 40% {
    //   transform: translateX(-55%);
    // }

    // 30% {
    //   transform: translateX(-40%);
    // }

    0% {
      transform: translateX(-100%);
    }
  }

  .rtitle {
    margin-top: 16px;
  }

  @keyframes slideIn {
    0% {
      transform: translateX(100%);
    }

    // 30% {
    //   transform: translateX(65%);
    // }

    // 40% {
    //   transform: translateX(40%);
    // }

    // 65% {
    //   transform: translateX(15%);
    // }

    // 85% {
    //   transform: translateX(25%);
    // }

    100% {
      transform: translateX(0%);
    }
  }

  .right-panel {
    position: fixed;
    z-index: 1;
    right: 83px;
    width: 330px;
    top: 100px;
    height: 937px;

    background-size: 100% 100%;
    transform: translate(122%);
    transition: transform 0.5s ease-in-out;

    .jk {
      margin-top: 12px;
      width: 90%;
      height: 200px;
    }

    .box {
      margin-top: 6px;
      margin-bottom: 18px;
      // background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      width: 94%;
      // height: 428px;

      .loudong {
        margin-top: 21px;
        margin-bottom: 25px;
      }

      .wenzi {
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 10px;
        color: #bdecf9;
        text-align: left;
        margin-left: 20px;
        margin-right: 20px;

        .h2biaoti {
          margin-bottom: 15px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 12px;
          color: #00ffff;
        }
      }

      .p {
        text-indent: 2em;
        margin-bottom: 1em;
        letter-spacing: 0.05em;
      }
    }

    .boxxx {
      margin-top: 6px;
      margin-bottom: 18px;
      background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      width: 333px;
      height: 420px;
      overflow-y: scroll;
      /* 设置垂直滚动条 */
      // overflow: hidden;
      /* 设置滚动条的样式 */

      .zengti {
        margin: 10px 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 315px;
        height: 38px;
        gap: 5px;

        .left {
          display: flex;
          align-items: center;
          justify-content: space-evenly;
          width: 84px;
          height: 27px;

          .yuan {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #08f7f7;
          }

          .wenziss {
            display: flex;
            align-items: flex-start;
            flex-direction: column;

            .p1 {
              font-family: Source Han Sans SC;
              font-weight: 500;
              font-size: 10px;
              color: #ffffff;
            }

            .p2 {
              font-family: Source Han Sans SC;
              font-weight: 500;
              font-size: 10px;
              color: #55cff9;
            }
          }
        }

        .right {
          background: url("../assets/image/rightbeij.png");
          background-size: 100% 100%;
          background-repeat: no-repeat;

          width: 217px;
          height: 38px;
          font-family: Source Han Sans SC;
          font-weight: 500;
          font-size: 11px;
          color: #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    .boxxx::-webkit-scrollbar {
      width: 1px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条轨道的样式 */
    .boxxx::-webkit-scrollbar-track {
      background-color: #f1f1f1;
      /* 设置滚动条轨道的背景色 */
    }

    /* 设置滚动条滑块的样式 */
    .boxxx::-webkit-scrollbar-thumb {
      background-color: #013363;
      /* 设置滚动条滑块的背景色 */
    }
  }

  .no-animation {
    transition: none;
  }

  .right-panel-active {
    transform: translate(0%);
    // animation: slideIn 1s ease-in-out ;
  }

  .right-panel-active1 {
    // transform: translate(0%);
    animation: slideIn 1s ease-in-out forwards;
  }

  @keyframes slideIn {
    0% {
      transform: translateX(100%);
    }

    // 30% {
    //   transform: translateX(65%);
    // }

    // 40% {
    //   transform: translateX(40%);
    // }

    // 65% {
    //   transform: translateX(15%);
    // }

    // 85% {
    //   transform: translateX(25%);
    // }

    100% {
      transform: translateX(0%);
    }
  }

  .completed {
    background: #7ad0ff;
  }

  .incomplete {
    background: #ff6041;
  }

  .warning {
    background: #00ffc0;
  }

  .completeds {
    color: #7ad0ff;
  }

  .incompletes {
    color: #ff6041;
  }

  .warnings {
    color: #00ffc0;
  }
}

.ql-center {
  display: flex;
  margin-top: 20px;
  justify-content: space-around;
  margin-top: 14px;

  .ql-Box {
    width: 75px;
    height: 49px;
    border: 1px solid #7ad0ff;
    // opacity: 0.6;
    border-radius: 2px;

    .ql-box1 {
      font-family: Alibaba PuHuiTi;
      font-weight: bold;
      font-size: 16px;
      color: #7ad0ff;
      margin-top: -10px;
    }

    .ql-box {
      display: flex;
      padding-left: 8px;
      padding-right: 9px;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 34px;

      .left_ql {
        width: 49px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #ffffff;

        .yuan {
          width: 7px;
          height: 7px;
          border-radius: 50%;
        }

        .pp {
          color: #fff;
          font-size: 12px;
        }
      }

      img {
        height: 12px;
        width: 7px;
      }
    }
  }
}

.warn1 {
  background: url("../assets/image/warnred.png");
}

.warn2 {
  background: url("../assets/image/warnyellow.png");
}

.warn3 {
  background: url("../assets/image/warngreen.png");
}

.warning12 {
  background-size: 100% 100%;
  // width: 365px;
  height: 47px;

  .info {
    margin-top: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: left;

    margin-left: 50px;

    .info1 {
      display: flex;
      flex-direction: column;

      p:nth-of-type(1) {
        font-size: 13px;
        font-family: Alibaba PuHuiTi;
        font-weight: bold;
      }

      .red {
        color: #ff0000;
      }

      .green {
        color: #00ffcc;
      }

      .yellow {
        color: #ffff00;
      }

      p:nth-of-type(2) {
        font-size: 16px;
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        color: #ffffff;
      }
    }

    .info2 {
      margin-right: 10px;
      font-size: 15px;
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      color: #cffff8;
    }
  }
}
.xxxx {
  position: absolute;
  top: 1%;
  right: 1%;
  width: 25px;
  height: 25px;
  z-index: 99999;
}
</style>
