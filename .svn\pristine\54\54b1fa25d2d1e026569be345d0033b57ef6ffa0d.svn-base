<template>
  <div class="echart" ref="echart"></div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  props: {
    chartData: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {};
  },
  watch: {
    chartData: {
      handler(newData) {
        // 数据变化时更新 ECharts
        this.init();
      },
      deep: true, // 深度监听对象内部数据
    },
  },
  mounted() {
    this.init();
  },

  methods: {
    init() {
      const myChart = echarts.init(this.$refs.echart);
      let data = this.chartData
        ? this.chartData
        : {
            title: ["已领用1", "未领用1"],
            xAxisdata1: [80, 80, 97, 53, 95, 70, 88],
            xAxisdata2: [100, 100, 100, 100, 100, 100, 100],
            yAxisdata: [
              "一月很长的名字",
              "二月",
              "三月",
              "四月",
              "五月",
              "六月",
              "七月",
            ], // 示例长标签
          };

      const option = {
        tooltip: {
          trigger: "item", // 鼠标悬停时触发 tooltip
          formatter: (params) => {
            return `${params.marker} ${data.yAxisdata[params.dataIndex]}: ${params.value}小时`;
          },
        },
        grid: {
          left: "18%", // 调整整个图表的左边距
          right: "0%",
          top: "10%",
          bottom: "10%",
        },
        xAxis: {
          type: "value",
          splitLine: { show: false },
          axisTick: { show: false },
          axisLabel: {
            textStyle: { color: "#fff", fontSize: 18 }, // White font for xAxis labels
          },
          axisLine: { lineStyle: { color: "#ddd" } },
        },
        yAxis: {
          type: "category",
          splitLine: { show: false },
          axisTick: { show: false },
          data: data.yAxisdata,
          axisLabel: {
            // rotate: 45, // 设置标签倾斜显示
            textStyle: { color: "#fff", fontSize: 18 }, // White font for yAxis labels
            formatter: (value) => {
              // 设置过长标签的省略号显示
              const maxLength =4; // 设定最大字符长度
              return value.length > maxLength ? value.slice(0, maxLength) + '...' : value;
            },
          },
          axisLine: { lineStyle: { color: "#fff" } },
        },
        series: [
          {
            name: "剩余",
            type: "bar",
            data: data.xAxisdata1,
            itemStyle: {
              emphasis: { barBorderRadius: 15 },
              normal: {
                barBorderRadius: 15,
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  { offset: 0, color: "#99FFFF" },
                  { offset: 1, color: "#2A71FD" },
                ]),
              },
            },
            label: {
              normal: {
                show: true,
                formatter: "{c}小时",
                position: "insideRight",
                offset: [-2, 1.5],
                textStyle: { color: "#fff", fontSize: 15 }, // White font for series labels
              },
            },
          },
        ],
        barCategoryGap: "40%",
      };

      myChart.setOption(option);
    },
  },
};
</script>

<style lang="less" scoped>
.echart {
  flex-grow: 1;
  width: 580px;
  height: 380px;
}
</style>
