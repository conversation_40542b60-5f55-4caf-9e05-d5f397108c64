<template>
  <div>
    <component :is="componentTag" @fatherMethoddd="fatherMethoddd" v-if="isshowwhat"></component>
    <div class="botbtn">
      <div v-for="(item, index) in  changeTitle " :key="index" :class="titactive == index ? 'btt1' : 'btt'"
        @click="changetit(index)">
        {{ item }}
      </div>

    </div>
    <tedai :ids="ids" :selectedItem="selectedItem" class="sbdetails" :zengtiimg="zengtiimg" v-if="false"
      @hidedetails="hidedetailsss"></tedai>
    <biaoGesss v-if="isshow" @hidedetails="hidedetails" :tableTitle="tableTitle" :tableDataItem="tableDataItem">
    </biaoGesss>
    <div class="container" v-if="!isshowwhat">
      <div class="left-panel" :class="{
        'left-panel-active': showdh,
        'no-animation': noAnimation,
        'left-panel-active1': showdh1,
      }">
        <Title2 class="ltitle1" tit="环境监管">
          <div class="box">
            <div>
              <el-input class="el-input" v-model="input" placeholder="请输入内容"></el-input>
            </div>
            <!-- <div class="xiaobox">
              <img class="siqiu" src="../assets/image/shixinqiu.png" alt="" />
              <div class="shuru">全部设备</div>
            </div> -->
            <div class="menu">
              <div v-for="(menu, index) in menus" :key="index" class="menu-group">
                <div class="menu-item" @click="toggleSubMenu(menu.id, menu.title, index)">
                  {{ menu.title }}
                </div>
                <div v-show="activeSubmenu === menu.id" class="submenu">
                  <div v-for="(item, subIndex) in menu.submenu" :key="subIndex" class="submenu-items">
                    <div @click="toggleSubSubMenu(item.id)">
                      {{ item.title }}
                    </div>
                    <div v-show="activeSubSubmenu === item.id" class="submenu">
                      <div v-for="(subItem, thirdIndex) in item.submenu" :key="thirdIndex" class="submenu-item"
                        @click="setContent(subItem, thirdIndex)">
                        {{ subItem.title }}
                        <!-- <div class="listtype">使用中</div> -->
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- shebei12.png -->
        </Title2>
      </div>
      <!-- shebeibgc.png -->
      <!-- 右侧内容 -->
      <!-- 右侧内容 -->

      <div v-show="detalis && detalis.length" class="right-panel" :class="{
        'right-panel-active': showdh,
        'no-animation': noAnimation,
        'right-panel-active1': showdh1,
      }">
        <Title3 :tit="cgqname">
          <div class="box">
            <div class="xiaoboxs" v-for="item in detalis" :key="item">
              <img class="siqiu" src="../assets/image/kongxin.png" alt="" />
              <div class="nihaowo">{{ item.name }}</div>
              <div class="shuru">{{ item.value }}</div>
            </div>
          </div>
          <hr class="hr" />
          <echarts2 style="width: 100%;"> </echarts2>
          <hr class="hr" />
          <p class="local">
            功能介绍：<br />
            &nbsp&nbsp&nbsp传感器可以测量周围环境的温度，并将其转换为数字
            信号或模拟信号输出;在温度超过设定范围后触发报警或关闭设备，以保护应用的安全性。
          </p>
          <hr class="hr" />
          <div class="local">
            <div style="display: flex;">
              <div>维护人：</div>
              <div>王工</div>
            </div>
            <div style="display: flex; margin-top: 10px">
              <div>联系方式：</div>
              <div>173****5896</div>
            </div>
          </div>
        </Title3>
      </div>
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import component0 from "@/views/tongji/huanjing.vue";
import huanxing from "@/components/echarts/huanxing.vue";
import zhexian from "@/components/echarts/zhexian.vue";
import zhexian1 from "@/components/echarts/zhexian1.vue";
import SystemDete from "@/components/echarts/SystemDete.vue";
import echarts1 from "@/components/echarts/bingjifang/echarts3.vue";
import tedai from "@/components/common/tedais.vue";
import biaoGesss from "@/components/common/biaoGesss.vue";
import shuangxiang from "@/components/echarts/shuangxiang.vue";
import echarts2 from "@/components/echarts/bingjifang/echarts4.vue";
// import details from "@/components/common/details.vue";
export default {
  // import引入的组件需要注入到对象中才能使用
  components: {
    component0,
    tedai,
    echarts2,
    huanxing,
    zhexian,
    zhexian1,
    SystemDete,
    echarts1,
    shuangxiang,
    biaoGesss, component0
  },
  props: ["tabledata", "zengtiimg"],
  //  cgqlistss: [
  //       { name: "湿度范围：", value: "5%RH~100%RH（无结露）" },
  //       { name: "湿度分辨率：", value: "0.04%" },
  //       { name: "IP地址：", value: "±3%RH（20%RH~80%RH）" },
  //       { name: "湿度精度：", value: "-40℃~+125℃" },
  //       { name: "温度分辨率：", value: "0.01℃" },
  //       { name: "温度精度：", value: "±0.5℃（10℃~60℃）" },
  //     ],

  data() {
    // 这里存放数据
    return {
      isshowwhat: true,
      isshowsss: false,
      titactive: 0,
      changeTitle: ['数据统计', '数据列表'],
      isshowsss: false,
      activeSubmenu: null, // 当前激活的二级菜单
      activeSubSubmenu: null, // 当前激活的三级菜单
      activeContent: null, // 当前显示的内容
      isshow: false,
      selectedIndex: null,
      selectedItem: null,
      xxxx: false,
      cgqlist: [
        { name: "温度", value: "20℃" },
        { name: "湿度", value: "12" },
        // { name: "品牌型号：", value: "海康/DS-2CD7205E-SH" },
        // { name: "IP地址：", value: "************" },
        // { name: "设备类型：", value: "高清半球型摄像机" },
      ],
      menus: [
        {
          id: "menu5",
          title: "5F",
          submenu: [
            {
              id: "submenu5-1",
              title: "501-通用实验室",
              submenu: [
                {
                  title: "501-温湿度传感器",
                  content: "501-温湿度传感器",
                  cgqlist: [
                    { name: "温度", value: "20℃" },
                    { name: "湿度", value: "15%RH" },
                  ],
                },
              ],

            },
            {
              id: "submenu5-2",
              title: "502-空调机房",
              submenu: [
                {
                  title: "502-温湿度传感器",
                  content: "502-温湿度传感器",
                  cgqlist: [
                    { name: "温度", value: "21.2℃" },
                    { name: "湿度", value: "14%RH" },
                  ],
                },
              ],
            },
          ],
        },
        {
          id: "menu4",
          title: "4F",
          submenu: [
            {
              id: "submenu4-1",
              title: "401-会议室",
              submenu: [
                {
                  title: "401-温湿度传感器",
                  content: "401-温湿度传感器",
                  id: 500190,
                  cgqlist: [
                    { name: "温度", value: "22.2℃" },
                    { name: "湿度", value: "13%RH" },
                  ],
                },
              ],
            },
            {
              id: "submenu4-2",
              title: "402-通用实验室",
              submenu: [
                {
                  title: "402-温湿度传感器",
                  content: "402-温湿度传感器",
                  id: 500191,
                  cgqlist: [
                    { name: "温度", value: "23.2℃" },
                    { name: "湿度", value: "11%RH" },
                  ],
                },

                {
                  "title": "402-压差传感器",
                  "content": "402-压差传感器",
                  id: 500195,
                  cgqlist: [
                    { name: "压差", value: "7pa" },
                  ],
                },
              ]
            },
            {
              id: "submenu4-3",
              title: "403-通用实验室",
              submenu: [
                {
                  title: "403-温湿度传感器",
                  content: "403-温湿度传感器",
                  id: 500192,
                  cgqlist: [
                    { name: "温度", value: "23.2℃" },
                    { name: "湿度", value: "11%RH" },
                  ],
                },
                {
                  "title": "403-压差传感器",
                  "content": "403-压差传感器",
                  id: 500196,
                  cgqlist: [
                    { name: "压差", value: "8pa" },


                  ],
                },
              ]

            },
            {
              id: "submenu4-4",
              title: "404-通用实验室",
              submenu: [
                {
                  title: "404-温湿度传感器",
                  content: "404-温湿度传感器",
                  id: 500193,
                  cgqlist: [
                    { name: "温度", value: "23.2℃" },
                    { name: "湿度", value: "11%RH" },
                  ],
                },
              ],
            },
            {
              id: "submenu4-5",
              title: "405-通用实验室",
              submenu: [
                {
                  title: "405-温湿度传感器",
                  content: "405-温湿度传感器",
                  id: 500194,
                  cgqlist: [
                    { name: "温度", value: "23.2℃" },
                    { name: "湿度", value: "11%RH" },
                  ],
                },
              ],
            },
          ],
        },
      ],
      data: [
        {
          category: "仪器设备",
          items: [
            {
              number: "LAB001",
              nanme: "显微镜",
              pingpai: "品牌X",
              baozhuang: "3楼",
              xiaobaozhuang: "A305",

              qita: "",
            },
            {
              number: "LAB002",
              nanme: "离心机",
              pingpai: "品牌Y",
              baozhuang: "2楼",
              xiaobaozhuang: "B210",

              qita: "",
            },
            {
              number: "LAB003",
              nanme: "培养箱",
              pingpai: "品牌Z",
              baozhuang: "3楼",
              xiaobaozhuang: "A307",

              qita: "",
            },
            {
              number: "LAB004",
              nanme: "天平",
              pingpai: "品牌W",
              baozhuang: "2楼",
              xiaobaozhuang: "B209",

              qita: "",
            },
            {
              number: "LAB005",
              nanme: "烘箱",
              pingpai: "品牌V",
              baozhuang: "4楼",
              xiaobaozhuang: "C401",

              qita: "",
            },
          ],
        },
        {
          category: "计算机和信息化设备",
          items: [
            {
              number: "LAB001",
              nanme: "实验室电脑",
              pingpai: "品牌A",
              baozhuang: "3楼",
              xiaobaozhuang: "A308",

              qita: "",
            },
            {
              number: "LAB002",
              nanme: "数据采集设备",
              pingpai: "品牌B",
              baozhuang: "3楼",
              xiaobaozhuang: "A310",

              qita: "",
            },
            {
              number: "LAB003",
              nanme: "服务器",
              pingpai: "品牌C",
              baozhuang: "1楼",
              xiaobaozhuang: "机房",

              qita: "",
            },
          ],
        },
        {
          category: "办公设备",
          items: [
            {
              number: "LAB001",
              nanme: "打印机",
              pingpai: "品牌D",
              baozhuang: "2楼",
              xiaobaozhuang: "B205",

              qita: "",
            },
            {
              number: "LAB002",
              nanme: "复印机",
              pingpai: "品牌E",
              baozhuang: "2楼",
              xiaobaozhuang: "B206",

              qita: "",
            },
            {
              number: "LAB003",
              nanme: "投影仪",
              pingpai: "品牌F",
              baozhuang: "3楼",
              xiaobaozhuang: "A309",

              qita: "",
            },
          ],
        },
        {
          category: "基础设施",
          items: [
            {
              number: "LAB001",
              nanme: "实验台",
              pingpai: "品牌G",
              baozhuang: "4楼",
              xiaobaozhuang: "C402",

              qita: "",
            },
            {
              number: "LAB002",
              nanme: "通风系统",
              pingpai: "品牌H",
              baozhuang: "5楼",
              xiaobaozhuang: "D501",

              qita: "",
            },
            {
              number: "LAB003",
              nanme: "实验室椅子",
              pingpai: "品牌I",
              baozhuang: "3楼",
              xiaobaozhuang: "A306",

              qita: "",
            },
          ],
        },
      ],
      input: "",
      activeTab: "today",
      listst: [
        {
          name: "广东质检中诚认证有限公司到中广...",
        },
        { name: "材料科学、化学工程及医药研发成..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
        { name: "植酸检测方法及作用" },
        { name: "兔拉检测：行业中芯片造假技术..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
      ],
      showdh: true,
      showdh1: false,
      noAnimation: false,

      tableDataItem: [],
      tableTitle: [
        { key: "" },
        { key: "资产名称" },
        { key: "资产品牌" },
        { key: "楼层" },
        { key: "房间号" },

        { key: "其他说明" },
      ],
      ids: null,
      nhlist: [
        {
          title: "供气压力",
          status: "0.3Mpa",
          unit: "℃",
        },

        {
          title: "供气流量",
          status: "6M3/min",
          unit: "㎡",
        },
        {
          title: "露点温度",
          status: "6℃",
          unit: "℃",
        },
        {
          title: "含氧量",
          status: "6PPM",
          unit: "㎡",
        },
      ],
      warnlist1: [
        {
          type: 1,
          name: "检测到烟雾，可能有着火灾的发生...",
          value: "",
          time: "09:13:59  2023-06-07",
        },
        {
          type: 2,
          name: "检测到烟雾，可能预示着火灾的发生..",
          value: "",
          time: "10:13:18  2023-06-12",
        },
        {
          type: 3,
          name: "实验室内检测到漏水，可能来自冷凝水..",
          value: "",
          time: "10:13:18  2023-06-12",
        },
      ],
      isButton2Active: false,
      status: "巡检中",
      status1: "已完成",
      status2: "待巡检",
      selectedIndex: 0,
      componentTag: "component0",
      sbtitle: "",
      isFirstTime: true,
      cgqname: "",
      detalis: [], // 用于标记是否是第一次调用
    };
  },
  // 计算属性类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {},
  // 方法集合
  methods: {
    changetit(index) {
      this.titactive = index
      this.isshowwhat = !index
      if (index == 1) {
        this.showdh = false;
        this.showdh1 = true;
        this.noAnimation = true;
      } else {
        this.showdh = true;
        this.showdh1 = false;
        this.noAnimation = false;
      }

    },
    hidedetailsss() {
      this.isshowsss = false;
    },
    setContent(content, index) {
      this.isshowsss = true;
      this.selectedItem = content.content;
      this.detalis = content.cgqlist;
      this.cgqname = content.title;
      console.log(this.detalis, "details");
      this.selectedIndex = index;

      // 保存旧的 sbtitle 值，用于与当前值比较
      const oldSbtitle = this.sbtitle;

      // 设置内容
      console.log(content.id, this.sbtitle, "设置内容");

      // 如果是第一次调用，或者 sbtitle 发生变化，则触发事件
      // if (this.isFirstTime || this.sbtitle !== oldSbtitle) {
      //   this.$emit('seedbuild', 1);  // 触发父组件的事件并传递数据
      //   this.$emit('seedfloor', this.sbtitle + 2);  // 触发父组件的事件并传递数据
      //   this.isFirstTime = false;  // 第一次调用后将标志置为 false
      // }

      this.isFirstTime = false; // 第一次调用后将标志置为 false
      // 延时 1 秒后触发 seedid 事件
      // setTimeout(() => {
      if (content.id) {
        this.$emit("seedid", content.id);
      }

      // }, 500);
    },

    // 切换二级菜单显示/隐藏
    toggleSubMenu(menuId, title, index) {
      this.activeSubmenu = this.activeSubmenu === menuId ? null : menuId;

      this.sbtitle = index
      this.$emit('seedbuild', '实验楼');  // 触发父组件的事件并传递数据
      this.$emit('seedfloor', title, '', '温湿度传感器,压力传感器', true);  // 触发父组件的事件并传递数据

    },
    // 切换三级菜单显示/隐藏
    toggleSubSubMenu(submenuId) {
      this.activeSubSubmenu =
        this.activeSubSubmenu === submenuId ? null : submenuId;
    },

    showdetails(item) {
      console.log(item.items);

      this.isshow = true;
      this.tableDataItem = item.items;
    },
    hidedetails() {
      this.isshow = false;
    },
    oc(value) {
      console.log(value, "收到的值");
      this.showdh = value;
    },
  },
  // 生命周期 - 创建完成（可以访问当前this实例）
  created() { },
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.showdh1 = true;
    // setTimeout(() => {
    //   this.showdh1 = false;
    //   this.noAnimation = false;
    // }, 1000); // 动画持续时间为1秒
    console.log(1222);
  },
  beforeCreate() { }, // 生命周期 - 创建之前
  beforeMount() { }, // 生命周期 - 挂载之前
  beforeUpdate() { }, // 生命周期 - 更新之前
  updated() { }, // 生命周期 - 更新之后
  beforeUnmount() {
    // 在组件销毁之前清除定时器
    console.log(1111);
  },

  unmounted() {
    console.log(2222);
  }, // 生命周期 - 销毁之前
  destroyed() {
    console.log(1221);
  }, // 生命周期 - 销毁完成
  activated() { }, // 如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="less" scoped>
.botbtn {
  position: fixed;
  top: 978px;
  left: 827px;
  width: 200px;
  height: 43px;
  background: #022d56;
  border-radius: 8px;
  z-index: 20;
  display: flex;
  align-items: center;
  justify-content: center;

  .btt {
    color: #fff;
    font-size: 17px;
    line-height: 44px;
    width: 88px;
    text-align: center;
    height: 43px;
    cursor: pointer;
  }

  .btt1 {
    color: rgb(8, 207, 241);
    font-size: 17px;
    line-height: 44px;
    width: 88px;
    text-align: center;
    height: 43px;
    cursor: pointer;
  }
}

.container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: stretch;
  height: 1080px;
  text-align: center;

  .sbdetails {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
  }

  .box {
    margin-top: 6px;
    padding-top: 5px;
    margin-bottom: 0.225rem;
    // height: 800px;
    width: 330px;
    // height: 800px;

    overflow-y: scroll;

    /* 设置垂直滚动条 */
    /* 设置滚动条的样式 */
    &::-webkit-scrollbar {
      width: 0.1px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条轨道的样式 */
    &::-webkit-scrollbar-track {
      background-color: #f1f1f1;
      /* 设置滚动条轨道的背景色 */
    }

    /* 设置滚动条滑块的样式 */
    &::-webkit-scrollbar-thumb {
      background-color: #334f6e;
      /* 设置滚动条滑块的背景色 */
    }

    /* 鼠标悬停在滚动条上时的样式 */
    &::-webkit-scrollbar-thumb:hover {
      background-color: #555;
      /* 设置鼠标悬停时滚动条滑块的背景色 */
    }

    .el-input {
      width: 305px;
      height: 34px;
      color: #fff !important;

      ::v-deep .el-input__wrapper {
        background: url("../assets/image/inputss.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        box-shadow: none !important;
      }
    }

    .suosuo {
      position: absolute;
      top: 66px;
      left: 285px;
    }

    .xiaobox {
      margin-top: 20px;
      display: flex;
      align-items: center;

      .siqiu {
        width: 16px;
        height: 16px;
      }

      .shuru {
        display: flex;
        align-items: center;
        padding-left: 10px;
        background: url("../assets/image/shebei12.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 261px;
        height: 32px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 17px;
        color: #ffffff;
      }
    }

    .xiaoboxs {
      cursor: pointer;
      margin-top: 14px;
      display: flex;
      margin-left: 5px;
      align-items: center;

      .nihaowo {
        width: 78px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 15px;
        color: #ffffff;
        display: flex;
        white-space: nowrap;
        /* 不换行 */
        overflow: hidden;
        /* 超出部分隐藏 */
        text-overflow: ellipsis;
        /* 超出部分显示省略号 */
      }

      .siqiu {
        width: 16px;
        height: 16px;
        margin-left: 10px;
        margin-right: 7px;
      }

      .shuru {
        display: flex;
        align-items: center;
        padding-left: 10px;
        background: url("../assets/image/shebei12.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 261px;
        height: 32px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 17px;
        color: #ffffff;
        white-space: nowrap;
        /* 不换行 */
        overflow: hidden;
        /* 超出部分隐藏 */
        text-overflow: ellipsis;
        /* 超出部分显示省略号 */
      }
    }
  }

  .left-panel {
    position: fixed;
    z-index: 1;
    top: 100px;
    left: 22px;
    width: 330px;
    height: 937px;

    background-size: 100% 100%;
    transform: translate(-122%);
    transition: transform 0.5s ease-in-out;
  }

  .left-panel-active {
    transform: translate(0%);
  }

  .left-panel-active1 {
    // transform: translate(0%);
    animation: slideOut 1s ease-in-out forwards;
  }

  @keyframes slideOut {
    100% {
      transform: translateX(0%);
    }

    // 85% {
    //   transform: translateX(-25%);
    // }

    // 65% {
    //   transform: translateX(-15%);
    // }

    // 40% {
    //   transform: translateX(-55%);
    // }

    // 30% {
    //   transform: translateX(-40%);
    // }

    0% {
      transform: translateX(-100%);
    }
  }

  .rtitle {
    margin-top: 16px;
  }

  @keyframes slideIn {
    0% {
      transform: translateX(100%);
    }

    // 30% {
    //   transform: translateX(65%);
    // }

    // 40% {
    //   transform: translateX(40%);
    // }

    // 65% {
    //   transform: translateX(15%);
    // }

    // 85% {
    //   transform: translateX(25%);
    // }

    100% {
      transform: translateX(0%);
    }
  }

  .right-panel {
    position: fixed;
    z-index: 1;
    right: 83px;
    width: 330px;
    top: 100px;
    height: 937px;

    background-size: 100% 100%;
    transform: translate(122%);
    transition: transform 0.5s ease-in-out;

    .jk {
      margin-top: 12px;
      width: 90%;
      height: 200px;
    }

    .box {
      margin-top: 6px;
      margin-bottom: 18px;
      // background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      width: 94%;
      // height: 428px;

      .loudong {
        margin-top: 21px;
        margin-bottom: 25px;
      }

      .wenzi {
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 10px;
        color: #bdecf9;
        text-align: left;
        margin-left: 20px;
        margin-right: 20px;

        .h2biaoti {
          margin-bottom: 15px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 12px;
          color: #00ffff;
        }
      }

      .p {
        text-indent: 2em;
        margin-bottom: 1em;
        letter-spacing: 0.05em;
      }
    }

    .boxxx {
      margin-top: 6px;
      margin-bottom: 18px;
      background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      width: 333px;
      height: 420px;
      overflow-y: scroll;
      /* 设置垂直滚动条 */
      // overflow: hidden;
      /* 设置滚动条的样式 */

      .zengti {
        margin: 10px 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 315px;
        height: 38px;
        gap: 5px;

        .left {
          display: flex;
          align-items: center;
          justify-content: space-evenly;
          width: 84px;
          height: 27px;

          .yuan {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #08f7f7;
          }

          .wenziss {
            display: flex;
            align-items: flex-start;
            flex-direction: column;

            .p1 {
              font-family: Source Han Sans SC;
              font-weight: 500;
              font-size: 10px;
              color: #ffffff;
            }

            .p2 {
              font-family: Source Han Sans SC;
              font-weight: 500;
              font-size: 10px;
              color: #55cff9;
            }
          }
        }

        .right {
          background: url("../assets/image/rightbeij.png");
          background-size: 100% 100%;
          background-repeat: no-repeat;

          width: 217px;
          height: 38px;
          font-family: Source Han Sans SC;
          font-weight: 500;
          font-size: 11px;
          color: #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    .boxxx::-webkit-scrollbar {
      width: 1px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条轨道的样式 */
    .boxxx::-webkit-scrollbar-track {
      background-color: #f1f1f1;
      /* 设置滚动条轨道的背景色 */
    }

    /* 设置滚动条滑块的样式 */
    .boxxx::-webkit-scrollbar-thumb {
      background-color: #013363;
      /* 设置滚动条滑块的背景色 */
    }
  }

  .no-animation {
    transition: none;
  }

  .right-panel-active {
    transform: translate(0%);
    // animation: slideIn 1s ease-in-out ;
  }

  .right-panel-active1 {
    // transform: translate(0%);
    animation: slideIn 1s ease-in-out forwards;
  }

  @keyframes slideIn {
    0% {
      transform: translateX(100%);
    }

    // 30% {
    //   transform: translateX(65%);
    // }

    // 40% {
    //   transform: translateX(40%);
    // }

    // 65% {
    //   transform: translateX(15%);
    // }

    // 85% {
    //   transform: translateX(25%);
    // }

    100% {
      transform: translateX(0%);
    }
  }

  .completed {
    background: #7ad0ff;
  }

  .incomplete {
    background: #ff6041;
  }

  .warning {
    background: #00ffc0;
  }

  .completeds {
    color: #7ad0ff;
  }

  .incompletes {
    color: #ff6041;
  }

  .warnings {
    color: #00ffc0;
  }
}

.ql-center {
  display: flex;
  margin-top: 20px;
  justify-content: space-around;
  margin-top: 14px;

  .ql-Box {
    width: 75px;
    height: 49px;
    border: 1px solid #7ad0ff;
    // opacity: 0.6;
    border-radius: 2px;

    .ql-box1 {
      font-family: Alibaba PuHuiTi;
      font-weight: bold;
      font-size: 16px;
      color: #7ad0ff;
      margin-top: -10px;
    }

    .ql-box {
      display: flex;
      padding-left: 8px;
      padding-right: 9px;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 34px;

      .left_ql {
        width: 49px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #ffffff;

        .yuan {
          width: 7px;
          height: 7px;
          border-radius: 50%;
        }

        .pp {
          color: #fff;
          font-size: 12px;
        }
      }

      img {
        height: 12px;
        width: 7px;
      }
    }
  }
}

.warn1 {
  background: url("../assets/image/warnred.png");
}

.warn2 {
  background: url("../assets/image/warnyellow.png");
}

.warn3 {
  background: url("../assets/image/warngreen.png");
}

.warning12 {
  background-size: 100% 100%;
  // width: 365px;
  height: 47px;

  .info {
    margin-top: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: left;

    margin-left: 50px;

    .info1 {
      display: flex;
      flex-direction: column;

      p:nth-of-type(1) {
        font-size: 13px;
        font-family: Alibaba PuHuiTi;
        font-weight: bold;
      }

      .red {
        color: #ff0000;
      }

      .green {
        color: #00ffcc;
      }

      .yellow {
        color: #ffff00;
      }

      p:nth-of-type(2) {
        font-size: 16px;
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        color: #ffffff;
      }
    }

    .info2 {
      margin-right: 10px;
      font-size: 15px;
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      color: #cffff8;
    }
  }
}

.xxxx {
  position: absolute;
  top: 1%;
  right: 1%;
  width: 25px;
  height: 25px;
  z-index: 99999;
}

/* 菜单容器 */
.menu-container {
  display: flex;
  gap: 20px;
}

/* 菜单样式 */
.menu {
  width: 100%;
  // background-color: #fff;
  // border: 1px solid #ddd;
  border-radius: 4px;
}

/* 菜单项样式 */
.menu-group {
  margin-top: 14px;
}

.menu-item {
  cursor: pointer;
  background: url("../assets/image/rightbeij.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;

  width: 100%;
  height: 36px;
  font-family: Source Han Sans SC;
  font-weight: 500;
  font-size: 20px;
  color: #ffffff;
  display: flex;
  align-items: center;
  padding-left: 10px;
  // justify-content: center;
}

.menu-item:hover {
  background-color: #f0f0f0 !important;
  color: #00ffc0;
}

.submenu {
  display: block;
  /* 确保子菜单是块级元素 */
  // background-color: #f9f9f9;
  padding-left: 8px;
}

.submenu-items:hover {
  color: #00ffc0;
}

.submenu-item:hover {
  color: #00ffc0;
}

.submenu-items {
  cursor: pointer;
  // background-color:#013363;
  width: 100%;

  font-family: Source Han Sans SC;
  font-weight: 500;
  font-size: 18px;
  color: #ffffff;
  /* 修改 display 属性 */
  display: flex;
  flex-direction: column;
  /* 垂直方向排列 */
  align-items: flex-start;
  padding-left: 10px;
  margin-top: 10px;
}

.submenu-item {
  cursor: pointer;
  background: url("../assets/image/rightbeij.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: 300px !important;
  /* 移除固定高度 */
  /* height: 30px; */
  font-family: Source Han Sans SC;
  font-weight: 500;
  font-size: 15px;
  color: #ffffff;
  /* 修改 display 属性 */
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding-left: 10px;

  margin-top: 10px;
}

.qiuqiu {
  display: flex;
  align-items: center;

  .siqiu {
    width: 16px;
    height: 16px;
    margin-left: 10px;
    margin-right: 7px;
  }
}

.listtype {
  background: url("../assets/image/xuanzhotiemmoon.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 80px;
  height: 26px;
  text-align: center;
  line-height: 26px;

  font-size: 13px;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  color: #fff;
}

.listtypes {
  background: url("../assets/image/xuanzhotiemmoon.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 100px;
  height: 36px;
  text-align: center;
  line-height: 36px;
  cursor: pointer;
  font-size: 13px;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  color: #fff;
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  font-size: 15px;
}

.listtypes {
  background: url("../assets/image/xuanzhotiemmoon.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 100px;
  height: 32px;
  text-align: center;
  line-height: 32px;
  cursor: pointer;
  font-size: 13px;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  color: #fff;
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 190px;
  /* 你可以根据需要调整宽度 */
  font-size: 15px;
}

.hr {
  margin-top: 24px;
  margin-bottom: 25px;
  width: 100%;
  background-color: rgba(36, 101, 138, 1);
  color: rgba(36, 101, 138, 1);
}

.local {
  text-align: left !important;

  font-family: Source Han Sans SC;
  font-weight: bold;
  font-size: 17px;
  color: #ffffff;

  // display: flex;
  // justify-content: space-between;
  .lianxi {
    margin-left: 118px;
  }
}
</style>
