import { createRouter, createWebHashHistory } from "vue-router";
import Home from "../views/Home.vue";
import Login from "../views/Login.vue";

const routes = [
  {
    path: "/",
    name: "Login",
    component: Login,
  },
  {
    path: "/home",
    name: "Home",
    component: Home,
    // 添加 props 配置来处理查询参数
    props: (route) => ({ ...route.query }),
    // 或者使用更具体的配置
    // props: (route) => ({ f: route.query.f }),
  },
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

// 修改路由守卫，保留查询参数
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem("token");
  
  if (token && to.path === "/") {
    // 保留查询参数
    next({ 
      path: "/home",
      query: to.query // 保留原有的查询参数
    });
  } else if (to.meta.requiresAuth && !token) {
    next({ 
      path: "/",
      query: to.query // 保留原有的查询参数
    });
  } else {
    next();
  }
});

export default router;