import { createRouter, createWebHashHistory } from "vue-router";
import Home from "../views/Home.vue";
import Login from "../views/Login.vue";

const routes = [
  {
    path: "/Login",
    name: "Login",
    component: Login,
  },
  {
    path: "/",
    name: "Home",
    component: Home,
    // meta: { requiresAuth: true },  // 需要登录认证
  },
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});
// // 路由守卫
// router.beforeEach((to, from, next) => {
//   const token = localStorage.getItem("token"); // 获取本地存储中的 token

//   // 如果用户已经登录，并且试图访问登录页，则重定向到主页（或其他页面）
//   if (token && to.path === '/') {
//     next({ path: '/home' }); // 跳转到主页或其他已登录页面
//   } else if (to.meta.requiresAuth && (!token || token === 'null')) {
//     // 如果目标路由需要授权且没有 token，跳转到登录页面
//     next({
//       path: '/',
//       query: { redirect: to.fullPath }, // 将用户重定向回原路径
//     });
//   } else {
//     next(); // 允许通过
//   }
// });

export default router;
