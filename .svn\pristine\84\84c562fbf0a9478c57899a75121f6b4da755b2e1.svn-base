<template>
  <div>
    <div class="zichanbeijin" v-if="ressss">
      <div class="title">
        <div>资产管理</div>
        <img class="img1" @click="close()" src="../../assets/image/table-x.png" alt="" />
      </div>
      <hr />
      <div class="titlecontent">
        <div class="xuan">
          <!-- <el-select v-model="value" placeholder="请选择材料品名">
            <el-option
              v-for="item in equipmentTags"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select> -->
          <el-cascader class="sect" placeholder="请选择类别" :options="equipmentTags" :show-all-levels="true"
            @change="handleCascaderChange"></el-cascader>
        </div>
        <div class="xuan">
          <el-input placeholder="请输入内容" v-model="input4">
            <i slot="prefix" class="el-input__icon el-icon-search"></i>
          </el-input>
        </div>
        <div class="xiang">
          <div class="item">
            <img src="../../assets/image/xiaoiconshousuo.png" alt="" />
            <div>搜索</div>
          </div>
          <div class="item">
            <img src="../../assets/image/xiaoiconzz.png" alt="" />
            <div>重置</div>
          </div>
          <div class="item item1">
            <img src="../../assets/image/xiaoiconschuan.png" alt="" />
            <div style="color: #ffaa3f">导出</div>
          </div>
        </div>
      </div>
      <div class="table-container">
        <div class="table-header">
          <div class="table-rowtitle">
            <div v-for="(item, index) in tableTitle" :key="index" class="table-cell">
              {{ item.key }}
            </div>
          </div>
        </div>

        <div class="table-body">
          <div v-for="(item, index) in devicedata" :key="index" class="table-row">
            <div class="table-cell">{{ item.name }}</div>
            <img class="table-cell imgg" :src="item.iconreal_url" alt="">
            <div class="table-cell">{{ item.price }}</div>
            <div class="table-cell">{{ new Date(item.purchased_date * 1000) }}</div>
            <div class="table-cell">{{ item.manu_at }}</div>
            <div class="table-cell">{{ item.manufacturer }}</div>
            <div class="table-cell">{{ item.location }}</div>
            <div class="table-cell">{{ !item.is_using ? '当前使用' : "可使用" }}</div>
            <div class="table-cell">{{ item.contact }}</div>
            <div class="table-cell">{{ item.phone }}</div>


            <!-- <div class="table-cell" style="color: #2ffff2">
              {{ item.status }}
            </div> -->

            <!-- <div class="table-cell">
              <img src="../../assets/image/imgiconxiaoc.png" alt="" />
            </div>

            <div class="table-cell">
              <img src="../../assets/image/xiaoviode.png" alt="" />
            </div>
            <div class="table-cell">
              <img src="../../assets/image/iconwenzi.png" alt="" />
            </div>
            <div class="table-cell">{{ item.responsible }}</div>
            <div class="table-cell">
              <img src="../../assets/image/zuochuoss.png" alt="" />
            </div> -->
          </div>
        </div>
      </div>
      <!-- <el-pagination
        class="fenye"
        small
        layout=" next, pager, prev"
        :total="50"
      >
      </el-pagination> -->
      <!-- <el-pagination small class="fenye" @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="currentPage4" :page-sizes="[16, 20, 30, 40]" :page-size="16"
        layout="total, sizes, next, pager,  prev, jumper" :total="0">
      </el-pagination> -->
      <el-pagination class="fenye" @size-change="handleSizeChange" hide-on-single-page="true"
        @current-change="handleCurrentChange" :page-size="16" :pager-count="4" layout="prev, pager, next,total"
        :total="total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
const baseURL = process.env.VUE_APP_BASE_API || '/lims/api';

const api = axios.create({
  baseURL
});
import axios from "axios";
import { mapActions, mapGetters } from 'vuex';
export default {
  // props: ["tableTitle", "tableDataItem"],
  data() {
    return {
      total: null,
      ressss: true,
      tableData: [
        {
          loucheng: "",
          shebeibianhao: "12121",
          shebeimingcheng: "保证正常",
          roomid: "房间号",
          model: "model",
          sbtypess: "sbtype",
          tytaisming: "sbtype",
        },
      ],
      tableTitle: [
        { key: "名称" },
        { key: "图片" },
        { key: "价格" },
        { key: "购入时间" },
        { key: "制造国家" },
        { key: "生产厂家" },
        { key: "位置" },
        { key: "状态" },
        { key: "负责人" },
        { key: "联系电话" },
      ],
      value: "",
      input4: "",
      options: [],
      devicedata: [

      ],
      token: '',
    };
  },

  computed: {
    ...mapGetters('equipment', ['equipmentTags']),
  },
  mounted() {
    this.gettoken('')
    // this.showdh1 = true;
    // this.fetchEquipmentTags();
  },
  methods: {
    handleCascaderChange(value) {
      console.log('选中的值:', value[1]);
      this.gettoken(value[1])
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.getyiqidetails(this.token, val - 1)
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.getyiqidetails(this.token, val - 1)
    },
    async gettoken(id) {
      try {
        const response = await api.post('', {
          "method": "equipment/searchEquipments",
          "params": {
            "criteria": {
              "cat": id,
              // "group": 1,
              // "searchtext": "搜索内容"

            }
          }
        });

        // 检查是否成功拿到 token
        if (response.data && response.data.response.token) {
          const token = response.data.response.token;
          this.token = token
          // 将 token 存入 localStorage
          // localStorage.setItem('authToken', token);
          console.log('535:', response.data.response);
          this.total = response.data.response.total
          this.handleSizeChange(1)

        } else {
          console.error('登录成功但未返回 token:', response.data.response);
        }
      } catch (error) {
        console.error('登录失败:', error);
      }

    },
    getyiqidetails(token, start) {
      console.log(token, start, 190);
      const headers = {
        clientid: '5a298e93-158d-4e22-83cf-6ceb62e9b4f1',
        clientsecret: '2c8ec39e-9887-482a-b28b-e64c496b601c'
      };
      const body = {
        "method": "equipment/getEquipments",
        "params": {
          "token": token,
          "start": start ? start * 16 : 0,
          "num": 16
        }
      };
      axios.post('http://yiqi.tju.edu.cn/lims/api', body, {})
        .then(response => {
          console.log(response.data, 203);
          this.devicedata = response.data.response
          this.loading = false
          console.log();
        })
        .catch(error => {
          console.error('Error:', error);
        });
    },

    close() {
      this.$emit("hidedetails");
    },
    anniu() {
      this.ressss = false;
    },
  },
};
</script>

<style lang="less" scoped>
.zichanbeijin {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: url("../../assets/image/zichanbeijin.png");
  width: 1863px;
  height: 868px;
  z-index: 99999;
  padding-left: 34px;
  padding-right: 22px;
  padding-top: 21px;

  .title {
    margin-bottom: 19px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-family: Source Han Sans SC;
    font-weight: bold;
    font-size: 18px;
    color: #ffffff;
  }

  .img1 {
    cursor: pointer;
    width: 15px;
    height: 15px;
  }

  hr {
    margin-bottom: 17px;
    border: none;
    border-top: 2px solid #466873;
    /* 设置边框颜色为红色 */
  }

  .titlecontent {
    width: 100%;
    display: flex;
    align-items: center;
    margin-bottom: 18px;

    .xuan {
      margin-right: 25px;
      width: 230px;
      height: 37px;
      color: #ffffff;

      ::v-deep .el-select__wrapper {
        background-color: rgba(28, 41, 50, 0.2);
        box-sizing: none;
        box-shadow: 0 0 0 1px #608e9a inset;
      }

      ::v-deep .el-input__wrapper {
        background-color: rgba(28, 41, 50, 0.2);
        box-sizing: none;
        box-shadow: 0 0 0 1px #608e9a inset;
      }
    }

    .xiang {
      width: 252px;
      height: 37px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .item {
        cursor: pointer;
        gap: 3px;
        width: 77px;
        height: 37px;
        border: 1px solid #537b86;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: Source Han Sans SC;
        font-weight: 400;
        font-size: 15px;
        color: #3cccf9;
        border-radius: 5px;
      }

      .item1 {
        cursor: pointer;
        border: 1px solid #d28e3c;
      }
    }
  }

  .table-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    color: #fff;
  }

  .table-row {
    color: #fff;
    display: flex;
    align-items: center;
    width: 100%;
    background-color: #1c2932;
    border: 1px solid #56808d;
    margin-bottom: -1px;
  }

  .table-header,
  .table-rowtitle {
    border: 1px solid #56808d;
    color: #fff;
    display: flex;
    width: 100%;
    background-color: #1c4064;
    margin-bottom: -1px;
  }

  .table-cell {
    flex: 1;
    padding: 10px;
    // border-bottom: 1px solid #ccc;
    text-align: center;
  }

  .table-header .table-cell {
    font-weight: bold;
  }

  .imgg {
    width: 20px;
    height: 160px;
  }

  .table-row:nth-child(even) {
    color: #fff;
    display: flex;
    width: 100%;
    background-color: #1c4064;
    border: 1px solid #56808d;
    margin-bottom: -1px;
  }

  .fenye {
    margin-top: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    // flex-direction: row-reverse;

    ::v-deep .el-pager li {
      background: url("../../assets/image/fenyebox.png");
      background-size: 100% 100%;
      color: #fff;
      margin: 3px !important;
    }
  }

}

::v-deep .el-pagination button {
  background: rgba(28, 41, 50, 0) !important;
  color: #fff !important;
}

::v-deep .el-pagination__jump {
  color: #fff !important;
}

::v-deep .el-pagination__total {
  color: #fff !important;
  margin-left: 10px !important;
}

::v-deep .el-select__wrapper {
  background-color: rgba(151, 173, 83, 0);
  box-sizing: none;
  box-shadow: 0 0 0 1px#694e31 inset;
  margin-right: 10px;
}

::v-deep .el-input__wrapper {
  background-color: rgba(28, 41, 50, 0);
  box-sizing: none;
  box-shadow: 0 0 0 1px #be8b34 inset;
}

.table-body {
  height: 625px;
  /* 设置容器的高度 */
  overflow-y: scroll;

  /* 设置垂直滚动条 */
  /* 默认情况下隐藏滚动条 */
  &::-webkit-scrollbar {
    width: 0;
    /* 设置滚动条的宽度为0，即隐藏滚动条 */
  }

  /* 鼠标悬停在容器上时显示滚动条 */
  &:hover::-webkit-scrollbar {
    width: 1px;
    /* 鼠标悬停时设置滚动条的宽度为10px */
  }

  /* 设置滚动条轨道的样式 */
  &::-webkit-scrollbar-track {
    background-color: #f1f1f1;
    /* 设置滚动条轨道的背景色 */
  }

  /* 设置滚动条滑块的样式 */
  &::-webkit-scrollbar-thumb {
    background-color: #888;
    /* 设置滚动条滑块的背景色 */
  }

  /* 鼠标悬停在滚动条上时的样式 */
  &::-webkit-scrollbar-thumb:hover {
    background-color: #555;
    /* 设置鼠标悬停时滚动条滑块的背景色 */
  }
}
</style>