<template>
  <div>
    <component :is="componentTag" @fatherMethoddd="fatherMethoddd" v-if="isshowwhat"></component>
    <div class="botbtn">
      <div v-for="(item, index) in  changeTitle " :key="index" :class="titactive == index ? 'btt1' : 'btt'"
        @click="changetit(index)">
        {{ item }}
      </div>
    </div>
    <div class="zichanbeijin" v-if="!isshowwhat">
      <div class="title">
        <div>报警监控</div>
        <img class="img1" @click="anniu()" src="../assets/image/table-x.png" alt="" />
      </div>
      <hr />
      <div class="titlecontent">
        <div class="xuan">
          <el-select v-model="value" placeholder="请选择设备类型">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </div>
        <div class="xuan">
          <el-input placeholder="请输入内容" v-model="input4">
            <i slot="prefix" class="el-input__icon el-icon-search"></i>
          </el-input>
        </div>
        <div class="xiang">
          <div class="item">
            <img src="../assets/image/xiaoiconshousuo.png" alt="" />
            <div>搜索</div>
          </div>
          <div class="item">
            <img src="../assets/image/xiaoiconzz.png" alt="" />
            <div>重置</div>
          </div>
          <div class="item item1">
            <img src="../assets/image/xiaoiconschuan.png" alt="" />
            <div style="color: #ffaa3f">导出</div>
          </div>
        </div>
      </div>
      <div class="table-container">
        <div class="table-header">
          <div class="table-rowtitle">
            <div class="table-cell">监控内容</div>
            <div class="table-cell">详细描述</div>

            <div class="table-cell">功能</div>
            <div class="table-cell">备注</div>

            <div class="table-cell">当前状态</div>
            <div class="table-cell">二维码</div>
            <div class="table-cell">视频</div>
            <div class="table-cell">位置</div>
            <div class="table-cell">责任人</div>
            <div class="table-cell">操作</div>
          </div>
        </div>
        <div class="table-body">
          <div v-for="(item, index) in tableData" :key="index" class="table-row">
            <div class="table-cell">{{ item.monitorContent }}</div>
            <div class="table-cell">{{ item.description }}</div>
            <div class="table-cell">{{ item.function }}</div>
            <div class="table-cell">{{ item.remarks }}</div>

            <div class="table-cell" style="color: #2ffff2">
              {{ item.status }}
            </div>

            <div class="table-cell">
              <img src="../assets/image/imgiconxiaoc.png" alt="" />
            </div>

            <div class="table-cell">
              <img src="../assets/image/xiaoviode.png" alt="" />
            </div>
            <div class="table-cell">
              <img src="../assets/image/iconwenzi.png" alt="" />
            </div>
            <div class="table-cell">{{ item.responsible }}</div>
            <div class="table-cell">
              <img src="../assets/image/zuochuoss.png" alt="" />
            </div>
          </div>
        </div>
      </div>
      <!-- <el-pagination
        class="fenye"
        small
        layout=" next, pager, prev"
        :total="50"
      >
      </el-pagination> -->
      <el-pagination small class="fenye" @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="currentPage4" :page-sizes="[100, 200, 300, 400]" :page-size="100"
        layout="total, sizes, next, pager,  prev, jumper" :total="5">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import component0 from "@/views/tongji/baojing.vue";
export default {
  components: {

    component0
  },
  data() {
    return {
      isshowwhat: true,
      componentTag: "component0",
      titactive: 0,
      changeTitle: ['数据统计', '数据列表'],
      ressss: true,
      tableData: [
        {
          monitorContent: "传感器温度",
          description: "温度过高",
          function: "监测温度",
          remarks: "处理相关",
          responsible: "张三",
        },
        {
          monitorContent: "通风柜",
          description: "记录报警",
          function: "及时告警",
          remarks: "处理相关",
          responsible: "张三",
        },
        {
          monitorContent: "气体泄漏",
          description: "二氧化碳泄漏报警",
          function: "监测二氧化碳气体",
          remarks: "处理相关",
          responsible: "张三",
        },
        {
          monitorContent: "传感器湿度",
          description: "湿度过低",
          function: "监测湿度",
          remarks: "处理相关",
          responsible: "张三",
        },
        {
          monitorContent: "气体泄漏",
          description: "氧气泄漏报警",
          function: "监测氧气气体",
          remarks: "处理相关",
          responsible: "张三",
        },

      ],
      value: "",
      input4: "",
      options: [],
      showdh: true,
      showdh1: false,
    };
  },
  methods: {
    changetit(index) {
      this.titactive = index
      this.isshowwhat = !index
      if (index == 1) {
        this.showdh = false;
        this.showdh1 = true;
        this.noAnimation = true;
      } else {
        this.showdh = true;
        this.showdh1 = false;
        this.noAnimation = false;
      }

    },
    anniu() {
      this.ressss = false;
    },
  },
};
</script>

<style lang="less" scoped>
.botbtn {
  position: fixed;
  top: 978px;
  right: 0;
  width: 200px;
  height: 43px;
  background: #022d56;
  border-radius: 8px;
  z-index: 20;
  display: flex;
  align-items: center;
  justify-content: center;

  .btt {
    color: #fff;
    font-size: 17px;
    line-height: 44px;
    width: 88px;
    text-align: center;
    height: 43px;
    cursor: pointer;
  }

  .btt1 {
    color: rgb(8, 207, 241);
    font-size: 17px;
    line-height: 44px;
    width: 88px;
    text-align: center;
    height: 43px;
    cursor: pointer;
  }
}

.zichanbeijin {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: url("../assets/image/zichanbeijin.png");
  width: 1863px;
  height: 868px;
  z-index: 99999;
  padding-left: 34px;
  padding-right: 22px;
  padding-top: 21px;


  .title {
    margin-bottom: 19px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-family: Source Han Sans SC;
    font-weight: bold;
    font-size: 20px;
    color: #ffffff;
  }

  .img1 {
    cursor: pointer;
    width: 15px;
    height: 15px;
  }

  hr {
    margin-bottom: 17px;
    border: none;
    border-top: 2px solid #466873;
    /* 设置边框颜色为红色 */
  }

  .titlecontent {
    width: 100%;
    display: flex;
    align-items: center;
    margin-bottom: 18px;

    .xuan {
      margin-right: 25px;
      width: 230px;
      height: 37px;
      color: #ffffff;

      ::v-deep .el-select__wrapper {
        background-color: rgba(28, 41, 50, 0.2);
        box-sizing: none;
        box-shadow: 0 0 0 1px #608e9a inset;
      }

      ::v-deep .el-input__wrapper {
        background-color: rgba(28, 41, 50, 0.2);
        box-sizing: none;
        box-shadow: 0 0 0 1px #608e9a inset;
      }
    }

    .xiang {
      width: 252px;
      height: 37px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .item {
        cursor: pointer;
        gap: 3px;
        width: 77px;
        height: 37px;
        border: 1px solid #537b86;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: Source Han Sans SC;
        font-weight: 400;
        font-size: 15px;
        color: #3cccf9;
        border-radius: 5px;
      }

      .item1 {
        cursor: pointer;
        border: 1px solid #d28e3c;
      }
    }
  }

  .table-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    color: #fff;
  }

  .table-body {
    font-size: 14px;
  }

  .table-row {
    color: #fff;
    display: flex;
    width: 100%;
    background-color: #1c2932;
    border: 1px solid #56808d;
    margin-bottom: -1px;
  }

  .table-header,
  .table-rowtitle {
    border: 1px solid #56808d;
    color: #fff;
    display: flex;
    width: 100%;
    background-color: #1c4064;
    margin-bottom: -1px;
    font-size: 16px;
  }

  .table-cell {

    flex: 1;
    padding: 10px;
    border-bottom: 1px solid #ccc;
    text-align: center;
  }

  .table-header .table-cell {
    font-weight: bold;
  }

  .table-row:nth-child(even) {
    color: #fff;
    display: flex;
    width: 100%;
    background-color: #1c4064;
    border: 1px solid #56808d;
    margin-bottom: -1px;

  }

  .fenye {
    margin-top: 23px;
    display: flex;
    flex-direction: row-reverse;

    ::v-deep .el-pager li {
      background: url("../assets/image/fenyebox.png");
      background-size: 100% 100%;
      color: #fff;
      margin: 3px !important;
      font-size: 14px;
    }
  }
}

::v-deep .el-pagination button {
  background: rgba(28, 41, 50, 0) !important;
  color: #fff !important;
}

::v-deep .el-pagination__jump {
  color: #fff !important;
}

::v-deep .el-pagination__total {
  color: #fff !important;
  margin-left: 10px !important;
}

::v-deep .el-select__wrapper {
  background-color: rgba(151, 173, 83, 0);
  box-sizing: none;
  box-shadow: 0 0 0 1px#694e31 inset;
  margin-right: 10px;
}

::v-deep .el-input__wrapper {
  background-color: rgba(28, 41, 50, 0);
  box-sizing: none;
  box-shadow: 0 0 0 1px #be8b34 inset;
}
</style>