// token相关的工具函数

/**
 * 检查token是否有效
 * @returns {boolean} token是否有效
 */
export function isTokenValid() {
  const token = localStorage.getItem('token');
  const expiration = localStorage.getItem('tokenExpiration');
  
  if (!token || !expiration) {
    return false;
  }

  // 检查是否过期
  const currentTime = new Date().getTime();
  return currentTime < parseInt(expiration);
}

/**
 * 清除token相关信息
 */
export function clearToken() {
  localStorage.removeItem('token');
  localStorage.removeItem('tokenExpiration');
}

/**
 * 获取token
 */
export function getToken() {
  return localStorage.getItem('token');
} 