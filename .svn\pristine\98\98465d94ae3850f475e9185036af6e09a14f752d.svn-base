import http from '@/utils/request'

// 基础配置
export const baseConfigs = (data) =>
  http.get("/system/dict/data/type/base_configs", data);

// 获取公钥
export const publicKey = (data) => http.get("/publicKey", data);

// 获取验证码
export const captchaImage = (data) => http.get("/captchaImage", data);

// 登录
export const apiLogin = (quest) => {
  return http.post("/apiLogin", null, quest);
};

// 设备相关
export const deviceapi = (data) => {
  return http.get("/device/api/resourceList", data);
};

export const resourceDeviceList = (data) => {
  return http.get("/device/api/resourceDeviceList", data);
};