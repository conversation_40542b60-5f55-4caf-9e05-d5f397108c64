<template>
  <div class="echart" ref="echart"></div>
</template>
    
<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  props: ["echartDatas"],
  data() {
    return {};
  },

  mounted() {
    this.init();
  },
  watch: {
    echartDatas: {
      handler(newData) {
        // 数据变化时更新 ECharts
        this.init();
      },
      deep: true // 深度监听对象内部数据
    }

  },
  methods: {
    // 获取最近7天的日期
    getLast7Days() {
      const dates = [];
      const today = new Date();

      // 往前推7天，包含今天
      for (let i = 6; i >= 0; i--) {
        const date = new Date(today);
        date.setDate(today.getDate() - i);

        // 格式化日期为 MM.DD
        const formattedDate = `${(date.getMonth() + 1).toString().padStart(2, '0')}.${date.getDate().toString().padStart(2, '0')}`;
        dates.push(formattedDate);
      }

      return dates;
    },
    initData() { },
    init() {
      const myChart = echarts.init(this.$refs.echart);
      const last7Days = this.getLast7Days(); // 获取最近7天的日期

      let yAxisData = last7Days
      // let data1 = [0.3, 0.4, 0.5, 0.35, 0.4, 0.3, 0.31, 0.35];
      // let data2 = [500, 520, 510, 512, 535, 521, 542, 530];
      let data1 = this.echartDatas.data1
      let data2 = this.echartDatas.data2
      const option = {
        title: {
          // text: "宽度(m/m)",
          textStyle: {
            color: "#fff",
            fontSize: 16,
          },
        },

        tooltip: {
          show: true,
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        legend: {
          data: ["面风速", "排风量"],
          top: 4,
          right: "14%",
          itemWidth: 13,
          itemHeight: 13,
          textStyle: {
            color: "#fff",
            fontSize: 18,
          },
        },

        grid: [
          {
            show: false,
            left: "10%",
            top: "10%",
            width: "40%",
            containLabel: true,
            bottom: 20,
          },
          {
            show: false,
            left: "4%",
            top: "10%",
            bottom: 32,
            width: "0%",
          },
          {
            show: false,
            left: "50%",
            top: "10%",
            bottom: 20,
            containLabel: true,
            width: "40%",
          },
        ],
        xAxis: [
          {
            type: "value",
            inverse: true,
            axisLabel: {
              show: true,
              color: "#fff",
              margin: 0,
              fontSize: 16,
              formatter: function (value) {
                return value === 0 ? "0" : value;
              },
            },
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
            },
          },
          {
            gridIndex: 1,
            show: true,
            axisLabel: {
              color: "#fff",
              margin: 0,
              fontSize: 16,
              formatter: function (value) {
                return value === 0 ? "0" : value;
              },
            },
            splitLine: {
              lineStyle: {
                color: "#fff",
                type: "dashed",
              },
            },
          },
          {
            gridIndex: 2,
            type: "value",
            axisLabel: {
              show: true,
              color: "#fff",
              margin: 0,
              fontSize: 16,
              formatter: function (value) {
                return value === 0 ? "0" : value;
              },
            },
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
            },
          },
        ],
        yAxis: [
          {
            type: "category",
            inverse: false,
            position: "right",
            axisLabel: {
              show: false,
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#fff",
                fontSize: 16,
              },
            },
            axisTick: {
              show: false,
            },
            data: yAxisData,
          },
          {
            type: "category",
            inverse: false,
            gridIndex: 1,
            position: "left",
            axisLabel: {
              align: "left",
              padding: [2, 0, 0, 0],
              fontSize: 16,
              fontWeight: 500,
              color: `#fff`,
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: "#fff",
              },
            },
            axisTick: {
              show: false,
            },
            data: yAxisData,
          },
          {
            type: "category",
            inverse: false,
            gridIndex: 2,
            position: "left",
            axisLabel: {
              show: false,
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#979797",
              },
            },
            axisTick: {
              show: false,
            },
            data: yAxisData,
          },
        ],
        series: [
          {
            type: "bar",
            barWidth: 12,
            name: "面风速",
            label: {
              normal: {
                show: false,
              },
            },
            itemStyle: {
              normal: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 1,
                  y2: 0,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(227, 86, 37, 0.78)",
                    },
                    {
                      offset: 1,
                      color: "rgba(227, 86, 37, 0.1)",
                    },
                  ],
                  globalCoord: false,
                },
              },
            },
            data: data1,
          },
          {
            type: "bar",
            barWidth: 12,
            xAxisIndex: 2,
            yAxisIndex: 2,
            name: "排风量",
            label: {
              normal: {
                show: false,
              },
            },
            itemStyle: {
              normal: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 1,
                  y2: 0,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(126,253,225, 0.1)",
                    },
                    {
                      offset: 1,
                      color: "rgba(126,253,225, 0.78)",
                    },
                  ],
                  globalCoord: false,
                },
              },
            },
            data: data2,
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>
    
<style lang="less" scoped>
.echart {
  width: 100%;
  height: 339px;
}

@media (max-height: 1080px) {
  .echart {
    width: 100%;
    height: 339px !important;
  }
}
</style>