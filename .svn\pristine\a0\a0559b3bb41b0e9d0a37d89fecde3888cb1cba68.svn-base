<template>
  <div class="echart" ref="echart"></div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  props: {
    chartData: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  methods: {
    init() {
      const myChart = echarts.init(this.$refs.echart);
      let data = this.chartData
        ? this.chartData
        : {
            title: ["已领用", "未领用"],
            xAxisdata: ["20", "21", "22", "23", "25", "24"],
            yAxisdata1: [4, 7, 5, 9, 6, 5],
            yAxisdata2: [4, 7, 5, 9, 6, 5],
          };

      const option = {
        tooltip: {
          trigger: "axis",
          axisPointer: { type: "shadow" },
          textStyle: { color: "#fff" }, // White font for tooltip
        },
        legend: {
          data: ["剩余", "总量"],
          right: "20",
          top: "20",
          textStyle: { color: "#fff" }, // White font for legend
        },
        xAxis: {
          type: "value",
          splitLine: { show: false },
          axisTick: { show: false },
          axisLabel: {
            textStyle: { color: "#fff", fontSize: 12 }, // White font for xAxis labels
          },
          axisLine: { lineStyle: { color: "#ddd" } },
        },
        yAxis: {
          type: "category",
          splitLine: { show: false },
          axisTick: { show: false },
          data: ["一月", "二月", "三月", "四月", "五月", "六月", "七月"],
          axisLabel: {
            textStyle: { color: "#fff", fontSize: 12 }, // White font for yAxis labels
          },
          axisLine: { lineStyle: { color: "#fff" } },
        },
        series: [
          {
            name: "剩余",
            type: "bar",
            data: [80, 80, 97, 53, 95, 70, 88],
            itemStyle: {
              emphasis: { barBorderRadius: 15 },
              normal: {
                barBorderRadius: 15,
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  { offset: 0, color: "#1CC6FC" },
                  { offset: 1, color: "#2A71FD" },
                ]),
              },
            },
            label: {
              normal: {
                show: true,
                formatter: "{c}",
                position: "insideRight",
                offset: [-2, 1.5],
                textStyle: { color: "#fff", fontSize: 10 }, // White font for series labels
              },
            },
          },
          {
            name: "总量",
            type: "bar",
            barGap: "-100%",
            itemStyle: {
              color: "rgba(0,0,0,0)",
              borderColor: "#2A71FD",
              emphasis: { barBorderRadius: 15 },
              normal: {
                barBorderRadius: 15,
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  { offset: 0, color: "#f2f2f2" },
                  { offset: 1, color: "#f2f2f2" },
                ]),
              },
            },
            label: {
              normal: {
                show: true,
                formatter: "{c}",
                position: "right",
                textStyle: { color: "#fff", fontSize: 12 }, // White font for secondary labels
              },
            },
            z: -10,
            data: [100, 100, 100, 100, 100, 100, 100],
          },
        ],
        barCategoryGap: "40%",
      };

      myChart.setOption(option);
    },
  },
};
</script>

<style lang="less" scoped>
.echart {
  width: 450px;
  height: 330px;
}
</style>
