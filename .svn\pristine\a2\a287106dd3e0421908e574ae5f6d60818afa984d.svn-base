const path = require('path')

module.exports = {
    pages: {
        index: {
            entry: 'src/main.js', // 入口文件
            title: '光明国际中医药智慧港'
        }
    },
    productionSourceMap: false,
    
    // publicPath: './',
     publicPath: process.env.NODE_ENV === "production" ? "././" : "",
    // 它支持webPack-dev-server的所有选项
    devServer: {
        host: "localhost", //也可以直接写IP地址这样方便真机测试************
        // host: "************", //也可以直接写IP地址这样方便真机测试************
        port: 8080, // 端口号
        https: false, // https:{type:Boolean}
        open: true, //配置自动启动浏览器
        hot: true, //自动保存
        proxy: {
            '/lims/api': {
              target: 'http://yiqi.tju.edu.cn/',
              changeOrigin: true,
              pathRewrite: { '^/lims/api': '/lims/api' }
            }
          }
        // proxy: {
        //     '/api': {
        //       target: 'http://luxsanapi.wocyd.com',
        //       changeOrigin: true,
        //       pathRewrite: { '^/api': '' },
        //       secure: false,
        //       logLevel: 'debug' // Optional: Enables additional logging
        //     }
        //   }
        // }
    },
    // pluginOptions: {
    //     'style-resources-loader': {
    //         preProcessor: 'less',
    //         patterns: [path.resolve(__dirname, './src/assets/styles/base.less')], // 可以设置多个
    //     },
    // },

}
