<template>
  <div class="echart" ref="echart"></div>
</template>
  
<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  methods: {
    initData() {},
    init() {
      const myChart = echarts.init(this.$refs.echart);

      app.title = "未优化用电量";

      var data1 = [20, 30, 20, 30, 20, 30, 20];
      var data2 = [9, 30, 9, 60, 70, 20, 59];
      var data3 = [20, 30, 20, 30, 20, 30, 20];
      var data4 = [9, 30, 9, 60, 70, 20, 59];
      var datacity = ["B1", "B2", "B3", "B4", "W1", "W2"];
      const option = {
        title: {
          text: "kwh",
          left: "20px",
          top: "14",
          textStyle: {
            color: "#fff",
            fontSize: 14,
            fontWeight: "400",
          },
        },
        color: [
          "#F2F9A1",
          "#EAA245",
          "#70EAFD",
          "#52B383",
          "#50A8D6",
          "#3F79FA",
          "#EB46FB",
        ],
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            crossStyle: {
              color: "#999",
            },
            lineStyle: {
              type: "dashed",
            },
          },
        },
        grid: {
          //图表的位置
          top: "20%",
          left: "2%",
          right: "2%",
          bottom: "5%",
          containLabel: true,
        },

        legend: {
          data: ["B1", "B2", "B3", "B4", "W1", "W2"],
          top: "8%",
          right: "40px",
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            color: "#fff",
          },
        },
        xAxis: {
          axisLabel: {
            interval: 0,
            show: true,
            splitNumber: 15,
            textStyle: {
              fontSize: 10,
              color: "#fff",
            },
          },
          type: "category",
          data: [
            "3/24",
            "3/25",
            "3/26",
            "3/27",
            "3/28",
            "3/29",
            "3/30",
            "3/31",
            "4/01",
          ],
          splitLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
        },
        yAxis: {
          type: "value",
          // max: max_value>=100? max_value + 100: max_value+10,
          // max: max_value > 100 ? max_value * 2 : max_value + 10,
          // interval: 10,
          // nameLocation: "center",
          axisLabel: {
            color: "#fff",
            textStyle: {
              fontSize: 12,
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "#F3F4F4",
            },
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
        },
        series: [
          {
            name: "B1",
            type: "line",
            z: 3,
            showSymbol: false,
            smooth: true, // 同样设置为true
            lineStyle: {
              width: 1,
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "#F2F9A1", // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: "#F2F9A1", // 100% 处的颜色
                  },
                ],
              },
              shadowBlur: 4,
              shadowColor: "rgba(69,126,247,.2)",
              shadowOffsetY: 4,
            },

            data: [
              40, 215, 441, 486, 336, 16, 46, 34, 275, 164, 55, 234, 105, 100,
            ],
          },
          {
            name: "B2",
            type: "line",
            z: 3,
            showSymbol: false,
            smooth: true, // 同样设置为true
            lineStyle: {
              width: 1,
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "#EAA245", // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: "#EAA245", // 100% 处的颜色
                  },
                ],
              },
              shadowBlur: 4,
              shadowColor: "rgba(69,126,247,.2)",
              shadowOffsetY: 4,
            },

            data: [
              69, 419, 47, 132, 454, 298, 354, 312, 446, 451, 67, 143, 229, 166,
            ],
          },
          {
            name: "B3",
            type: "line",
            z: 3,
            showSymbol: false,
            smooth: true, // 同样设置为true
            lineStyle: {
              width: 1,
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "#70EAFD", // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: "#70EAFD", // 100% 处的颜色
                  },
                ],
              },
              shadowBlur: 4,
              shadowColor: "rgba(69,126,247,.2)",
              shadowOffsetY: 4,
            },

            data: [
              227, 268, 347, 477, 148, 252, 263, 429, 484, 431, 388, 303, 97,
              230,
            ],
          },
          {
            name: "B4",
            type: "line",
            z: 3,
            showSymbol: false,
            smooth: true, // 同样设置为true
            lineStyle: {
              width: 1,
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "#52B383", // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: "#52B383", // 100% 处的颜色
                  },
                ],
              },
              shadowBlur: 4,
              shadowColor: "rgba(69,126,247,.2)",
              shadowOffsetY: 4,
            },

            data: [
              480, 405, 221, 258, 68, 254, 136, 485, 69, 373, 121, 396, 81, 429,
            ],
          },
          {
            name: "W1",
            type: "line",
            z: 3,
            showSymbol: false,
            smooth: true, // 同样设置为true
            lineStyle: {
              width: 1,
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "#50A8D6", // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: "#50A8D6", // 100% 处的颜色
                  },
                ],
              },
              shadowBlur: 4,
              shadowColor: "rgba(69,126,247,.2)",
              shadowOffsetY: 4,
            },

            data: [
              74, 237, 233, 236, 325, 172, 221, 360, 365, 122, 235, 307, 239,
              41,
            ],
          },
          {
            name: "W2",
            type: "line",
            z: 3,
            showSymbol: false,
            smooth: true, // 同样设置为true
            lineStyle: {
              width: 1,
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "#3F79FA", // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: "#3F79FA", // 100% 处的颜色
                  },
                ],
              },
              shadowBlur: 4,
              shadowColor: "rgba(69,126,247,.2)",
              shadowOffsetY: 4,
            },

            data: [
              308, 380, 135, 286, 353, 24, 93, 134, 482, 105, 327, 162, 418,
              433,
            ],
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>
  
<style lang="less" scoped>
.echart {
  width:680px;
  height: 380px;
}


</style>