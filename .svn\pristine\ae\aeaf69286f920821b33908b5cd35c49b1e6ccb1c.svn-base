<template>
    <div class="echart" ref="echart"></div>
</template>

<script>
import * as echarts from "echarts";

export default {
    name: "IoTequip",
    props: {
        chartData: {
            type: Object,
            required: true,
        },
    },
    data() {
        return {};
    },

    mounted() {
        this.init();
    },

    methods: {
        init() {
            const myChart = echarts.init(this.$refs.echart);
            var chart_title = '行业';
            //图表X轴数据 每个学院
            var Xdata = ["医学工程与转化医学研究院", " 精密仪器与光电子工程学院", "化工学院", "天津化学化工协同创新中心(临)", " 分子+研究院", "材料科学与工程学院", "地球系统科学学院", "分子聚集态科学研究院",
                "国家储能技术产教融合创新平台",
                "环境科学与工程学院",
                "生命科学学院",
                "应急医学研究院",
                "药物科学与技术学院",
                "理学院",
                "机械工程学院",
                "电气自动化与信息工程学院",
                "天津纳米颗粒与纳米系统国际研究中心",
                "微电子学院",
                "石化中心",
                "分析中心",
                "资产处",
                "分析中心1",
                "海洋科学与技术学院",
                "建筑工程学院"];

            //图表项目  //订单状态区分普通 受管制 总数
            var fp_key = ['普试剂', '管制类试剂',];

            //图表项目数据
            var fp_nan = { // 普试剂
                9: [563, 396, 388, 108, 325, 121, 181, 293, 201, 310, 564, 397, 389, 109, 326, 122, 182, 294, 202, 311, 565, 398, 390, 110],
                10: [301, 351, 301, 251, 201, 151, 101, 151, 201, 251, 564, 397, 389, 109, 326, 123, 183, 295, 203, 312, 566, 399, 391, 111]
            };

            var fp_nv = { // 管制类试剂
                9: [570, 410, 390, 115, 330, 125, 185, 300, 210, 315, 570, 410, 390, 115, 330, 125, 185, 300, 210, 315, 570, 410, 390, 115],
                10: [305, 355, 305, 255, 205, 155, 105, 155, 205, 255, 570, 410, 390, 115, 330, 126, 186, 301, 211, 316, 572, 412, 392, 117]
            };

            var fp_val = {//总数

                9: [563, 396, 388, 108, 325, 120, 180, 292, 200, 309,],
                10: [300, 350, 300, 250, 200, 150, 100, 150, 200, 250],

            };
            //图表月份
            var timeLineData = [9, 10];

            var fp_coler = ['#4A65EA', '#91CC75', '#4AC9E9', '#258df6', '#f5814b', '#6fce7d'];
            const option = {
                baseOption: {

                    timeline: {
                        show: true,
                        axisType: 'category',
                        tooltip: {
                            show: true,
                            formatter: function (params) {
                                return params.name + '月份数据统计';
                            }
                        },
                        autoPlay: true,
                        currentIndex: 0,
                        playInterval: 5000,
                        label: {
                            normal: {
                                show: true,
                                color: '#fff',
                                interval: 'auto',
                                formatter: function (params) {
                                    return params
                                }
                            },
                        },
                        lineStyle: {
                            show: true,
                            color: '#fff'
                        },
                        itemStyle: {
                            show: true,
                            color: '#fff'
                        },
                        //   checkpointStyle: {
                        //         show: true,
                        //         color: '#20dbfd'
                        //     },
                        controlStyle: {
                            show: true,
                            color: '#fff',
                            borderColor: '#ffff'
                        },
                        left: "0",
                        right: "0",
                        bottom: '0',
                        padding: [15, 0],
                        data: timeLineData,
                    },
                    title: {
                        top: 1,
                        left: '3%',
                        textStyle: {
                            color: '#fff',
                            fontSize: 20,
                        },
                    },
                    legend: {
                        data: fp_key,
                        top: 1,
                        right: '5%',
                        textStyle: {
                            color: '#fff',
                            fontSize: 15
                        },
                    },
                    tooltip: {
                        show: true,
                        trigger: 'axis',
                        //formatter: '{b}<br/>{a}: {c}人',
                        axisPointer: {
                            type: 'shadow',
                        }
                    },
                    grid: [{
                        show: false,
                        left: "2%",
                        top: 40,
                        bottom: 60,
                        containLabel: true,
                        width: '43%',
                    }, {
                        show: false,
                        left: "9%",
                        top: 60,
                        bottom: 60,
                        width: '14%',
                    }, {
                        show: false,
                        left: "16%",
                        top: 29,
                        bottom: 60,
                        containLabel: true,
                        width: '80%',
                    },],

                    xAxis: [
                        {
                            type: 'value',
                            triggerEvent: true,
                            inverse: true,
                            axisLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                            position: 'top',
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    color: '#fff',
                                    fontSize: 20,
                                },
                            },
                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: '#ffff',
                                    width: 1,
                                    type: 'solid',
                                },
                            },
                        },
                        {
                            gridIndex: 1,
                            show: false,
                        },
                        {
                            gridIndex: 2,
                            type: 'value',
                            axisLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                            position: 'top',
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    color: '#ffff',
                                    fontSize: 20,
                                },
                            },
                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: '#ffff',
                                    width: 1,
                                    type: 'solid',
                                },
                            },
                        }
                    ],
                    yAxis: [{
                        type: 'category',
                        inverse: true,
                        position: 'right',
                        axisLine: {
                            show: false
                        },
                        axisTick: {
                            show: false
                        },
                        axisLabel: {
                            show: false,
                            margin: 8,
                            textStyle: {
                                color: '#fff',
                                fontSize: 20,
                            },

                        },
                        data: Xdata,
                    }, {
                        gridIndex: 1,
                        type: 'category',
                        inverse: true,
                        position: 'left',
                        axisLine: {
                            show: false
                        },
                        axisTick: {
                            show: false
                        },
                        axisLabel: {
                            formatter: function (value) {
                                // 每5个字符换行
                                var lineLength = 10
                                var regex = new RegExp('.{1,' + lineLength + '}', 'g');
                                return value.match(regex).join('\n');
                            },
                            show: true,
                            textStyle: {
                                color: '#ffff',
                                fontSize: 14
                            },

                        },
                        data: Xdata.map(function (value) {
                            return {
                                value: value,
                                textStyle: {
                                    align: 'center',
                                }
                            }
                        }),
                    }, {
                        gridIndex: 2,
                        type: 'category',
                        inverse: true,
                        position: 'left',
                        axisLine: {
                            show: false
                        },
                        axisTick: {
                            show: false
                        },
                        axisLabel: {
                            show: false,
                            textStyle: {
                                color: '#ffff',
                                fontSize: 20,
                            },

                        },
                        data: Xdata,
                    },],
                    series: [],

                },
                options: [],
            };
            for (var i = 0; i < timeLineData.length; i++) {
                // option.baseOption.timeline.data.push(timeLineData[i]);
                option.options.push({
                    title: {
                        text: timeLineData[i] + '月份统计',
                    },
                    series: [
                        // {
                        //     name: fp_key[0],
                        //     type: 'bar',
                        //     stack: 'one',
                        //     barGap: 15,
                        //     barWidth: 15,
                        //     label: {
                        //         normal: {
                        //             show: true,
                        //             position: 'inside',
                        //             textStyle: {
                        //                 color: '#fff',

                        //                 fontSize: 20,
                        //             },
                        //         },
                        //         emphasis: {
                        //             show: true,
                        //             position: 'inside',
                        //             offset: [0, 0],
                        //             textStyle: {
                        //                 color: '#fff',
                        //                 fontSize: 20,
                        //             },
                        //         },
                        //     },
                        //     itemStyle: {
                        //         normal: {
                        //             color: function (params) {
                        //                 return fp_coler[0]
                        //             },
                        //             opacity: 1,

                        //         },
                        //         emphasis: {
                        //             opacity: 1,
                        //         },
                        //     },
                        //     data: fp_nan[timeLineData[i]],
                        // },
                        // {
                        //     name: fp_key[1],
                        //     type: 'bar',
                        //     stack: 'one',
                        //     barGap: 15,
                        //     barWidth: 15,
                        //     label: {
                        //         normal: {
                        //             show: true,
                        //             position: 'inside',
                        //             textStyle: {
                        //                 color: '#fff',

                        //                 fontSize: 20,
                        //             },
                        //         },
                        //         emphasis: {
                        //             show: true,
                        //             position: 'inside',
                        //             offset: [0, 0],
                        //             textStyle: {
                        //                 color: '#fff',
                        //                 fontSize: 20,
                        //             },
                        //         },
                        //     },
                        //     itemStyle: {
                        //         normal: {
                        //             color: function (params) {
                        //                 return fp_coler[1]
                        //             },
                        //             opacity: 1,

                        //         },
                        //         emphasis: {
                        //             opacity: 1,
                        //         },
                        //     },
                        //     data: fp_nv[timeLineData[i]],
                        // },
                        // {
                        //     name: fp_key[2],
                        //     stack: 'one',
                        //     type: 'bar',
                        //     barGap: 15,
                        //     barWidth: 15,
                        //     label: {
                        //         normal: {
                        //             show: true,
                        //             position: 'inside',
                        //             textStyle: {
                        //                 color: '#fff',

                        //                 fontSize: 20,
                        //             },
                        //         },
                        //         emphasis: {
                        //             show: true,
                        //             position: 'inside',
                        //             offset: [0, 0],
                        //             textStyle: {
                        //                 color: '#fff',
                        //                 fontSize: 20,
                        //             },
                        //         },
                        //     },
                        //     itemStyle: {
                        //         normal: {
                        //             color: function (params) {
                        //                 return fp_coler[2]
                        //             },
                        //             opacity: 1,

                        //         },
                        //         emphasis: {
                        //             opacity: 1,
                        //         },
                        //     },
                        //     data: fp_val[timeLineData[i]],
                        // },
                        {
                            name: fp_key[0],
                            stack: 'right',
                            type: 'bar',
                            barGap: 15,
                            barWidth: 15,
                            xAxisIndex: 2,
                            yAxisIndex: 2,
                            label: {
                                normal: {
                                    show: true,
                                    position: 'inside',
                                    textStyle: {
                                        color: '#fff',

                                        fontSize: 20,
                                    },
                                },
                                emphasis: {
                                    show: true,
                                    position: 'inside',
                                    offset: [0, 0],
                                    textStyle: {
                                        color: '#fff',
                                        fontSize: 20,
                                    },
                                },
                            },
                            itemStyle: {
                                normal: {
                                    color: function (params) {
                                        return fp_coler[0]
                                    },
                                    opacity: 1,

                                },
                                emphasis: {
                                    opacity: 1,
                                },
                            },

                            data: fp_nan[timeLineData[i]],
                        },
                        {
                            name: fp_key[1],
                            stack: 'right',
                            type: 'bar',
                            barGap: 15,
                            barWidth: 15,
                            xAxisIndex: 2,
                            yAxisIndex: 2,
                            label: {
                                normal: {
                                    show: true,
                                    position: 'inside',
                                    textStyle: {
                                        color: '#fff',
                                        fontSize: 20,
                                    },
                                },
                                emphasis: {
                                    show: true,
                                    position: 'inside',
                                    offset: [0, 0],
                                    textStyle: {
                                        color: '#fff',
                                        fontSize: 20,
                                    },
                                },
                            },
                            itemStyle: {
                                normal: {
                                    color: function (params) {
                                        return fp_coler[1]
                                    },
                                    opacity: 1,

                                },
                                emphasis: {
                                    opacity: 1,
                                },
                            },
                            data: fp_nv[timeLineData[i]],
                        },
                        // {
                        //     name: fp_key[2],
                        //     stack: 'right',
                        //     type: 'bar',
                        //     barGap: 15,
                        //     barWidth: 15,
                        //     xAxisIndex: 2,
                        //     yAxisIndex: 2,
                        //     label: {
                        //         normal: {
                        //             show: true,
                        //             position: 'inside',
                        //             textStyle: {
                        //                 color: '#fff',
                        //                 fontSize: 20,
                        //             },
                        //         },
                        //         emphasis: {
                        //             show: true,
                        //             position: 'inside',
                        //             offset: [0, 0],
                        //             textStyle: {
                        //                 color: '#fff',
                        //                 fontSize: 20,
                        //             },
                        //         },
                        //     },
                        //     itemStyle: {
                        //         normal: {
                        //             color: function (params) {
                        //                 return fp_coler[2]
                        //             },
                        //             opacity: 1,

                        //         },
                        //         emphasis: {
                        //             opacity: 1,
                        //         },
                        //     },
                        //     data: fp_val[timeLineData[i]],
                        // },
                    ]
                });
            }



            myChart.setOption(option);
        },
    },
};
</script>

<style lang="less" scoped>
.echart {
    width: 1095px;
    height: 800px;
}
</style>
