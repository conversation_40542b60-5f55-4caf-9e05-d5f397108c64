<template>
  <div class="echart" ref="echart"></div>
</template>
  
<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  props: ['chartData'],
  data() {
    return {};
  },

  mounted() {
    this.init();
  },
  watch: {
    chartData: {
      handler(newData) {
        // 数据变化时更新 ECharts
        this.init();
      },
      deep: true // 深度监听对象内部数据
    }

  },
  methods: {
    initData() { },
    init() {
      const myChart = echarts.init(this.$refs.echart);

      var colorList = ["#228c38", "#1a57b2", "#b04b07", "#af8108"];
      var colorListSub = [
        "rgba(35,143,56,.5)",
        "rgba(26,87,178,.5)",
        "rgba(176,75,7,.5)",
        "rgba(175,129,8,.5)",
      ];
      let dashedPic =
        "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAM8AAAAOBAMAAAB6G1V9AAAAD1BMVEX////Kysrk5OTj4+TJycoJ0iFPAAAAG0lEQVQ4y2MYBaNgGAMTQQVFOiABhlEwCugOAMqzCykGOeENAAAAAElFTkSuQmCC";
      let color = ["#00e473", "#009DFF", "yellow", "#009DFF"];
      let chartData = this.chartData
        // {
        //   name: "离线",
        //   value: 5037,
        //   unit: "个",
        // },
        ;
      let arrName = [];
      let arrValue = [];
      let sum = 0;
      let pieSeries = [],
        lineYAxis = [];

      // 数据处理
      chartData.forEach((v, i) => {
        arrName.push(v.name);
        arrValue.push(v.value);
        sum = sum + v.value;
      });

      // 图表option整理
      chartData.forEach((v, i) => {
        pieSeries.push({
          name: "设备",
          type: "pie",
          clockWise: false,
          hoverAnimation: false,
          radius: [65 - i * 15 + "%", 52 - i * 15 + "%"],
          center: ["45%", "50%"],
          label: {
            show: false,
          },
          data: [
            {
              value: v.value,
              name: v.name,
            },
            {
              value: sum - v.value,
              name: "",
              itemStyle: {
                color: "rgba(0,0,0,0)",
              },
            },
          ],
        });
        pieSeries.push({
          name: "",
          type: "pie",
          silent: true,
          z: 1,
          clockWise: false, //顺时加载
          hoverAnimation: false, //鼠标移入变大
          radius: [65 - i * 15 + "%", 52 - i * 15 + "%"],
          center: ["45%", "50%"],
          label: {
            show: false,
          },
          data: [
            {
              value: 7.5,
              itemStyle: {
                color: "#E3F0FF",
              },
            },
            {
              value: 2.5,
              name: "",
              itemStyle: {
                color: "rgba(0,0,0,0)",
              },
            },
          ],
        });
        // v.percent = ((v.value / sum) * 100).toFixed(1) + "%";
        v.percent = v.value
        lineYAxis.push({
          value: i,
          textStyle: {
            rich: {
              circle: {
                color: color[i],
                padding: [0, 5],
              },
            },
          },
        });
      });

      const option = {
        color: color,
        grid: {
          top: "15%",
          bottom: "50%",
          left: "45%",
          right: "30%",
          containLabel: false,
        },
        yAxis: [
          {
            type: "category",
            inverse: true,
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              formatter: function (params) {
                let item = chartData[params];
                console.log(item);
                return (
                  "{circle|●}{name|" +
                  item.name +
                  "}{bd||}{percent|" +
                  item.percent +
                  "}{unit|" +
                  '台' +
                  "}"
                );
              },

              interval: 0,
              inside: true,
              textStyle: {
                color: "#fff",
                fontSize: 20,
                rich: {

                  line: {
                    width: 170,
                    height: 10,
                    backgroundColor: { image: dashedPic },
                  },
                  name: {
                    color: "#fff",
                    fontSize: 20,
                  },
                  bd: {
                    color: "#fff",
                    padding: [0, 5],
                    fontSize: 14,
                  },
                  percent: {
                    color: "#fff",
                    fontSize: 25,
                  },
                  value: {
                    color: "#fff",
                    fontSize: 30,

                    padding: [0, 0, 0, 1],
                  },
                  unit: {
                    fontSize: 20,
                  },
                },
              },
              show: true,
            },
            data: lineYAxis,
          },
        ],
        xAxis: [
          {
            show: false,
          },
        ],
        series: pieSeries,
      };

      myChart.setOption(option);
    },
  },
};
</script>
  
<style lang="less" scoped>
.echart {
  width: 420px;
  height: 330px;
}
</style>