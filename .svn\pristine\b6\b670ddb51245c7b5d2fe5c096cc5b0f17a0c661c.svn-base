<template>
  <div>
    <!-- <component :is="componentTag" @fatherMethoddd="fatherMethoddd"></component> -->

    <tedai
      :ids="ids"
      :selectedItem="selectedItem"
      class="sbdetails"
      :zengtiimg="zengtiimg"
      v-if="isshow"
      @hidedetails="hidedetails"
    ></tedai>
    <div class="container">
      <div
        class="left-panel"
        :class="{
          'left-panel-active': showdh,
          'no-animation': noAnimation,
          'left-panel-active1': showdh1,
        }"
      >
        <Title2 class="ltitle1" tit="报警列表">
          <div class="box">
            <div>
              <el-input
                class="el-input"
                v-model="input"
                placeholder="请输入内容"
              ></el-input>
            </div>
            <!-- <div class="xiaobox">
              <img class="siqiu" src="../assets/image/shixinqiu.png" alt="" />
              <div class="shuru">全部设备</div>
            </div> -->
            <div
              class="xiaoboxs"
              v-for="item in data"
              :key="item"
              @click="showdetails(item)"
            >
              <img class="siqiu" src="../assets/image/kongxin.png" alt="" />
              <div class="shuru">{{ item.name }}</div>
            </div>
          </div>
          <!-- shebei12.png -->
        </Title2>
      </div>
      <!-- shebeibgc.png -->
      <!-- 右侧内容 -->
      <!-- 右侧内容 -->

      <div
        class="right-panel"
        :class="{
          'right-panel-active': showdh,
          'no-animation': noAnimation,
          'right-panel-active1': showdh1,
        }"
      >
        <Title3 tit="报警柜详情">
          <div class="box">
            <div class="xiaoboxs" v-for="item in cgqlist" :key="item">
              <img class="siqiu" src="../assets/image/kongxin.png" alt="" />
              <div class="nihaowo">{{ item.name }}</div>
              <div class="shuru">{{ item.value }}</div>
            </div>
          </div>
          <!-- <img class="jk" src="../assets/jiankong.png" alt=""> -->
        </Title3>
      </div>
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import huanxing from "@/components/echarts/huanxing.vue";
import zhexian from "@/components/echarts/zhexian.vue";
import zhexian1 from "@/components/echarts/zhexian1.vue";
import SystemDete from "@/components/echarts/SystemDete.vue";
import echarts1 from "@/components/echarts/bingjifang/echarts3.vue";
import tedai from "@/components/common/tedai.vue";
import shuangxiang from "@/components/echarts/shuangxiang.vue";
// import details from "@/components/common/details.vue";
export default {
  // import引入的组件需要注入到对象中才能使用
  components: {
    tedai,
    huanxing,
    zhexian,
    zhexian1,
    SystemDete,
    echarts1,
    shuangxiang,
  },
  props: ["tabledata", "zengtiimg"],
  //  cgqlistss: [
  //       { name: "湿度范围：", value: "5%RH~100%RH（无结露）" },
  //       { name: "湿度分辨率：", value: "0.04%" },
  //       { name: "IP地址：", value: "±3%RH（20%RH~80%RH）" },
  //       { name: "湿度精度：", value: "-40℃~+125℃" },
  //       { name: "温度分辨率：", value: "0.01℃" },
  //       { name: "温度精度：", value: "±0.5℃（10℃~60℃）" },
  //     ],

  data() {
    // 这里存放数据
    return {
      isshow: false,
      xxxx: false,
      cgqlist: [
        { name: "新风量范围：", value: "200~2000立方米/小时" },
        { name: "新风量精度：", value: "±5%" },
        { name: "二氧化碳浓度监控范围：", value: "0~5000ppm" },
        { name: "二氧化碳浓度报警阈值：", value: "1000ppm" },
        { name: "空气质量监控：", value: "VOC、PM2.5、PM10实时监测" },
        { name: "VOC检测范围：", value: "0~1000ppb" },
        { name: "PM2.5监控范围：", value: "0~500μg/m³" },
        { name: "PM10监控范围：", value: "0~1000μg/m³" },
        { name: "温度适应范围：", value: "-10℃~+50℃" },
        { name: "湿度适应范围：", value: "5%RH~95%RH（无结露）" },
        { name: "温度精度：", value: "±0.5℃" },
        { name: "湿度精度：", value: "±3%RH" },
        {
          name: "新风系统滤网类型：",
          value: "初效过滤器、中效过滤器、HEPA过滤器",
        },
        { name: "滤网更换提醒：", value: "压差监测提醒" },
        { name: "噪声范围：", value: "40~65分贝" },
        { name: "噪声精度：", value: "±2分贝" },
        { name: "风机功率：", value: "100W~1000W" },
        { name: "电源电压：", value: "220V~240V，50Hz" },
        { name: "控制面板类型：", value: "数字显示面板或触摸屏" },
        {
          name: "显示内容：",
          value: "风量、温度、湿度、二氧化碳浓度、VOC浓度",
        },
        { name: "远程监控：", value: "支持Wi-Fi或局域网连接" },
        { name: "新风系统故障报警：", value: "风机故障、滤网堵塞、气流异常" },
        { name: "能耗监控：", value: "实时记录电力消耗" },
        {
          name: "自动控制功能：",
          value: "根据空气质量和人员活动自动调节新风量",
        },
        { name: "紧急切断功能：", value: "支持远程和自动紧急切断" },
        { name: "IP防护等级：", value: "IP44或更高" },
        { name: "适用面积：", value: "50~500平方米" },
      ],
      data: [
        {
          deviceResourceMapId: 9002647,
          xAxis: 0.0,
          yAxis: 0.0,
          zAxis: 0.0,
          otherData: '{"width":10,"height":10}',
          resourceNote: "",
          id: 1611106,
          type: "CGQ11",
          name: "新风量监控",
          brand: "",
          model: "",
          description: "",
          note: "",
          tag: "",
          icon: "",
          thumb: "",
          pictures: "",
          status: "Y",
          workStatus: "",
          warningStatus: "健康",
          communicateStatus: "离线",
          activationTime: "",
          scrapTime: "",
          life: "100%",
          company: "",
          contact: "",
          mobile: "",
          position: "",
          maintenance: "",
          maintenanceSpan: "",
          nameplate: "",
          files: "",
          serviceLife: "",
          ratedPower: "0",
          energyLever: "0",
          createdAt: "2024-08-19 14:33:09",
          resourceId: "",
          baseResourceId: "3338",
          resourceName: "实验室4F",
          resourceCategory: "实验室",
          resourceType: "base",
          resourceOid: "0",
          resourceVal: "/uploads/fx4F.png",
          resourceWidth: "1400",
          resourceHeight: "709",
          resourceOtherData: "",
          deviceDataBase: [
            {
              dmItemId: 1611106,
              dmId: "*********",
              dmItemDataId: "*********",
              dmOid: 0,
              dmName: "二氧化碳浓度",
              dmRelateItemDataId: "",
              dmTag: "status",
              dmNote: "",
              dItemId: "1611106",
              dId: "*********",
              dVal: "",
              dDataType: "string",
              dDataUnit: "",
              dCoefficient: "1.0000",
              dDataMaxVal: "",
              dDataMinVal: "",
              dOtherData: "1:开;0:关",
              dFunc: "read",
              dHasSync: "Y",
              dLocked: "",
              dUpdatedAt: "2024-08-21 10:33:01",
              drId: "",
              drVal: "",
              drDataType: "",
              drDataUnit: "",
              drCoefficient: "",
              drDataMaxVal: "",
              drDataMinVal: "",
              drOtherData: "",
              drFunc: "",
              drHasSync: "",
              drLocked: "",
              drUpdatedAt: "",
            },
          ],
          deviceDataEnv: [],
          deviceDataWarn: [],
          healthPercent: 100,
          warningRuleList: [],
          strategyList: [],
        },
        {
          deviceResourceMapId: 9002646,
          xAxis: 0.0,
          yAxis: 0.0,
          zAxis: 0.0,
          otherData: '{"width":10,"height":10}',
          resourceNote: "",
          id: 1611107,
          type: "CGQ11",
          name: "二氧化碳浓度监控",
          brand: "",
          model: "",
          description: "",
          note: "",
          tag: "",
          icon: "",
          thumb: "",
          pictures: "",
          status: "Y",
          workStatus: "",
          warningStatus: "健康",
          communicateStatus: "离线",
          activationTime: "",
          scrapTime: "",
          life: "100%",
          company: "",
          contact: "",
          mobile: "",
          position: "",
          maintenance: "",
          maintenanceSpan: "",
          nameplate: "",
          files: "",
          serviceLife: "",
          ratedPower: "0",
          energyLever: "0",
          createdAt: "2024-08-19 14:33:09",
          resourceId: "",
          baseResourceId: "3338",
          resourceName: "实验室4F",
          resourceCategory: "实验室",
          resourceType: "base",
          resourceOid: "0",
          resourceVal: "/uploads/fx4F.png",
          resourceWidth: "1400",
          resourceHeight: "709",
          resourceOtherData: "",
          deviceDataBase: [
            {
              dmItemId: 1611107,
              dmId: "*********",
              dmItemDataId: "*********",
              dmOid: 0,
              dmName: "二氧化碳浓度",
              dmRelateItemDataId: "",
              dmTag: "status",
              dmNote: "",
              dItemId: "1611107",
              dId: "*********",
              dVal: "",
              dDataType: "string",
              dDataUnit: "",
              dCoefficient: "1.0000",
              dDataMaxVal: "",
              dDataMinVal: "",
              dOtherData: "1:开;0:关",
              dFunc: "read",
              dHasSync: "Y",
              dLocked: "",
              dUpdatedAt: "2024-08-21 10:33:01",
              drId: "",
              drVal: "",
              drDataType: "",
              drDataUnit: "",
              drCoefficient: "",
              drDataMaxVal: "",
              drDataMinVal: "",
              drOtherData: "",
              drFunc: "",
              drHasSync: "",
              drLocked: "",
              drUpdatedAt: "",
            },
          ],
          deviceDataEnv: [],
          deviceDataWarn: [],
          healthPercent: 100,
          warningRuleList: [],
          strategyList: [],
        },
        {
          deviceResourceMapId: 9002640,
          xAxis: 0.0,
          yAxis: 0.0,
          zAxis: 0.0,
          otherData: '{"width":10,"height":10}',
          resourceNote: "",
          id: 1611109,
          type: "CGQ11",
          name: "空气质量监控",
          brand: "",
          model: "",
          description: "",
          note: "",
          tag: "",
          icon: "",
          thumb: "",
          pictures: "",
          status: "Y",
          workStatus: "",
          warningStatus: "健康",
          communicateStatus: "离线",
          activationTime: "",
          scrapTime: "",
          life: "100%",
          company: "",
          contact: "",
          mobile: "",
          position: "",
          maintenance: "",
          maintenanceSpan: "",
          nameplate: "",
          files: "",
          serviceLife: "",
          ratedPower: "0",
          energyLever: "0",
          createdAt: "2024-08-19 14:33:09",
          resourceId: "",
          baseResourceId: "3338",
          resourceName: "实验室4F",
          resourceCategory: "实验室",
          resourceType: "base",
          resourceOid: "0",
          resourceVal: "/uploads/fx4F.png",
          resourceWidth: "1400",
          resourceHeight: "709",
          resourceOtherData: "",
          deviceDataBase: [
            {
              dmItemId: 1611109,
              dmId: "*********",
              dmItemDataId: "*********",
              dmOid: 0,
              dmName: "二氧化碳浓度",
              dmRelateItemDataId: "",
              dmTag: "status",
              dmNote: "",
              dItemId: "1611109",
              dId: "*********",
              dVal: "",
              dDataType: "string",
              dDataUnit: "",
              dCoefficient: "1.0000",
              dDataMaxVal: "",
              dDataMinVal: "",
              dOtherData: "1:开;0:关",
              dFunc: "read",
              dHasSync: "Y",
              dLocked: "",
              dUpdatedAt: "2024-08-21 10:33:01",
              drId: "",
              drVal: "",
              drDataType: "",
              drDataUnit: "",
              drCoefficient: "",
              drDataMaxVal: "",
              drDataMinVal: "",
              drOtherData: "",
              drFunc: "",
              drHasSync: "",
              drLocked: "",
              drUpdatedAt: "",
            },
          ],
          deviceDataEnv: [],
          deviceDataWarn: [],
          healthPercent: 100,
          warningRuleList: [],
          strategyList: [],
        },
        {
          deviceResourceMapId: 9002636,
          xAxis: 0.0,
          yAxis: 0.0,
          zAxis: 0.0,
          otherData: '{"width":10,"height":10}',
          resourceNote: "",
          id: 1611110,
          type: "CGQ11",
          name: "滤网状态监控",
          brand: "",
          model: "",
          description: "",
          note: "",
          tag: "",
          icon: "",
          thumb: "",
          pictures: "",
          status: "Y",
          workStatus: "",
          warningStatus: "健康",
          communicateStatus: "离线",
          activationTime: "",
          scrapTime: "",
          life: "100%",
          company: "",
          contact: "",
          mobile: "",
          position: "",
          maintenance: "",
          maintenanceSpan: "",
          nameplate: "",
          files: "",
          serviceLife: "",
          ratedPower: "0",
          energyLever: "0",
          createdAt: "2024-08-19 14:33:09",
          resourceId: "",
          baseResourceId: "3338",
          resourceName: "实验室4F",
          resourceCategory: "实验室",
          resourceType: "base",
          resourceOid: "0",
          resourceVal: "/uploads/fx4F.png",
          resourceWidth: "1400",
          resourceHeight: "709",
          resourceOtherData: "",
          deviceDataBase: [
            {
              dmItemId: 1611110,
              dmId: "*********",
              dmItemDataId: "*********",
              dmOid: 0,
              dmName: "二氧化碳浓度",
              dmRelateItemDataId: "",
              dmTag: "status",
              dmNote: "",
              dItemId: "1611110",
              dId: "*********",
              dVal: "",
              dDataType: "string",
              dDataUnit: "",
              dCoefficient: "1.0000",
              dDataMaxVal: "",
              dDataMinVal: "",
              dOtherData: "1:开;0:关",
              dFunc: "read",
              dHasSync: "Y",
              dLocked: "",
              dUpdatedAt: "2024-08-21 10:33:01",
              drId: "",
              drVal: "",
              drDataType: "",
              drDataUnit: "",
              drCoefficient: "",
              drDataMaxVal: "",
              drDataMinVal: "",
              drOtherData: "",
              drFunc: "",
              drHasSync: "",
              drLocked: "",
              drUpdatedAt: "",
            },
          ],
          deviceDataEnv: [],
          deviceDataWarn: [],
          healthPercent: 100,
          warningRuleList: [],
          strategyList: [],
        },
        {
          deviceResourceMapId: 9002636,
          xAxis: 0.0,
          yAxis: 0.0,
          zAxis: 0.0,
          otherData: '{"width":10,"height":10}',
          resourceNote: "",
          id: 1611110,
          type: "CGQ11",
          name: " 温湿度控制与监控",
          brand: "",
          model: "",
          description: "",
          note: "",
          tag: "",
          icon: "",
          thumb: "",
          pictures: "",
          status: "Y",
          workStatus: "",
          warningStatus: "健康",
          communicateStatus: "离线",
          activationTime: "",
          scrapTime: "",
          life: "100%",
          company: "",
          contact: "",
          mobile: "",
          position: "",
          maintenance: "",
          maintenanceSpan: "",
          nameplate: "",
          files: "",
          serviceLife: "",
          ratedPower: "0",
          energyLever: "0",
          createdAt: "2024-08-19 14:33:09",
          resourceId: "",
          baseResourceId: "3338",
          resourceName: "实验室4F",
          resourceCategory: "实验室",
          resourceType: "base",
          resourceOid: "0",
          resourceVal: "/uploads/fx4F.png",
          resourceWidth: "1400",
          resourceHeight: "709",
          resourceOtherData: "",
          deviceDataBase: [
            {
              dmItemId: 1611110,
              dmId: "*********",
              dmItemDataId: "*********",
              dmOid: 0,
              dmName: "二氧化碳浓度",
              dmRelateItemDataId: "",
              dmTag: "status",
              dmNote: "",
              dItemId: "1611110",
              dId: "*********",
              dVal: "",
              dDataType: "string",
              dDataUnit: "",
              dCoefficient: "1.0000",
              dDataMaxVal: "",
              dDataMinVal: "",
              dOtherData: "1:开;0:关",
              dFunc: "read",
              dHasSync: "Y",
              dLocked: "",
              dUpdatedAt: "2024-08-21 10:33:01",
              drId: "",
              drVal: "",
              drDataType: "",
              drDataUnit: "",
              drCoefficient: "",
              drDataMaxVal: "",
              drDataMinVal: "",
              drOtherData: "",
              drFunc: "",
              drHasSync: "",
              drLocked: "",
              drUpdatedAt: "",
            },
          ],
          deviceDataEnv: [],
          deviceDataWarn: [],
          healthPercent: 100,
          warningRuleList: [],
          strategyList: [],
        },
        {
          deviceResourceMapId: 9002636,
          xAxis: 0.0,
          yAxis: 0.0,
          zAxis: 0.0,
          otherData: '{"width":10,"height":10}',
          resourceNote: "",
          id: 1611110,
          type: "CGQ11",
          name: "新风系统的故障监控",
          brand: "",
          model: "",
          description: "",
          note: "",
          tag: "",
          icon: "",
          thumb: "",
          pictures: "",
          status: "Y",
          workStatus: "",
          warningStatus: "健康",
          communicateStatus: "离线",
          activationTime: "",
          scrapTime: "",
          life: "100%",
          company: "",
          contact: "",
          mobile: "",
          position: "",
          maintenance: "",
          maintenanceSpan: "",
          nameplate: "",
          files: "",
          serviceLife: "",
          ratedPower: "0",
          energyLever: "0",
          createdAt: "2024-08-19 14:33:09",
          resourceId: "",
          baseResourceId: "3338",
          resourceName: "实验室4F",
          resourceCategory: "实验室",
          resourceType: "base",
          resourceOid: "0",
          resourceVal: "/uploads/fx4F.png",
          resourceWidth: "1400",
          resourceHeight: "709",
          resourceOtherData: "",
          deviceDataBase: [
            {
              dmItemId: 1611110,
              dmId: "*********",
              dmItemDataId: "*********",
              dmOid: 0,
              dmName: "二氧化碳浓度",
              dmRelateItemDataId: "",
              dmTag: "status",
              dmNote: "",
              dItemId: "1611110",
              dId: "*********",
              dVal: "",
              dDataType: "string",
              dDataUnit: "",
              dCoefficient: "1.0000",
              dDataMaxVal: "",
              dDataMinVal: "",
              dOtherData: "1:开;0:关",
              dFunc: "read",
              dHasSync: "Y",
              dLocked: "",
              dUpdatedAt: "2024-08-21 10:33:01",
              drId: "",
              drVal: "",
              drDataType: "",
              drDataUnit: "",
              drCoefficient: "",
              drDataMaxVal: "",
              drDataMinVal: "",
              drOtherData: "",
              drFunc: "",
              drHasSync: "",
              drLocked: "",
              drUpdatedAt: "",
            },
          ],
          deviceDataEnv: [],
          deviceDataWarn: [],
          healthPercent: 100,
          warningRuleList: [],
          strategyList: [],
        },
        {
          deviceResourceMapId: 9002636,
          xAxis: 0.0,
          yAxis: 0.0,
          zAxis: 0.0,
          otherData: '{"width":10,"height":10}',
          resourceNote: "",
          id: 1611110,
          type: "CGQ11",
          name: "能效监控",
          brand: "",
          model: "",
          description: "",
          note: "",
          tag: "",
          icon: "",
          thumb: "",
          pictures: "",
          status: "Y",
          workStatus: "",
          warningStatus: "健康",
          communicateStatus: "离线",
          activationTime: "",
          scrapTime: "",
          life: "100%",
          company: "",
          contact: "",
          mobile: "",
          position: "",
          maintenance: "",
          maintenanceSpan: "",
          nameplate: "",
          files: "",
          serviceLife: "",
          ratedPower: "0",
          energyLever: "0",
          createdAt: "2024-08-19 14:33:09",
          resourceId: "",
          baseResourceId: "3338",
          resourceName: "实验室4F",
          resourceCategory: "实验室",
          resourceType: "base",
          resourceOid: "0",
          resourceVal: "/uploads/fx4F.png",
          resourceWidth: "1400",
          resourceHeight: "709",
          resourceOtherData: "",
          deviceDataBase: [
            {
              dmItemId: 1611110,
              dmId: "*********",
              dmItemDataId: "*********",
              dmOid: 0,
              dmName: "二氧化碳浓度",
              dmRelateItemDataId: "",
              dmTag: "status",
              dmNote: "",
              dItemId: "1611110",
              dId: "*********",
              dVal: "",
              dDataType: "string",
              dDataUnit: "",
              dCoefficient: "1.0000",
              dDataMaxVal: "",
              dDataMinVal: "",
              dOtherData: "1:开;0:关",
              dFunc: "read",
              dHasSync: "Y",
              dLocked: "",
              dUpdatedAt: "2024-08-21 10:33:01",
              drId: "",
              drVal: "",
              drDataType: "",
              drDataUnit: "",
              drCoefficient: "",
              drDataMaxVal: "",
              drDataMinVal: "",
              drOtherData: "",
              drFunc: "",
              drHasSync: "",
              drLocked: "",
              drUpdatedAt: "",
            },
          ],
          deviceDataEnv: [],
          deviceDataWarn: [],
          healthPercent: 100,
          warningRuleList: [],
          strategyList: [],
        },
        {
          deviceResourceMapId: 9002636,
          xAxis: 0.0,
          yAxis: 0.0,
          zAxis: 0.0,
          otherData: '{"width":10,"height":10}',
          resourceNote: "",
          id: 1611110,
          type: "CGQ11",
          name: "远程监控与控制",
          brand: "",
          model: "",
          description: "",
          note: "",
          tag: "",
          icon: "",
          thumb: "",
          pictures: "",
          status: "Y",
          workStatus: "",
          warningStatus: "健康",
          communicateStatus: "离线",
          activationTime: "",
          scrapTime: "",
          life: "100%",
          company: "",
          contact: "",
          mobile: "",
          position: "",
          maintenance: "",
          maintenanceSpan: "",
          nameplate: "",
          files: "",
          serviceLife: "",
          ratedPower: "0",
          energyLever: "0",
          createdAt: "2024-08-19 14:33:09",
          resourceId: "",
          baseResourceId: "3338",
          resourceName: "实验室4F",
          resourceCategory: "实验室",
          resourceType: "base",
          resourceOid: "0",
          resourceVal: "/uploads/fx4F.png",
          resourceWidth: "1400",
          resourceHeight: "709",
          resourceOtherData: "",
          deviceDataBase: [
            {
              dmItemId: 1611110,
              dmId: "*********",
              dmItemDataId: "*********",
              dmOid: 0,
              dmName: "二氧化碳浓度",
              dmRelateItemDataId: "",
              dmTag: "status",
              dmNote: "",
              dItemId: "1611110",
              dId: "*********",
              dVal: "",
              dDataType: "string",
              dDataUnit: "",
              dCoefficient: "1.0000",
              dDataMaxVal: "",
              dDataMinVal: "",
              dOtherData: "1:开;0:关",
              dFunc: "read",
              dHasSync: "Y",
              dLocked: "",
              dUpdatedAt: "2024-08-21 10:33:01",
              drId: "",
              drVal: "",
              drDataType: "",
              drDataUnit: "",
              drCoefficient: "",
              drDataMaxVal: "",
              drDataMinVal: "",
              drOtherData: "",
              drFunc: "",
              drHasSync: "",
              drLocked: "",
              drUpdatedAt: "",
            },
          ],
          deviceDataEnv: [],
          deviceDataWarn: [],
          healthPercent: 100,
          warningRuleList: [],
          strategyList: [],
        },
      ],

      input: "",
      activeTab: "today",
      listst: [
        {
          name: "广东质检中诚认证有限公司到中广...",
        },
        { name: "材料科学、化学工程及医药研发成..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
        { name: "植酸检测方法及作用" },
        { name: "兔拉检测：行业中芯片造假技术..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
      ],
      showdh: true,
      showdh1: false,
      noAnimation: false,
      selectedItem: null,
      ids: null,
      nhlist: [
        {
          title: "供气压力",
          status: "0.3Mpa",
          unit: "℃",
        },

        {
          title: "供气流量",
          status: "6M3/min",
          unit: "㎡",
        },
        {
          title: "露点温度",
          status: "6℃",
          unit: "℃",
        },
        {
          title: "含氧量",
          status: "6PPM",
          unit: "㎡",
        },
      ],
      warnlist1: [
        {
          type: 1,
          name: "检测到烟雾，可能有着火灾的发生...",
          value: "",
          time: "09:13:59  2023-06-07",
        },
        {
          type: 2,
          name: "检测到烟雾，可能预示着火灾的发生..",
          value: "",
          time: "10:13:18  2023-06-12",
        },
        {
          type: 3,
          name: "实验室内检测到漏水，可能来自冷凝水..",
          value: "",
          time: "10:13:18  2023-06-12",
        },
      ],
      isButton2Active: false,
      status: "巡检中",
      status1: "已完成",
      status2: "待巡检",
      selectedIndex: 0,
      componentTag: "component0",
    };
  },
  // 计算属性类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {},
  // 方法集合
  methods: {
    showdetails(item) {
      if (item.ids) {
        this.ids = item.ids;
      }

      this.isshow = true;
      this.selectedItem = item;
    },
    hidedetails() {
      this.isshow = false;
    },
    oc(value) {
      console.log(value, "收到的值");
      this.showdh = value;
    },
  },
  // 生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.showdh1 = true;
    setTimeout(() => {
      this.showdh1 = false;
      this.noAnimation = false;
    }, 1000); // 动画持续时间为1秒
    console.log(1222);
  },
  beforeCreate() {}, // 生命周期 - 创建之前
  beforeMount() {}, // 生命周期 - 挂载之前
  beforeUpdate() {}, // 生命周期 - 更新之前
  updated() {}, // 生命周期 - 更新之后
  beforeUnmount() {
    // 在组件销毁之前清除定时器
    console.log(1111);
  },

  unmounted() {
    console.log(2222);
  }, // 生命周期 - 销毁之前
  destroyed() {
    console.log(1221);
  }, // 生命周期 - 销毁完成
  activated() {}, // 如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="less" scoped>
.container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: stretch;
  height: 1080px;
  text-align: center;

  .sbdetails {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
  }

  .box {
    margin-top: 6px;
    padding-top: 5px;
    margin-bottom: 0.225rem;
    height: 800px;
    width: 330px;
    // height: 800px;

    overflow-y: scroll;

    /* 设置垂直滚动条 */
    /* 设置滚动条的样式 */
    &::-webkit-scrollbar {
      width: 0.1px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条轨道的样式 */
    &::-webkit-scrollbar-track {
      background-color: #f1f1f1;
      /* 设置滚动条轨道的背景色 */
    }

    /* 设置滚动条滑块的样式 */
    &::-webkit-scrollbar-thumb {
      background-color: #334f6e;
      /* 设置滚动条滑块的背景色 */
    }

    /* 鼠标悬停在滚动条上时的样式 */
    &::-webkit-scrollbar-thumb:hover {
      background-color: #555;
      /* 设置鼠标悬停时滚动条滑块的背景色 */
    }

    .el-input {
      width: 305px;
      height: 34px;
      color: #fff !important;

      ::v-deep .el-input__wrapper {
        background: url("../assets/image/inputss.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        box-shadow: none !important;
      }
    }

    .suosuo {
      position: absolute;
      top: 66px;
      left: 285px;
    }

    .xiaobox {
      margin-top: 20px;
      display: flex;
      align-items: center;

      .siqiu {
        width: 16px;
        height: 16px;
      }

      .shuru {
        display: flex;
        align-items: center;
        padding-left: 10px;
        background: url("../assets/image/shebei12.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 261px;
        height: 26px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 12px;
        color: #ffffff;
      }
    }

    .xiaoboxs {
      cursor: pointer;
      margin-top: 14px;
      display: flex;
      margin-left: 5px;
      align-items: center;

      .nihaowo {
        width: 78px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 10px;
        color: #ffffff;
        display: flex;
        white-space: nowrap; /* 不换行 */
        overflow: hidden; /* 超出部分隐藏 */
        text-overflow: ellipsis; /* 超出部分显示省略号 */
      }

      .siqiu {
        width: 16px;
        height: 16px;
        margin-left: 10px;
        margin-right: 7px;
      }

      .shuru {
        display: flex;
        align-items: center;
        padding-left: 10px;
        background: url("../assets/image/shebei12.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 261px;
        height: 26px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 12px;
        color: #ffffff;
        white-space: nowrap; /* 不换行 */
        overflow: hidden; /* 超出部分隐藏 */
        text-overflow: ellipsis; /* 超出部分显示省略号 */
      }
    }
  }

  .left-panel {
    position: fixed;
    z-index: 1;
    top: 100px;
    left: 22px;
    width: 330px;
    height: 937px;

    background-size: 100% 100%;
    transform: translate(-122%);
    transition: transform 0.5s ease-in-out;
  }

  .left-panel-active {
    transform: translate(0%);
  }

  .left-panel-active1 {
    // transform: translate(0%);
    animation: slideOut 1s ease-in-out forwards;
  }

  @keyframes slideOut {
    100% {
      transform: translateX(0%);
    }

    // 85% {
    //   transform: translateX(-25%);
    // }

    // 65% {
    //   transform: translateX(-15%);
    // }

    // 40% {
    //   transform: translateX(-55%);
    // }

    // 30% {
    //   transform: translateX(-40%);
    // }

    0% {
      transform: translateX(-100%);
    }
  }

  .rtitle {
    margin-top: 16px;
  }

  @keyframes slideIn {
    0% {
      transform: translateX(100%);
    }

    // 30% {
    //   transform: translateX(65%);
    // }

    // 40% {
    //   transform: translateX(40%);
    // }

    // 65% {
    //   transform: translateX(15%);
    // }

    // 85% {
    //   transform: translateX(25%);
    // }

    100% {
      transform: translateX(0%);
    }
  }

  .right-panel {
    position: fixed;
    z-index: 1;
    right: 83px;
    width: 330px;
    top: 100px;
    height: 937px;

    background-size: 100% 100%;
    transform: translate(122%);
    transition: transform 0.5s ease-in-out;

    .jk {
      margin-top: 12px;
      width: 90%;
      height: 200px;
    }

    .box {
      margin-top: 6px;
      margin-bottom: 18px;
      // background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      width: 94%;
      // height: 428px;

      .loudong {
        margin-top: 21px;
        margin-bottom: 25px;
      }

      .wenzi {
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 10px;
        color: #bdecf9;
        text-align: left;
        margin-left: 20px;
        margin-right: 20px;

        .h2biaoti {
          margin-bottom: 15px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 12px;
          color: #00ffff;
        }
      }

      .p {
        text-indent: 2em;
        margin-bottom: 1em;
        letter-spacing: 0.05em;
      }
    }

    .boxxx {
      margin-top: 6px;
      margin-bottom: 18px;
      background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      width: 333px;
      height: 420px;
      overflow-y: scroll;
      /* 设置垂直滚动条 */
      // overflow: hidden;
      /* 设置滚动条的样式 */

      .zengti {
        margin: 10px 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 315px;
        height: 38px;
        gap: 5px;

        .left {
          display: flex;
          align-items: center;
          justify-content: space-evenly;
          width: 84px;
          height: 27px;

          .yuan {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #08f7f7;
          }

          .wenziss {
            display: flex;
            align-items: flex-start;
            flex-direction: column;

            .p1 {
              font-family: Source Han Sans SC;
              font-weight: 500;
              font-size: 10px;
              color: #ffffff;
            }

            .p2 {
              font-family: Source Han Sans SC;
              font-weight: 500;
              font-size: 10px;
              color: #55cff9;
            }
          }
        }

        .right {
          background: url("../assets/image/rightbeij.png");
          background-size: 100% 100%;
          background-repeat: no-repeat;

          width: 217px;
          height: 38px;
          font-family: Source Han Sans SC;
          font-weight: 500;
          font-size: 11px;
          color: #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    .boxxx::-webkit-scrollbar {
      width: 1px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条轨道的样式 */
    .boxxx::-webkit-scrollbar-track {
      background-color: #f1f1f1;
      /* 设置滚动条轨道的背景色 */
    }

    /* 设置滚动条滑块的样式 */
    .boxxx::-webkit-scrollbar-thumb {
      background-color: #013363;
      /* 设置滚动条滑块的背景色 */
    }
  }

  .no-animation {
    transition: none;
  }

  .right-panel-active {
    transform: translate(0%);
    // animation: slideIn 1s ease-in-out ;
  }

  .right-panel-active1 {
    // transform: translate(0%);
    animation: slideIn 1s ease-in-out forwards;
  }

  @keyframes slideIn {
    0% {
      transform: translateX(100%);
    }

    // 30% {
    //   transform: translateX(65%);
    // }

    // 40% {
    //   transform: translateX(40%);
    // }

    // 65% {
    //   transform: translateX(15%);
    // }

    // 85% {
    //   transform: translateX(25%);
    // }

    100% {
      transform: translateX(0%);
    }
  }

  .completed {
    background: #7ad0ff;
  }

  .incomplete {
    background: #ff6041;
  }

  .warning {
    background: #00ffc0;
  }

  .completeds {
    color: #7ad0ff;
  }

  .incompletes {
    color: #ff6041;
  }

  .warnings {
    color: #00ffc0;
  }
}

.ql-center {
  display: flex;
  margin-top: 20px;
  justify-content: space-around;
  margin-top: 14px;

  .ql-Box {
    width: 75px;
    height: 49px;
    border: 1px solid #7ad0ff;
    // opacity: 0.6;
    border-radius: 2px;

    .ql-box1 {
      font-family: Alibaba PuHuiTi;
      font-weight: bold;
      font-size: 16px;
      color: #7ad0ff;
      margin-top: -10px;
    }

    .ql-box {
      display: flex;
      padding-left: 8px;
      padding-right: 9px;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 34px;

      .left_ql {
        width: 49px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #ffffff;

        .yuan {
          width: 7px;
          height: 7px;
          border-radius: 50%;
        }

        .pp {
          color: #fff;
          font-size: 12px;
        }
      }

      img {
        height: 12px;
        width: 7px;
      }
    }
  }
}

.warn1 {
  background: url("../assets/image/warnred.png");
}

.warn2 {
  background: url("../assets/image/warnyellow.png");
}

.warn3 {
  background: url("../assets/image/warngreen.png");
}

.warning12 {
  background-size: 100% 100%;
  // width: 365px;
  height: 47px;

  .info {
    margin-top: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: left;

    margin-left: 50px;

    .info1 {
      display: flex;
      flex-direction: column;

      p:nth-of-type(1) {
        font-size: 13px;
        font-family: Alibaba PuHuiTi;
        font-weight: bold;
      }

      .red {
        color: #ff0000;
      }

      .green {
        color: #00ffcc;
      }

      .yellow {
        color: #ffff00;
      }

      p:nth-of-type(2) {
        font-size: 16px;
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        color: #ffffff;
      }
    }

    .info2 {
      margin-right: 10px;
      font-size: 15px;
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      color: #cffff8;
    }
  }
}
.xxxx {
  position: absolute;
  top: 1%;
  right: 1%;
  width: 25px;
  height: 25px;
  z-index: 99999;
}
</style>
