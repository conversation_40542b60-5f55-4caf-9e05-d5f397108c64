<template>
  <div>
    <!-- <component :is="componentTag" @fatherMethoddd="fatherMethoddd"></component> -->

    <tedai
      :ids="ids"
      :selectedItem="selectedItem"
      class="sbdetails"
      :zengtiimg="zengtiimg"
      v-if="isshow"
      @hidedetails="hidedetails"
    ></tedai>
    <div class="container">
      <div
        class="left-panel"
        :class="{
          'left-panel-active': showdh,
          'no-animation': noAnimation,
          'left-panel-active1': showdh1,
        }"
      >
        <Title2 class="ltitle1" tit="供气列表">
          <div class="box">
            <div>
              <el-input
                class="el-input"
                v-model="input"
                placeholder="请输入内容"
              ></el-input>
            </div>
            <!-- <div class="xiaobox">
              <img class="siqiu" src="../assets/image/shixinqiu.png" alt="" />
              <div class="shuru">全部设备</div>
            </div> -->
            <div
              class="xiaoboxs"
              v-for="item in data"
              :key="item"
              @click="showdetails(item)"
            >
              <img class="siqiu" src="../assets/image/kongxin.png" alt="" />
              <div class="shuru">{{ item.name }}</div>
            </div>
          </div>
          <!-- shebei12.png -->
        </Title2>
      </div>
      <!-- shebeibgc.png -->
      <!-- 右侧内容 -->
      <!-- 右侧内容 -->

      <div
        class="right-panel"
        :class="{
          'right-panel-active': showdh,
          'no-animation': noAnimation,
          'right-panel-active1': showdh1,
        }"
      >
        <Title3 tit="供气详情">
          <div class="box">
            <div class="xiaoboxs" v-for="item in cgqlist" :key="item">
              <img class="siqiu" src="../assets/image/kongxin.png" alt="" />
              <div class="nihaowo">{{ item.name }}</div>
              <div class="shuru">{{ item.value }}</div>
            </div>
          </div>
          <!-- <img class="jk" src="../assets/jiankong.png" alt=""> -->
        </Title3>
      </div>
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import huanxing from "@/components/echarts/huanxing.vue";
import zhexian from "@/components/echarts/zhexian.vue";
import zhexian1 from "@/components/echarts/zhexian1.vue";
import SystemDete from "@/components/echarts/SystemDete.vue";
import echarts1 from "@/components/echarts/bingjifang/echarts3.vue";
import tedai from "@/components/common/tedai.vue";
import shuangxiang from "@/components/echarts/shuangxiang.vue";
// import details from "@/components/common/details.vue";
export default {
  // import引入的组件需要注入到对象中才能使用
  components: {
    tedai,
    huanxing,
    zhexian,
    zhexian1,
    SystemDete,
    echarts1,
    shuangxiang,
  },
  props: ["tabledata", "zengtiimg"],
  //  cgqlistss: [
  //       { name: "湿度范围：", value: "5%RH~100%RH（无结露）" },
  //       { name: "湿度分辨率：", value: "0.04%" },
  //       { name: "IP地址：", value: "±3%RH（20%RH~80%RH）" },
  //       { name: "湿度精度：", value: "-40℃~+125℃" },
  //       { name: "温度分辨率：", value: "0.01℃" },
  //       { name: "温度精度：", value: "±0.5℃（10℃~60℃）" },
  //     ],

  data() {
    // 这里存放数据
    return {
      isshow: false,
      xxxx: false,
      cgqlist: [
        { name: "湿度范围：", value: "5%RH~100%RH（无结露）" },
        { name: "湿度分辨率：", value: "0.04%" },
        { name: "IP地址：", value: "±3%RH（20%RH~80%RH）" },
        { name: "湿度精度：", value: "-40℃~+125℃" },
        { name: "温度分辨率：", value: "0.01℃" },
        { name: "温度精度：", value: "±0.5℃（10℃~60℃）" },
      ],

      data: [
        {
          deviceResourceMapId: 9002630,
          xAxis: 0.0,
          yAxis: 0.0,
          zAxis: 0.0,
          otherData: '{"width":10,"height":10}',
          resourceNote: "",
          id: 1611080,
          type: "CGQ11",
          name: "一般试剂423温湿度传感器-4X",
          brand: "",
          model: "",
          description: "",
          note: "",
          tag: "",
          icon: "",
          thumb: "",
          pictures: "",
          status: "Y",
          workStatus: "",
          warningStatus: "健康",
          communicateStatus: "离线",
          activationTime: "",
          scrapTime: "",
          life: "100%",
          company: "",
          contact: "",
          mobile: "",
          position: "",
          maintenance: "",
          maintenanceSpan: "",
          nameplate: "",
          files: "",
          serviceLife: "",
          ratedPower: "0",
          energyLever: "0",
          createdAt: "2024-08-19 14:33:09",
          resourceId: "",
          baseResourceId: "3338",
          resourceName: "实验室4F",
          resourceCategory: "实验室",
          resourceType: "base",
          resourceOid: "0",
          resourceVal: "/uploads/fx4F.png",
          resourceWidth: "1400",
          resourceHeight: "709",
          resourceOtherData: "",
          deviceDataBase: [
            {
              dmItemId: 1611080,
              dmId: "*********",
              dmItemDataId: "*********",
              dmOid: 0,
              dmName: "二氧化碳浓度",
              dmRelateItemDataId: "",
              dmTag: "status",
              dmNote: "",
              dItemId: "1611080",
              dId: "*********",
              dVal: "",
              dDataType: "string",
              dDataUnit: "",
              dCoefficient: "1.0000",
              dDataMaxVal: "",
              dDataMinVal: "",
              dOtherData: "1:开;0:关",
              dFunc: "read",
              dHasSync: "Y",
              dLocked: "",
              dUpdatedAt: "2024-08-21 10:33:01",
              drId: "",
              drVal: "",
              drDataType: "",
              drDataUnit: "",
              drCoefficient: "",
              drDataMaxVal: "",
              drDataMinVal: "",
              drOtherData: "",
              drFunc: "",
              drHasSync: "",
              drLocked: "",
              drUpdatedAt: "",
            },
          ],
          deviceDataEnv: [],
          deviceDataWarn: [],
          healthPercent: 100,
          warningRuleList: [],
          strategyList: [],
        },
        {
          deviceResourceMapId: 9002639,
          xAxis: 0.0,
          yAxis: 0.0,
          zAxis: 0.0,
          otherData: '{"width":10,"height":10}',
          resourceNote: "",
          id: 1611081,
          type: "CGQ11",
          name: "废弃物436温湿度传感器-4X",
          brand: "",
          model: "",
          description: "",
          note: "",
          tag: "",
          icon: "",
          thumb: "",
          pictures: "",
          status: "Y",
          workStatus: "",
          warningStatus: "健康",
          communicateStatus: "离线",
          activationTime: "",
          scrapTime: "",
          life: "100%",
          company: "",
          contact: "",
          mobile: "",
          position: "",
          maintenance: "",
          maintenanceSpan: "",
          nameplate: "",
          files: "",
          serviceLife: "",
          ratedPower: "0",
          energyLever: "0",
          createdAt: "2024-08-19 14:33:09",
          resourceId: "",
          baseResourceId: "3338",
          resourceName: "实验室4F",
          resourceCategory: "实验室",
          resourceType: "base",
          resourceOid: "0",
          resourceVal: "/uploads/fx4F.png",
          resourceWidth: "1400",
          resourceHeight: "709",
          resourceOtherData: "",
          deviceDataBase: [
            {
              dmItemId: 1611081,
              dmId: "*********",
              dmItemDataId: "*********",
              dmOid: 0,
              dmName: "二氧化碳浓度",
              dmRelateItemDataId: "",
              dmTag: "status",
              dmNote: "",
              dItemId: "1611081",
              dId: "*********",
              dVal: "",
              dDataType: "string",
              dDataUnit: "",
              dCoefficient: "1.0000",
              dDataMaxVal: "",
              dDataMinVal: "",
              dOtherData: "1:开;0:关",
              dFunc: "read",
              dHasSync: "Y",
              dLocked: "",
              dUpdatedAt: "2024-08-21 10:33:01",
              drId: "",
              drVal: "",
              drDataType: "",
              drDataUnit: "",
              drCoefficient: "",
              drDataMaxVal: "",
              drDataMinVal: "",
              drOtherData: "",
              drFunc: "",
              drHasSync: "",
              drLocked: "",
              drUpdatedAt: "",
            },
          ],
          deviceDataEnv: [],
          deviceDataWarn: [],
          healthPercent: 100,
          warningRuleList: [],
          strategyList: [],
        },
        {
          deviceResourceMapId: 9002632,
          xAxis: 0.0,
          yAxis: 0.0,
          zAxis: 0.0,
          otherData: '{"width":10,"height":10}',
          resourceNote: "",
          id: 1611083,
          type: "CGQ11",
          name: "剧毒422防爆温湿度传感器-4X",
          brand: "",
          model: "",
          description: "",
          note: "",
          tag: "",
          icon: "",
          thumb: "",
          pictures: "",
          status: "Y",
          workStatus: "",
          warningStatus: "健康",
          communicateStatus: "离线",
          activationTime: "",
          scrapTime: "",
          life: "100%",
          company: "",
          contact: "",
          mobile: "",
          position: "",
          maintenance: "",
          maintenanceSpan: "",
          nameplate: "",
          files: "",
          serviceLife: "",
          ratedPower: "0",
          energyLever: "0",
          createdAt: "2024-08-19 14:33:09",
          resourceId: "",
          baseResourceId: "3338",
          resourceName: "实验室4F",
          resourceCategory: "实验室",
          resourceType: "base",
          resourceOid: "0",
          resourceVal: "/uploads/fx4F.png",
          resourceWidth: "1400",
          resourceHeight: "709",
          resourceOtherData: "",
          deviceDataBase: [
            {
              dmItemId: 1611083,
              dmId: "*********",
              dmItemDataId: "*********",
              dmOid: 0,
              dmName: "二氧化碳浓度",
              dmRelateItemDataId: "",
              dmTag: "status",
              dmNote: "",
              dItemId: "1611083",
              dId: "*********",
              dVal: "",
              dDataType: "string",
              dDataUnit: "",
              dCoefficient: "1.0000",
              dDataMaxVal: "",
              dDataMinVal: "",
              dOtherData: "1:开;0:关",
              dFunc: "read",
              dHasSync: "Y",
              dLocked: "",
              dUpdatedAt: "2024-08-21 10:33:01",
              drId: "",
              drVal: "",
              drDataType: "",
              drDataUnit: "",
              drCoefficient: "",
              drDataMaxVal: "",
              drDataMinVal: "",
              drOtherData: "",
              drFunc: "",
              drHasSync: "",
              drLocked: "",
              drUpdatedAt: "",
            },
          ],
          deviceDataEnv: [],
          deviceDataWarn: [],
          healthPercent: 100,
          warningRuleList: [],
          strategyList: [],
        },
        {
          deviceResourceMapId: 9002654,
          xAxis: 0.0,
          yAxis: 0.0,
          zAxis: 0.0,
          otherData: '{"width":10,"height":10}',
          resourceNote: "",
          id: 1611084,
          type: "CGQ11",
          name: "电梯间418旁边温湿度传感器-4X",
          brand: "",
          model: "",
          description: "",
          note: "",
          tag: "",
          icon: "",
          thumb: "",
          pictures: "",
          status: "Y",
          workStatus: "",
          warningStatus: "健康",
          communicateStatus: "离线",
          activationTime: "",
          scrapTime: "",
          life: "100%",
          company: "",
          contact: "",
          mobile: "",
          position: "",
          maintenance: "",
          maintenanceSpan: "",
          nameplate: "",
          files: "",
          serviceLife: "",
          ratedPower: "0",
          energyLever: "0",
          createdAt: "2024-08-19 14:33:09",
          resourceId: "",
          baseResourceId: "3338",
          resourceName: "实验室4F",
          resourceCategory: "实验室",
          resourceType: "base",
          resourceOid: "0",
          resourceVal: "/uploads/fx4F.png",
          resourceWidth: "1400",
          resourceHeight: "709",
          resourceOtherData: "",
          deviceDataBase: [
            {
              dmItemId: 1611084,
              dmId: "*********",
              dmItemDataId: "*********",
              dmOid: 0,
              dmName: "二氧化碳浓度",
              dmRelateItemDataId: "",
              dmTag: "status",
              dmNote: "",
              dItemId: "1611084",
              dId: "*********",
              dVal: "",
              dDataType: "string",
              dDataUnit: "",
              dCoefficient: "1.0000",
              dDataMaxVal: "",
              dDataMinVal: "",
              dOtherData: "1:开;0:关",
              dFunc: "read",
              dHasSync: "Y",
              dLocked: "",
              dUpdatedAt: "2024-08-21 10:33:01",
              drId: "",
              drVal: "",
              drDataType: "",
              drDataUnit: "",
              drCoefficient: "",
              drDataMaxVal: "",
              drDataMinVal: "",
              drOtherData: "",
              drFunc: "",
              drHasSync: "",
              drLocked: "",
              drUpdatedAt: "",
            },
          ],
          deviceDataEnv: [],
          deviceDataWarn: [],
          healthPercent: 100,
          warningRuleList: [],
          strategyList: [],
        },
        {
          deviceResourceMapId: 9002652,
          xAxis: 0.0,
          yAxis: 0.0,
          zAxis: 0.0,
          otherData: '{"width":10,"height":10}',
          resourceNote: "",
          id: 1611085,
          type: "CGQ11",
          name: "液质418温湿度传感器-4X",
          brand: "",
          model: "",
          description: "",
          note: "",
          tag: "",
          icon: "",
          thumb: "",
          pictures: "",
          status: "Y",
          workStatus: "",
          warningStatus: "健康",
          communicateStatus: "离线",
          activationTime: "",
          scrapTime: "",
          life: "100%",
          company: "",
          contact: "",
          mobile: "",
          position: "",
          maintenance: "",
          maintenanceSpan: "",
          nameplate: "",
          files: "",
          serviceLife: "",
          ratedPower: "0",
          energyLever: "0",
          createdAt: "2024-08-19 14:33:09",
          resourceId: "",
          baseResourceId: "3338",
          resourceName: "实验室4F",
          resourceCategory: "实验室",
          resourceType: "base",
          resourceOid: "0",
          resourceVal: "/uploads/fx4F.png",
          resourceWidth: "1400",
          resourceHeight: "709",
          resourceOtherData: "",
          deviceDataBase: [
            {
              dmItemId: 1611085,
              dmId: "*********",
              dmItemDataId: "*********",
              dmOid: 0,
              dmName: "二氧化碳浓度",
              dmRelateItemDataId: "",
              dmTag: "status",
              dmNote: "",
              dItemId: "1611085",
              dId: "*********",
              dVal: "",
              dDataType: "string",
              dDataUnit: "",
              dCoefficient: "1.0000",
              dDataMaxVal: "",
              dDataMinVal: "",
              dOtherData: "1:开;0:关",
              dFunc: "read",
              dHasSync: "Y",
              dLocked: "",
              dUpdatedAt: "2024-08-21 10:33:01",
              drId: "",
              drVal: "",
              drDataType: "",
              drDataUnit: "",
              drCoefficient: "",
              drDataMaxVal: "",
              drDataMinVal: "",
              drOtherData: "",
              drFunc: "",
              drHasSync: "",
              drLocked: "",
              drUpdatedAt: "",
            },
          ],
          deviceDataEnv: [],
          deviceDataWarn: [],
          healthPercent: 100,
          warningRuleList: [],
          strategyList: [],
        },
        {
          deviceResourceMapId: 9002638,
          xAxis: 0.0,
          yAxis: 0.0,
          zAxis: 0.0,
          otherData: '{"width":10,"height":10}',
          resourceNote: "",
          id: 1611086,
          type: "CGQ11",
          name: "尿碘426温湿度传感器-4X",
          brand: "",
          model: "",
          description: "",
          note: "",
          tag: "",
          icon: "",
          thumb: "",
          pictures: "",
          status: "Y",
          workStatus: "",
          warningStatus: "健康",
          communicateStatus: "离线",
          activationTime: "",
          scrapTime: "",
          life: "100%",
          company: "",
          contact: "",
          mobile: "",
          position: "",
          maintenance: "",
          maintenanceSpan: "",
          nameplate: "",
          files: "",
          serviceLife: "",
          ratedPower: "0",
          energyLever: "0",
          createdAt: "2024-08-19 14:33:09",
          resourceId: "",
          baseResourceId: "3338",
          resourceName: "实验室4F",
          resourceCategory: "实验室",
          resourceType: "base",
          resourceOid: "0",
          resourceVal: "/uploads/fx4F.png",
          resourceWidth: "1400",
          resourceHeight: "709",
          resourceOtherData: "",
          deviceDataBase: [
            {
              dmItemId: 1611086,
              dmId: "*********",
              dmItemDataId: "*********",
              dmOid: 0,
              dmName: "二氧化碳浓度",
              dmRelateItemDataId: "",
              dmTag: "status",
              dmNote: "",
              dItemId: "1611086",
              dId: "*********",
              dVal: "",
              dDataType: "string",
              dDataUnit: "",
              dCoefficient: "1.0000",
              dDataMaxVal: "",
              dDataMinVal: "",
              dOtherData: "1:开;0:关",
              dFunc: "read",
              dHasSync: "Y",
              dLocked: "",
              dUpdatedAt: "2024-08-21 10:33:01",
              drId: "",
              drVal: "",
              drDataType: "",
              drDataUnit: "",
              drCoefficient: "",
              drDataMaxVal: "",
              drDataMinVal: "",
              drOtherData: "",
              drFunc: "",
              drHasSync: "",
              drLocked: "",
              drUpdatedAt: "",
            },
          ],
          deviceDataEnv: [],
          deviceDataWarn: [],
          healthPercent: 100,
          warningRuleList: [],
          strategyList: [],
        },
        {
          deviceResourceMapId: 9002653,
          xAxis: 0.0,
          yAxis: 0.0,
          zAxis: 0.0,
          otherData: '{"width":10,"height":10}',
          resourceNote: "",
          id: 1611087,
          type: "CGQ11",
          name: "生物前处理424温湿度传感器-4X",
          brand: "",
          model: "",
          description: "",
          note: "",
          tag: "",
          icon: "",
          thumb: "",
          pictures: "",
          status: "Y",
          workStatus: "",
          warningStatus: "健康",
          communicateStatus: "离线",
          activationTime: "",
          scrapTime: "",
          life: "100%",
          company: "",
          contact: "",
          mobile: "",
          position: "",
          maintenance: "",
          maintenanceSpan: "",
          nameplate: "",
          files: "",
          serviceLife: "",
          ratedPower: "0",
          energyLever: "0",
          createdAt: "2024-08-19 14:33:09",
          resourceId: "",
          baseResourceId: "3338",
          resourceName: "实验室4F",
          resourceCategory: "实验室",
          resourceType: "base",
          resourceOid: "0",
          resourceVal: "/uploads/fx4F.png",
          resourceWidth: "1400",
          resourceHeight: "709",
          resourceOtherData: "",
          deviceDataBase: [
            {
              dmItemId: 1611087,
              dmId: "*********",
              dmItemDataId: "*********",
              dmOid: 0,
              dmName: "二氧化碳浓度",
              dmRelateItemDataId: "",
              dmTag: "status",
              dmNote: "",
              dItemId: "1611087",
              dId: "*********",
              dVal: "",
              dDataType: "string",
              dDataUnit: "",
              dCoefficient: "1.0000",
              dDataMaxVal: "",
              dDataMinVal: "",
              dOtherData: "1:开;0:关",
              dFunc: "read",
              dHasSync: "Y",
              dLocked: "",
              dUpdatedAt: "2024-08-21 10:33:01",
              drId: "",
              drVal: "",
              drDataType: "",
              drDataUnit: "",
              drCoefficient: "",
              drDataMaxVal: "",
              drDataMinVal: "",
              drOtherData: "",
              drFunc: "",
              drHasSync: "",
              drLocked: "",
              drUpdatedAt: "",
            },
          ],
          deviceDataEnv: [],
          deviceDataWarn: [],
          healthPercent: 100,
          warningRuleList: [],
          strategyList: [],
        },
        {
          deviceResourceMapId: 9002629,
          xAxis: 0.0,
          yAxis: 0.0,
          zAxis: 0.0,
          otherData: '{"width":10,"height":10}',
          resourceNote: "",
          id: 1611088,
          type: "CGQ11",
          name: "ICP-MS417温湿度传感器-4X",
          brand: "",
          model: "",
          description: "",
          note: "",
          tag: "",
          icon: "",
          thumb: "",
          pictures: "",
          status: "Y",
          workStatus: "",
          warningStatus: "健康",
          communicateStatus: "离线",
          activationTime: "",
          scrapTime: "",
          life: "100%",
          company: "",
          contact: "",
          mobile: "",
          position: "",
          maintenance: "",
          maintenanceSpan: "",
          nameplate: "",
          files: "",
          serviceLife: "",
          ratedPower: "0",
          energyLever: "0",
          createdAt: "2024-08-19 14:33:09",
          resourceId: "",
          baseResourceId: "3338",
          resourceName: "实验室4F",
          resourceCategory: "实验室",
          resourceType: "base",
          resourceOid: "0",
          resourceVal: "/uploads/fx4F.png",
          resourceWidth: "1400",
          resourceHeight: "709",
          resourceOtherData: "",
          deviceDataBase: [
            {
              dmItemId: 1611088,
              dmId: "*********",
              dmItemDataId: "*********",
              dmOid: 0,
              dmName: "二氧化碳浓度",
              dmRelateItemDataId: "",
              dmTag: "status",
              dmNote: "",
              dItemId: "1611088",
              dId: "*********",
              dVal: "",
              dDataType: "string",
              dDataUnit: "",
              dCoefficient: "1.0000",
              dDataMaxVal: "",
              dDataMinVal: "",
              dOtherData: "1:开;0:关",
              dFunc: "read",
              dHasSync: "Y",
              dLocked: "",
              dUpdatedAt: "2024-08-21 10:33:01",
              drId: "",
              drVal: "",
              drDataType: "",
              drDataUnit: "",
              drCoefficient: "",
              drDataMaxVal: "",
              drDataMinVal: "",
              drOtherData: "",
              drFunc: "",
              drHasSync: "",
              drLocked: "",
              drUpdatedAt: "",
            },
          ],
          deviceDataEnv: [],
          deviceDataWarn: [],
          healthPercent: 100,
          warningRuleList: [],
          strategyList: [],
        },
        {
          deviceResourceMapId: 9002650,
          xAxis: 0.0,
          yAxis: 0.0,
          zAxis: 0.0,
          otherData: '{"width":10,"height":10}',
          resourceNote: "",
          id: 1611089,
          type: "CGQ11",
          name: "泵房416温湿度传感器-4X",
          brand: "",
          model: "",
          description: "",
          note: "",
          tag: "",
          icon: "",
          thumb: "",
          pictures: "",
          status: "Y",
          workStatus: "",
          warningStatus: "健康",
          communicateStatus: "离线",
          activationTime: "",
          scrapTime: "",
          life: "100%",
          company: "",
          contact: "",
          mobile: "",
          position: "",
          maintenance: "",
          maintenanceSpan: "",
          nameplate: "",
          files: "",
          serviceLife: "",
          ratedPower: "0",
          energyLever: "0",
          createdAt: "2024-08-19 14:33:09",
          resourceId: "",
          baseResourceId: "3338",
          resourceName: "实验室4F",
          resourceCategory: "实验室",
          resourceType: "base",
          resourceOid: "0",
          resourceVal: "/uploads/fx4F.png",
          resourceWidth: "1400",
          resourceHeight: "709",
          resourceOtherData: "",
          deviceDataBase: [
            {
              dmItemId: 1611089,
              dmId: "*********",
              dmItemDataId: "*********",
              dmOid: 0,
              dmName: "二氧化碳浓度",
              dmRelateItemDataId: "",
              dmTag: "status",
              dmNote: "",
              dItemId: "1611089",
              dId: "*********",
              dVal: "",
              dDataType: "string",
              dDataUnit: "",
              dCoefficient: "1.0000",
              dDataMaxVal: "",
              dDataMinVal: "",
              dOtherData: "1:开;0:关",
              dFunc: "read",
              dHasSync: "Y",
              dLocked: "",
              dUpdatedAt: "2024-08-21 10:33:01",
              drId: "",
              drVal: "",
              drDataType: "",
              drDataUnit: "",
              drCoefficient: "",
              drDataMaxVal: "",
              drDataMinVal: "",
              drOtherData: "",
              drFunc: "",
              drHasSync: "",
              drLocked: "",
              drUpdatedAt: "",
            },
          ],
          deviceDataEnv: [],
          deviceDataWarn: [],
          healthPercent: 100,
          warningRuleList: [],
          strategyList: [],
        },
        {
          deviceResourceMapId: 9002649,
          xAxis: 0.0,
          yAxis: 0.0,
          zAxis: 0.0,
          otherData: '{"width":10,"height":10}',
          resourceNote: "",
          id: 1611090,
          type: "CGQ11",
          name: "气质414温湿度传感器-4X",
          brand: "",
          model: "",
          description: "",
          note: "",
          tag: "",
          icon: "",
          thumb: "",
          pictures: "",
          status: "Y",
          workStatus: "",
          warningStatus: "健康",
          communicateStatus: "离线",
          activationTime: "",
          scrapTime: "",
          life: "100%",
          company: "",
          contact: "",
          mobile: "",
          position: "",
          maintenance: "",
          maintenanceSpan: "",
          nameplate: "",
          files: "",
          serviceLife: "",
          ratedPower: "0",
          energyLever: "0",
          createdAt: "2024-08-19 14:33:09",
          resourceId: "",
          baseResourceId: "3338",
          resourceName: "实验室4F",
          resourceCategory: "实验室",
          resourceType: "base",
          resourceOid: "0",
          resourceVal: "/uploads/fx4F.png",
          resourceWidth: "1400",
          resourceHeight: "709",
          resourceOtherData: "",
          deviceDataBase: [
            {
              dmItemId: 1611090,
              dmId: "*********",
              dmItemDataId: "*********",
              dmOid: 0,
              dmName: "二氧化碳浓度",
              dmRelateItemDataId: "",
              dmTag: "status",
              dmNote: "",
              dItemId: "1611090",
              dId: "*********",
              dVal: "",
              dDataType: "string",
              dDataUnit: "",
              dCoefficient: "1.0000",
              dDataMaxVal: "",
              dDataMinVal: "",
              dOtherData: "1:开;0:关",
              dFunc: "read",
              dHasSync: "Y",
              dLocked: "",
              dUpdatedAt: "2024-08-21 10:33:01",
              drId: "",
              drVal: "",
              drDataType: "",
              drDataUnit: "",
              drCoefficient: "",
              drDataMaxVal: "",
              drDataMinVal: "",
              drOtherData: "",
              drFunc: "",
              drHasSync: "",
              drLocked: "",
              drUpdatedAt: "",
            },
          ],
          deviceDataEnv: [],
          deviceDataWarn: [],
          healthPercent: 100,
          warningRuleList: [],
          strategyList: [],
        },
        {
          deviceResourceMapId: 9002648,
          xAxis: 0.0,
          yAxis: 0.0,
          zAxis: 0.0,
          otherData: '{"width":10,"height":10}',
          resourceNote: "",
          id: 1611091,
          type: "CGQ11",
          name: "气相427温湿度传感器-4X",
          brand: "",
          model: "",
          description: "",
          note: "",
          tag: "",
          icon: "",
          thumb: "",
          pictures: "",
          status: "Y",
          workStatus: "",
          warningStatus: "健康",
          communicateStatus: "离线",
          activationTime: "",
          scrapTime: "",
          life: "100%",
          company: "",
          contact: "",
          mobile: "",
          position: "",
          maintenance: "",
          maintenanceSpan: "",
          nameplate: "",
          files: "",
          serviceLife: "",
          ratedPower: "0",
          energyLever: "0",
          createdAt: "2024-08-19 14:33:09",
          resourceId: "",
          baseResourceId: "3338",
          resourceName: "实验室4F",
          resourceCategory: "实验室",
          resourceType: "base",
          resourceOid: "0",
          resourceVal: "/uploads/fx4F.png",
          resourceWidth: "1400",
          resourceHeight: "709",
          resourceOtherData: "",
          deviceDataBase: [
            {
              dmItemId: 1611091,
              dmId: "*********",
              dmItemDataId: "*********",
              dmOid: 0,
              dmName: "二氧化碳浓度",
              dmRelateItemDataId: "",
              dmTag: "status",
              dmNote: "",
              dItemId: "1611091",
              dId: "*********",
              dVal: "",
              dDataType: "string",
              dDataUnit: "",
              dCoefficient: "1.0000",
              dDataMaxVal: "",
              dDataMinVal: "",
              dOtherData: "1:开;0:关",
              dFunc: "read",
              dHasSync: "Y",
              dLocked: "",
              dUpdatedAt: "2024-08-21 10:33:01",
              drId: "",
              drVal: "",
              drDataType: "",
              drDataUnit: "",
              drCoefficient: "",
              drDataMaxVal: "",
              drDataMinVal: "",
              drOtherData: "",
              drFunc: "",
              drHasSync: "",
              drLocked: "",
              drUpdatedAt: "",
            },
          ],
          deviceDataEnv: [],
          deviceDataWarn: [],
          healthPercent: 100,
          warningRuleList: [],
          strategyList: [],
        },
        {
          deviceResourceMapId: 9002634,
          xAxis: 0.0,
          yAxis: 0.0,
          zAxis: 0.0,
          otherData: '{"width":10,"height":10}',
          resourceNote: "",
          id: 1611092,
          type: "CGQ11",
          name: "原吸/ICP413温湿度传感器-4X",
          brand: "",
          model: "",
          description: "",
          note: "",
          tag: "",
          icon: "",
          thumb: "",
          pictures: "",
          status: "Y",
          workStatus: "",
          warningStatus: "健康",
          communicateStatus: "离线",
          activationTime: "",
          scrapTime: "",
          life: "100%",
          company: "",
          contact: "",
          mobile: "",
          position: "",
          maintenance: "",
          maintenanceSpan: "",
          nameplate: "",
          files: "",
          serviceLife: "",
          ratedPower: "0",
          energyLever: "0",
          createdAt: "2024-08-19 14:33:09",
          resourceId: "",
          baseResourceId: "3338",
          resourceName: "实验室4F",
          resourceCategory: "实验室",
          resourceType: "base",
          resourceOid: "0",
          resourceVal: "/uploads/fx4F.png",
          resourceWidth: "1400",
          resourceHeight: "709",
          resourceOtherData: "",
          deviceDataBase: [
            {
              dmItemId: 1611092,
              dmId: "*********",
              dmItemDataId: "*********",
              dmOid: 0,
              dmName: "二氧化碳浓度",
              dmRelateItemDataId: "",
              dmTag: "status",
              dmNote: "",
              dItemId: "1611092",
              dId: "*********",
              dVal: "",
              dDataType: "string",
              dDataUnit: "",
              dCoefficient: "1.0000",
              dDataMaxVal: "",
              dDataMinVal: "",
              dOtherData: "1:开;0:关",
              dFunc: "read",
              dHasSync: "Y",
              dLocked: "",
              dUpdatedAt: "2024-08-21 10:33:01",
              drId: "",
              drVal: "",
              drDataType: "",
              drDataUnit: "",
              drCoefficient: "",
              drDataMaxVal: "",
              drDataMinVal: "",
              drOtherData: "",
              drFunc: "",
              drHasSync: "",
              drLocked: "",
              drUpdatedAt: "",
            },
          ],
          deviceDataEnv: [],
          deviceDataWarn: [],
          healthPercent: 100,
          warningRuleList: [],
          strategyList: [],
        },
        {
          deviceResourceMapId: 9002635,
          xAxis: 0.0,
          yAxis: 0.0,
          zAxis: 0.0,
          otherData: '{"width":10,"height":10}',
          resourceNote: "",
          id: 1611093,
          type: "CGQ11",
          name: "原子荧光412温湿度传感器-4X",
          brand: "",
          model: "",
          description: "",
          note: "",
          tag: "",
          icon: "",
          thumb: "",
          pictures: "",
          status: "Y",
          workStatus: "",
          warningStatus: "健康",
          communicateStatus: "离线",
          activationTime: "",
          scrapTime: "",
          life: "100%",
          company: "",
          contact: "",
          mobile: "",
          position: "",
          maintenance: "",
          maintenanceSpan: "",
          nameplate: "",
          files: "",
          serviceLife: "",
          ratedPower: "0",
          energyLever: "0",
          createdAt: "2024-08-19 14:33:09",
          resourceId: "",
          baseResourceId: "3338",
          resourceName: "实验室4F",
          resourceCategory: "实验室",
          resourceType: "base",
          resourceOid: "0",
          resourceVal: "/uploads/fx4F.png",
          resourceWidth: "1400",
          resourceHeight: "709",
          resourceOtherData: "",
          deviceDataBase: [
            {
              dmItemId: 1611093,
              dmId: "*********",
              dmItemDataId: "*********",
              dmOid: 0,
              dmName: "二氧化碳浓度",
              dmRelateItemDataId: "",
              dmTag: "status",
              dmNote: "",
              dItemId: "1611093",
              dId: "*********",
              dVal: "",
              dDataType: "string",
              dDataUnit: "",
              dCoefficient: "1.0000",
              dDataMaxVal: "",
              dDataMinVal: "",
              dOtherData: "1:开;0:关",
              dFunc: "read",
              dHasSync: "Y",
              dLocked: "",
              dUpdatedAt: "2024-08-21 10:33:01",
              drId: "",
              drVal: "",
              drDataType: "",
              drDataUnit: "",
              drCoefficient: "",
              drDataMaxVal: "",
              drDataMinVal: "",
              drOtherData: "",
              drFunc: "",
              drHasSync: "",
              drLocked: "",
              drUpdatedAt: "",
            },
          ],
          deviceDataEnv: [],
          deviceDataWarn: [],
          healthPercent: 100,
          warningRuleList: [],
          strategyList: [],
        },
        {
          deviceResourceMapId: 9002631,
          xAxis: 0.0,
          yAxis: 0.0,
          zAxis: 0.0,
          otherData: '{"width":10,"height":10}',
          resourceNote: "",
          id: 1611094,
          type: "CGQ11",
          name: "制水间411温湿度传感器-4X",
          brand: "",
          model: "",
          description: "",
          note: "",
          tag: "",
          icon: "",
          thumb: "",
          pictures: "",
          status: "Y",
          workStatus: "",
          warningStatus: "健康",
          communicateStatus: "离线",
          activationTime: "",
          scrapTime: "",
          life: "100%",
          company: "",
          contact: "",
          mobile: "",
          position: "",
          maintenance: "",
          maintenanceSpan: "",
          nameplate: "",
          files: "",
          serviceLife: "",
          ratedPower: "0",
          energyLever: "0",
          createdAt: "2024-08-19 14:33:09",
          resourceId: "",
          baseResourceId: "3338",
          resourceName: "实验室4F",
          resourceCategory: "实验室",
          resourceType: "base",
          resourceOid: "0",
          resourceVal: "/uploads/fx4F.png",
          resourceWidth: "1400",
          resourceHeight: "709",
          resourceOtherData: "",
          deviceDataBase: [
            {
              dmItemId: 1611094,
              dmId: "*********",
              dmItemDataId: "*********",
              dmOid: 0,
              dmName: "二氧化碳浓度",
              dmRelateItemDataId: "",
              dmTag: "status",
              dmNote: "",
              dItemId: "1611094",
              dId: "*********",
              dVal: "",
              dDataType: "string",
              dDataUnit: "",
              dCoefficient: "1.0000",
              dDataMaxVal: "",
              dDataMinVal: "",
              dOtherData: "1:开;0:关",
              dFunc: "read",
              dHasSync: "Y",
              dLocked: "",
              dUpdatedAt: "2024-08-21 10:33:01",
              drId: "",
              drVal: "",
              drDataType: "",
              drDataUnit: "",
              drCoefficient: "",
              drDataMaxVal: "",
              drDataMinVal: "",
              drOtherData: "",
              drFunc: "",
              drHasSync: "",
              drLocked: "",
              drUpdatedAt: "",
            },
          ],
          deviceDataEnv: [],
          deviceDataWarn: [],
          healthPercent: 100,
          warningRuleList: [],
          strategyList: [],
        },
        {
          deviceResourceMapId: 9002645,
          xAxis: 0.0,
          yAxis: 0.0,
          zAxis: 0.0,
          otherData: '{"width":10,"height":10}',
          resourceNote: "",
          id: 1611095,
          type: "CGQ11",
          name: "机房410温湿度传感器-4X",
          brand: "",
          model: "",
          description: "",
          note: "",
          tag: "",
          icon: "",
          thumb: "",
          pictures: "",
          status: "Y",
          workStatus: "",
          warningStatus: "健康",
          communicateStatus: "离线",
          activationTime: "",
          scrapTime: "",
          life: "100%",
          company: "",
          contact: "",
          mobile: "",
          position: "",
          maintenance: "",
          maintenanceSpan: "",
          nameplate: "",
          files: "",
          serviceLife: "",
          ratedPower: "0",
          energyLever: "0",
          createdAt: "2024-08-19 14:33:09",
          resourceId: "",
          baseResourceId: "3338",
          resourceName: "实验室4F",
          resourceCategory: "实验室",
          resourceType: "base",
          resourceOid: "0",
          resourceVal: "/uploads/fx4F.png",
          resourceWidth: "1400",
          resourceHeight: "709",
          resourceOtherData: "",
          deviceDataBase: [
            {
              dmItemId: 1611095,
              dmId: "*********",
              dmItemDataId: "*********",
              dmOid: 0,
              dmName: "二氧化碳浓度",
              dmRelateItemDataId: "",
              dmTag: "status",
              dmNote: "",
              dItemId: "1611095",
              dId: "*********",
              dVal: "",
              dDataType: "string",
              dDataUnit: "",
              dCoefficient: "1.0000",
              dDataMaxVal: "",
              dDataMinVal: "",
              dOtherData: "1:开;0:关",
              dFunc: "read",
              dHasSync: "Y",
              dLocked: "",
              dUpdatedAt: "2024-08-21 10:33:01",
              drId: "",
              drVal: "",
              drDataType: "",
              drDataUnit: "",
              drCoefficient: "",
              drDataMaxVal: "",
              drDataMinVal: "",
              drOtherData: "",
              drFunc: "",
              drHasSync: "",
              drLocked: "",
              drUpdatedAt: "",
            },
          ],
          deviceDataEnv: [],
          deviceDataWarn: [],
          healthPercent: 100,
          warningRuleList: [],
          strategyList: [],
        },
        {
          deviceResourceMapId: 9002642,
          xAxis: 0.0,
          yAxis: 0.0,
          zAxis: 0.0,
          otherData: '{"width":10,"height":10}',
          resourceNote: "",
          id: 1611096,
          type: "CGQ11",
          name: "无机前处理409温湿度传感器-4X",
          brand: "",
          model: "",
          description: "",
          note: "",
          tag: "",
          icon: "",
          thumb: "",
          pictures: "",
          status: "Y",
          workStatus: "",
          warningStatus: "健康",
          communicateStatus: "离线",
          activationTime: "",
          scrapTime: "",
          life: "100%",
          company: "",
          contact: "",
          mobile: "",
          position: "",
          maintenance: "",
          maintenanceSpan: "",
          nameplate: "",
          files: "",
          serviceLife: "",
          ratedPower: "0",
          energyLever: "0",
          createdAt: "2024-08-19 14:33:09",
          resourceId: "",
          baseResourceId: "3338",
          resourceName: "实验室4F",
          resourceCategory: "实验室",
          resourceType: "base",
          resourceOid: "0",
          resourceVal: "/uploads/fx4F.png",
          resourceWidth: "1400",
          resourceHeight: "709",
          resourceOtherData: "",
          deviceDataBase: [
            {
              dmItemId: 1611096,
              dmId: "*********",
              dmItemDataId: "*********",
              dmOid: 0,
              dmName: "二氧化碳浓度",
              dmRelateItemDataId: "",
              dmTag: "status",
              dmNote: "",
              dItemId: "1611096",
              dId: "*********",
              dVal: "",
              dDataType: "string",
              dDataUnit: "",
              dCoefficient: "1.0000",
              dDataMaxVal: "",
              dDataMinVal: "",
              dOtherData: "1:开;0:关",
              dFunc: "read",
              dHasSync: "Y",
              dLocked: "",
              dUpdatedAt: "2024-08-21 10:33:01",
              drId: "",
              drVal: "",
              drDataType: "",
              drDataUnit: "",
              drCoefficient: "",
              drDataMaxVal: "",
              drDataMinVal: "",
              drOtherData: "",
              drFunc: "",
              drHasSync: "",
              drLocked: "",
              drUpdatedAt: "",
            },
          ],
          deviceDataEnv: [],
          deviceDataWarn: [],
          healthPercent: 100,
          warningRuleList: [],
          strategyList: [],
        },
        {
          deviceResourceMapId: 9002644,
          xAxis: 0.0,
          yAxis: 0.0,
          zAxis: 0.0,
          otherData: '{"width":10,"height":10}',
          resourceNote: "",
          id: 1611097,
          type: "CGQ11",
          name: "有机前处理408温湿度传感器-4X",
          brand: "",
          model: "",
          description: "",
          note: "",
          tag: "",
          icon: "",
          thumb: "",
          pictures: "",
          status: "Y",
          workStatus: "",
          warningStatus: "健康",
          communicateStatus: "离线",
          activationTime: "",
          scrapTime: "",
          life: "100%",
          company: "",
          contact: "",
          mobile: "",
          position: "",
          maintenance: "",
          maintenanceSpan: "",
          nameplate: "",
          files: "",
          serviceLife: "",
          ratedPower: "0",
          energyLever: "0",
          createdAt: "2024-08-19 14:33:09",
          resourceId: "",
          baseResourceId: "3338",
          resourceName: "实验室4F",
          resourceCategory: "实验室",
          resourceType: "base",
          resourceOid: "0",
          resourceVal: "/uploads/fx4F.png",
          resourceWidth: "1400",
          resourceHeight: "709",
          resourceOtherData: "",
          deviceDataBase: [
            {
              dmItemId: 1611097,
              dmId: "*********",
              dmItemDataId: "*********",
              dmOid: 0,
              dmName: "二氧化碳浓度",
              dmRelateItemDataId: "",
              dmTag: "status",
              dmNote: "",
              dItemId: "1611097",
              dId: "*********",
              dVal: "",
              dDataType: "string",
              dDataUnit: "",
              dCoefficient: "1.0000",
              dDataMaxVal: "",
              dDataMinVal: "",
              dOtherData: "1:开;0:关",
              dFunc: "read",
              dHasSync: "Y",
              dLocked: "",
              dUpdatedAt: "2024-08-21 10:33:01",
              drId: "",
              drVal: "",
              drDataType: "",
              drDataUnit: "",
              drCoefficient: "",
              drDataMaxVal: "",
              drDataMinVal: "",
              drOtherData: "",
              drFunc: "",
              drHasSync: "",
              drLocked: "",
              drUpdatedAt: "",
            },
          ],
          deviceDataEnv: [],
          deviceDataWarn: [],
          healthPercent: 100,
          warningRuleList: [],
          strategyList: [],
        },
        {
          deviceResourceMapId: 9002657,
          xAxis: 0.0,
          yAxis: 0.0,
          zAxis: 0.0,
          otherData: '{"width":10,"height":10}',
          resourceNote: "",
          id: 1611098,
          type: "CGQ11",
          name: "预留428温湿度传感器-4X",
          brand: "",
          model: "",
          description: "",
          note: "",
          tag: "",
          icon: "",
          thumb: "",
          pictures: "",
          status: "Y",
          workStatus: "",
          warningStatus: "健康",
          communicateStatus: "离线",
          activationTime: "",
          scrapTime: "",
          life: "100%",
          company: "",
          contact: "",
          mobile: "",
          position: "",
          maintenance: "",
          maintenanceSpan: "",
          nameplate: "",
          files: "",
          serviceLife: "",
          ratedPower: "0",
          energyLever: "0",
          createdAt: "2024-08-19 14:33:09",
          resourceId: "",
          baseResourceId: "3338",
          resourceName: "实验室4F",
          resourceCategory: "实验室",
          resourceType: "base",
          resourceOid: "0",
          resourceVal: "/uploads/fx4F.png",
          resourceWidth: "1400",
          resourceHeight: "709",
          resourceOtherData: "",
          deviceDataBase: [
            {
              dmItemId: 1611098,
              dmId: "*********",
              dmItemDataId: "*********",
              dmOid: 0,
              dmName: "二氧化碳浓度",
              dmRelateItemDataId: "",
              dmTag: "status",
              dmNote: "",
              dItemId: "1611098",
              dId: "*********",
              dVal: "",
              dDataType: "string",
              dDataUnit: "",
              dCoefficient: "1.0000",
              dDataMaxVal: "",
              dDataMinVal: "",
              dOtherData: "1:开;0:关",
              dFunc: "read",
              dHasSync: "Y",
              dLocked: "",
              dUpdatedAt: "2024-08-21 10:33:01",
              drId: "",
              drVal: "",
              drDataType: "",
              drDataUnit: "",
              drCoefficient: "",
              drDataMaxVal: "",
              drDataMinVal: "",
              drOtherData: "",
              drFunc: "",
              drHasSync: "",
              drLocked: "",
              drUpdatedAt: "",
            },
          ],
          deviceDataEnv: [],
          deviceDataWarn: [],
          healthPercent: 100,
          warningRuleList: [],
          strategyList: [],
        },
        {
          deviceResourceMapId: 9002651,
          xAxis: 0.0,
          yAxis: 0.0,
          zAxis: 0.0,
          otherData: '{"width":10,"height":10}',
          resourceNote: "",
          id: 1611099,
          type: "CGQ11",
          name: "液相429温湿度传感器-4X",
          brand: "",
          model: "",
          description: "",
          note: "",
          tag: "",
          icon: "",
          thumb: "",
          pictures: "",
          status: "Y",
          workStatus: "",
          warningStatus: "健康",
          communicateStatus: "离线",
          activationTime: "",
          scrapTime: "",
          life: "100%",
          company: "",
          contact: "",
          mobile: "",
          position: "",
          maintenance: "",
          maintenanceSpan: "",
          nameplate: "",
          files: "",
          serviceLife: "",
          ratedPower: "0",
          energyLever: "0",
          createdAt: "2024-08-19 14:33:09",
          resourceId: "",
          baseResourceId: "3338",
          resourceName: "实验室4F",
          resourceCategory: "实验室",
          resourceType: "base",
          resourceOid: "0",
          resourceVal: "/uploads/fx4F.png",
          resourceWidth: "1400",
          resourceHeight: "709",
          resourceOtherData: "",
          deviceDataBase: [
            {
              dmItemId: 1611099,
              dmId: "*********",
              dmItemDataId: "*********",
              dmOid: 0,
              dmName: "二氧化碳浓度",
              dmRelateItemDataId: "",
              dmTag: "status",
              dmNote: "",
              dItemId: "1611099",
              dId: "*********",
              dVal: "",
              dDataType: "string",
              dDataUnit: "",
              dCoefficient: "1.0000",
              dDataMaxVal: "",
              dDataMinVal: "",
              dOtherData: "1:开;0:关",
              dFunc: "read",
              dHasSync: "Y",
              dLocked: "",
              dUpdatedAt: "2024-08-21 10:33:01",
              drId: "",
              drVal: "",
              drDataType: "",
              drDataUnit: "",
              drCoefficient: "",
              drDataMaxVal: "",
              drDataMinVal: "",
              drOtherData: "",
              drFunc: "",
              drHasSync: "",
              drLocked: "",
              drUpdatedAt: "",
            },
          ],
          deviceDataEnv: [],
          deviceDataWarn: [],
          healthPercent: 100,
          warningRuleList: [],
          strategyList: [],
        },
        {
          deviceResourceMapId: 9002655,
          xAxis: 0.0,
          yAxis: 0.0,
          zAxis: 0.0,
          otherData: '{"width":10,"height":10}',
          resourceNote: "",
          id: 1611100,
          type: "CGQ11",
          name: "离子色谱流动注射430温湿度传感器-4X",
          brand: "",
          model: "",
          description: "",
          note: "",
          tag: "",
          icon: "",
          thumb: "",
          pictures: "",
          status: "Y",
          workStatus: "",
          warningStatus: "健康",
          communicateStatus: "离线",
          activationTime: "",
          scrapTime: "",
          life: "100%",
          company: "",
          contact: "",
          mobile: "",
          position: "",
          maintenance: "",
          maintenanceSpan: "",
          nameplate: "",
          files: "",
          serviceLife: "",
          ratedPower: "0",
          energyLever: "0",
          createdAt: "2024-08-19 14:33:09",
          resourceId: "",
          baseResourceId: "3338",
          resourceName: "实验室4F",
          resourceCategory: "实验室",
          resourceType: "base",
          resourceOid: "0",
          resourceVal: "/uploads/fx4F.png",
          resourceWidth: "1400",
          resourceHeight: "709",
          resourceOtherData: "",
          deviceDataBase: [
            {
              dmItemId: 1611100,
              dmId: "*********",
              dmItemDataId: "*********",
              dmOid: 0,
              dmName: "二氧化碳浓度",
              dmRelateItemDataId: "",
              dmTag: "status",
              dmNote: "",
              dItemId: "1611100",
              dId: "*********",
              dVal: "",
              dDataType: "string",
              dDataUnit: "",
              dCoefficient: "1.0000",
              dDataMaxVal: "",
              dDataMinVal: "",
              dOtherData: "1:开;0:关",
              dFunc: "read",
              dHasSync: "Y",
              dLocked: "",
              dUpdatedAt: "2024-08-21 10:33:01",
              drId: "",
              drVal: "",
              drDataType: "",
              drDataUnit: "",
              drCoefficient: "",
              drDataMaxVal: "",
              drDataMinVal: "",
              drOtherData: "",
              drFunc: "",
              drHasSync: "",
              drLocked: "",
              drUpdatedAt: "",
            },
          ],
          deviceDataEnv: [],
          deviceDataWarn: [],
          healthPercent: 100,
          warningRuleList: [],
          strategyList: [],
        },
        {
          deviceResourceMapId: 9002633,
          xAxis: 0.0,
          yAxis: 0.0,
          zAxis: 0.0,
          otherData: '{"width":10,"height":10}',
          resourceNote: "",
          id: 1611101,
          type: "CGQ11",
          name: "化学分析室（食品、环境）406温湿度传感器-4X",
          brand: "",
          model: "",
          description: "",
          note: "",
          tag: "",
          icon: "",
          thumb: "",
          pictures: "",
          status: "Y",
          workStatus: "",
          warningStatus: "健康",
          communicateStatus: "离线",
          activationTime: "",
          scrapTime: "",
          life: "100%",
          company: "",
          contact: "",
          mobile: "",
          position: "",
          maintenance: "",
          maintenanceSpan: "",
          nameplate: "",
          files: "",
          serviceLife: "",
          ratedPower: "0",
          energyLever: "0",
          createdAt: "2024-08-19 14:33:09",
          resourceId: "",
          baseResourceId: "3338",
          resourceName: "实验室4F",
          resourceCategory: "实验室",
          resourceType: "base",
          resourceOid: "0",
          resourceVal: "/uploads/fx4F.png",
          resourceWidth: "1400",
          resourceHeight: "709",
          resourceOtherData: "",
          deviceDataBase: [
            {
              dmItemId: 1611101,
              dmId: "*********",
              dmItemDataId: "*********",
              dmOid: 0,
              dmName: "二氧化碳浓度",
              dmRelateItemDataId: "",
              dmTag: "status",
              dmNote: "",
              dItemId: "1611101",
              dId: "*********",
              dVal: "",
              dDataType: "string",
              dDataUnit: "",
              dCoefficient: "1.0000",
              dDataMaxVal: "",
              dDataMinVal: "",
              dOtherData: "1:开;0:关",
              dFunc: "read",
              dHasSync: "Y",
              dLocked: "",
              dUpdatedAt: "2024-08-21 10:33:01",
              drId: "",
              drVal: "",
              drDataType: "",
              drDataUnit: "",
              drCoefficient: "",
              drDataMaxVal: "",
              drDataMinVal: "",
              drOtherData: "",
              drFunc: "",
              drHasSync: "",
              drLocked: "",
              drUpdatedAt: "",
            },
          ],
          deviceDataEnv: [],
          deviceDataWarn: [],
          healthPercent: 100,
          warningRuleList: [],
          strategyList: [],
        },
        {
          deviceResourceMapId: 9002658,
          xAxis: 0.0,
          yAxis: 0.0,
          zAxis: 0.0,
          otherData: '{"width":10,"height":10}',
          resourceNote: "",
          id: 1611102,
          type: "CGQ11",
          name: "高温室407温湿度传感器-4X",
          brand: "",
          model: "",
          description: "",
          note: "",
          tag: "",
          icon: "",
          thumb: "",
          pictures: "",
          status: "Y",
          workStatus: "",
          warningStatus: "健康",
          communicateStatus: "离线",
          activationTime: "",
          scrapTime: "",
          life: "100%",
          company: "",
          contact: "",
          mobile: "",
          position: "",
          maintenance: "",
          maintenanceSpan: "",
          nameplate: "",
          files: "",
          serviceLife: "",
          ratedPower: "0",
          energyLever: "0",
          createdAt: "2024-08-19 14:33:09",
          resourceId: "",
          baseResourceId: "3338",
          resourceName: "实验室4F",
          resourceCategory: "实验室",
          resourceType: "base",
          resourceOid: "0",
          resourceVal: "/uploads/fx4F.png",
          resourceWidth: "1400",
          resourceHeight: "709",
          resourceOtherData: "",
          deviceDataBase: [
            {
              dmItemId: 1611102,
              dmId: "*********",
              dmItemDataId: "*********",
              dmOid: 0,
              dmName: "二氧化碳浓度",
              dmRelateItemDataId: "",
              dmTag: "status",
              dmNote: "",
              dItemId: "1611102",
              dId: "*********",
              dVal: "",
              dDataType: "string",
              dDataUnit: "",
              dCoefficient: "1.0000",
              dDataMaxVal: "",
              dDataMinVal: "",
              dOtherData: "1:开;0:关",
              dFunc: "read",
              dHasSync: "Y",
              dLocked: "",
              dUpdatedAt: "2024-08-21 10:33:01",
              drId: "",
              drVal: "",
              drDataType: "",
              drDataUnit: "",
              drCoefficient: "",
              drDataMaxVal: "",
              drDataMinVal: "",
              drOtherData: "",
              drFunc: "",
              drHasSync: "",
              drLocked: "",
              drUpdatedAt: "",
            },
          ],
          deviceDataEnv: [],
          deviceDataWarn: [],
          healthPercent: 100,
          warningRuleList: [],
          strategyList: [],
        },
        {
          deviceResourceMapId: 9002656,
          xAxis: 0.0,
          yAxis: 0.0,
          zAxis: 0.0,
          otherData: '{"width":10,"height":10}',
          resourceNote: "",
          id: 1611103,
          type: "CGQ11",
          name: "紫外431温湿度传感器-4X",
          brand: "",
          model: "",
          description: "",
          note: "",
          tag: "",
          icon: "",
          thumb: "",
          pictures: "",
          status: "Y",
          workStatus: "",
          warningStatus: "健康",
          communicateStatus: "离线",
          activationTime: "",
          scrapTime: "",
          life: "100%",
          company: "",
          contact: "",
          mobile: "",
          position: "",
          maintenance: "",
          maintenanceSpan: "",
          nameplate: "",
          files: "",
          serviceLife: "",
          ratedPower: "0",
          energyLever: "0",
          createdAt: "2024-08-19 14:33:09",
          resourceId: "",
          baseResourceId: "3338",
          resourceName: "实验室4F",
          resourceCategory: "实验室",
          resourceType: "base",
          resourceOid: "0",
          resourceVal: "/uploads/fx4F.png",
          resourceWidth: "1400",
          resourceHeight: "709",
          resourceOtherData: "",
          deviceDataBase: [
            {
              dmItemId: 1611103,
              dmId: "*********",
              dmItemDataId: "*********",
              dmOid: 0,
              dmName: "二氧化碳浓度",
              dmRelateItemDataId: "",
              dmTag: "status",
              dmNote: "",
              dItemId: "1611103",
              dId: "*********",
              dVal: "",
              dDataType: "string",
              dDataUnit: "",
              dCoefficient: "1.0000",
              dDataMaxVal: "",
              dDataMinVal: "",
              dOtherData: "1:开;0:关",
              dFunc: "read",
              dHasSync: "Y",
              dLocked: "",
              dUpdatedAt: "2024-08-21 10:33:01",
              drId: "",
              drVal: "",
              drDataType: "",
              drDataUnit: "",
              drCoefficient: "",
              drDataMaxVal: "",
              drDataMinVal: "",
              drOtherData: "",
              drFunc: "",
              drHasSync: "",
              drLocked: "",
              drUpdatedAt: "",
            },
          ],
          deviceDataEnv: [],
          deviceDataWarn: [],
          healthPercent: 100,
          warningRuleList: [],
          strategyList: [],
        },
        {
          deviceResourceMapId: 9002637,
          xAxis: 0.0,
          yAxis: 0.0,
          zAxis: 0.0,
          otherData: '{"width":10,"height":10}',
          resourceNote: "",
          id: 1611104,
          type: "CGQ11",
          name: "天平室432温湿度传感器-4X",
          brand: "",
          model: "",
          description: "",
          note: "",
          tag: "",
          icon: "",
          thumb: "",
          pictures: "",
          status: "Y",
          workStatus: "",
          warningStatus: "健康",
          communicateStatus: "离线",
          activationTime: "",
          scrapTime: "",
          life: "100%",
          company: "",
          contact: "",
          mobile: "",
          position: "",
          maintenance: "",
          maintenanceSpan: "",
          nameplate: "",
          files: "",
          serviceLife: "",
          ratedPower: "0",
          energyLever: "0",
          createdAt: "2024-08-19 14:33:09",
          resourceId: "",
          baseResourceId: "3338",
          resourceName: "实验室4F",
          resourceCategory: "实验室",
          resourceType: "base",
          resourceOid: "0",
          resourceVal: "/uploads/fx4F.png",
          resourceWidth: "1400",
          resourceHeight: "709",
          resourceOtherData: "",
          deviceDataBase: [
            {
              dmItemId: 1611104,
              dmId: "*********",
              dmItemDataId: "*********",
              dmOid: 0,
              dmName: "二氧化碳浓度",
              dmRelateItemDataId: "",
              dmTag: "status",
              dmNote: "",
              dItemId: "1611104",
              dId: "*********",
              dVal: "",
              dDataType: "string",
              dDataUnit: "",
              dCoefficient: "1.0000",
              dDataMaxVal: "",
              dDataMinVal: "",
              dOtherData: "1:开;0:关",
              dFunc: "read",
              dHasSync: "Y",
              dLocked: "",
              dUpdatedAt: "2024-08-21 10:33:01",
              drId: "",
              drVal: "",
              drDataType: "",
              drDataUnit: "",
              drCoefficient: "",
              drDataMaxVal: "",
              drDataMinVal: "",
              drOtherData: "",
              drFunc: "",
              drHasSync: "",
              drLocked: "",
              drUpdatedAt: "",
            },
          ],
          deviceDataEnv: [],
          deviceDataWarn: [],
          healthPercent: 100,
          warningRuleList: [],
          strategyList: [],
        },
        {
          deviceResourceMapId: 9002641,
          xAxis: 0.0,
          yAxis: 0.0,
          zAxis: 0.0,
          otherData: '{"width":10,"height":10}',
          resourceNote: "",
          id: 1611105,
          type: "CGQ11",
          name: "放射间（环卫科）仪器分析404温湿度传感器-4X",
          brand: "",
          model: "",
          description: "",
          note: "",
          tag: "",
          icon: "",
          thumb: "",
          pictures: "",
          status: "Y",
          workStatus: "",
          warningStatus: "健康",
          communicateStatus: "离线",
          activationTime: "",
          scrapTime: "",
          life: "100%",
          company: "",
          contact: "",
          mobile: "",
          position: "",
          maintenance: "",
          maintenanceSpan: "",
          nameplate: "",
          files: "",
          serviceLife: "",
          ratedPower: "0",
          energyLever: "0",
          createdAt: "2024-08-19 14:33:09",
          resourceId: "",
          baseResourceId: "3338",
          resourceName: "实验室4F",
          resourceCategory: "实验室",
          resourceType: "base",
          resourceOid: "0",
          resourceVal: "/uploads/fx4F.png",
          resourceWidth: "1400",
          resourceHeight: "709",
          resourceOtherData: "",
          deviceDataBase: [
            {
              dmItemId: 1611105,
              dmId: "*********",
              dmItemDataId: "*********",
              dmOid: 0,
              dmName: "二氧化碳浓度",
              dmRelateItemDataId: "",
              dmTag: "status",
              dmNote: "",
              dItemId: "1611105",
              dId: "*********",
              dVal: "",
              dDataType: "string",
              dDataUnit: "",
              dCoefficient: "1.0000",
              dDataMaxVal: "",
              dDataMinVal: "",
              dOtherData: "1:开;0:关",
              dFunc: "read",
              dHasSync: "Y",
              dLocked: "",
              dUpdatedAt: "2024-08-21 10:33:01",
              drId: "",
              drVal: "",
              drDataType: "",
              drDataUnit: "",
              drCoefficient: "",
              drDataMaxVal: "",
              drDataMinVal: "",
              drOtherData: "",
              drFunc: "",
              drHasSync: "",
              drLocked: "",
              drUpdatedAt: "",
            },
          ],
          deviceDataEnv: [],
          deviceDataWarn: [],
          healthPercent: 100,
          warningRuleList: [],
          strategyList: [],
        },
        {
          deviceResourceMapId: 9002647,
          xAxis: 0.0,
          yAxis: 0.0,
          zAxis: 0.0,
          otherData: '{"width":10,"height":10}',
          resourceNote: "",
          id: 1611106,
          type: "CGQ11",
          name: "样品准备405温湿度传感器-4X",
          brand: "",
          model: "",
          description: "",
          note: "",
          tag: "",
          icon: "",
          thumb: "",
          pictures: "",
          status: "Y",
          workStatus: "",
          warningStatus: "健康",
          communicateStatus: "离线",
          activationTime: "",
          scrapTime: "",
          life: "100%",
          company: "",
          contact: "",
          mobile: "",
          position: "",
          maintenance: "",
          maintenanceSpan: "",
          nameplate: "",
          files: "",
          serviceLife: "",
          ratedPower: "0",
          energyLever: "0",
          createdAt: "2024-08-19 14:33:09",
          resourceId: "",
          baseResourceId: "3338",
          resourceName: "实验室4F",
          resourceCategory: "实验室",
          resourceType: "base",
          resourceOid: "0",
          resourceVal: "/uploads/fx4F.png",
          resourceWidth: "1400",
          resourceHeight: "709",
          resourceOtherData: "",
          deviceDataBase: [
            {
              dmItemId: 1611106,
              dmId: "*********",
              dmItemDataId: "*********",
              dmOid: 0,
              dmName: "二氧化碳浓度",
              dmRelateItemDataId: "",
              dmTag: "status",
              dmNote: "",
              dItemId: "1611106",
              dId: "*********",
              dVal: "",
              dDataType: "string",
              dDataUnit: "",
              dCoefficient: "1.0000",
              dDataMaxVal: "",
              dDataMinVal: "",
              dOtherData: "1:开;0:关",
              dFunc: "read",
              dHasSync: "Y",
              dLocked: "",
              dUpdatedAt: "2024-08-21 10:33:01",
              drId: "",
              drVal: "",
              drDataType: "",
              drDataUnit: "",
              drCoefficient: "",
              drDataMaxVal: "",
              drDataMinVal: "",
              drOtherData: "",
              drFunc: "",
              drHasSync: "",
              drLocked: "",
              drUpdatedAt: "",
            },
          ],
          deviceDataEnv: [],
          deviceDataWarn: [],
          healthPercent: 100,
          warningRuleList: [],
          strategyList: [],
        },
        {
          deviceResourceMapId: 9002646,
          xAxis: 0.0,
          yAxis: 0.0,
          zAxis: 0.0,
          otherData: '{"width":10,"height":10}',
          resourceNote: "",
          id: 1611107,
          type: "CGQ11",
          name: "标准物质433温湿度传感器-4X",
          brand: "",
          model: "",
          description: "",
          note: "",
          tag: "",
          icon: "",
          thumb: "",
          pictures: "",
          status: "Y",
          workStatus: "",
          warningStatus: "健康",
          communicateStatus: "离线",
          activationTime: "",
          scrapTime: "",
          life: "100%",
          company: "",
          contact: "",
          mobile: "",
          position: "",
          maintenance: "",
          maintenanceSpan: "",
          nameplate: "",
          files: "",
          serviceLife: "",
          ratedPower: "0",
          energyLever: "0",
          createdAt: "2024-08-19 14:33:09",
          resourceId: "",
          baseResourceId: "3338",
          resourceName: "实验室4F",
          resourceCategory: "实验室",
          resourceType: "base",
          resourceOid: "0",
          resourceVal: "/uploads/fx4F.png",
          resourceWidth: "1400",
          resourceHeight: "709",
          resourceOtherData: "",
          deviceDataBase: [
            {
              dmItemId: 1611107,
              dmId: "*********",
              dmItemDataId: "*********",
              dmOid: 0,
              dmName: "二氧化碳浓度",
              dmRelateItemDataId: "",
              dmTag: "status",
              dmNote: "",
              dItemId: "1611107",
              dId: "*********",
              dVal: "",
              dDataType: "string",
              dDataUnit: "",
              dCoefficient: "1.0000",
              dDataMaxVal: "",
              dDataMinVal: "",
              dOtherData: "1:开;0:关",
              dFunc: "read",
              dHasSync: "Y",
              dLocked: "",
              dUpdatedAt: "2024-08-21 10:33:01",
              drId: "",
              drVal: "",
              drDataType: "",
              drDataUnit: "",
              drCoefficient: "",
              drDataMaxVal: "",
              drDataMinVal: "",
              drOtherData: "",
              drFunc: "",
              drHasSync: "",
              drLocked: "",
              drUpdatedAt: "",
            },
          ],
          deviceDataEnv: [],
          deviceDataWarn: [],
          healthPercent: 100,
          warningRuleList: [],
          strategyList: [],
        },
        {
          deviceResourceMapId: 9002640,
          xAxis: 0.0,
          yAxis: 0.0,
          zAxis: 0.0,
          otherData: '{"width":10,"height":10}',
          resourceNote: "",
          id: 1611109,
          type: "CGQ11",
          name: "收样室401温湿度传感器-4X",
          brand: "",
          model: "",
          description: "",
          note: "",
          tag: "",
          icon: "",
          thumb: "",
          pictures: "",
          status: "Y",
          workStatus: "",
          warningStatus: "健康",
          communicateStatus: "离线",
          activationTime: "",
          scrapTime: "",
          life: "100%",
          company: "",
          contact: "",
          mobile: "",
          position: "",
          maintenance: "",
          maintenanceSpan: "",
          nameplate: "",
          files: "",
          serviceLife: "",
          ratedPower: "0",
          energyLever: "0",
          createdAt: "2024-08-19 14:33:09",
          resourceId: "",
          baseResourceId: "3338",
          resourceName: "实验室4F",
          resourceCategory: "实验室",
          resourceType: "base",
          resourceOid: "0",
          resourceVal: "/uploads/fx4F.png",
          resourceWidth: "1400",
          resourceHeight: "709",
          resourceOtherData: "",
          deviceDataBase: [
            {
              dmItemId: 1611109,
              dmId: "*********",
              dmItemDataId: "*********",
              dmOid: 0,
              dmName: "二氧化碳浓度",
              dmRelateItemDataId: "",
              dmTag: "status",
              dmNote: "",
              dItemId: "1611109",
              dId: "*********",
              dVal: "",
              dDataType: "string",
              dDataUnit: "",
              dCoefficient: "1.0000",
              dDataMaxVal: "",
              dDataMinVal: "",
              dOtherData: "1:开;0:关",
              dFunc: "read",
              dHasSync: "Y",
              dLocked: "",
              dUpdatedAt: "2024-08-21 10:33:01",
              drId: "",
              drVal: "",
              drDataType: "",
              drDataUnit: "",
              drCoefficient: "",
              drDataMaxVal: "",
              drDataMinVal: "",
              drOtherData: "",
              drFunc: "",
              drHasSync: "",
              drLocked: "",
              drUpdatedAt: "",
            },
          ],
          deviceDataEnv: [],
          deviceDataWarn: [],
          healthPercent: 100,
          warningRuleList: [],
          strategyList: [],
        },
        {
          deviceResourceMapId: 9002636,
          xAxis: 0.0,
          yAxis: 0.0,
          zAxis: 0.0,
          otherData: '{"width":10,"height":10}',
          resourceNote: "",
          id: 1611110,
          type: "CGQ11",
          name: "器皿室403温湿度传感器-4X",
          brand: "",
          model: "",
          description: "",
          note: "",
          tag: "",
          icon: "",
          thumb: "",
          pictures: "",
          status: "Y",
          workStatus: "",
          warningStatus: "健康",
          communicateStatus: "离线",
          activationTime: "",
          scrapTime: "",
          life: "100%",
          company: "",
          contact: "",
          mobile: "",
          position: "",
          maintenance: "",
          maintenanceSpan: "",
          nameplate: "",
          files: "",
          serviceLife: "",
          ratedPower: "0",
          energyLever: "0",
          createdAt: "2024-08-19 14:33:09",
          resourceId: "",
          baseResourceId: "3338",
          resourceName: "实验室4F",
          resourceCategory: "实验室",
          resourceType: "base",
          resourceOid: "0",
          resourceVal: "/uploads/fx4F.png",
          resourceWidth: "1400",
          resourceHeight: "709",
          resourceOtherData: "",
          deviceDataBase: [
            {
              dmItemId: 1611110,
              dmId: "*********",
              dmItemDataId: "*********",
              dmOid: 0,
              dmName: "二氧化碳浓度",
              dmRelateItemDataId: "",
              dmTag: "status",
              dmNote: "",
              dItemId: "1611110",
              dId: "*********",
              dVal: "",
              dDataType: "string",
              dDataUnit: "",
              dCoefficient: "1.0000",
              dDataMaxVal: "",
              dDataMinVal: "",
              dOtherData: "1:开;0:关",
              dFunc: "read",
              dHasSync: "Y",
              dLocked: "",
              dUpdatedAt: "2024-08-21 10:33:01",
              drId: "",
              drVal: "",
              drDataType: "",
              drDataUnit: "",
              drCoefficient: "",
              drDataMaxVal: "",
              drDataMinVal: "",
              drOtherData: "",
              drFunc: "",
              drHasSync: "",
              drLocked: "",
              drUpdatedAt: "",
            },
          ],
          deviceDataEnv: [],
          deviceDataWarn: [],
          healthPercent: 100,
          warningRuleList: [],
          strategyList: [],
        },
      ],

      input: "",
      activeTab: "today",
      listst: [
        {
          name: "广东质检中诚认证有限公司到中广...",
        },
        { name: "材料科学、化学工程及医药研发成..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
        { name: "植酸检测方法及作用" },
        { name: "兔拉检测：行业中芯片造假技术..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
      ],
      showdh: true,
      showdh1: false,
      noAnimation: false,
      selectedItem: null,
      ids: null,
      nhlist: [
        {
          title: "供气压力",
          status: "0.3Mpa",
          unit: "℃",
        },

        {
          title: "供气流量",
          status: "6M3/min",
          unit: "㎡",
        },
        {
          title: "露点温度",
          status: "6℃",
          unit: "℃",
        },
        {
          title: "含氧量",
          status: "6PPM",
          unit: "㎡",
        },
      ],
      warnlist1: [
        {
          type: 1,
          name: "检测到烟雾，可能有着火灾的发生...",
          value: "",
          time: "09:13:59  2023-06-07",
        },
        {
          type: 2,
          name: "检测到烟雾，可能预示着火灾的发生..",
          value: "",
          time: "10:13:18  2023-06-12",
        },
        {
          type: 3,
          name: "实验室内检测到漏水，可能来自冷凝水..",
          value: "",
          time: "10:13:18  2023-06-12",
        },
      ],
      isButton2Active: false,
      status: "巡检中",
      status1: "已完成",
      status2: "待巡检",
      selectedIndex: 0,
      componentTag: "component0",
    };
  },
  // 计算属性类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {},
  // 方法集合
  methods: {
    showdetails(item) {
      if (item.ids) {
        this.ids = item.ids;
      }

      this.isshow = true;
      this.selectedItem = item;
    },
    hidedetails() {
      this.isshow = false;
    },
    oc(value) {
      console.log(value, "收到的值");
      this.showdh = value;
    },
  },
  // 生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.showdh1 = true;
    setTimeout(() => {
      this.showdh1 = false;
      this.noAnimation = false;
    }, 1000); // 动画持续时间为1秒
    console.log(1222);
  },
  beforeCreate() {}, // 生命周期 - 创建之前
  beforeMount() {}, // 生命周期 - 挂载之前
  beforeUpdate() {}, // 生命周期 - 更新之前
  updated() {}, // 生命周期 - 更新之后
  beforeUnmount() {
    // 在组件销毁之前清除定时器
    console.log(1111);
  },

  unmounted() {
    console.log(2222);
  }, // 生命周期 - 销毁之前
  destroyed() {
    console.log(1221);
  }, // 生命周期 - 销毁完成
  activated() {}, // 如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="less" scoped>
.container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: stretch;
  height: 1080px;
  text-align: center;

  .sbdetails {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
  }

  .box {
    margin-top: 6px;
    padding-top: 5px;
    margin-bottom: 0.225rem;
height: 800px;
    width: 330px;
    // height: 800px;

    overflow-y: scroll;

    /* 设置垂直滚动条 */
    /* 设置滚动条的样式 */
    &::-webkit-scrollbar {
      width: 0.1px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条轨道的样式 */
    &::-webkit-scrollbar-track {
      background-color: #f1f1f1;
      /* 设置滚动条轨道的背景色 */
    }

    /* 设置滚动条滑块的样式 */
    &::-webkit-scrollbar-thumb {
      background-color: #334f6e;
      /* 设置滚动条滑块的背景色 */
    }

    /* 鼠标悬停在滚动条上时的样式 */
    &::-webkit-scrollbar-thumb:hover {
      background-color: #555;
      /* 设置鼠标悬停时滚动条滑块的背景色 */
    }

    .el-input {
      width: 305px;
      height: 34px;
      color: #fff !important;

      ::v-deep .el-input__wrapper {
        background: url("../assets/image/inputss.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        box-shadow: none !important;
      }
    }

    .suosuo {
      position: absolute;
      top: 66px;
      left: 285px;
    }

    .xiaobox {
      margin-top: 20px;
      display: flex;
      align-items: center;

      .siqiu {
        width: 16px;
        height: 16px;
      }

      .shuru {
        display: flex;
        align-items: center;
        padding-left: 10px;
        background: url("../assets/image/shebei12.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 261px;
        height: 26px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 12px;
        color: #ffffff;
      }
    }

    .xiaoboxs {
      
      cursor: pointer;
      margin-top: 14px;
      display: flex;
      margin-left: 5px;
      align-items: center;
     
      .nihaowo {
        width: 78px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 10px;
        color: #ffffff;
        display: flex;
      }

      .siqiu {
        width: 16px;
        height: 16px;
        margin-left: 10px;
        margin-right: 7px;
      }

      .shuru {
        display: flex;
        align-items: center;
        padding-left: 10px;
        background: url("../assets/image/shebei12.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 261px;
        height: 26px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 12px;
        color: #ffffff;
      }
    }
  }

  .left-panel {
    position: fixed;
    z-index: 1;
    top: 100px;
    left: 22px;
    width: 330px;
    height: 937px;

    background-size: 100% 100%;
    transform: translate(-122%);
    transition: transform 0.5s ease-in-out;
  }

  .left-panel-active {
    transform: translate(0%);
  }

  .left-panel-active1 {
    // transform: translate(0%);
    animation: slideOut 1s ease-in-out forwards;
  }

  @keyframes slideOut {
    100% {
      transform: translateX(0%);
    }

    // 85% {
    //   transform: translateX(-25%);
    // }

    // 65% {
    //   transform: translateX(-15%);
    // }

    // 40% {
    //   transform: translateX(-55%);
    // }

    // 30% {
    //   transform: translateX(-40%);
    // }

    0% {
      transform: translateX(-100%);
    }
  }

  .rtitle {
    margin-top: 16px;
  }

  @keyframes slideIn {
    0% {
      transform: translateX(100%);
    }

    // 30% {
    //   transform: translateX(65%);
    // }

    // 40% {
    //   transform: translateX(40%);
    // }

    // 65% {
    //   transform: translateX(15%);
    // }

    // 85% {
    //   transform: translateX(25%);
    // }

    100% {
      transform: translateX(0%);
    }
  }

  .right-panel {
    position: fixed;
    z-index: 1;
    right: 83px;
    width: 330px;
    top: 100px;
    height: 937px;

    background-size: 100% 100%;
    transform: translate(122%);
    transition: transform 0.5s ease-in-out;

    .jk {
      margin-top: 12px;
      width: 90%;
      height: 200px;
    }

    .box {
      margin-top: 6px;
      margin-bottom: 18px;
      // background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      width: 94%;
      // height: 428px;

      .loudong {
        margin-top: 21px;
        margin-bottom: 25px;
      }

      .wenzi {
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 10px;
        color: #bdecf9;
        text-align: left;
        margin-left: 20px;
        margin-right: 20px;

        .h2biaoti {
          margin-bottom: 15px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 12px;
          color: #00ffff;
        }
      }

      .p {
        text-indent: 2em;
        margin-bottom: 1em;
        letter-spacing: 0.05em;
      }
    }

    .boxxx {
      margin-top: 6px;
      margin-bottom: 18px;
      background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      width: 333px;
      height: 420px;
      overflow-y: scroll;
      /* 设置垂直滚动条 */
      // overflow: hidden;
      /* 设置滚动条的样式 */

      .zengti {
        margin: 10px 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 315px;
        height: 38px;
        gap: 5px;

        .left {
          display: flex;
          align-items: center;
          justify-content: space-evenly;
          width: 84px;
          height: 27px;

          .yuan {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #08f7f7;
          }

          .wenziss {
            display: flex;
            align-items: flex-start;
            flex-direction: column;

            .p1 {
              font-family: Source Han Sans SC;
              font-weight: 500;
              font-size: 10px;
              color: #ffffff;
            }

            .p2 {
              font-family: Source Han Sans SC;
              font-weight: 500;
              font-size: 10px;
              color: #55cff9;
            }
          }
        }

        .right {
          background: url("../assets/image/rightbeij.png");
          background-size: 100% 100%;
          background-repeat: no-repeat;

          width: 217px;
          height: 38px;
          font-family: Source Han Sans SC;
          font-weight: 500;
          font-size: 11px;
          color: #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    .boxxx::-webkit-scrollbar {
      width: 1px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条轨道的样式 */
    .boxxx::-webkit-scrollbar-track {
      background-color: #f1f1f1;
      /* 设置滚动条轨道的背景色 */
    }

    /* 设置滚动条滑块的样式 */
    .boxxx::-webkit-scrollbar-thumb {
      background-color: #013363;
      /* 设置滚动条滑块的背景色 */
    }
  }

  .no-animation {
    transition: none;
  }

  .right-panel-active {
    transform: translate(0%);
    // animation: slideIn 1s ease-in-out ;
  }

  .right-panel-active1 {
    // transform: translate(0%);
    animation: slideIn 1s ease-in-out forwards;
  }

  @keyframes slideIn {
    0% {
      transform: translateX(100%);
    }

    // 30% {
    //   transform: translateX(65%);
    // }

    // 40% {
    //   transform: translateX(40%);
    // }

    // 65% {
    //   transform: translateX(15%);
    // }

    // 85% {
    //   transform: translateX(25%);
    // }

    100% {
      transform: translateX(0%);
    }
  }

  .completed {
    background: #7ad0ff;
  }

  .incomplete {
    background: #ff6041;
  }

  .warning {
    background: #00ffc0;
  }

  .completeds {
    color: #7ad0ff;
  }

  .incompletes {
    color: #ff6041;
  }

  .warnings {
    color: #00ffc0;
  }
}

.ql-center {
  display: flex;
  margin-top: 20px;
  justify-content: space-around;
  margin-top: 14px;

  .ql-Box {
    width: 75px;
    height: 49px;
    border: 1px solid #7ad0ff;
    // opacity: 0.6;
    border-radius: 2px;

    .ql-box1 {
      font-family: Alibaba PuHuiTi;
      font-weight: bold;
      font-size: 16px;
      color: #7ad0ff;
      margin-top: -10px;
    }

    .ql-box {
      display: flex;
      padding-left: 8px;
      padding-right: 9px;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 34px;

      .left_ql {
        width: 49px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #ffffff;

        .yuan {
          width: 7px;
          height: 7px;
          border-radius: 50%;
        }

        .pp {
          color: #fff;
          font-size: 12px;
        }
      }

      img {
        height: 12px;
        width: 7px;
      }
    }
  }
}

.warn1 {
  background: url("../assets/image/warnred.png");
}

.warn2 {
  background: url("../assets/image/warnyellow.png");
}

.warn3 {
  background: url("../assets/image/warngreen.png");
}

.warning12 {
  background-size: 100% 100%;
  // width: 365px;
  height: 47px;

  .info {
    margin-top: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: left;

    margin-left: 50px;

    .info1 {
      display: flex;
      flex-direction: column;

      p:nth-of-type(1) {
        font-size: 13px;
        font-family: Alibaba PuHuiTi;
        font-weight: bold;
      }

      .red {
        color: #ff0000;
      }

      .green {
        color: #00ffcc;
      }

      .yellow {
        color: #ffff00;
      }

      p:nth-of-type(2) {
        font-size: 16px;
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        color: #ffffff;
      }
    }

    .info2 {
      margin-right: 10px;
      font-size: 15px;
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      color: #cffff8;
    }
  }
}
.xxxx {
  position: absolute;
  top: 1%;
  right: 1%;
  width: 25px;
  height: 25px;
  z-index: 99999;
}
</style>
