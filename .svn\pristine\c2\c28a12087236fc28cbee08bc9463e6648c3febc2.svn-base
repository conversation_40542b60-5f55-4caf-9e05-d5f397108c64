<template>
  <div class="quanbu">
    <!-- <FitScreen :width="1920" :height="1080"  mode="fit">
       -->

    <v-scale-screen delay="100" width="1920" height="1080">
      <div class="container">
        <div
          class="icon12"
          :class="{
            'left-panel-active': showdh,
            'no-animation': noAnimation,
            'left-panel-active1': showdh1,
          }"
        >
          <!-- <img src="../assets/image/listicon1.png" alt="" @click="selectBot(0, '首页')" /> -->
          <img src="../assets/image/listicon2.png" @click="resert()" alt="" />
          <!-- <img :src="require(`区域碳排放综合态势../assets/image/listicon${!isnum ? 3 : 4}.png`)" alt="" @click="openclose(isnum)" /> -->
        </div>
        <baojing class="tablezujian" v-if="false" @closebj="closebj"></baojing>
        <!-- <baojing class="bapjing"></baojing> -->
        <div v-if="false" id="loading-page" class="loading_page">
          <div class="inner-box">
            <div class="inner"></div>
            <div class="inner"></div>
            <div class="inner"></div>
            <div class="inner"></div>
            <div class="inner"></div>
            <div class="inner"></div>
          </div>
          <div class="loading-text">正在加载中,请耐心等候</div>
          <!-- 添加文本 -->
        </div>
        <component
          :is="componentTag"
          ref="child"
          :resItems="resItems"
          :title="floortitle"
          @openclose1="openclose1"
          @childEvent="selectItem"
          @returnhome="returnhome1()"
          @open-bj="showbj()"
        >
        </component>

        <iframe
          id="ifram"
          ref="mainIframe"
          class="iframe"
          name="mainIframe"
          :src="iframe"
          frameborder="0"
          v-if="true"
        ></iframe>
        <!-- 头部内容 -->
        <img class="topbg" src="../assets/topbg.png" alt="" />
        <img class="botbg" src="../assets/botbg.png" alt="" />
        <div class="head">
          <div class="title">
            <div class="titlewenzi">
              {{ captions }}
            </div>
          </div>
        </div>
        <div class="bott">
          <div class="imgimg">
            <img src="../assets/image/zuoimg.png" alt="" />
            <img
              src="../assets/image/zhongimg.png"
              v-if="xians1"
              @click="moni"
              alt=""
              class="anniuniu"
            />
            <img
              v-else
              src="../assets/image/zhangting.png"
              @click="ychang"
              class="anniuniu"
              alt=""
            />
            <img src="../assets/image/youimg.png" alt="" />
          </div>
          <div
            :class="isactive == index ? 'bottit1' : 'bottit'"
            v-for="(item, index) in botlist"
            :key="index"
            @click="switchTab1(item, index)"
          >
            <img :src="isactive == index ? item.imgs : item.imgs" alt="" />
            {{ item.name }}
          </div>
        </div>
        <div class="floorcd" v-if="true">
          <!-- <p class="tit">楼栋</p> -->
          <div
            @click="selectbuild(item, index1)"
            :class="floorindex1 == index1 ? 'flist1' : 'flist'"
            v-for="(item, index1) in fllist"
            :key="index1"
          >
            {{ item.name }}
          </div>
        </div>
        <div class="floorcd1" v-if="showfloor">
          <!-- <p class="tit">{{ fllist[floorindex].name }}</p> -->
          <!-- <img class="return" @click="returnbuild()" src="../assets/image/return.png" alt=""> -->
          <div
            class="flist"
            @click="
              selectfloor(
                item,
                index1,
                fllist[floorindex].name,
                fllist[floorindex].list.length
              )
            "
            v-for="(item, index1) in fllist[floorindex].list"
            :key="index1"
            :class="floorindex2 == index1 ? 'flist1' : 'flist'"
          >
            {{ item }}
          </div>
        </div>
        <div class="tablist" :class="{ tablistisshow: !showdh }" v-if="false">
          <div
            class="item"
            v-for="(item, index) in buildingInfo"
            :key="index"
            :style="{ backgroundImage: `url(${item.backgroundImage})` }"
          >
            <div class="list">{{ item.value }}</div>
            <div class="name">{{ item.name }}</div>
          </div>
        </div>
        <div class="now-time" v-if="true">
          <span>{{ timeStr }}</span>
        </div>
        <div class="title-right">
          <div class="tianqi">
            <img
              class="taiyang"
              @click="changetqlist"
              src="../assets/image/taiyang.png"
              alt=""
            />

            <!-- <div class="wendu">23℃</div> -->
          </div>
          <div class="opt">
            <img
              class="opt1"
              src="../assets/image/opt1.png"
              alt=""
              @click="showbj"
            />
            <img
              class="opt1"
              @click="openclose(isnum)"
              src="../assets/image/opt2.png"
              alt=""
            />
            <img
              @click="tuichu"
              class="opt1"
              src="../assets/image/opt3.png"
              alt=""
            />
          </div>
        </div>
        <!-- <img class="logo" src="../assets/image/logo.png" alt="" /> -->

        <div class="content1" v-if="showtq">
          <div class="xuanzeqi">
            <p class="pp">时间与现实同步</p>
            <input
              class="switch-btn switch-btn-animbg"
              v-model="isChecked"
              @change="handleCheckboxChange"
              type="checkbox"
              checked
            />
          </div>
          <div :class="isChecked == true ? 'tianjiandtime1' : 'tianjiandtime'">
            <div
              class="tianqi"
              v-for="(item, index) in tqlist"
              :key="index"
              @click="tqchange1(index, item.time)"
            >
              <img
                class="img"
                :src="
                  require(`../assets/img/${
                    tq1 == index ? 'tianqi1' : 'tianqi'
                  }/tianqi${index + 1}.png`)
                "
                alt=""
              />
              <p class="time">{{ item.time }}</p>
            </div>
          </div>
          <div class="xuanzeqi">
            <p class="pp">天气设置</p>
          </div>
          <div :class="isChecked == true ? 'tianjiandtime1' : 'tianjiandtime'">
            <div
              class="tianqi"
              v-for="(item, index) in tqlist1"
              :key="index"
              @click="tqchange(index, item.time)"
            >
              <img
                class="img"
                :src="
                  require(`../assets/img/${
                    tq2 == index ? 'tianqi1' : 'tianqi'
                  }/tianqi${index + 5}.png`)
                "
                alt=""
              />
              <p class="time">{{ item.time }}</p>
            </div>
          </div>
          <!-- <label><input class="switch-btn switch-btn-animbg" type="checkbox" checked> 默认选中</label> -->
        </div>
      </div>
    </v-scale-screen>
  </div>
</template>
<script>
import Title from "@/components/common/Title.vue";
import Title1 from "@/components/common/Title1.vue";
import tedai from "@/components/common/tedai.vue";
import baojing from "@/components/common/table1.vue";
import component1 from "@/views/index.vue";
import component2 from "@/views/shebei.vue";
// import component3 from "@/views/floor.vue";

import VScaleScreen from "v-scale-screen";
import { deviceapi } from "@/api/admin.js";

// import component21 from "@/views/nenghao.vue";
import axios from "axios";
export default {
  components: {
    component2,
    baojing,
    component1,
    // component2,
    // component3,
    VScaleScreen,
    tedai,
    // component11,

    Title,
    Title1,
  },

  data() {
    return {
      intervalId: null, // 用来存储定时器的ID
      index: 0,

      xians1: true, // 控制按钮显示
      resItems: [],
      isscale: true,
      isshow: false,
      lastClicked: "",
      showtq: false,
      xians1: true,
      tqlist: [
        {
          time: "7:00",
        },
        {
          time: "12:00",
        },
        {
          time: "17:00",
        },
        {
          time: "22:00",
        },
      ],
      isChecked: true,
      tqlist1: [
        {
          time: "晴朗",
        },
        {
          time: "多云",
        },
        {
          time: "下雨",
        },
        {
          time: "下雪",
        },
      ],
      titles: "首页",
      show3: false,
      show4: false,
      listtabe: [
        { name: "首页" },
        { name: "疾控中心" },
        { name: "疾控中心4F" },
        { name: "疾控中心3F" },
        { name: "疾控中心2F" },
        { name: "疾控中心1F" },
      ],
      flist: [
        "整体建筑",
        "A1栋",
        "A2栋",
        "A3栋",
        "A4栋",
        "A5栋",
        "B1栋",
        "B2栋",
        "B3栋",
        "B4栋",
        "B5栋",
        "B6栋",
        "A6地下室",
        "B7地下室",
      ],
      fllist: [
        {
          name: "整体建筑",
          list: [""],
        },
        {
          name: "1#行政楼",
          list: ["整体", "楼顶", "5F", "4F", "3F", "2F", "1F"],
        },
        {
          name: "2#游泳馆",
          list: ["整体", "楼顶", "2F", "1F"],
        },
        {
          name: "塔楼",
          list: [""],
        },
        {
          name: "3#体育馆",
          list: ["整体", "楼顶", "2F", "1F"],
        },

        {
          name: "4#礼堂",
          list: ["整体", "楼顶", "2F", "1F"],
        },
        {
          name: "5#教学楼A",
          list: ["整体", "楼顶", "5F", "4F", "3F", "2F", "1F"],
        },
        {
          name: "5#教学楼B",
          list: ["整体", "楼顶", "5F", "4F", "3F", "2F", "1F"],
        },
        {
          name: "5#教学楼C",
          list: ["整体", "楼顶", "5F", "4F", "3F", "2F", "1F"],
        },
        {
          name: "6#宿舍楼",
          list: ["整体", "楼顶", "6F", "5F", "4F", "3F", "2F", "1F"],
        },
      ],
      selectedItem: null,
      showdh: true,
      showdh1: false,
      noAnimation: false,
      value4: true,
      floortitle: "",
      isOn: false,
      buildingInfo: [
        {
          name: "建筑面积",
          value: "12710㎡",
          backgroundImage: require("../assets/image/itemlist.png"),
        },
        {
          name: "总层数",
          value: "12层",
          backgroundImage: require("../assets/image/itemlist1.png"),
        },
        {
          name: "开发商",
          value: "上海现代建筑设计 (集团)有限公司",
          backgroundImage: require("../assets/image/itemlist.png"),
        },
        {
          name: "地址",
          value: "石门二路258号",
          backgroundImage: require("../assets/image/itemlist1.png"),
        },
      ],

      feel: "",
      sysname: "",
      loading: true,
      setlou: false,
      lablevalue: false,
      show4: false,
      xuanzindex: "",
      res1Items: [],
      res2Items: [],
      res3Items: [],
      res4Items: [],
      fwshow: true,
      timeStr: "",
      weather: "晴朗",
      isExpanded: true, // 控制bot容器展开/收起
      fwshow1: false,
      lastClickedTitle: "",
      showlist: false,
      showdata: true,
      componentTag: "component1",
      iframe,

      selectedIndex: 0,
      isButton2Active: false,
      captions,
      selectvalue: "整体场景",
      selectvalue1: "",
      activef: 0,
      isshow: true,
      isactive: 0,
      condition: false,
      buildId: "",
      floorId: "",
      showfloor: false,
      floorindex: 1,
      floorindex1: 0,
      floorindex2: 0,
      botlist: [
        {
          name: "首页",
          code: "",
          imgs: require("../assets/image/xiaoicon1.png"),
          imgs1: require("../assets/image/zhoxiaoicon1.png"),
        },

        {
          name: "资产管理",
          code: "CGQ10",
          img: "http://3d.dddtask.cn/engineer-qilei/fengxian/fangbao.png",
          imgs: require("../assets/image/xiaoicon2.png"),
          imgs1: require("../assets/image/zhoxiaoicon2.png"),
        },
        {
          name: "用户预约",
          code: "LRY193",
          img: "http://3d.dddtask.cn/engineer-qilei/fengxian/bingxiang.png",
          imgs: require("../assets/image/xiaoicon3.png"),
          imgs1: require("../assets/image/zhoxiaoicon3.png"),
        },
        {
          name: "环境监控",
          code: "CGQ13",
          img: "http://3d.dddtask.cn/engineer-qilei/fengxian/bingxiang.png",
          imgs: require("../assets/image/xiaoicon4.png"),
          imgs1: require("../assets/image/zhoxiaoicon4.png"),
        },
        {
          name: "视频监控",
          code: "CGQ7",
          img: "http://3d.dddtask.cn/engineer-qilei/fengxian/fangbao.png",
          imgs: require("../assets/image/xiaoicon5.png"),
          imgs1: require("../assets/image/zhoxiaoicon5.png"),
        },
        {
          name: "通风柜监控",
          code: "CGQ9",
          img: "http://3d.dddtask.cn/engineer-qilei/fengxian/huanjingco2.png",
          imgs: require("../assets/image/xiaoicon6.png"),
          imgs1: require("../assets/image/zhoxiaoicon6.png"),
        },
        {
          name: "新风监控",
          code: "CGQ3",
          img: "http://3d.dddtask.cn/engineer-qilei/fengxian/huanjingco2.png",
          imgs: require("../assets/image/xiaoicon7.png"),
          imgs1: require("../assets/image/zhoxiaoicon7.png"),
        },
        {
          name: "供气监控",
          code: "CGQ8",
          img: "http://3d.dddtask.cn/engineer-qilei/fengxian/jiawan.png",
          imgs: require("../assets/image/xiaoicon8.png"),
          imgs1: require("../assets/image/zhoxiaoicon8.png"),
        },
        {
          name: "能耗监控",
          code: "CGQ2",
          img: "http://3d.dddtask.cn/engineer-qilei/fengxian/kqyc.png",
          imgs: require("../assets/image/xiaoicon9.png"),
          imgs1: require("../assets/image/zhoxiaoicon9.png"),
        },
        {
          name: "报警监控",
          code: "CGQ2",
          img: "http://3d.dddtask.cn/engineer-qilei/fengxian/kqyc.png",
          imgs: require("../assets/image/xiaoicon9.png"),
          imgs1: require("../assets/image/zhoxiaoicon9.png"),
        },
        {
          name: "资产管理",
          code: "CGQ2",
          img: "http://3d.dddtask.cn/engineer-qilei/fengxian/kqyc.png",
          imgs: require("../assets/image/xiaoicon9.png"),
          imgs1: require("../assets/image/zhoxiaoicon9.png"),
        },
      ],
      build: "B3",
      system: "整体总览",
      finished: false,
      defaultOpeneds: [],
      isnum: false,
    };
  },
  computed: {
    buttonText() {
      return this.isOn ? "隐藏标签" : "显示标签";
    },
  },
  created() {
    // this.changedeviceapi();
    // this.selectedItem = this.listtabe[0];
    setInterval(() => {
      this.formatDate();
    }, 1000);
  },
  mounted() {
    var that = this;
    this.showdh1 = true;
    setTimeout(() => {
      this.showdh1 = false;
      this.noAnimation = false;
    }, 1000); // 动画持续时间为1秒
    console.log(1222);
    window.addEventListener("message", function (event) {
      //event.data获取传过来的数据
      // let name = event.data.name;
    });
  },
  beforeDestroy() {},
  methods: {
    selectbuild(item, index) {
      this.seedbuild(index);
      this.buildId = item.name;
      console.log(this.buildId, "当前楼栋");
      let name = item.name; //楼层
      // if (item.name == 'A6地下室') {
      //   name = 'A6地下室-2F';
      //   this.floorId = '-2F'
      // } else if (item.name == 'B7地下室') {
      //   name = 'B7地下室-2F';
      //   this.floorId = '-2F'
      // }

      // this.sendToUE4(this.lastClickedTitle);
      // if (this.lastClickedTitle.includes('-2F') || this.lastClickedTitle.includes('-1F')) {
      //   setTimeout(() => {
      //     this.sendToUE4(name);
      //   }, 1500);
      // } else {
      //   this.sendToUE4(name);
      // }

      this.lastClickedTitle = name;
      if (index && index != 3) {
        this.showfloor = true;
        this.floorindex = index;
      } else if (index == 3) {
        this.showfloor = false;
        // this.floorindex = index
      } else {
        this.showfloor = false;
      }
      this.floorindex1 = index;
      this.floorindex2 = 0;
    },

    async selectfloor(item, index, name, length) {
      this.seedfloor(index);
      if (index == 1) {
        item = length - 1 + "F";
      } else if (index == 0) {
        item = "";
      }
      console.log(item, index, name);
      if (
        (index == 0 && name == "A6地下室") ||
        (index == 0 && name == "B7地下室")
      ) {
        item = "-2F";
      } else if (
        (index == 1 && name == "A6地下室") ||
        (index == 1 && name == "B7地下室")
      ) {
        item = "-1F";
      }
      this.floorId = item;
      console.log(this.floorId, "当前楼层");
      this.floorindex2 = index;

      this.lastClickedTitle = name + item;
    },
    async switchTab1(item, index) {
      console.log(item, index, "item.img");
      // this.zengtiimg = item.img;

      this.deviceTypes = item.code;

      // this.switchactivef(item, item.code);
      this.isactive = index;
      if (index == 0) {
        this.componentTag = "component1";
      } else if (index == 1) {
        this.componentTag = "component2";
      } else {
        this.componentTag = "component1";
      }
      // if (index) {
      //   this.componentTag = "shebei";
      //   this.isshow = false;
      //   this.showdh = true;
      //   this.showdh1 = false;
      // } else {
      //   this.componentTag = "";
      //   this.isshow = true;
      //   this.showdh = false;
      //   this.showdh1 = true;
      // }
    },

    async changedeviceapi() {
      const res = await deviceapi({
        buildingId: 1,
        category: "实验室",
        type: "base",
      });
      console.log(res.data);

      this.res1Items = res.data.filter((item) => item.groupName === "实验室1F");
      this.res2Items = res.data.filter((item) => item.groupName === "实验室2F");
      this.res3Items = res.data.filter((item) => item.groupName === "实验室3F");
      this.res4Items = res.data.filter((item) => item.groupName === "实验室4F");
    },
    moni() {
      this.xians1 = false; // 隐藏 "开始" 按钮，显示 "停止" 按钮
      let index = 0;
      this.intervalId = setInterval(() => {
        if (this.botlist.length > 0) {
          const item = this.botlist[index];
          this.switchTab1(item, index);
          index = (index + 1) % this.botlist.length; // 循环遍历数组
        }
      }, 10000);
    },
    ychang() {
      if (this.intervalId) {
        clearInterval(this.intervalId); // 关闭定时器
        this.intervalId = null; // 重置定时器ID
        this.xians1 = true; // 显示 "开始" 按钮，隐藏 "停止" 按钮
        console.log("定时器已关闭");
      }
    },
    showbj() {
      console.log(12121221);
      this.isshow = true;
    },
    closebj() {
      console.log(12121221);
      this.isshow = false;
    },
    changetqlist() {
      this.showtq = !this.showtq;
    },
    handleCheckboxChange: function () {
      // 复选框状态发生改变时触发的方法getlist
      if (this.isChecked) {
        console.log("复选框被选中");
        // this.getweather()
        this.sendToUE4("开");
      } else {
        console.log("复选框未被选中");

        this.sendToUE4("关");
      }
    },
    tqchange(index, name) {
      this.tq2 = index;
      this.sendToUE4(name);
    },
    tqchange1(index, name) {
      this.tq1 = index;
      if (name == "7:00") {
        this.sendToUE4("早上");
      } else if (name == "12:00") {
        this.sendToUE4("中午");
      } else if (name == "17:00") {
        this.sendToUE4("旁晚");
      } else if (name == "22:00") {
        this.sendToUE4("晚上");
      }
    },

    tuichu() {
      // 清除 token
      localStorage.removeItem("token"); // 如果 token 存储在 localStorage 中
      // 或者
      sessionStorage.removeItem("token"); // 如果 token 存储在 sessionStorage 中

      // 跳转到首页
      this.$router.push("/");

      // 调用 UE4 退出方法
      ue4("tuichu");
    },

    seedUE(data) {
      ue4(data);
      console.log(data, "发送了");
    },
    resert() {
      this.seedbuild(0);
      this.condition = false;
      this.sendToUE4(this.lastClickedTitle);
      ue4("fuwei");
      this.lastClickedTitle = "";

      if (!this.isscale) {
        this.openclose(true);
      }
    },
    returnhome1() {
      this.selectItem({ name: "疾控中心" }, 0);
    },
    async selectItem(item, index) {
      if (index == -1) {
        this.returnhome(), this.resert();
      } else {
        if (!this.isscale) {
          this.openclose(true);
        }
        if (index == 1) {
          this.resItems = this.res4Items;
          console.log(this.res4Items, "this.res4Items");
        } else if (index == 2) {
          this.resItems = this.res3Items;
        } else if (index == 3) {
          this.resItems = this.res2Items;
        } else if (index == 4) {
          this.resItems = this.res1Items;
        }
        console.log(item.name);
        this.titles = item.name;
        this.show3 = false;
        this.selectedItem = item;
        this.componentTag =
          index == 0 || index == 1 ? "component" + (index + 2) : "component3";
        this.floortitle = 5 - index;

        if (index != 0) {
          if (this.floortitle == 1) {
          } else if (this.floortitle == 2) {
          } else if (this.floortitle == 3) {
          } else if (this.floortitle == 4) {
          }

          await this.fetchProjectSet(
            1,
            "eMcIowiN0o77xNYBl9vufA==",
            "0",
            "疾控中心",
            this.floortitle + "F"
          );
          if (this.condition) {
            this.$refs.child.switchactivef("实验楼", 0);
            this.$refs.child.switchTab1("总览", 0);
          }
          this.condition = true;
        }
        this.sendToUE4(this.lastClickedTitle);
        this.sendTo(item.name);

        if (index == 0) {
          this.condition = false;
          this.lastClickedTitle = "";
        }
      }
    },
    returnhome() {
      this.componentTag = "component1";
      this.selectedItem = null;
      this.show3 = false;
      this.titles = "首页";
    },
    changebq(value) {
      console.log(value);
      this.seed("标签", value);
    },
    toggleButton() {
      this.isOn = !this.isOn;
    },
    openclose(isnum) {
      console.log(isnum);
      this.isscale = isnum;
      // if (isnum) {
      //   this.componentTag = "component" + parseInt(this.selectedIndex + 1);
      // } else {
      //   this.componentTag = ''
      // }
      this.showdh = isnum;
      this.$refs.child.oc(isnum);

      this.isnum = !this.isnum;
    },
    openclose1() {
      // if (isnum) {
      //   this.componentTag = "component" + parseInt(this.selectedIndex + 1);
      // } else {
      //   this.componentTag = ''
      // }
      this.showdh = true;
    },
    // eMcIowiN0o77xNYBl9vufA==  疾控中心
    async fetchProjectSet(type, projectId, parkId, buildId, floorId) {
      try {
        const response = await axios.get(
          "https://api-dh3d-test.3dzhanting.cn:8080/projectSet/all",
          {
            params: {
              type: type,
              projectId: projectId,
              deviceId: "", // 如果不需要 deviceId，可以将其删除或保留为空字符串
              parkId: parkId,
              buildId: buildId,
              floorId: floorId,
            },
          }
        );
        console.log(type, projectId, parkId, buildId, floorId);
        console.log("Response data:", response.data);
        this.sendToUE41("shebei", response.data.data);
        return response.data;
      } catch (error) {
        console.error("Error fetching project set:", error);
        throw error; // 重新抛出错误以便调用者处理
      }
    },
    sendToUE41(st, data) {
      console.log();
      // 调用UE4的相关函数，传递数据
      ue4(st, data);
      console.log(st, data, "UEst收到的");
    },
    sendTo(op, data) {
      this.sendToUE4(op, {});
      this.lastClickedTitle = op;
      // console.log(this.lastClickedTitle);
    },
    findIndexByName(name) {
      for (let i = 0; i < this.botlist.length; i++) {
        if (this.botlist[i].name === name) {
          return i;
        }
      }
      return -1; // 如果找不到匹配的name，返回-1
    },

    sendToUE4(data) {
      // 调用UE4的相关函数，传递数据
      ue4(data);
      console.log(data, "UE收到的");
    },
    changela() {
      // 切换lablevalue的布尔状态
      this.lablevalue = !this.lablevalue;
      // 将最新的lablevalue状态传递给seed1方法
      this.seed1(this.lablevalue);
    },
    loucxuanz(index, data) {
      this.system = "";
      this.xuanzindex = index;
      this.seed(data);
    },
    sendTofloor(value) {
      this.seed(value);
      this.selectvalue = value;
      this.showlist = false;
    },
    sendlou(value) {
      this.seed(value);
      this.selectvalue = value;
      this.showlist = false;
    },
    toggleContent() {
      this.showlist = !this.showlist;
    },
    beforeEnter(el) {
      el.style.height = "0"; // 设置初始高度
    },
    enter(el, done) {
      requestAnimationFrame(() => {
        el.style.transition = "height 0.3s ease"; // 应用过渡样式
        el.style.height = "255px"; // 目标高度
        el.addEventListener("transitionend", done);
      });
    },
    beforeLeave(el) {
      el.style.height = `${el.offsetHeight}px`; // 确保从当前高度开始过渡
    },
    leave(el, done) {
      requestAnimationFrame(() => {
        el.style.transition = "height 0.3s ease"; // 再次确认过渡样式
        el.style.height = "0"; // 收缩目标高度
        el.addEventListener("transitionend", done);
      });
    },
    toggleContent1() {
      this.fwshow1 = !this.fwshow1;
    },
    beforeEnter1(el) {
      el.style.height = "20px"; // 设置初始高度
    },
    enter1(el, done) {
      requestAnimationFrame(() => {
        el.style.transition = "height 0.1s ease"; // 应用过渡样式
        el.style.height = "102px"; // 目标高度
        el.addEventListener("transitionend", done);
      });
    },
    beforeLeave1(el) {
      el.style.height = `${el.offsetHeight}px`; // 确保从当前高度开始过渡
    },
    leave1(el, done) {
      requestAnimationFrame(() => {
        el.style.transition = "height 0.1s ease"; // 再次确认过渡样式
        el.style.height = "20px"; // 收缩目标高度
        el.addEventListener("transitionend", done);
      });
    },
    handleChange(value) {
      this.seed(value);
      console.log(value);
    },
    toggleBot() {
      this.isExpanded = !this.isExpanded;
      console.log(this.isExpanded);
    },
    //https://rest{api.amap.com/v3/weather/weatherInfo?city=320200&key=1c046ae4b42c14be43fb7966539e744e
    getweather() {
      const apiUrl = "https://restapi.amap.com/v3/weather/weatherInfo";
      const cityCode = "310000"; // 你的城市代码
      const apiKey = "1c046ae4b42c14be43fb7966539e744e";
      const params = {
        city: cityCode,
        key: apiKey,
      };
      axios
        .get(apiUrl, { params })
        .then((response) => {
          // 请求成功处理
          console.log("响应数据:", response.data.lives[0]);
          const weatherCondition = response.data.lives[0].weather;
          if (weatherCondition.includes("晴")) {
            this.weather = "晴朗";
          } else if (weatherCondition.includes("雨")) {
            this.weather = "下雨";
          } else if (weatherCondition.includes("雪")) {
            this.weather = "下雪";
          } else {
            this.weather = "阴天";
          }
          console.log(this.weather);
          this.sendToUE4(this.weather);
        })
        .catch((error) => {
          // 请求失败处理
          console.error("请求失败:", error);
        });
    },
    seedbuild(item) {
      console.log(item, "发送了");
      const frame = document.getElementById("ifram");
      frame.contentWindow.postMessage(
        {
          type: "build",
          param: { data: item },
        },
        "*"
      );
    },
    seedfloor(item) {
      const frame = document.getElementById("ifram");
      frame.contentWindow.postMessage(
        {
          type: "floor",
          param: { data: item },
        },
        "*"
      );
    },
    seed(item, value, index) {
      console.log(item, 1112);
      // const frame = document.getElementById("ifram");
      // frame.contentWindow.postMessage(
      //   {
      //     type: "function",
      //     name: "exec3d",
      //     param: { type: 1, data: item, value: value },
      //   },
      //   "*"
      // );
    },
    seed1(item) {
      console.log(item);

      // const frame = document.getElementById("ifram");
      // frame.contentWindow.postMessage(
      //   {
      //     type: "function",
      //     name: "exec3d",
      //     param: { type: 2, data: item },
      //   },
      //   "*"
      // );
    },
    slideLeft() {
      const container = this.$refs.bot1Container;
      const scrollAmount = -900; // 调整滑动距离，负值表示向左滑动
      container.scrollLeft += scrollAmount;
    },
    slideRight() {
      const container = this.$refs.bot1Container;
      const scrollAmount = 900; // 调整滑动距离，正值表示向右滑动
      container.scrollLeft += scrollAmount;
    },

    selectBot(index, value) {
      if (!this.showdh) {
        this.isnum = false;
        this.showdh = true;
      }

      if (index == 0) {
        this.setlou = false;
      } else {
        this.setlou = true;
      }

      this.seed(value, index);

      this.selectedIndex = index;
      this.componentTag = "component" + parseInt(index + 1);
      console.log(this.selectedIndex);
      if (index == 1) {
        this.fwshow = false;
      } else {
        this.fwshow = true;
      }
    },

    formatDate() {
      let now = new Date();
      let year = now.getFullYear();
      let month = (now.getMonth() + 1).toString().padStart(2, "0");
      let date = now.getDate().toString().padStart(2, "0");
      let hh = now.getHours().toString().padStart(2, "0");
      let mm = now.getMinutes().toString().padStart(2, "0");
      let ss = now.getSeconds().toString().padStart(2, "0");
      this.timeStr = `${year}-${month}-${date}   ${hh}:${mm}:${ss}`;
    },
  },
};
</script>
<style lang="less" scoped>
.v-screen-box {
  background: none !important;
}

.active {
  background-color: rgba(113, 155, 224, 0.7) !important;
}

.floorcd {
  cursor: pointer;
  // z-index: 999;
  position: fixed;
  top: 320px;
  right: 420.864px;

  .return {
    position: absolute;
    top: -200px;
    left: 10px;
    width: 34px;
    height: 35px;
  }

  .flist {
    margin-top: 2px;
    margin-bottom: 8px;
    background: url("../assets/image/floor.png");
    background-size: 100% 100%;
    width: 119px;
    height: 29px;
    font-family: DingTalk JinBuTi;
    font-weight: 400;
    font-size: 13px;
    color: #ffffff;
    line-height: 26.3px;
    text-align: center;
  }

  .flist1 {
    margin-top: 2px;
    margin-bottom: 8px;
    background: url("../assets/image/floor1.png");
    background-size: 100% 100%;
    width: 119px;
    height: 29px;
    font-family: DingTalk JinBuTi;
    font-weight: 400;
    font-size: 15px;
    color: #ffffff;
    line-height: 26.3px;
    text-align: center;
  }
}

.floorcd1 {
  cursor: pointer;
  // z-index: 88;
  position: fixed;
  top: 322px;
  right: 535.864px;

  .tit {
    line-height: 21px;
    position: absolute;
    top: -26px;
    left: -13px;
    width: 102px;
    height: 45px;
    font-family: DingTalk JinBuTi;
    font-weight: 400;
    font-size: 23px;
    color: #ffffff;
    // line-height: 52px;
    // z-index: 1;
    background: linear-gradient(0deg, #ffffff 0%, #0e96fa 98.6328125%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-align: center;
  }

  .return {
    position: absolute;
    top: -244px;
    left: 11px;
    width: 34px;
    height: 35px;
  }

  .flist {
    margin-top: 2px;
    margin-bottom: 8px;
    background: url("../assets/image/floor.png");
    background-size: 100% 100%;
    width: 79.5px;
    height: 26.3px;
    font-family: DingTalk JinBuTi;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    line-height: 26.3px;
    text-align: center;
  }

  .flist1 {
    margin-top: 2px;
    margin-bottom: 8px;
    background: url("../assets/image/floor1.png");
    background-size: 100% 100%;
    width: 79.5px;
    height: 26.3px;
    font-family: DingTalk JinBuTi;
    font-weight: 400;
    font-size: 15px;
    color: #ffffff;
    line-height: 26.3px;
    text-align: center;
  }

  .flist:hover {
    background: url("../assets/image/floor1.png");
    // background: url('../assets/image/floor.png');
    background-size: 100% 100%;
    width: 79.5px;
    height: 26.3px;
  }
}

.bapjing {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);

  z-index: 200;
}

.bott {
  position: fixed;
  z-index: 1;
  bottom: 4px;
  left: 16px;
  width: 1885px;
  height: 50px;
  display: flex;
  flex-direction: row;
  cursor: pointer;

  justify-content: space-between;

  .bottit {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 148px;
    height: 49px;
    background: url("../assets/image/bot_b.png");
    background-size: 100% 100%;

    font-family: Source Han Sans SC;
    font-weight: bold;
    font-size: 17px;
    color: #ffffff;

    cursor: pointer;
    gap: 12px;
  }

  .bottit1 {
    width: 148px;
    height: 65px;
    background: url("../assets/image/bot_a.png");
    background-size: 100% 100%;

    font-family: Source Han Sans SC;
    font-weight: bold;
    font-size: 17px;
    color: #ffffff;

    cursor: pointer;
    margin-top: -16px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-top: 16px;
    gap: 12px;
  }
}

.icon12 {
  width: 57px;
  height: 199px;
  position: absolute;
  top: 86px;
  left: 412px;
  z-index: 200;
  cursor: pointer;
  transform: translate(-582%);
  transition: transform 0.5s ease-in-out;

  img {
    width: 57px;
    height: 54px;
  }

  // .rotated {
  //   transform: rotate(180deg);
  // }
}

.left-panel-active {
  transform: translate(0%) !important;
}

.left-panel-active1 {
  // transform: translate(0%);
  animation: slideOut 1s ease-in-out forwards !important;
}

@keyframes slideOut {
  100% {
    transform: translateX(0%);
  }

  85% {
    transform: translateX(-25%);
  }

  65% {
    transform: translateX(-15%);
  }

  // 40% {
  //   transform: translateX(-55%);
  // }

  // 30% {
  //   transform: translateX(-40%);
  // }

  0% {
    transform: translateX(-100%);
  }
}

/deep/.el-switch__core {
  border: 1px solid #3f637e;
}

.iframe {
  width: 100%;
  height: 100%;
  position: absolute;
  border: 0;
  z-index: 0;
}

.ml-2 {
  opacity: 1;
  position: fixed;
  left: 358px;
  top: 1005px;
  z-index: 200;
  transform: translate(-420%);
  transition: transform 0.5s ease-in-out;
}

.toggle-label {
  position: absolute;
  left: 90px;
  top: 5px;
  font-family: DingTalk JinBuTi;
  font-weight: 400;
  font-size: 11px;
  color: #86a6b7 !important;
  line-height: 24px;
}

.toggle-button.on ~ .toggle-label {
  color: transparent;
}

.caidan {
  position: fixed;
  z-index: 12;
  top: 5.4%;
  left: 24.6%;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;

  .cdimg {
    position: absolute;
    top: 10px;
    z-index: 10;
  }

  .cd {
    position: absolute;
    top: 10px;
    z-index: 1;
    padding-top: 22.5px;
    border-radius: 5px;
    width: 100px;
    height: 146px;
    background: #1a284d;
    background-size: 100% 100%;
    pointer-events: none;
    display: flex;
    flex-direction: column;
    align-items: center;

    img {
      pointer-events: auto;
      margin-top: 10px;
      width: 87px;
      height: 23px;
    }
  }
}

.fuwei {
  position: fixed;
  z-index: 2;
  top: 69px;
  left: 424px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;

  img {
    width: 57px;
    height: 57px;
  }

  p {
    font-family: DingTalk JinBuTi;
    font-weight: 400;
    font-size: 18px;
    color: rgba(30, 227, 253, 0.64);
    background: linear-gradient(
      0deg,
      rgba(255, 255, 255, 0.98) 1.8798828125%,
      rgba(51, 156, 237, 0.64) 35.7177734375%
    );
    -webkit-background-clip: text;
    // -webkit-text-fill-color: transparent;
  }
}

.container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: stretch;
  height: 100%;

  // /* 定义动画效果 */
  // .expand-enter-active,
  // .expand-leave-active {
  //   transition: height 0.3s ease;
  //   height: 255px;
  // }

  // .expand-enter,
  // .expand-leave-to

  // /* 初始和结束状态 */
  //   {
  //   height: 0;
  // }

  .select {
    text-align: center;
    z-index: 999;
    position: fixed;
    top: 8px;
    right: 210.864px;

    .el-select {
      width: 142.8px;
      height: 26.9px;
      background: url("../assets/image/select.png");
      background-size: 100% 100%;
      font-family: Adobe Heiti Std;
      font-weight: normal;
      font-size: 13px;
      // color: #FEFEFE;
      text-align: center;

      cursor: pointer;
      line-height: 26px;
      z-index: 20;

      .sp {
        font-size: 15px;
        color: #e4f3ff;
        position: absolute;
        right: 10px;
        z-index: 999;
      }

      .pp {
        font-family: Adobe Heiti Std;
        font-weight: normal;
        font-size: 14px;
        color: #fefefe;
        // line-height: 113px;
      }
    }

    /* 定义动画 */

    .content::-webkit-scrollbar {
      width: 4px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条滑块的样式 */
    .warnlist::-webkit-scrollbar-thumb {
      background-color: #628091;
      /* 设置滚动条滑块的背景色 */
    }

    .warnlist::-webkit-scrollbar {
      width: 4px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条滑块的样式 */
    .content::-webkit-scrollbar-thumb {
      background-color: #628091;
      /* 设置滚动条滑块的背景色 */
    }

    /* 鼠标悬停在滚动条上时的样式 */
    .content {
      overflow: hidden; // 隐藏过渡中的溢出内容
      /* 设置滚动条的样式 */
      padding-bottom: 10px;
      overflow-y: auto;
      background: rgb(19, 44, 75);
      background-size: 100% 100%;
      position: fixed;
      top: 40px;
      right: 210px;
      width: 145px;
      height: 255px;
      cursor: pointer;
      display: flex;
      flex-wrap: wrap;
      z-index: 999;
      border: 0.5px solid rgb(19, 44, 75);
      border-radius: 8px;

      .butn {
        background: url("../assets/image/lou.png");
        background-size: 100% 100%;
        width: 70px;
        height: 21px;
        text-align: center;
        line-height: 21px;
        font-size: 12px;
        font-family: HYQiHei;
        font-weight: normal;
        color: #ffffff;
        margin-left: 10px;
        margin-top: 11px;
        // margin-right: 80px;
      }

      .btnbtn {
        margin-top: 10px;
        float: left;

        .btn1:hover {
          // color: aqua;
          background-color: aqua;
          // background-color: rgb(119, 119, 119);
        }

        .btn1 {
          border: none;
          // float: left;
          margin-left: 11px;
          width: 51px;
          height: 18px;
          color: #ffffff;
          text-align: center;
          line-height: 18px;
          background: rgba(87, 174, 235, 0.18);
          font-size: 12px;
          border-radius: 2px;
        }
      }
    }

    img {
      position: absolute;
      right: 20px;
      top: 4.2px;
      cursor: pointer;
    }
  }

  .btt {
    z-index: 999;
    cursor: pointer;
    position: fixed;
    top: 8.8px;
    right: 20px;
    // width: 160px;
    height: 36px;
    display: flex;

    .btt1 {
      margin-left: 24px;
      flex: 1;
      align-items: center;
      justify-content: center;
      display: flex;
      flex-direction: column;
      background: transparent;
      border: none;
      cursor: pointer;

      img {
        margin-top: -5px;
        // width: 38.8px;
        height: 38.8px;
      }

      .imgg {
        width: 26px;
        height: 26px;
      }

      p {
        margin-top: 5px;
        font-size: 16px;
        font-family: Source Han Sans CN;
        font-weight: 300;
        color: #6896b3;
      }
    }
  }

  .collapsed {
    height: 0 !important;
    // visibility: hidden;
    /* 当collapsed类被应用时，设置高度为0 */
  }

  .bot {
    top: 20px;
    left: 206px;
    position: fixed;
    pointer-events: none;
    z-index: 2;
    width: 50%;
    // background: url("../assets/image/homebot.png");
    background-size: 100% 100%;
    height: 93.6px;
    transition: height 0.3s ease-out;
    /* 调整动画持续时间和缓动函数 */
    // overflow: hidden;

    /* 避免内容在动画过程中溢出 */
    .opt {
      width: 35px;
      height: 25px;
      position: fixed;
      bottom: 50px;
      left: 0;
      right: 0;
      margin: auto;
      cursor: pointer;
      pointer-events: auto;
      transition: transform 0.3s ease-out;
    }

    .opt-up {
      transform: translateY(0px);
      /* 当容器展开时，图标向上移动 */
    }

    .opt-down {
      transform: translateY(30px);
      /* 当容器收起时，图标向下移动 */
    }

    .bot1-container {
      margin-top: 25px;
      margin-left: 126px;

      // width: 100vw;
      // overflow-x: auto;
      display: flex;
      pointer-events: auto;

      // overflow: hidden;
      .bot1 {
        margin-left: 0.2px;
        margin-right: 13.8px;
        cursor: pointer;

        .activeimg {
          display: flex;
          width: 93px;
          height: 17px;
          background: url("../assets/image/bot-a.png");
          background-size: 100% 100%;
          padding-left: 10px;
          // padding-top: 5.2px;
          display: flex;
          flex-direction: row;
          align-items: center;
        }

        .img {
          display: flex;
          flex-direction: row;
          align-items: center;
          display: flex;
          width: 93px;
          height: 17px;
          background: url("../assets/image/bot.png");
          background-size: 100% 100%;
          padding-left: 10px;
          // padding-top: 6px;
        }

        .icon {
          //  width: 23.4px;
          // height: 20.2px;
          // margin-left: 5px;
          // width: 19%;
          // height: 55%;
        }

        .p1 {
          width: 94.2px;
          text-align: center;
          margin-left: -5.2px;
          margin-top: -47px;
          font-size: 18px;
          font-family: DingTalk JinBuTi;
          font-weight: 400;
          color: #ffffff;
          opacity: 0.6;
          text-shadow: 0px 4px 27px #0072ff;
          background: linear-gradient(
            0deg,
            rgba(113, 200, 255, 0.91) 0%,
            rgba(255, 255, 255, 0.91) 100%
          );
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        .p2 {
          width: 94.2px;
          text-align: center;
          margin-left: -5.2px;
          margin-top: -47px;
          font-size: 18px;
          font-family: DingTalk JinBuTi;
          font-weight: 400;
          color: #bfecec;
          text-shadow: 0px 0px 1px rgba(1, 17, 32, 0.35);
          background: linear-gradient(0deg, #b7ecff 0%, #ffffff 99.31640625%);
          -webkit-background-clip: text;
          // -webkit-text-fill-color: transparent;
        }
      }

      .bot2 {
        margin-left: 13.8px;
        margin-right: 13.8px;
        cursor: pointer;
        position: absolute;
        z-index: 20;
        top: -25%;
        left: 41.7%;
        right: 0;
        margin: auto;

        .activeimg {
          display: flex;
          width: 145px;
          height: 38.4px;
          // background: url("../assets/image/bot-a.png");
          background-size: 100% 100%;
          // padding-left: 10px;
          // padding-top: 5.2px;
          display: flex;
          flex-direction: row;
          align-items: center;
        }

        .img {
          display: flex;
          flex-direction: row;
          align-items: center;
          display: flex;
          width: 145.2px;
          height: 38.4px;
          background: url("../assets/image/bot.png");
          background-size: 100% 100%;
          // padding-left: 10px;
          // padding-top: 6px;
        }

        .p1 {
          width: 145.2px;
          text-align: center;
          margin-left: 1.8px;
          font-size: 18px;
          font-family: DingTalk JinBuTi;
          font-weight: 400;
          color: #ffffff;
          opacity: 0.6;
          text-shadow: 0px 4px 27px #0072ff;
          background: linear-gradient(
            0deg,
            rgba(113, 200, 255, 0.91) 0%,
            rgba(255, 255, 255, 0.91) 100%
          );
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        .p2 {
          text-align: center;
          width: 145.2px;
          margin-left: 1.8px;
          font-size: 18px;
          font-family: DingTalk JinBuTi;
          font-weight: 400;
          color: #bfecec;
          text-shadow: 0px 0px 1px rgba(1, 17, 32, 0.35);
          background: linear-gradient(0deg, #b7ecff 0%, #ffffff 99.31640625%);
          -webkit-background-clip: text;
          // -webkit-text-fill-color: transparent;
        }
      }

      .bot3 {
        z-index: 20;
        margin-left: 13.8px;
        margin-right: 13.8px;
        cursor: pointer;
        position: absolute;
        top: -25%;
        left: 50.5%;
        right: 0;
        margin: auto;

        .activeimg {
          display: flex;
          width: 145px;
          height: 38.4px;
          // background: url("../assets/image/bot-a.png");
          background-size: 100% 100%;
          padding-left: 18px;
          // padding-top: 5.2px;
          display: flex;
          flex-direction: row;
          align-items: center;
        }

        .img {
          display: flex;
          flex-direction: row;
          align-items: center;
          display: flex;
          width: 145px;
          height: 38.4px;
          // background: url("../assets/image/bot.png");
          background-size: 100% 100%;
          padding-left: 18px;
          // padding-top: 6px;
        }

        .p1 {
          width: 116.2px;
          text-align: center;
          margin-left: -5.2px;
          font-size: 18px;
          font-family: DingTalk JinBuTi;
          font-weight: 400;
          color: #ffffff;
          opacity: 0.6;
          text-shadow: 0px 4px 27px #0072ff;
          background: linear-gradient(
            0deg,
            rgba(113, 200, 255, 0.91) 0%,
            rgba(255, 255, 255, 0.91) 100%
          );
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        .p2 {
          width: 116.2px;
          text-align: center;
          margin-left: -5.2px;
          font-size: 18px;
          font-family: DingTalk JinBuTi;
          font-weight: 400;
          color: #bfecec;
          text-shadow: 0px 0px 1px rgba(1, 17, 32, 0.35);
          background: linear-gradient(0deg, #b7ecff 0%, #ffffff 99.31640625%);
          -webkit-background-clip: text;
          // -webkit-text-fill-color: transparent;
        }
      }
    }
  }

  .huangse {
    color: #ff831f;
  }

  .topbg {
    position: fixed;
    width: 1920px;
    height: 86px;
    top: 0;
    left: 0;
    z-index: 1;
  }

  .botbg {
    position: fixed;
    width: 1920px;
    height: 86px;
    bottom: 0;
    left: 0;
    z-index: 1;
  }

  .head {
    position: fixed;
    width: 1920px;
    height: 56px;
    display: flex;
    top: 5px;
    justify-content: center;
    z-index: 20;
    // pointer-events: none;

    .img {
      overflow: hidden;
      width: 100%;
      height: 18.75%;
    }

    .title {
      width: 1208px;
      height: 56px;
      background: url("../assets/image/head.png");
      background-size: 100% 100%;

      position: relative;

      .titlewenzi {
        // position: absolute;
        line-height: 48px;
        text-align: center;

        // font-size: 39px;
        // color: #FFFFFF;
        // text-stroke: 1px #FFFFFF;
        // background: linear-gradient(0deg, #71C3F7 0%, #FFFFFF 100%);
        // -webkit-text-stroke: 1px #FFFFFF;
        // -webkit-background-clip: text;
        // -webkit-text-fill-color: transparent;
        letter-spacing: 0.5px;
        font-family: Alibaba PuHuiTi;
        font-weight: 800;
        font-size: 23px;
        color: #c6ffff;
      }
    }
  }

  .now-time {
    position: fixed;
    top: 13px;
    left: -32px;
    display: flex;
    font-family: Bahnschrift;
    font-weight: normal;
    font-size: 24px;
    color: #e4f3ff;
    display: flex;
    z-index: 3;

    img {
      width: 126.8px;
      height: 39.8px;
      margin-top: -2px;
    }

    span {
      margin-left: 62px;
    }
  }

  .title-right {
    z-index: 33;
    position: absolute;
    top: 10px;
    right: 22px;
    display: flex;

    .opt {
      // margin-top: 1px;
      // margin-left: 8px;
      // width: 100px;
      display: flex;
      align-items: center;
      cursor: pointer;

      .opt1 {
        margin-left: 20px;
      }
    }

    .tianqi {
      cursor: pointer;
      margin-top: 2px;
      margin-left: 18px;
      // width: 45px;
      display: flex;
      align-items: center;
      width: 30px;
      height: 30px;

      .taiyang {
        width: 32px;
        height: 32px;
      }

      .wendu {
        font-family: DingTalk JinBuTi;
        font-weight: 400;
        font-size: 18px;
        color: #ffffff;
      }
    }
  }

  .logo {
    position: absolute;
    top: 11px;
    left: 15px;
    width: 171px;
    height: 35px;
  }

  .chart {
    margin-top: 24px;
    margin-bottom: 24px;
  }
}

.caidanlist {
  position: fixed;
  top: 6.3%;
  right: 22.1%;
  z-index: 10;

  .caidanimg1 {
    background: url("../assets/image/caidanimg1.png");
    background-repeat: no-repeat;
    width: 111px;
    height: 29px;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;
    cursor: pointer;

    .caidanicon1 {
      width: 14px;
      height: 16px;
    }

    .caifonsiez {
      font-family: DingTalk JinBuTi;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
    }

    .caixiangxia {
      width: 12px;
      height: 7px;
      cursor: pointer;
    }
  }

  .list {
    // 108px x 30px
    width: 111px;
    height: 29px;
    background-color: rgba(113, 155, 224, 0.2);

    font-family: DingTalk JinBuTi;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-top: 1px solid #719bdf;
  }

  .list:hover {
    background-color: rgba(113, 155, 224, 0.5);
    background-repeat: no-repeat;
    width: 111px;
    height: 29px;
    background-size: 100% 100%;
  }

  //
}

.active {
  background-color: rgba(113, 155, 224, 0.7) !important;
}

.sysname {
  z-index: 999;
  position: fixed;
  top: 42px;
  right: 418.864px;
  color: #fff;
  font-family: DingTalk JinBuTi;
  font-weight: 400;
  font-size: 14px;
  width: 100px;
  text-align: center;
}

.groups {
  z-index: 999;
  position: fixed;
  top: 43px;
  right: 423.864px;

  .subitem {
    margin-left: 3px;
    margin-right: -12px;
  }
}

/deep/.el-sub-menu .el-sub-menu__icon-arrow {
  margin-right: -12px !important;
}

/deep/.el-menu--vertical:not(.el-menu--collapse):not(.el-menu--popup-container)
  .el-menu-item,
.el-menu--vertical:not(.el-menu--collapse):not(.el-menu--popup-container)
  .el-menu-item-group__title,
.el-menu--vertical:not(.el-menu--collapse):not(.el-menu--popup-container)
  .el-sub-menu__title {
  padding-left: 27px !important;
}

/deep/ .el-menu-item-group__title {
  display: none !important;
}

/deep/ .el-sub-menu__title {
  background: url("../assets/image/caidanimg1.png");
  background-size: 100% 100%;
  font-family: DingTalk JinBuTi;
  font-weight: 400;
  font-size: 14px;
  color: #ccc;
  height: 32px;
}

/deep/ .el-menu-item {
  height: 32px !important;
  font-family: DingTalk JinBuTi;
  font-weight: 400;
  font-size: 14px;
  color: #ccc;
}

/deep/ .el-menu {
  border: none;
}

.loading_page {
  position: fixed;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 99999;
  display: flex;
  flex-direction: column;
  /* Stack items vertically */
  justify-content: center;
  /* Center items vertically */
  align-items: center;
  /* Center items horizontally */

  background-color: rgb(33, 33, 33);
  margin: 0;

  .inner-box {
    margin-left: 32.5px;
    position: relative;
    width: 36px;
    height: 36px;
    transform-style: preserve-3d;
    transform-origin: center;
    animation: 3s ctn infinite;
    transform-origin: 0 0;
    transform: rotateX(-30deg) rotateY(45deg) translate(0, 0);
  }

  .inner {
    position: absolute;
    width: 36px;
    height: 36px;
    text-align: center;
    line-height: 36px;
    color: #fff;
    border-radius: 6px;
    background: rgba(7, 127, 240, 0.1);
    border: 2px solid rgba(19, 108, 241, 0.986);
    transform-origin: center;
  }

  .inner:nth-child(1) {
    transform: rotateX(90deg) translateZ(18px);
    animation: 3s top infinite;
  }

  .inner:nth-child(2) {
    transform: rotateX(-90deg) translateZ(18px);
    animation: 3s bottom infinite;
  }

  .inner:nth-child(3) {
    transform: rotateY(90deg) translateZ(18px);
    animation: 3s left infinite;
  }

  .inner:nth-child(4) {
    transform: rotateY(-90deg) translateZ(18px);
    animation: 3s right infinite;
  }

  .inner:nth-child(5) {
    transform: translateZ(18px);
    animation: 3s front infinite;
  }

  .inner:nth-child(6) {
    transform: rotateY(180deg) translateZ(18px);
    animation: 3s back infinite;
  }

  @keyframes ctn {
    from {
      transform: rotateX(-35deg) rotateY(45deg) translate(-50%, -50%);
    }

    50% {
      transform: rotateX(-35deg) rotateY(405deg) translate(-50%, -50%);
    }

    to {
      transform: rotateX(-35deg) rotateY(405deg) translate(-50%, -50%);
    }
  }

  @keyframes top {
    from {
      transform: rotateX(90deg) translateZ(18px);
    }

    50% {
      transform: rotateX(90deg) translateZ(18px);
    }

    75% {
      transform: rotateX(90deg) translateZ(36px);
    }

    to {
      transform: rotateX(90deg) translateZ(18px);
    }
  }

  @keyframes bottom {
    from {
      transform: rotateX(-90deg) translateZ(18px);
    }

    50% {
      transform: rotateX(-90deg) translateZ(18px);
    }

    75% {
      transform: rotateX(-90deg) translateZ(36px);
    }

    to {
      transform: rotateX(-90deg) translateZ(18px);
    }
  }

  @keyframes left {
    from {
      transform: rotateY(90deg) translateZ(18px);
    }

    50% {
      transform: rotateY(90deg) translateZ(18px);
    }

    75% {
      transform: rotateY(90deg) translateZ(36px);
    }

    to {
      transform: rotateY(90deg) translateZ(18px);
    }
  }

  @keyframes right {
    from {
      transform: rotateY(-90deg) translateZ(18px);
    }

    50% {
      transform: rotateY(-90deg) translateZ(18px);
    }

    75% {
      transform: rotateY(-90deg) translateZ(36px);
    }

    to {
      transform: rotateY(-90deg) translateZ(18px);
    }
  }

  @keyframes front {
    from {
      transform: translateZ(18px);
    }

    50% {
      transform: translateZ(18px);
    }

    75% {
      transform: translateZ(36px);
    }

    to {
      transform: translateZ(18px);
    }
  }

  @keyframes back {
    from {
      transform: rotateY(180deg) translateZ(18px);
    }

    50% {
      transform: rotateY(180deg) translateZ(18px);
    }

    75% {
      transform: rotateY(180deg) translateZ(36px);
    }

    to {
      transform: rotateY(180deg) translateZ(18px);
    }
  }

  .loading-text {
    z-index: 9999;
    color: #fff;
    /* Text color */
    margin-top: 15px;
    /* Space between the cube and text */
    font-size: 16px;
    /* Text size */
    letter-spacing: 1px;
    /* Letter spacing */
    text-align: center;
  }
}

.tablist {
  width: 735px;
  height: 90px;
  position: absolute;
  top: 130px;
  left: 604px;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.5s ease-in-out;
  opacity: 1;

  .item {
    font-family: Alibaba PuHuiTi;
    font-weight: bold;
    font-size: 16px;
    color: #ffffff;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;

    .list {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-top: 10px;

      background-repeat: no-repeat;
      background-size: 100% 100%;
      width: 131px;
      height: 67px;
    }

    .name {
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      font-size: 12px;
      color: #ffffff;
    }
  }
}

.tablistisshow {
  pointer-events: none;
  opacity: 0;
}

.dapanniu {
  position: absolute;
  top: 10px;
  left: 384px;
  // width: 260px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-family: Source Han Sans SC;
  font-weight: 500;
  font-size: 13px;
  color: #ccfffe;
  cursor: pointer;

  cursor: pointer;

  .item {
    background: url("../assets/image/zuoanniu.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 114px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      margin-left: 4px;
    }
  }
}

.beijing {
  background: url("../assets/image/xialabeij.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 104px;
  height: 167px;
  position: absolute;
  top: 50px;
  left: 391px;
  padding-top: 4px;
  z-index: 22220;

  .item {
    width: 104px;
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: Source Han Sans SC;
    font-weight: 500;
    font-size: 13px;
    color: #86a6b7;
    cursor: pointer;
    transition: background-color 0.3s;
    /* 平滑的颜色过渡 */
  }

  .item1 {
    cursor: pointer;
    background: url("../assets/image/itembgcs.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;

    // width: 114px;
    // height: 26px;
    // display: flex;
    // align-items: center;
    // justify-content: center;
    // font-family: Source Han Sans SC;
    // font-weight: 500;
    // font-size: 13px;
    color: #ffffff;
  }

  .item:hover {
    cursor: pointer;
    background: url("../assets/image/itembgcs.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;

    color: #ffffff;
  }
}

.beijing2 {
  background: url("../assets/image/xialabeij.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 114px;
  height: 251px;
  position: absolute;
  top: 50px;
  left: 308px;
  z-index: 999;
  padding-top: 10px;

  .item {
    width: 114px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: Source Han Sans SC;
    font-weight: 500;
    font-size: 10px;
    color: #86a6b7;
    cursor: pointer;
    transition: background-color 0.3s;
    /* 平滑的颜色过渡 */
  }

  .item1 {
    cursor: pointer;
    background: url("../assets/image/itembgcs.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 114px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: Source Han Sans SC;
    font-weight: 500;
    font-size: 10px;
    color: #ffffff;
  }

  .item:hover {
    cursor: pointer;
    background: url("../assets/image/itembgcs.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 114px;
    height: 24px;
    color: #ffffff;
  }
}

.tablezujian {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
}

.content1 {
  /* 设置滚动条的样式 */
  padding-bottom: 10px;
  background: url("../assets/img/tqbg.png");
  background-size: 100% 100%;
  position: fixed;
  top: 52px;
  right: 24.4px;
  width: 282px;
  height: 255px;

  z-index: 99999999999999999;

  .xuanzeqi {
    height: 54px;
    display: flex;
    align-items: center;
    cursor: pointer;

    .pp {
      margin-left: 10px;
      margin-right: 10px;
      font-size: 20px;
      color: #fff;
    }
  }

  .tianjiandtime {
    cursor: pointer;
    display: flex;
    justify-content: space-evenly;
    align-items: center;

    .tianqi {
      .img {
        width: 36px;
        height: 36px;
      }

      .img1 {
        width: 36px;
        height: 36px;
      }

      .time {
        color: #fff;
        margin-top: 10px;
      }
    }
  }

  .tianjiandtime2 {
    display: flex;
    justify-content: space-evenly;
    align-items: center;

    .tianqi {
      .img {
        width: 36px;
        height: 36px;
      }

      .img1 {
        width: 36px;
        height: 36px;
      }

      .time {
        color: #fff;
        margin-top: 10px;
      }
    }
  }

  .tianjiandtime1 {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    opacity: 0.5;
    pointer-events: none;

    .tianqi {
      .img {
        width: 36px;
        height: 36px;
      }

      .img1 {
        width: 36px;
        height: 36px;
      }

      .time {
        color: #686868;
        margin-top: 10px;
      }
    }
  }
}

.switch-btn {
  cursor: pointer;
  width: 37.2px;
  height: 18.8px;
  position: relative;
  border: 1px solid #dfdfdf;
  background-color: #fdfdfd;
  box-shadow: #dfdfdf 0 0 0 0 inset;
  border-radius: 15px;
  background-clip: content-box;
  display: inline-block;
  -webkit-appearance: none;
  user-select: none;
  outline: none;
}

.switch-btn:before {
  content: "";
  width: 18px;
  height: 18px;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 20px;
  background-color: #fff;
  box-shadow: 0 0.0125rem 0.0375rem rgba(0, 0, 0, 0.4);
}

.switch-btn:checked {
  border-color: #56b0d4;
  box-shadow: #56b0d4 0 0 0 0.2rem inset;
  background-color: #56b0d4;
}

.switch-btn:checked:before {
  left: 18px;
}

.switch-btn.switch-btn-animbg {
  transition: background-color ease 0.4s;
}

.switch-btn.switch-btn-animbg:before {
  transition: left 0.3s;
}

.switch-btn.switch-btn-animbg:checked {
  box-shadow: #dfdfdf 0 0 0 0 inset;
  background-color: #56b0d4;
  transition: border-color 0.4s, background-color ease 0.4s;
}

.switch-btn.switch-btn-animbg:checked:before {
  transition: left 0.3s;
}
.imgimg {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.anniuniu {
  width: 30px;
  height: 25px;
}
</style>
