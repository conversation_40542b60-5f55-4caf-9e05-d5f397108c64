<template>
  <div class="echart" ref="echart"></div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  props: ["echartData"],
  data() {
    return {};
  },
  watch: {
    echartData(newVal) {
      this.init();
    },
  },
  mounted() {
    this.init();
  },

  methods: {
    init() {
      const myChart = echarts.init(this.$refs.echart);

      let dataArr = [
        {
          name: "一级告警",
          value: 215,
          unit: "",
        },
        {
          name: "二级告警",
          value: 168,
          unit: "",
        },
        {
          name: "三级告警",
          value: 84,
          unit: "",
        },
        {
          name: "四级告警",
          value: 84,
          unit: "",
        },
      ];
      let data = [];
      let total = 0;
      let colorList = ["#5C9DEE", "#89F6C1", "#F1C274", "#DC3526"];
      let placeHolderStyle = {
        normal: {
          label: {
            show: false,
          },
          labelLine: {
            show: false,
          },
          color: "rgba(0, 0, 0, 0)",
          borderColor: "rgba(0, 0, 0, 0)",
          borderWidth: 0,
        },
      };
      for (let i = 0; i < dataArr.length; i++) {
        total += dataArr[i].value;
      }
      for (let i = 0; i < dataArr.length; i++) {
        data.push(dataArr[i]);
        data.push({
          value: parseInt(total * 0.015),
          name: "",
          itemStyle: placeHolderStyle,
        });
      }

      const option = {
        tooltip: {
          show: true,
          trigger: "item",
          formatter: function (data) {
            return (
              data.name +
              "：" +
              "<br/>" +
              " 数量： " +
              data.value +
              "<br/> 占比： " +
              data.percent +
              "%"
            );
          },
        },
        legend: {
          orient: "vertical",
          top: "1%",
          right:"1%",
          itemWidth: 5,
          itemHeight: 5,
       
          textStyle: {
            color: "#fff",
            fontSize:13,
          },

        },
        graphic: {
          elements: [
            {
              type: "group",
              left: "center",
              top: "40%",
              children: [
                {
                  type: "text",
                  z: 100,
                  top: -22,
                  left: "center",
                  style: {
                    fill: "rgba(255,255,255,0.7)",
                    fontWeight: "normal",
                    fontSize: 18,
                    textAlign: "center",
                    text: ["累计", ""].join("\n"),
                  },
                },
                {
                  type: "text",
                  z: 101,
                  left: "center",
                  top: 0,
                  style: {
                    fill: "#1ee3c5",
                    fontSize: 25,
                    textAlign: "center",
                    text: [total].join("\n"),
                  },
                },
              ],
            },
          ],
        },
        series: [
          {
            hoverOffset: 0,
            startAngle: 90,
            type: "pie",
            radius: [58, 56],
            color: colorList,
            center: ["50%", "50%"],
            tooltip: {
              show: false,
            },
            labelLine: {
              show: false,
            },
            label: {
              show: false,
            },
            data: data,
          },
          {
            hoverOffset: 1,
            startAngle: 90,
            type: "pie",
            radius: [60, 77],
            center: ["50%", "50%"],
            color: colorList,
            label: {
              normal: {
                formatter: (datas) => {
                  // var unit = datas.data.unit ? datas.data.unit : "%";
                  if (datas.name) {
                    return "{d|" + datas.data.value + "}";
                  } else {
                    return "";
                  }
                },
                rich: {
                  b: {
                    fontSize: 12,
                    color: "#fff",
                    align: "left",
                    padding: [-12, 0, 0, 0],
                  },
                  d: {
                    fontSize: 18,
                    align: "left",
                    fontFamily: "DINOT",
                    padding: [-5, 4, 0, 0],
                    lineHeight: 24,
                  },
                  c0: {
                    fontSize: 14,
                    align: "left",
                    padding: [2, 10, 4, 10],
                    borderColor: colorList[0],
                    borderWidth: 1,
                  },
                },
              },
            },
            labelLine: {
              normal: {
                show: true,
                length: 10,
                length2: 20,
              },
            },
            data: data,
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>

<style lang="less" scoped>
.echart {
  // margin-top: 40px;
  width: 100%;
  height: 100%;
}
</style>
