<template>
  <div class="echart" ref="echart"></div>
</template>
  
<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  methods: {
    initData() { },
    init() {
      const myChart = echarts.init(this.$refs.echart);



      var data1 = [20, 30, 20, 30, 20, 30, 20];
      var data2 = [9, 30, 9, 60, 70, 20, 59];
      var data3 = [20, 30, 20, 30, 20, 30, 20];
      var data4 = [9, 30, 9, 60, 70, 20, 59];
      var datacity = [
        "一楼仪器设备",
        "二楼仪器设备",
        "三楼仪器设备",
        "四楼仪器设备",
        "五楼仪器设备",

      ];
      const option = {
        title: {
          text: "",
          left: "20px",
          top: "14",
          textStyle: {
            color: "#fff",
            fontSize: 14,
            fontWeight: "400",
          },
        },
        color: [
          // "#66C4FC",
          // "#7DFDD2",
          // "#83FB45",
          "#E1FC4A",
          "#EE8B3D",
          "#E93437",
          "#EB46FB",
        ],
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            crossStyle: {
              color: "#999",
            },
            lineStyle: {
              type: "dashed",
            },
          },
        },
        grid: {
          //图表的位置
          top: "20%",
          left: "3%",
          right: "4%",
          bottom: "5%",
          containLabel: true,
        },

        legend: {
          data: ["氧气O₂", "氮气N₂", "氦气He", "氢气H₂",],
          top: "2%",
          right: "50",
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            color: "#fff",
            fontSize: 15,
          },
        },
        xAxis: {
          axisLabel: {
            interval: 0,
            show: true,
            splitNumber: 15,
            textStyle: {
              fontSize: 10,
              color: "#fff",
            },
          },
          type: "category",
          data: [
            "1月",
            "2月",
            "3月",
            "4月",
            "5月",
            "6月",
            "7月",
            "8月",
            "9月",
            "10月",
            "11月",
            // "12月 ",
          ],
          splitLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
        },
        yAxis: {
          type: "value",
          // max: max_value>=100? max_value + 100: max_value+10,
          // max: max_value > 100 ? max_value * 2 : max_value + 10,
          // interval: 10,
          // nameLocation: "center",
          axisLabel: {
            color: "#fff",
            textStyle: {
              fontSize: 12,
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "#F3F4F4",
            },
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
        },
        series: [
          {
            name: "氧气O₂",
            type: "line",
            smooth: true,
            data: [70, 74, 60, 50, 55, 65, 78, 69, 72, 77, 68,],
          },
          {
            name: "氮气N₂",
            type: "line",
            smooth: true,
            data: [67, 70, 75, 50, 60, 72, 65, 79, 60, 78, 63,],
          },
          {
            name: "氦气He",
            type: "line",
            smooth: true,
            data: [50, 60, 62, 58, 53, 73, 67, 60, 72, 66, 70,],
          },
          {
            name: "氢气H₂",
            type: "line",
            smooth: true,
            data: [68, 70, 50, 59, 63, 71, 75, 78, 64, 76, 79,],
          },
          // {
          //   name: "二氧化碳CO₂",
          //   type: "line",
          //   smooth: true,
          //   data: [60, 72, 66, 57, 73, 64, 78, 60, 74, 68, 69,],
          // },
          // {
          //   name: "一氧化碳CO",
          //   type: "line",
          //   smooth: true,
          //   data: [55, 65, 72, 60, 63, 74, 61, 69, 65, 77, 78,],
          // },
          // {
          //   name: "氨气NH₃",
          //   type: "line",
          //   smooth: true,
          //   data: [60, 67, 55, 58, 68, 62, 76, 65, 73, 69, 72,],
          // },
          // {
          //   name: "氩气Ar",
          //   type: "line",
          //   smooth: true,
          //   data: [52, 74, 60, 65, 58, 63, 76, 69, 77, 65, 73, ],
          // }
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>
  
<style lang="less" scoped>
.echart {
  width: 593px;
  height: 390px;
}
</style>