<template>
  <div class="echart" ref="echart"></div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  props: {
    chartData: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  methods: {
    init() {
      const myChart = echarts.init(this.$refs.echart);
      let data = this.chartData
        ? this.chartData
        : {
          title: ["已领用", "未领用"],
          xAxisdata: ["20", "21", "22", "23", "25", "24"],
          yAxisdata1: [4, 7, 5, 9, 6, 5],
          yAxisdata2: [4, 7, 5, 9, 6, 5],
        };
      var xData = function () {
        var data = [];
        for (var i = 1; i < 13; i++) {
          data.push(i + "月份");
        }
        return data;
      }();
      const option = {
        "title": {
          // "text": "本年商场顾客男女人数统计",
          // "subtext": "B<PERSON> Wang <PERSON>ding",
          x: "4%",

          textStyle: {
            color: '#fff',
            fontSize: '22'
          },
          subtextStyle: {
            color: '#90979c',
            fontSize: '16',

          },
        },
        "tooltip": {
          "trigger": "axis",
          "axisPointer": {
            "type": "shadow",
            textStyle: {
              color: "#fff"
            }

          },
        },
        "grid": {
          "borderWidth": 0,
          "top": 40,
          "bottom": 65,
          textStyle: {
            color: "#fff"
          }
        },
        "legend": {
          x: '8%',
          top: '0%',
          textStyle: {
            "color": "#fff",
            fontSize: 15
          },
          "data": ['当月使用量', '当月库存量', '平均月需求量']
        },


        "calculable": true,
        "xAxis": [{
          "type": "category",
          "axisLine": {
            lineStyle: {
              color: '#90979c'
            }
          },
          "splitLine": {
            "show": false
          },
          "axisTick": {
            "show": false
          },
          "splitArea": {
            "show": false
          },
          "axisLabel": {
            rotate: 45,
            "interval": 0,
            fontSize: 14,
            color: "#fff"
          },
          "data": ["氧气O₂", "氮气N₂", "氦气He", "氢气H₂", "二氧化碳CO₂", "一氧化碳CO", "氨气NH₃", "氩气Ar"],
        }],
        "yAxis": [{
          "type": "value",
          "splitLine": {
            "show": false
          },
          "axisLine": {
            lineStyle: {
              color: '#90979c'
            }
          },
          "axisTick": {
            "show": false
          },
          "axisLabel": {
            "interval": 0,
            color: "#fff",
            fontSize: 16
          },
          "splitArea": {
            "show": false
          },

        }],
        // "dataZoom": [{
        //     "show": true,
        //     "height": 30,
        //     "xAxisIndex": [
        //         0
        //     ],
        //     bottom: 30,
        //     "start": 10,
        //     "end": 80,
        //     handleIcon: 'path://M306.1,413c0,2.2-1.8,4-4,4h-59.8c-2.2,0-4-1.8-4-4V200.8c0-2.2,1.8-4,4-4h59.8c2.2,0,4,1.8,4,4V413z',
        //     handleSize: '110%',
        //     handleStyle:{
        //         color:"#d3dee5",

        //     },
        //        textStyle:{
        //         color:"#fff"},
        //        borderColor:"#90979c"


        // }, {
        //     "type": "inside",
        //     "show": true,
        //     "height": 15,
        //     "start": 1,
        //     "end": 35
        // }],
        "series": [{
          "name": "当月使用量",
          "type": "bar",
          "stack": "总量",
          "barMaxWidth": 35,
          "barGap": "10%",
          "itemStyle": {
            "normal": {
              "color": "rgba(255,144,128,1)",
              "label": {
                "show": true,
                "textStyle": {
                  "color": "#fff"
                },
                "position": "inside",
                formatter: function (p) {
                  return p.value > 0 ? (p.value) : '';
                }
              }
            }
          },
          "data": [92, 317, 211, 148, 262, 74, 115, 190],
        },

        {
          "name": "当月库存量",
          "type": "bar",
          "stack": "总量",
          "itemStyle": {
            "normal": {
              "color": "rgba(0,191,183,1)",
              "barBorderRadius": 0,
              "label": {
                "show": true,
                "position": "inside",
                formatter: function (p) {
                  return p.value > 0 ? (p.value) : '';
                }
              }
            }
          },
          "data": [408, 248, 335, 297, 445, 234, 387, 301]
        }, {
          "name": "平均月需求量",
          "type": "line",
          symbolSize: 10,
          symbol: 'circle',
          "itemStyle": {
            "normal": {
              "color": "rgba(252,230,48,1)",
              "barBorderRadius": 0,
              "label": {
                "show": true,
                "position": "top",
                formatter: function (p) {
                  return p.value > 0 ? (p.value) : '';
                },
                "textStyle": {
                  "color": "#fff", // 数值颜色设置为红色
                  fontSize: 15
                }
              }
            }
          },
          "data": [385, 745, 606, 415, 607, 208, 402, 691]
        },
        ]
      };

      var app = {
        currentIndex: -1,
      };
      // setInterval(function () {
      //   var dataLen = option.series[0].data.length;

      //   // 取消之前高亮的图形
      //   myChart.dispatchAction({
      //     type: 'downplay',
      //     seriesIndex: 0,
      //     dataIndex: app.currentIndex
      //   });
      //   app.currentIndex = (app.currentIndex + 1) % dataLen;
      //   //console.log(app.currentIndex);
      //   // 高亮当前图形
      //   myChart.dispatchAction({
      //     type: 'highlight',
      //     seriesIndex: 0,
      //     dataIndex: app.currentIndex,
      //   });
      //   // 显示 tooltip
      //   myChart.dispatchAction({
      //     type: 'showTip',
      //     seriesIndex: 0,
      //     dataIndex: app.currentIndex
      //   });


      // }, 1000);

      myChart.setOption(option);
    },
  },
};
</script>

<style lang="less" scoped>
.echart {
  width: 450px;
  height: 380px;
}
</style>
