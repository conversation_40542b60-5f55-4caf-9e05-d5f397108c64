<template>
  <div>
    <component :is="componentTag" @fatherMethoddd="fatherMethoddd" v-if="isshowwhat"></component>
    <div class="botbtn">
      <div v-for="(item, index) in changeTitle" :key="index" :class="titactive == index ? 'btt1' : 'btt'"
        @click="changetit(index)">
        {{ item }}
      </div>
    </div>
    <tedai :ids="ids" :selectedItem="selectedItem" class="sbdetails" :zengtiimg="zengtiimg" v-if="isshowsss"
      @hidedetails="hidedetailsss"></tedai>
    <biaoGe :Title="Title" @xuanze-dialog="xuanzedialog" v-if="isshow" @hidedetails="hidedetails" :tableTitle="tableTitle"
      :tableDataItem="devicedata"></biaoGe>
    <div class="container" v-if="!isshowwhat">
      <div class="left-panel" :class="{
        'left-panel-active': showdh,
        'no-animation': noAnimation,
        'left-panel-active1': showdh1,
      }">
        <Title2 @open-dialog="opendialog" class="ltitle1" tit="大仪管理">
          <el-cascader class="sect" placeholder="请选择类别" :options="equipmentTags" :show-all-levels="true"
            @change="handleCascaderChange"></el-cascader>

          <el-input class="el-input" v-model="input" placeholder="请输入关键字"></el-input>
          <img class="suosuo" src="../assets/image/suosuo.png" alt="">
          <div class="box">

            <!-- <div class="xiaobox">
              <img class="siqiu" src="../assets/image/shixinqiu.png" alt="" />
              <div class="shuru">全部设备</div>
            </div> -->
            <div class="menu-container">
              <!-- 动态生成菜单 -->
              <div class="menu">
                <div v-for="(menu, index) in devicedata" :key="index" class="menu-group">
                  <div class="qiuqiu">
                    <img class="siqiu" src="../assets/image/shixinqiu.png" alt="" />
                    <div class="menu-item" @click="toggleSubMenu(menu)">
                      {{ menu.name }}
                    </div>
                    <!-- <div class="listtypes" @click="showdetails(menu)">详情</div> -->
                  </div>

                  <!-- <div v-show="activeSubmenu === menu.id" class="submenu">
                    <div v-for="(item, subIndex) in menu.items" :key="subIndex" class="submenu-item"
                      @click="setContent(item)">
                      <p class="ellipsis" :title="item.name">{{ item.name }}</p>
                    </div>
                  </div> -->
                </div>
              </div>
            </div>
          </div>
          <el-pagination @size-change="handleSizeChange" hide-on-single-page="true" @current-change="handleCurrentChange"
            :page-size="16" :pager-count="4" layout="prev, pager, next,total" :total="total">
          </el-pagination>
          <!-- shebei12.png -->
        </Title2>
      </div>
      <!-- shebeibgc.png -->
      <!-- 右侧内容 -->
      <!-- 右侧内容 -->

      <div class="right-panel" :class="{
        'right-panel-active': showdh,
        'no-animation': noAnimation,
        'right-panel-active1': showdh1,
      }"></div>
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import { mapActions, mapGetters } from 'vuex';
import component0 from "@/views/dayi/zichan.vue";
import huanxing from "@/components/echarts/huanxing.vue";
import zhexian from "@/components/echarts/zhexian.vue";
import zhexian1 from "@/components/echarts/zhexian1.vue";
import SystemDete from "@/components/echarts/SystemDete.vue";
import echarts1 from "@/components/echarts/bingjifang/echarts3.vue";
import tedai from "@/components/common/cl_details.vue";
import biaoGe from "@/components/common/biaoGes.vue";
import shuangxiang from "@/components/echarts/shuangxiang.vue";
import axios from "axios";
// import details from "@/components/common/details.vue";
// 使用环境变量设置基础 API 地址
const baseURL = process.env.VUE_APP_BASE_API || '/lims/api';

const api = axios.create({
  baseURL
});
export default {
  // import引入的组件需要注入到对象中才能使用
  components: {
    tedai,
    huanxing,
    zhexian,
    zhexian1,
    SystemDete,
    echarts1,
    shuangxiang,
    biaoGe,
    component0
  },
  props: ["tabledata", "zengtiimg"],

  data() {
    // 这里存放数据
    return {
      currentPage1: 5,
      isshowwhat: true,
      isshowsss: false,
      titactive: 0,
      total: null,
      changeTitle: ["数据统计", "数据列表"],
      activeSubmenu: null, // 当前激活的子菜单
      activeContent: null, // 当前显示的内容
      newArr: [],
      isshow: false,
      xxxx: false,
      cgqlist: [],
      listtable: [],
      options: [
        {
          value: 'zhinan',
          label: '指南',
          children: [{
            value: 'shejiyuanze',
            label: '设计原则',

          }, {
            value: 'daohang',
            label: '导航',

          }]
        },
      ],
      devicedata: [

      ],
      input: "",
      activeTab: "today",
      listst: [
        {
          name: "广东质检中诚认证有限公司到中广...",
        },
        { name: "材料科学、化学工程及医药研发成..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
        { name: "植酸检测方法及作用" },
        { name: "兔拉检测：行业中芯片造假技术..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
      ],
      showdh: true,
      showdh1: false,
      noAnimation: false,
      selectedItem: {
        name: "离子溅射仪", //产品名称
        imgurl: "https://3d.dddtask.cn/enginner-xufeng/image/tianjin/lizi.png",
        location: "北洋园校区54楼, E105", //位置
        status: "已领用", //仪器状态  可领用
        details: [
          {
            name: "产品名称",
            value: "离子溅射仪",
          },
          {
            name: "原值",
            value: "--",
          },
          {
            name: "购入时间",
            value: "2020/09/02",
          },
          {
            name: "品牌",
            value: "--",
          },
          {
            name: "生产厂家",
            value: "--",
          },
          {
            name: "供应商名称",
            value: "--",
          },
          {
            name: "供应商联系信息",
            value: "--",
          },
        ],
        maintenance_records: {
          maintenance_content: "校准和系统升级", //维护内容
          date: "2024-01-10", //维护时间
          next_maintenance_date: "2022-01-10", //下次维护时间
        },
        management_name: "王工",
        management_contact_info: "15698567542",
      },
      tableDataItem: [],
      Title: "资产管理",
      tableTitle: [
        { key: "楼层" },
        { key: "设备编号" },
        { key: "设备名称" },
        { key: "房间号" },
        { key: "模型" },
        { key: "设备状态" },
        { key: "状态说明" },
      ],
      ids: null,
      nhlist: [
        {
          title: "供气压力",
          status: "0.3Mpa",
          unit: "℃",
        },

        {
          title: "供气流量",
          status: "6M3/min",
          unit: "㎡",
        },
        {
          title: "露点温度",
          status: "6℃",
          unit: "℃",
        },
        {
          title: "含氧量",
          status: "6PPM",
          unit: "㎡",
        },
      ],
      warnlist1: [
        {
          type: 1,
          name: "检测到烟雾，可能有着火灾的发生...",
          value: "",
          time: "09:13:59  2023-06-07",
        },
        {
          type: 2,
          name: "检测到烟雾，可能预示着火灾的发生..",
          value: "",
          time: "10:13:18  2023-06-12",
        },
        {
          type: 3,
          name: "实验室内检测到漏水，可能来自冷凝水..",
          value: "",
          time: "10:13:18  2023-06-12",
        },
      ],
      isButton2Active: false,
      status: "巡检中",
      status1: "已完成",
      status2: "待巡检",
      selectedIndex: 0,
      componentTag: "component0",
      token: '',
    };
  },
  // 计算属性类似于data概念
  computed: {
    ...mapGetters('equipment', ['equipmentTags']),
  },
  // 监控data中的数据变化
  watch: {},
  // 方法集合
  methods: {
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.getyiqidetails(this.token, val - 1)
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.getyiqidetails(this.token, val - 1)
    },
    async gettoken(id) {
      try {
        const response = await api.post('', {
          "method": "equipment/searchEquipments",
          "params": {
            "criteria": {
              "cat": id,
              // "group": 1,
              // "searchtext": "搜索内容"

            }
          }
        });

        // 检查是否成功拿到 token
        if (response.data && response.data.response.token) {
          const token = response.data.response.token;
          this.token = token
          // 将 token 存入 localStorage
          // localStorage.setItem('authToken', token);
          console.log('535:', response.data.response);
          this.total = response.data.response.total
          this.handleSizeChange(1)

        } else {
          console.error('登录成功但未返回 token:', response.data.response);
        }
      } catch (error) {
        console.error('登录失败:', error);
      }

    },
    getyiqidetails(token, start) {
      const headers = {
        clientid: '5a298e93-158d-4e22-83cf-6ceb62e9b4f1',
        clientsecret: '2c8ec39e-9887-482a-b28b-e64c496b601c'
      };
      const body = {
        "method": "equipment/getEquipments",
        "params": {
          "token": token,
          "start": start ? start * 16 : 0,
          "num": 16
        }
      };
      axios.post('http://yiqi.tju.edu.cn/lims/api', body, {})
        .then(response => {
          console.log(response.data, 535);
          this.devicedata = response.data.response
        })
        .catch(error => {
          console.error('Error:', error);
        });
    },
    ...mapActions('equipment', ['fetchEquipmentTags']),
    changetit(index) {
      this.titactive = index
      this.isshowwhat = !index
      if (index == 1) {
        this.showdh = false;
        this.showdh1 = true;
        this.noAnimation = true;
      } else {
        this.showdh = true;
        this.showdh1 = false;
        this.noAnimation = false;
      }

    },
    handleCascaderChange(value) {
      console.log('选中的值:', value[1]);
      this.gettoken(value[1])
    },
    hidedetailsss() {
      this.isshowsss = false;
    },
    opendialog(payload) {
      if (payload == 1) {
        this.isshow = true;

        this.data.forEach((item) => {
          if (item.category == "电子显微镜") {
            this.isshow = true;
            this.tableDataItem = item.items;
            console.log(this.tableDataItem);
          }
        });
      }

      // 在这里处理事件
    },
    toggleSubMenu(item) {
      this.isshowsss = true
      console.log(item, '选中的设备信息');
      this.selectedItem = {
        name: item.name,
        imgurl: item.icon128_url,
        location: item.location + item.location2, //位置
        status: !item.is_using ? '当前使用' : "可使用", //仪器状态  可领用
        details: [
          {
            name: "产品名称",
            value: item.name,
          },
          {
            name: "价格",
            value: item.price,
          },
          {
            name: "购入时间",
            value: new Date(item.purchased_date * 1000),
          },
      {
        name: "制造国家",
        value: item.manu_at,
      },
      {
        name: "生产厂家",
        value: item.manufacturer,
      },
      {
        name: "负责人",
        value: item.contact,
      },
      {
        name: "联系电话",
        value: item.phone,
      },
        ],
      maintenance_records: {
        maintenance_content: "校准和系统升级", //维护内容
          date: "2024-01-10", //维护时间
            next_maintenance_date: "2022-01-10", //下次维护时间
        },
      management_name: "王工",
        management_contact_info: "15698567542",
      }
  },
  // // 设置内容
  // setContent(content) {
  //   this.isshowsss = true;
  //   this.selectedItem = content;
  //   console.log(this.selectedItem);

  //   this.activeContent = content;
  // },
  showdetails(item) {
    // item.items.forEach((item) => {
    //   this.newArr.push({ name: item.name });
    // });
    // console.log(this.newArr);

    this.isshow = true;
    this.tableDataItem = item.items;
  },
  hidedetails() {
    this.isshow = false;
  },

  oc(value) {
    console.log(value, "收到的值");
    this.showdh = value;
  },
  xuanzedialog(value) {
    const optionMapping = {
      选项1: 0,
      选项2: 1,
      选项3: 2,
      选项4: 3,
      选项5: 4,
      选项6: 5,
      选项7: 6,
      选项8: 7,
      选项9: 8,
      选项10: 9,
      选项11: 10,
      选项12: 11,
      选项13: 12,
    };

    const index = optionMapping[value];
    if (index !== undefined) {
      this.tableDataItem = this.data[index].items;
    } else {
      console.error("无效的选项: ", value);
    }
  },
},
// 生命周期 - 创建完成（可以访问当前this实例）
created() { },
// 生命周期 - 挂载完成（可以访问DOM元素）
mounted() {
  this.gettoken('')
  this.showdh1 = true;
  this.fetchEquipmentTags();
  // setTimeout(() => {
  //   this.showdh1 = false;
  //   this.noAnimation = false;
  // }, 1000); // 动画持续时间为1秒
  console.log(1222);
},
beforeCreate() { }, // 生命周期 - 创建之前
beforeMount() { }, // 生命周期 - 挂载之前
beforeUpdate() { }, // 生命周期 - 更新之前
updated() { }, // 生命周期 - 更新之后
beforeUnmount() {
  // 在组件销毁之前清除定时器
  console.log(1111);
},

unmounted() {
  console.log(2222);
}, // 生命周期 - 销毁之前
destroyed() {
  console.log(1221);
}, // 生命周期 - 销毁完成
activated() { }, // 如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="less" scoped>
.container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: stretch;
  height: 1080px;

  // text-align: center;
  /* 定位 el-cascader 的 placeholder 样式 */

  /deep/.sect.el-tooltip__trigger {
    text-align: left;
    width: 200px;
    color: #fff;
    // background-color: #00ffc0;
  }

  /deep/.el-input__wrapper {
    box-shadow: none;
    border: none;
    background-color: #5A6972;
    color: #fff;
  }

  /deep/.sect .el-input__inner {
    color: #fff;
  }

  .sbdetails {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
  }

  .el-input {
    margin-left: 5px;
    width: 145px;
    height: 34px;
    color: #fff !important;

    ::v-deep .el-input__wrapper {
      background: url("../assets/image/inputss.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
      box-shadow: none !important;
      color: #fff !important;
    }

    /deep/.el-input__inner {
      color: #fff !important;
    }

    .el-input__inner::placeholder {
      color: #fff;
      /* 设置占位符颜色 */

    }
  }

  .suosuo {
    position: absolute;
    top: 62px;
    right: -32px;
    cursor: pointer;
  }

  .box {
    // margin-top: 6px;
    padding-top: 5px;
    margin-bottom: 0.225rem;
    max-height: 745px;
    width: 330px;
    // height: 800px;

    overflow-y: scroll;

    /* 设置垂直滚动条 */
    /* 设置滚动条的样式 */
    &::-webkit-scrollbar {
      width: 0.1px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条轨道的样式 */
    &::-webkit-scrollbar-track {
      background-color: #f1f1f1;
      /* 设置滚动条轨道的背景色 */
    }

    /* 设置滚动条滑块的样式 */
    &::-webkit-scrollbar-thumb {
      background-color: #334f6e;
      /* 设置滚动条滑块的背景色 */
    }

    /* 鼠标悬停在滚动条上时的样式 */
    &::-webkit-scrollbar-thumb:hover {
      background-color: #555;
      /* 设置鼠标悬停时滚动条滑块的背景色 */
    }





    .xiaobox {
      margin-top: 20px;
      display: flex;
      align-items: center;

      .siqiu {
        width: 16px;
        height: 16px;
      }

      .shuru {
        display: flex;
        align-items: center;
        padding-left: 10px;
        background: url("../assets/image/shebei12.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 261px;
        height: 32px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 17px;
        color: #ffffff;
      }
    }

    .xiaoboxs {
      cursor: pointer;
      margin-top: 14px;
      display: flex;
      margin-left: 5px;
      align-items: center;

      .nihaowo {
        width: 78px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 10px;
        color: #ffffff;
        display: flex;
        white-space: nowrap;
        /* 不换行 */
        overflow: hidden;
        /* 超出部分隐藏 */
        text-overflow: ellipsis;
        /* 超出部分显示省略号 */
      }

      .siqiu {
        width: 16px;
        height: 16px;
        margin-left: 10px;
        margin-right: 7px;
      }

      .shuru {
        display: flex;
        align-items: center;
        padding-left: 10px;
        background: url("../assets/image/shebei12.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 261px;
        height: 32px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 17px;
        color: #ffffff;
        white-space: nowrap;
        /* 不换行 */
        overflow: hidden;
        /* 超出部分隐藏 */
        text-overflow: ellipsis;
        /* 超出部分显示省略号 */
      }
    }
  }

  .left-panel {
    position: fixed;
    z-index: 1;
    top: 100px;
    left: 22px;
    width: 330px;
    height: 937px;

    background-size: 100% 100%;
    transform: translate(-122%);
    transition: transform 0.5s ease-in-out;
  }

  .left-panel-active {
    transform: translate(0%);
  }

  .left-panel-active1 {
    // transform: translate(0%);
    animation: slideOut 1s ease-in-out forwards;
  }

  @keyframes slideOut {
    100% {
      transform: translateX(0%);
    }

    // 85% {
    //   transform: translateX(-25%);
    // }

    // 65% {
    //   transform: translateX(-15%);
    // }

    // 40% {
    //   transform: translateX(-55%);
    // }

    // 30% {
    //   transform: translateX(-40%);
    // }

    0% {
      transform: translateX(-100%);
    }
  }

  .rtitle {
    margin-top: 16px;
  }

  @keyframes slideIn {
    0% {
      transform: translateX(100%);
    }

    // 30% {
    //   transform: translateX(65%);
    // }

    // 40% {
    //   transform: translateX(40%);
    // }

    // 65% {
    //   transform: translateX(15%);
    // }

    // 85% {
    //   transform: translateX(25%);
    // }

    100% {
      transform: translateX(0%);
    }
  }

  .right-panel {
    position: fixed;
    z-index: 1;
    right: 83px;
    width: 330px;
    top: 100px;
    height: 937px;

    background-size: 100% 100%;
    transform: translate(122%);
    transition: transform 0.5s ease-in-out;

    .jk {
      margin-top: 12px;
      width: 90%;
      height: 200px;
    }

    .box {
      margin-top: 6px;
      margin-bottom: 18px;
      // background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      width: 94%;
      // height: 428px;

      .loudong {
        margin-top: 21px;
        margin-bottom: 25px;
      }

      .wenzi {
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 10px;
        color: #bdecf9;
        text-align: left;
        margin-left: 20px;
        margin-right: 20px;

        .h2biaoti {
          margin-bottom: 15px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 12px;
          color: #00ffff;
        }
      }

      .p {
        text-indent: 2em;
        margin-bottom: 1em;
        letter-spacing: 0.05em;
      }
    }

    .boxxx {
      margin-top: 6px;
      margin-bottom: 18px;
      background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      width: 333px;
      height: 420px;
      overflow-y: scroll;
      /* 设置垂直滚动条 */
      // overflow: hidden;
      /* 设置滚动条的样式 */

      .zengti {
        margin: 10px 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 315px;
        height: 38px;
        gap: 5px;

        .left {
          display: flex;
          align-items: center;
          justify-content: space-evenly;
          width: 84px;
          height: 27px;

          .yuan {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #08f7f7;
          }

          .wenziss {
            display: flex;
            align-items: flex-start;
            flex-direction: column;

            .p1 {
              font-family: Source Han Sans SC;
              font-weight: 500;
              font-size: 10px;
              color: #ffffff;
            }

            .p2 {
              font-family: Source Han Sans SC;
              font-weight: 500;
              font-size: 10px;
              color: #55cff9;
            }
          }
        }

        .right {
          background: url("../assets/image/rightbeij.png");
          background-size: 100% 100%;
          background-repeat: no-repeat;

          width: 217px;
          height: 38px;
          font-family: Source Han Sans SC;
          font-weight: 500;
          font-size: 11px;
          color: #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    .boxxx::-webkit-scrollbar {
      width: 1px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条轨道的样式 */
    .boxxx::-webkit-scrollbar-track {
      background-color: #f1f1f1;
      /* 设置滚动条轨道的背景色 */
    }

    /* 设置滚动条滑块的样式 */
    .boxxx::-webkit-scrollbar-thumb {
      background-color: #013363;
      /* 设置滚动条滑块的背景色 */
    }
  }

  .no-animation {
    transition: none;
  }

  .right-panel-active {
    transform: translate(0%);
    // animation: slideIn 1s ease-in-out ;
  }

  .right-panel-active1 {
    // transform: translate(0%);
    animation: slideIn 1s ease-in-out forwards;
  }

  @keyframes slideIn {
    0% {
      transform: translateX(100%);
    }

    // 30% {
    //   transform: translateX(65%);
    // }

    // 40% {
    //   transform: translateX(40%);
    // }

    // 65% {
    //   transform: translateX(15%);
    // }

    // 85% {
    //   transform: translateX(25%);
    // }

    100% {
      transform: translateX(0%);
    }
  }

  .completed {
    background: #7ad0ff;
  }

  .incomplete {
    background: #ff6041;
  }

  .warning {
    background: #00ffc0;
  }

  .completeds {
    color: #7ad0ff;
  }

  .incompletes {
    color: #ff6041;
  }

  .warnings {
    color: #00ffc0;
  }
}

.ql-center {
  display: flex;
  margin-top: 20px;
  justify-content: space-around;
  margin-top: 14px;

  .ql-Box {
    width: 75px;
    height: 49px;
    border: 1px solid #7ad0ff;
    // opacity: 0.6;
    border-radius: 2px;

    .ql-box1 {
      font-family: Alibaba PuHuiTi;
      font-weight: bold;
      font-size: 16px;
      color: #7ad0ff;
      margin-top: -10px;
    }

    .ql-box {
      display: flex;
      padding-left: 8px;
      padding-right: 9px;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 34px;

      .left_ql {
        width: 49px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #ffffff;

        .yuan {
          width: 7px;
          height: 7px;
          border-radius: 50%;
        }

        .pp {
          color: #fff;
          font-size: 12px;
        }
      }

      img {
        height: 12px;
        width: 7px;
      }
    }
  }
}

.warn1 {
  background: url("../assets/image/warnred.png");
}

.warn2 {
  background: url("../assets/image/warnyellow.png");
}

.warn3 {
  background: url("../assets/image/warngreen.png");
}

.warning12 {
  background-size: 100% 100%;
  // width: 365px;
  height: 47px;

  .info {
    margin-top: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: left;

    margin-left: 50px;

    .info1 {
      display: flex;
      flex-direction: column;

      p:nth-of-type(1) {
        font-size: 13px;
        font-family: Alibaba PuHuiTi;
        font-weight: bold;
      }

      .red {
        color: #ff0000;
      }

      .green {
        color: #00ffcc;
      }

      .yellow {
        color: #ffff00;
      }

      p:nth-of-type(2) {
        font-size: 16px;
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        color: #ffffff;
      }
    }

    .info2 {
      margin-right: 10px;
      font-size: 15px;
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      color: #cffff8;
    }
  }
}

.xxxx {
  position: absolute;
  top: 1%;
  right: 1%;
  width: 25px;
  height: 25px;
  z-index: 99999;
}

/* 菜单容器 */
.menu-container {
  display: flex;
}

/* 菜单样式 */
.menu {
  width: 100%;
  // background-color: #fff;
  // border: 1px solid #ddd;
  border-radius: 4px;
}

/deep/.el-pagination {
  --el-pagination-bg-color: none;
  --el-pagination-button-color: #fff;
  --el-pagination-font-size: 17px;
  margin-left: -13px;
}

/deep/.el-pagination__total {
  color: #fff;
  font-size: 15px;
}

/deep/.el-pagination button:disabled {
  background: none;
  font-size: 17px;
}

/deep/.el-pagination button {
  background: none;
  font-size: 17px;
}

/deep/.el-icon {
  font-size: 17px !important;
}

/* 菜单项样式 */
.menu-group {
  margin-top: 14px;
}

.menu-item {
  cursor: pointer;
  background: url("../assets/image/rightbeij.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;

  width: 100%;
  height: 32px;
  font-family: Source Han Sans SC;
  font-weight: 500;
  font-size: 17px;
  color: #ffffff;
  display: flex;
  align-items: center;
  padding-left: 10px;
  // justify-content: center;
}

.menu-item:hover {
  // background-color: #f0f0f0;
}

.submenu {
  // background-color: #f9f9f9;
  padding-left: 20px;
}

.submenu-item {
  padding: 3px;
  padding-left: 12px;
  margin: 8px;
  font-size: 16px;
  color: #ffffff;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #2c343f;
}

.submenu-item:hover {
  background-color: #163561;
}

.qiuqiu {
  display: flex;
  align-items: center;

  .siqiu {
    width: 16px;
    height: 16px;
    margin-left: 10px;
    margin-right: 7px;
  }
}

.listtype {
  background: url("../assets/image/xuanzhotiemmoon.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 80px;
  height: 26px;
  text-align: center;
  line-height: 26px;

  font-size: 13px;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  color: #fff;
}

.listtypes {
  background: url("../assets/image/xuanzhotiemmoon.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 100px;
  height: 32px;
  text-align: center;
  line-height: 32px;
  cursor: pointer;
  font-size: 13px;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  color: #fff;
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 190px;
  /* 你可以根据需要调整宽度 */
  font-size: 16px;
}

.botbtn {
  position: fixed;
  top: 978px;
  left: 382px;
  width: 200px;
  height: 43px;
  background: #022d56;
  border-radius: 8px;
  z-index: 20;
  display: flex;
  align-items: center;
  justify-content: center;

  .btt {
    color: #fff;
    font-size: 17px;
    line-height: 44px;
    width: 88px;
    text-align: center;
    height: 43px;
    cursor: pointer;
  }

  .btt1 {
    color: rgb(8, 207, 241);
    font-size: 17px;
    line-height: 44px;
    width: 88px;
    text-align: center;
    height: 43px;
    cursor: pointer;
  }
}
</style>
