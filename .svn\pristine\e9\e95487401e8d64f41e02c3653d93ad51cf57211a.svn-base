<template>
  <div>
    <!-- <component :is="componentTag" @fatherMethoddd="fatherMethoddd"></component> -->

    <tedai
      :ids="ids"
      :selectedItem="selectedItem"
      class="sbdetails"
      :zengtiimg="zengtiimg"
      v-if="isshowsss"
      @hidedetails="hidedetailsss"
    ></tedai>
    <biaoGesss
      v-if="isshow"
      @hidedetails="hidedetails"
      :tableTitle="tableTitle"
      :tableDataItem="tableDataItem"
      :Title="Title"
    ></biaoGesss>
    <div class="container">
      <div
        class="left-panel"
        :class="{
          'left-panel-active': showdh,
          'no-animation': noAnimation,
          'left-panel-active1': showdh1,
        }"
      >
        <Title2 @open-dialog="opendialog" class="ltitle1" tit="大仪管理">
          <div class="box">
            <div>
              <el-input
                class="el-input"
                v-model="input"
                placeholder="请输入内容"
              ></el-input>
            </div>
            <!-- <div class="xiaobox">
              <img class="siqiu" src="../assets/image/shixinqiu.png" alt="" />
              <div class="shuru">全部设备</div>
            </div> -->
            <div class="menu">
              <div
                v-for="(menu, index) in menus"
                :key="menu.id"
                class="menu-group"
              >
                <div class="menu-item" @click="toggleSubMenu(menu.id)">
                  {{ menu.title }}
                </div>
                <div v-show="activeSubmenu === menu.id" class="submenu">
                  <div
                    v-for="(item, subIndex) in menu.submenu"
                    :key="subIndex"
                    class="submenu-items"
                  >
                    <div @click="toggleSubSubMenu(item.id)">
                      {{ item.title }}
                    </div>
                    <div v-show="activeSubSubmenu === item.id" class="submenu">
                      <div
                        v-for="(subItem, thirdIndex) in item.submenu"
                        :key="thirdIndex"
                        class="submenu-item"
                        @click="setContent(subItem.content)"
                      >
                        {{ subItem.title }}
                        <div class="listtype">使用中</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- shebei12.png -->
        </Title2>
      </div>
      <!-- shebeibgc.png -->
      <!-- 右侧内容 -->
      <!-- 右侧内容 -->

      <div
        class="right-panel"
        :class="{
          'right-panel-active': showdh,
          'no-animation': noAnimation,
          'right-panel-active1': showdh1,
        }"
      >
        <Title3 tit="大仪管理详情">
          <div class="box">
            <div class="xiaoboxs" v-for="item in cgqlist" :key="item">
              <img class="siqiu" src="../assets/image/kongxin.png" alt="" />
              <div class="nihaowo">{{ item.name }}</div>
              <div class="shuru">{{ item.value }}</div>
            </div>
          </div>
          <!-- <img class="jk" src="../assets/jiankong.png" alt=""> -->
        </Title3>
      </div>
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import huanxing from "@/components/echarts/huanxing.vue";
import zhexian from "@/components/echarts/zhexian.vue";
import zhexian1 from "@/components/echarts/zhexian1.vue";
import SystemDete from "@/components/echarts/SystemDete.vue";
import echarts1 from "@/components/echarts/bingjifang/echarts3.vue";
import tedai from "@/components/common/tedai.vue";
import biaoGesss from "@/components/common/biaoGesss.vue";
import shuangxiang from "@/components/echarts/shuangxiang.vue";

// import details from "@/components/common/details.vue";
export default {
  // import引入的组件需要注入到对象中才能使用
  components: {
    tedai,
    huanxing,
    zhexian,
    zhexian1,
    SystemDete,
    echarts1,
    shuangxiang,
    biaoGesss,
  },
  props: ["tabledata", "zengtiimg"],
  //  cgqlistss: [
  //       { name: "湿度范围：", value: "5%RH~100%RH（无结露）" },
  //       { name: "湿度分辨率：", value: "0.04%" },
  //       { name: "IP地址：", value: "±3%RH（20%RH~80%RH）" },
  //       { name: "湿度精度：", value: "-40℃~+125℃" },
  //       { name: "温度分辨率：", value: "0.01℃" },
  //       { name: "温度精度：", value: "±0.5℃（10℃~60℃）" },
  //     ],

  data() {
    // 这里存放数据
    return {
      Title: "大仪管理",
      isshowsss: false,
      activeSubmenu: null, // 当前激活的二级菜单
      activeSubSubmenu: null, // 当前激活的三级菜单
      activeContent: null, // 当前显示的内容
      isshow: false,
      selectedIndex: null,
      selectedItem: null,
      xxxx: false,
      cgqlist: [],
      menus: [
        {
          id: "menu-5F",
          title: "5F",
          submenu: [
            {
              id: "submenu-5-1",
              title: "A区5F-1",
              submenu: [
                {
                  title: "离子溅射仪",
                  content: "离子溅射仪",
                },
                {
                  title: "场发射扫描电子显微镜 （S4800）",
                  content: "场发射扫描电子显微镜 （S4800）",
                },
                {
                  title: "场发射扫描电子显微镜（Apreo S LoVac）",
                  content: "场发射扫描电子显微镜（Apreo S LoVac）",
                },
              ],
            },
            {
              id: "submenu-5-2",
              title: "A区5F-2",
              submenu: [
                {
                  title: "场发射扫描电子显微镜（Regulus 8100）",
                  content: "场发射扫描电子显微镜（Regulus 8100）",
                },
                {
                  title: "场发射扫描电子显微镜（Regulus 8100）",
                  content: "场发射扫描电子显微镜（Regulus 8100）",
                },
              ],
            },
          ],
        },
        {
          id: "menu-4F",
          title: "4F",
          submenu: [
            {
              id: "submenu-4-1",
              title: "A区4F-1",
              submenu: [
                {
                  title: "PCR仪",
                  content: "PCR仪",
                },
                {
                  title: "场发射透射电子显微镜JEM-2100F能谱仪Ultim Max",
                  content: "场发射透射电子显微镜JEM-2100F能谱仪Ultim Max",
                },
              ],
            },
            {
              id: "submenu-4-2",
              title: "A区4F-2",
              submenu: [
                {
                  title: "场发射透射电子显微镜JEM-F200",
                  content: "场发射透射电子显微镜JEM-F200",
                },
                {
                  title: "JEM-F200",
                  content: "JEM-F200",
                },
              ],
            },
          ],
        },
        {
          id: "menu-3F",
          title: "3F",
          submenu: [
            {
              id: "submenu-3-1",
              title: "A区3F-1",
              submenu: [
                {
                  title: "台式扫描电子显微镜",
                  content: "台式扫描电子显微镜",
                },
                {
                  title: "台式原位疲劳试验电镜系统",
                  content: "台式原位疲劳试验电镜系统",
                },
              ],
            },
            {
              id: "submenu-3-2",
              title: "A区3F-2",
              submenu: [
                {
                  title: "台式电子显微镜",
                  content: "台式电子显微镜",
                },
                {
                  title: "透射电镜原位系统",
                  content: "透射电镜原位系统",
                },
              ],
            },
          ],
        },
        {
          id: "menu-2F",
          title: "2F",
          submenu: [
            {
              id: "submenu-2-1",
              title: "A区2F-1",
              submenu: [
                {
                  title: "质谱仪（MS）",
                  content: "质谱仪（MS）",
                },
                {
                  title: "X射线衍射仪（XRD）",
                  content: "X射线衍射仪（XRD）",
                },
              ],
            },
            {
              id: "submenu-2-2",
              title: "A区2F-2",
              submenu: [
                {
                  title: "超净工作台",
                  content: "超净工作台",
                },
                {
                  title: "培养箱",
                  content: "培养箱",
                },
              ],
            },
          ],
        },
        {
          id: "menu-1F",
          title: "1F",
          submenu: [
            {
              id: "submenu-1-1",
              title: "A区1F-1",
              submenu: [
                {
                  title: "显微镜",
                  content: "显微镜",
                },
                {
                  title: "热重分析仪（TGA）",
                  content: "热重分析仪（TGA）",
                },
              ],
            },
            {
              id: "submenu-1-2",
              title: "A区1F-2",
              submenu: [
                {
                  title: "PH计",
                  content: "PH计",
                },
                {
                  title: "搅拌器",
                  content: "搅拌器",
                },
              ],
            },
          ],
        },
        {
          id: "menu-B1F",
          title: "B1F",
          submenu: [
            {
              id: "submenu-B1-1",
              title: "A区B1F-1",
              submenu: [
                {
                  title: "力学测试仪",
                  content: "力学测试仪",
                },
                {
                  title: "离心机",
                  content: "离心机",
                },
              ],
            },
            {
              id: "submenu-B1-2",
              title: "A区B1F-2",
              submenu: [
                {
                  title: "质谱仪（MS）",
                  content: "质谱仪（MS）",
                },
                {
                  title: "台式扫描电子显微镜",
                  content: "台式扫描电子显微镜",
                },
              ],
            },
          ],
        },
      ],
      data: [
        {
          category: "仪器设备",
          items: [
            {
              number: "LAB001",
              nanme: "显微镜",
              pingpai: "品牌X",
              baozhuang: "3楼",
              xiaobaozhuang: "A305",

              qita: "",
            },
            {
              number: "LAB002",
              nanme: "离心机",
              pingpai: "品牌Y",
              baozhuang: "2楼",
              xiaobaozhuang: "B210",

              qita: "",
            },
            {
              number: "LAB003",
              nanme: "培养箱",
              pingpai: "品牌Z",
              baozhuang: "3楼",
              xiaobaozhuang: "A307",

              qita: "",
            },
            {
              number: "LAB004",
              nanme: "天平",
              pingpai: "品牌W",
              baozhuang: "2楼",
              xiaobaozhuang: "B209",

              qita: "",
            },
            {
              number: "LAB005",
              nanme: "烘箱",
              pingpai: "品牌V",
              baozhuang: "4楼",
              xiaobaozhuang: "C401",

              qita: "",
            },
          ],
        },
        {
          category: "计算机和信息化设备",
          items: [
            {
              number: "LAB001",
              nanme: "实验室电脑",
              pingpai: "品牌A",
              baozhuang: "3楼",
              xiaobaozhuang: "A308",

              qita: "",
            },
            {
              number: "LAB002",
              nanme: "数据采集设备",
              pingpai: "品牌B",
              baozhuang: "3楼",
              xiaobaozhuang: "A310",

              qita: "",
            },
            {
              number: "LAB003",
              nanme: "服务器",
              pingpai: "品牌C",
              baozhuang: "1楼",
              xiaobaozhuang: "机房",

              qita: "",
            },
          ],
        },
        {
          category: "办公设备",
          items: [
            {
              number: "LAB001",
              nanme: "打印机",
              pingpai: "品牌D",
              baozhuang: "2楼",
              xiaobaozhuang: "B205",

              qita: "",
            },
            {
              number: "LAB002",
              nanme: "复印机",
              pingpai: "品牌E",
              baozhuang: "2楼",
              xiaobaozhuang: "B206",

              qita: "",
            },
            {
              number: "LAB003",
              nanme: "投影仪",
              pingpai: "品牌F",
              baozhuang: "3楼",
              xiaobaozhuang: "A309",

              qita: "",
            },
          ],
        },
        {
          category: "基础设施",
          items: [
            {
              number: "LAB001",
              nanme: "实验台",
              pingpai: "品牌G",
              baozhuang: "4楼",
              xiaobaozhuang: "C402",

              qita: "",
            },
            {
              number: "LAB002",
              nanme: "通风系统",
              pingpai: "品牌H",
              baozhuang: "5楼",
              xiaobaozhuang: "D501",

              qita: "",
            },
            {
              number: "LAB003",
              nanme: "实验室椅子",
              pingpai: "品牌I",
              baozhuang: "3楼",
              xiaobaozhuang: "A306",

              qita: "",
            },
          ],
        },
      ],
      input: "",
      activeTab: "today",
      listst: [
        {
          name: "广东质检中诚认证有限公司到中广...",
        },
        { name: "材料科学、化学工程及医药研发成..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
        { name: "植酸检测方法及作用" },
        { name: "兔拉检测：行业中芯片造假技术..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
      ],
      showdh: true,
      showdh1: false,
      noAnimation: false,

      tableDataItem: [],
      tableTitle: [
        { key: "" },
        { key: "资产名称" },
        { key: "资产品牌" },
        { key: "楼层" },
        { key: "房间号" },

        { key: "其他说明" },
      ],
      ids: null,
      nhlist: [
        {
          title: "供气压力",
          status: "0.3Mpa",
          unit: "℃",
        },

        {
          title: "供气流量",
          status: "6M3/min",
          unit: "㎡",
        },
        {
          title: "露点温度",
          status: "6℃",
          unit: "℃",
        },
        {
          title: "含氧量",
          status: "6PPM",
          unit: "㎡",
        },
      ],
      warnlist1: [
        {
          type: 1,
          name: "检测到烟雾，可能有着火灾的发生...",
          value: "",
          time: "09:13:59  2023-06-07",
        },
        {
          type: 2,
          name: "检测到烟雾，可能预示着火灾的发生..",
          value: "",
          time: "10:13:18  2023-06-12",
        },
        {
          type: 3,
          name: "实验室内检测到漏水，可能来自冷凝水..",
          value: "",
          time: "10:13:18  2023-06-12",
        },
      ],
      isButton2Active: false,
      status: "巡检中",
      status1: "已完成",
      status2: "待巡检",
      selectedIndex: 0,
      componentTag: "component0",
    };
  },
  // 计算属性类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {},
  // 方法集合
  methods: {
    opendialog() {
      this.isshow = true;

    },
    hidedetailsss() {
      this.isshowsss = false;
    },
    setContent(content, index) {
      this.isshowsss = true;
      this.selectedItem = content;
      this.selectedIndex = index;
      // 设置内容
      console.log(content);
    },
    // 切换二级菜单显示/隐藏
    toggleSubMenu(menuId) {
      this.activeSubmenu = this.activeSubmenu === menuId ? null : menuId;
    },
    // 切换三级菜单显示/隐藏
    toggleSubSubMenu(submenuId) {
      this.activeSubSubmenu =
        this.activeSubSubmenu === submenuId ? null : submenuId;
    },

    showdetails(item) {
      console.log(item.items);

      this.isshow = true;
      this.tableDataItem = item.items;
    },
    hidedetails() {
      this.isshow = false;
    },
    oc(value) {
      console.log(value, "收到的值");
      this.showdh = value;
    },
  },
  // 生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.showdh1 = true;
    setTimeout(() => {
      this.showdh1 = false;
      this.noAnimation = false;
    }, 1000); // 动画持续时间为1秒
    console.log(1222);
  },
  beforeCreate() {}, // 生命周期 - 创建之前
  beforeMount() {}, // 生命周期 - 挂载之前
  beforeUpdate() {}, // 生命周期 - 更新之前
  updated() {}, // 生命周期 - 更新之后
  beforeUnmount() {
    // 在组件销毁之前清除定时器
    console.log(1111);
  },

  unmounted() {
    console.log(2222);
  }, // 生命周期 - 销毁之前
  destroyed() {
    console.log(1221);
  }, // 生命周期 - 销毁完成
  activated() {}, // 如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="less" scoped>
.container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: stretch;
  height: 1080px;
  text-align: center;

  .sbdetails {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
  }

  .box {
    margin-top: 6px;
    padding-top: 5px;
    margin-bottom: 0.225rem;
    height: 800px;
    width: 330px;
    // height: 800px;

    overflow-y: scroll;

    /* 设置垂直滚动条 */
    /* 设置滚动条的样式 */
    &::-webkit-scrollbar {
      width: 0.1px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条轨道的样式 */
    &::-webkit-scrollbar-track {
      background-color: #f1f1f1;
      /* 设置滚动条轨道的背景色 */
    }

    /* 设置滚动条滑块的样式 */
    &::-webkit-scrollbar-thumb {
      background-color: #334f6e;
      /* 设置滚动条滑块的背景色 */
    }

    /* 鼠标悬停在滚动条上时的样式 */
    &::-webkit-scrollbar-thumb:hover {
      background-color: #555;
      /* 设置鼠标悬停时滚动条滑块的背景色 */
    }

    .el-input {
      width: 305px;
      height: 34px;
      color: #fff !important;

      ::v-deep .el-input__wrapper {
        background: url("../assets/image/inputss.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        box-shadow: none !important;
      }
    }

    .suosuo {
      position: absolute;
      top: 66px;
      left: 285px;
    }

    .xiaobox {
      margin-top: 20px;
      display: flex;
      align-items: center;

      .siqiu {
        width: 16px;
        height: 16px;
      }

      .shuru {
        display: flex;
        align-items: center;
        padding-left: 10px;
        background: url("../assets/image/shebei12.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 261px;
        height: 32px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 17px;
        color: #ffffff;
      }
    }

    .xiaoboxs {
      cursor: pointer;
      margin-top: 14px;
      display: flex;
      margin-left: 5px;
      align-items: center;

      .nihaowo {
        width: 78px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 10px;
        color: #ffffff;
        display: flex;
        white-space: nowrap; /* 不换行 */
        overflow: hidden; /* 超出部分隐藏 */
        text-overflow: ellipsis; /* 超出部分显示省略号 */
      }

      .siqiu {
        width: 16px;
        height: 16px;
        margin-left: 10px;
        margin-right: 7px;
      }

      .shuru {
        display: flex;
        align-items: center;
        padding-left: 10px;
        background: url("../assets/image/shebei12.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 261px;
        height: 32px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 17px;
        color: #ffffff;
        white-space: nowrap; /* 不换行 */
        overflow: hidden; /* 超出部分隐藏 */
        text-overflow: ellipsis; /* 超出部分显示省略号 */
      }
    }
  }

  .left-panel {
    position: fixed;
    z-index: 1;
    top: 100px;
    left: 22px;
    width: 330px;
    height: 937px;

    background-size: 100% 100%;
    transform: translate(-122%);
    transition: transform 0.5s ease-in-out;
  }

  .left-panel-active {
    transform: translate(0%);
  }

  .left-panel-active1 {
    // transform: translate(0%);
    animation: slideOut 1s ease-in-out forwards;
  }

  @keyframes slideOut {
    100% {
      transform: translateX(0%);
    }

    // 85% {
    //   transform: translateX(-25%);
    // }

    // 65% {
    //   transform: translateX(-15%);
    // }

    // 40% {
    //   transform: translateX(-55%);
    // }

    // 30% {
    //   transform: translateX(-40%);
    // }

    0% {
      transform: translateX(-100%);
    }
  }

  .rtitle {
    margin-top: 16px;
  }

  @keyframes slideIn {
    0% {
      transform: translateX(100%);
    }

    // 30% {
    //   transform: translateX(65%);
    // }

    // 40% {
    //   transform: translateX(40%);
    // }

    // 65% {
    //   transform: translateX(15%);
    // }

    // 85% {
    //   transform: translateX(25%);
    // }

    100% {
      transform: translateX(0%);
    }
  }

  .right-panel {
    position: fixed;
    z-index: 1;
    right: 83px;
    width: 330px;
    top: 100px;
    height: 937px;

    background-size: 100% 100%;
    transform: translate(122%);
    transition: transform 0.5s ease-in-out;

    .jk {
      margin-top: 12px;
      width: 90%;
      height: 200px;
    }

    .box {
      margin-top: 6px;
      margin-bottom: 18px;
      // background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      width: 94%;
      // height: 428px;

      .loudong {
        margin-top: 21px;
        margin-bottom: 25px;
      }

      .wenzi {
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 10px;
        color: #bdecf9;
        text-align: left;
        margin-left: 20px;
        margin-right: 20px;

        .h2biaoti {
          margin-bottom: 15px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 12px;
          color: #00ffff;
        }
      }

      .p {
        text-indent: 2em;
        margin-bottom: 1em;
        letter-spacing: 0.05em;
      }
    }

    .boxxx {
      margin-top: 6px;
      margin-bottom: 18px;
      background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      width: 333px;
      height: 420px;
      overflow-y: scroll;
      /* 设置垂直滚动条 */
      // overflow: hidden;
      /* 设置滚动条的样式 */

      .zengti {
        margin: 10px 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 315px;
        height: 38px;
        gap: 5px;

        .left {
          display: flex;
          align-items: center;
          justify-content: space-evenly;
          width: 84px;
          height: 27px;

          .yuan {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #08f7f7;
          }

          .wenziss {
            display: flex;
            align-items: flex-start;
            flex-direction: column;

            .p1 {
              font-family: Source Han Sans SC;
              font-weight: 500;
              font-size: 10px;
              color: #ffffff;
            }

            .p2 {
              font-family: Source Han Sans SC;
              font-weight: 500;
              font-size: 10px;
              color: #55cff9;
            }
          }
        }

        .right {
          background: url("../assets/image/rightbeij.png");
          background-size: 100% 100%;
          background-repeat: no-repeat;

          width: 217px;
          height: 38px;
          font-family: Source Han Sans SC;
          font-weight: 500;
          font-size: 11px;
          color: #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    .boxxx::-webkit-scrollbar {
      width: 1px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条轨道的样式 */
    .boxxx::-webkit-scrollbar-track {
      background-color: #f1f1f1;
      /* 设置滚动条轨道的背景色 */
    }

    /* 设置滚动条滑块的样式 */
    .boxxx::-webkit-scrollbar-thumb {
      background-color: #013363;
      /* 设置滚动条滑块的背景色 */
    }
  }

  .no-animation {
    transition: none;
  }

  .right-panel-active {
    transform: translate(0%);
    // animation: slideIn 1s ease-in-out ;
  }

  .right-panel-active1 {
    // transform: translate(0%);
    animation: slideIn 1s ease-in-out forwards;
  }

  @keyframes slideIn {
    0% {
      transform: translateX(100%);
    }

    // 30% {
    //   transform: translateX(65%);
    // }

    // 40% {
    //   transform: translateX(40%);
    // }

    // 65% {
    //   transform: translateX(15%);
    // }

    // 85% {
    //   transform: translateX(25%);
    // }

    100% {
      transform: translateX(0%);
    }
  }

  .completed {
    background: #7ad0ff;
  }

  .incomplete {
    background: #ff6041;
  }

  .warning {
    background: #00ffc0;
  }

  .completeds {
    color: #7ad0ff;
  }

  .incompletes {
    color: #ff6041;
  }

  .warnings {
    color: #00ffc0;
  }
}

.ql-center {
  display: flex;
  margin-top: 20px;
  justify-content: space-around;
  margin-top: 14px;

  .ql-Box {
    width: 75px;
    height: 49px;
    border: 1px solid #7ad0ff;
    // opacity: 0.6;
    border-radius: 2px;

    .ql-box1 {
      font-family: Alibaba PuHuiTi;
      font-weight: bold;
      font-size: 16px;
      color: #7ad0ff;
      margin-top: -10px;
    }

    .ql-box {
      display: flex;
      padding-left: 8px;
      padding-right: 9px;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 34px;

      .left_ql {
        width: 49px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #ffffff;

        .yuan {
          width: 7px;
          height: 7px;
          border-radius: 50%;
        }

        .pp {
          color: #fff;
          font-size: 12px;
        }
      }

      img {
        height: 12px;
        width: 7px;
      }
    }
  }
}

.warn1 {
  background: url("../assets/image/warnred.png");
}

.warn2 {
  background: url("../assets/image/warnyellow.png");
}

.warn3 {
  background: url("../assets/image/warngreen.png");
}

.warning12 {
  background-size: 100% 100%;
  // width: 365px;
  height: 47px;

  .info {
    margin-top: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: left;

    margin-left: 50px;

    .info1 {
      display: flex;
      flex-direction: column;

      p:nth-of-type(1) {
        font-size: 13px;
        font-family: Alibaba PuHuiTi;
        font-weight: bold;
      }

      .red {
        color: #ff0000;
      }

      .green {
        color: #00ffcc;
      }

      .yellow {
        color: #ffff00;
      }

      p:nth-of-type(2) {
        font-size: 16px;
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        color: #ffffff;
      }
    }

    .info2 {
      margin-right: 10px;
      font-size: 15px;
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      color: #cffff8;
    }
  }
}
.xxxx {
  position: absolute;
  top: 1%;
  right: 1%;
  width: 25px;
  height: 25px;
  z-index: 99999;
}

/* 菜单容器 */
.menu-container {
  display: flex;
  gap: 20px;
}

/* 菜单样式 */
.menu {
  width: 100%;
  // background-color: #fff;
  // border: 1px solid #ddd;
  border-radius: 4px;
}

/* 菜单项样式 */
.menu-group {
  margin-top: 14px;
}

.menu-item {
  cursor: pointer;
  background: url("../assets/image/rightbeij.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;

  width: 100%;
  height: 36px;
  font-family: Source Han Sans SC;
  font-weight: 500;
  font-size: 20px;
  color: #ffffff;
  display: flex;
  align-items: center;
  padding-left: 10px;
  // justify-content: center;
}

.menu-item:hover {
  background-color: #f0f0f0 !important;
  color: #00ffc0;
}

.submenu {
  display: block; /* 确保子菜单是块级元素 */
  // background-color: #f9f9f9;
  padding-left: 8px;
}
.submenu-items:hover {
  color: #00ffc0;
}
.submenu-item:hover {
  color: #00ffc0;
}
.submenu-items {
  cursor: pointer;
  // background-color:#013363;
  width: 100%;

  font-family: Source Han Sans SC;
  font-weight: 500;
  font-size: 18px;
  color: #ffffff;
  /* 修改 display 属性 */
  display: flex;
  flex-direction: column; /* 垂直方向排列 */
  align-items: flex-start;
  padding-left: 10px;
  margin-top: 10px;
}
.submenu-item {
  cursor: pointer;
  background: url("../assets/image/rightbeij.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: 300px !important;
  /* 移除固定高度 */
  /* height: 30px; */
  font-family: Source Han Sans SC;
  font-weight: 500;
  font-size: 15px;
  color: #ffffff;
  /* 修改 display 属性 */
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding-left: 10px;

  margin-top: 10px;
}

.qiuqiu {
  display: flex;
  align-items: center;
  .siqiu {
    width: 16px;
    height: 16px;
    margin-left: 10px;
    margin-right: 7px;
  }
}
.listtype {
  background: url("../assets/image/xuanzhotiemmoon.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 80px;
  height: 26px;
  text-align: center;
  line-height: 26px;

  font-size: 13px;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  color: #fff;
}
.listtypes {
  background: url("../assets/image/xuanzhotiemmoon.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 100px;
  height: 36px;
  text-align: center;
  line-height: 36px;
  cursor: pointer;
  font-size: 13px;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  color: #fff;
}
.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  font-size: 15px;
}
</style>
