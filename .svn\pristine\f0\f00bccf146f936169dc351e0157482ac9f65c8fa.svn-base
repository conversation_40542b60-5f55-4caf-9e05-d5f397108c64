<template>
  <div class="echart" ref="echart"></div>
</template>
  
<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  methods: {
    initData() {},
    init() {
      const myChart = echarts.init(this.$refs.echart);

      const data = [
          {
          label: "温度报警(100/5)",
          value: 15,
        },
        {
          label: "压差报警(60/3)",
          value:30,
        },
        {
          label: "氧气浓度报警(30/2)",
          value: 10,
        },
        {
          label: "气体泄露报警(60/5)",
          value: 10,
        },
        {
          label: "湿度报警(100/2)",
          value: 40,
        },
      
      ];
      const colors = [
        "37, 171, 200",
        "214, 128, 120",
        "252, 182, 53",
        "47, 255, 242",
        "42,191,191",
      ];
      const option = {
        legend: {
          // orient: "vertical",
          top: "0",
          right: "-1%",
          data: data.map((it) => it.label),
          textStyle: {
            color: "#fff",
            fontSize: 16,
            fontFamily: "Alibaba PuHuiTi", // 你可以替换成你需要的字体
          },
          itemWidth: 12,
          itemHeight: 12,
        },
        tooltip: {
          trigger: "item",
          formatter: "{a} <br/>{b} : {c} ({d}%)",
          textStyle: {
            fontSize: 10,
          },
        },
        series: [
          {
            right: "0%",
            name: "报警",
            type: "pie",
            radius: ["30%", "75%"],
            center: ["50%", "65%"],
            roseType: "radius",
            label: {
              show: true,
              normal: {
                position: "outside",
                fontSize: 18,
                formatter: "{d}%",
                color: "#fff",
              },
            },
            labelLine: {
              length: 1,
              length2: 7,
            },
            data: data.map((it, i) => {
              return {
                value: it.value,
                name: it.label,
                itemStyle: {
                  color: `rgba(${colors[i]},0.7)`,
                  borderColor: `rgba(${colors[i]},1)`,
                  borderWidth: 1,
                },
              };
            }),
          },
        ],
      };
      myChart.setOption(option);
    },
  },
};
</script>
  
<style lang="less" scoped>
.echart {
  width: 100%;
  height: 100%;
}

@media (max-height: 1080px) {
  .echart {
    width: 100%;
    height: 100% !important;
  }
}
</style>