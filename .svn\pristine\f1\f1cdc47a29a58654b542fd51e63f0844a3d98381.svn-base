<template>
  <div class="echart" ref="echart"></div>
</template>
  
<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  data() {
    return {};
  },
  props: {
    chartData: {
      type: Object,
      required: true,
    },
  },
  mounted() {
    this.init();
  },

  methods: {
    initData() { },
    init() {
      const myChart = echarts.init(this.$refs.echart);

      app.title = "未优化用电量";

      var data1 = [20, 30, 20, 30, 20, 30, 20];
      var data2 = [9, 30, 9, 60, 70, 20, 59];
      var data3 = [20, 30, 20, 30, 20, 30, 20];
      var data4 = [9, 30, 9, 60, 70, 20, 59];
      var datacity = [
        "一楼仪器设备",
        "二楼仪器设备",
        "三楼仪器设备",
        "四楼仪器设备",
        "五楼仪器设备",

      ];
      const option = {
        title: {
          text: "台",
          left: "20px",
          top: "14",
          textStyle: {
            color: "#fff",
            fontSize: 14,
            fontWeight: "400",
          },
        },
        color: [
          "#00FF00",
          "#FF3333",
          "#83FB45",
          "#FFFF33",
          "#FF00FF",
          "#0000FF",
          "#EB46FB",
        ],
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            crossStyle: {
              color: "#999",
            },
            lineStyle: {
              type: "dashed",
            },
          },
        },
        grid: {
          //图表的位置
          top: "20%",
          left: "3%",
          right: "4%",
          bottom: "5%",
          containLabel: true,
        },

        legend: {
          data: [
            "一楼",
            "二楼",
            "三楼",
            "四楼",
            "五楼",

          ],
          top: "2%",
          right: "0px",
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            color: "#fff",
            fontSize: 11,
          },
        },
        xAxis: {
          axisLabel: {
            interval: 0,
            show: true,
            splitNumber: 15,
            textStyle: {
              fontSize: 10,
              color: "#fff",
            },
          },
          type: "category",
          data: [
            "1月",
            "2月",
            "3月",
            "4月",
            "5月",
            "6月",
            "7月",
            "8月",
            "9月",
            "10月",
            "11月",
            "12月 ",
          ],
          splitLine: {
            show: true,
          },
          axisTick: {
            show: true,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#F3F4F4",
            },
          },
        },
        yAxis: {
          type: "value",
          // max: max_value>=100? max_value + 100: max_value+10,
          // max: max_value > 100 ? max_value * 2 : max_value + 10,
          // interval: 10,
          // nameLocation: "center",
          axisLabel: {
            color: "#fff",
            textStyle: {
              fontSize: 12,
            },
          },
          splitLine: {
            show: false,
            lineStyle: {
              color: "#F3F4F4",
            },
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#F3F4F4",
            },
          },
        },
        series: [
          // {
          //   name: "一楼",
          //   type: "line",
          //   smooth: true,
          //   data: [70, 124, 152, 171, 252, 260, 264, 336, 340, 348, 429, 448],
          // },
          {
            name: "一楼",
            type: "line",
            smooth: true,
            data: [100, 119, 124, 130, 145, 149, 159, 173, 183, 192, 196, 215],
          },
          {
            name: "二楼",
            type: "line",
            smooth: true,
            data: [50, 69, 83, 99, 106, 121, 132, 152, 160, 168, 183, 194],
          },
          {
            name: "三楼",
            type: "line",
            smooth: true,
            data:  [70, 73, 83, 94, 100, 114, 129, 145, 164, 179, 187, 194],
          },
          {
            name: "四楼",
            type: "line",
            smooth: true,
            data: [90, 107, 117, 124, 142, 146, 163, 164, 183, 188, 197, 201],
          },
          {
            name: "五楼",
            type: "line",
            smooth: true,
            data: [60, 63, 72, 74, 79, 99, 104, 117, 120, 131, 132, 138, 142, 155]
          },

        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>
  
<style lang="less" scoped>
.echart {
  width: 593px;
  height: 230px;
}
</style>