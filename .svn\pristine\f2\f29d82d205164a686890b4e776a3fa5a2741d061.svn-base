<template>
  <div class="contents" v-if="isshow">
    <div class="toubu">
      <div style="margin-left: 20px; display: flex; align-items: center" v-if="false">
        <div style="display: flex; width: 100%; align-items: center">
          <span class="sp">当前位置：</span>
          <el-select class="el-select" v-model="selectvalue1" placeholder="selectvalue1"
            style="width: 64px; height: 35.1px" @change="handleChange">
            <el-option v-for="item in options1" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-select class="el-select" v-model="selectvalue2" placeholder="selectvalue2"
            style="width: 64px; height: 35.1px" @change="handleChange">
            <el-option v-for="item in options2" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-select class="el-select" v-model="selectvalue3" placeholder="selectvalue3"
            style="width: 78px; height: 35.1px" @change="handleChange">
            <el-option v-for="item in options3" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <img v-if="isshow" class="img1sss" @click="anniu()" src="../../assets/image/table-x.png" alt="" />
      </div>

      <div class="all">
        <div class="all1">
          <Titles class="ltitle" tit="仪器设备状态分析">
            <zhuzhuangtu class="zhuzhuangtu" :chartData="chartData1"></zhuzhuangtu>
            <!-- <div class="nenghao">累计总能耗:</div>
            <p class="nhp">92673.0 kwh</p>
            <div class="nh">
              <img class="nhimg" src="../../assets/image/nenghao1.png" alt="" />
              <div class="nhtit">
                <p class="p11">122</p>
                <p class="p2">本日累计能耗</p>
              </div>
              <div class="nhtit1">
                <img class="nhimg1" src="../../assets/image/nhxia.png" alt="" />
                <p class="pp1">5%</p>
              </div>
            </div>
            <div class="nh">
              <img class="nhimg" src="../../assets/image/nenghao1.png" alt="" />
              <div class="nhtit">
                <p class="p11">23456</p>
                <p class="p2">近7日累计能耗</p>
              </div>
              <div class="nhtit1">
                <img class="nhimg1" src="../../assets/image/nhxia.png" alt="" />
                <p class="pp1">10%</p>
              </div>
            </div>
            <div class="nh">
              <img class="nhimg" src="../../assets/image/nenghao2.png" alt="" />
              <div class="nhtit">
                <p class="p12">4567999</p>
                <p class="p2">近30日累计能耗</p>
              </div>
              <div class="nht">
                <div class="nhtit1">
                  <img
                    class="nhimg1"
                    src="../../assets/image/nhshang.png"
                    alt=""
                  />
                  <p class="pp2">10%</p>
                </div>
                <p class="pp">环比</p>
              </div>
            </div> -->

          </Titles>
          <Titles class="ltitle11" tit="办公设备状态分析">
            <zhuzhuangtu class="zhuzhuangtu" :chartData="chartData2"></zhuzhuangtu>
            <!-- <div class="shinei">
              <Electricity4></Electricity4>
            </div> -->
          </Titles>
        </div>
        <div class="line1"></div>
        <div class="all2">
          <Titles class="ltitle1" tit="仪器设备使用情况">
            <div class="shinei">
              <Electricity8></Electricity8>
            </div>
          </Titles>
          <Titles class="ltitle" tit="办公设备使用情况">
            <div class="shinei">
              <Electricity8></Electricity8>
            </div>
          </Titles>
          <div>
            <Titles class="ltitle1" tit="设备购入时间">
              <div class="shinei">
                <Electricity3 :chartData="chartData3"></Electricity3>
              </div>
            </Titles>
          </div>
        </div>
        <div class="all3">
          <Titles class="ltitle1" tit="各楼层仪器设备统计">
            <div class="shinei">
              <Electricity6></Electricity6>
            </div>
          </Titles>
          <Titles class="ltitle1" tit="各楼层办公设备统计">
            <div class="shinei">
              <Electricity7></Electricity7>
            </div>
          </Titles>
          <Titles class="ltitle1" tit="资产占比分析">
            <div class="shinei">
              <huanxing :chartData="chartData"></huanxing>
            </div>
          </Titles>
          <!-- <div class="shuantitle">
            <div style="width: 50%">
              <div class="title">实时负载率</div>
              <div class="nenghao">实时负载率:</div>
              <p class="nhp">30%</p>
            </div>
            <div style="width: 50%">
              <div class="title">实时总功率</div>
              <div class="nenghao">实时总功率:</div>
              <p class="nhp">200Kw</p>
            </div>
          </div>
      -->



        </div>
      </div>
    </div>
  </div>
</template> 

<script>
import Electricity from "@/components/echarts/dianbiao/biao1.vue";
import biao1s from "@/components/echarts/dianbiao/biao1s.vue";
import biao1ss from "@/components/echarts/dianbiao/biao1ss.vue";
import Titles from "@/components/common/Titles.vue";

import Electricity2 from "@/components/echarts/dianbiao/Electricity2.vue";
import Electricity3 from "@/components/fuyong//zhexiantu.vue";
import Electricity4 from "@/components/echarts/dianbiao/Electricity4.vue";
import Electricity5 from "@/components/echarts/dianbiao/Electricity5.vue";
import Electricity6 from "@/components/fuyong/Electricity6.vue";
import Electricity7 from "@/components/fuyong/Electricity7.vue";
// import Electricity7 from "@/components/echarts/dianbiao/Electricity7.vue";
import Electricity8 from "@/components/fuyong/Electricity8.vue";
import huanxing from "@/components/fuyong/xiaobingtu.vue";
import zhuzhuangtu from "@/components/fuyong/zhuzhuangtu.vue";
export default {
  components: {
    Titles,
    Electricity,
    Electricity2,
    Electricity3,
    Electricity4,
    Electricity5,
    Electricity6,
    Electricity7,
    Electricity8,
    huanxing,
    biao1s,
    biao1ss,
    zhuzhuangtu
  },
  data() {
    return {
      chartData: {
        value: [112, 132,],
        legend: [
          "仪器设备",
          "办公设备",
        ],
      },
      chartData1: {
        title: ["已领用", "未领用"],
        xAxisdata: ["20日", "21日", "22日", "23日", "25日", "24日",],
        yAxisdata1: [720, 710, 730, 705, 715, 725],
        yAxisdata2: [480, 490, 470, 495, 485, 475]
      },
      chartData2: {
        title: [" ", "未领用"],
        xAxisdata: ["20日", "21日", "22日", "23日", "25日", "24日",],
        yAxisdata1: [600, 580, 620, 590, 610, 605],
        yAxisdata2: [300, 320, 280, 310, 290, 295]
      },
      chartData3: {
        title: ["仪器设备", "办公设备"],
        xAxisdata: [
          "10/16",
          "10/17",
          "10/18",
          "10/19",
          "10/20",
          "10/21",
          "10/22",
          "10/23",
          "10/24",
          "10/25",
          "10/26",
          "10/27",
          "10/28",

        ],
        yAxisdata1: [4, 17, 5, 9, 6, 5, 0, 0,12, 0,4, 0, 1],
        yAxisdata2: [14, 7, 2, 3, 16, 5, 0, 0, 2, 0, 13, 0, 0],
      },
      isshow: true,
      options: [
        {
          value: "总览",
          label: "总览",
        },
        {
          value: "能耗分析",
          label: "能耗分析",
        },
        {
          value: "能流分析",
          label: "能流分析",
        },
        {
          value: "设备状态",
          label: "设备状态",
        },
        {
          value: "一键抄表",
          label: "一键抄表",
        },
        {
          value: "费用管理",
          label: "费用管理",
        },
        {
          value: "碳排放管理",
          label: "碳排放管理",
        },
      ],
      selectvalue2: "B3",
      options2: [
        {
          value: "B3",
          label: "B3",
        },
      ],
      selectvalue3: "B3",
      options3: [
        {
          value: "B3",
          label: "B3",
        },
      ],
      selectvalue1: "B3",
      options1: [
        {
          value: "B3",
          label: "B3",
        },
      ],
      selectvalue3: "B1栋",
      options4: [
        {
          value: "B1栋",
          label: "B1栋",
        },
        {
          value: "B2栋",
          label: "B2栋",
        },
        {
          value: "B3栋",
          label: "B3栋",
        },
        {
          value: "B4栋",
          label: "B4栋",
        },
        {
          value: "W1栋",
          label: "W1栋",
        },
        {
          value: "W2栋",
          label: "W2栋",
        },
      ],
      selectvalue4: "B1栋",
      optionData: [
        {
          name: "一级告警",
          value: 16,
          itemStyle: { color: "#EB6877", opacity: 0.9 },
        },
        {
          name: "二级告警",
          value: 27,
          itemStyle: { color: "#F8B551", opacity: 0.9 },
        },
        {
          name: "三级告警",
          value: 17,
          itemStyle: { color: "#B954E8", opacity: 0.9 },
        },
        {
          name: "四级告警",
          value: 40,
          itemStyle: { color: "#0284F0", opacity: 0.9 },
        },
      ],
      optionData1: [
        {
          name: "一级告警",
          value: 16,
          itemStyle: { color: "#EB6877", opacity: 0.9 },
        },
        {
          name: "二级告警",
          value: 27,
          itemStyle: { color: "#F8B551", opacity: 0.9 },
        },
        {
          name: "三级告警",
          value: 17,
          itemStyle: { color: "#B954E8", opacity: 0.9 },
        },
        {
          name: "四级告警",
          value: 40,
          itemStyle: { color: "#0284F0", opacity: 0.9 },
        },
      ],
    };
  },
  methods: {
    anniu() {
      this.isshow = false;
    },
  },
};
</script>

<style lang="less" scoped>
.zhuzhuangtu {
  margin-top: 20px;
  margin-bottom: 10px;
}

.all {
  display: flex;
  flex-direction: row;
  margin-top: 5px;

  .zong {
    display: flex;
    flex-direction: row;
    margin-top: 10px;

    .echart1,
    .echart2 {
      flex: 1;

      .center {
        margin-top: -24px;
        font-family: "Source Han Sans SC", sans-serif;
        font-weight: 400;
        font-size: 17px;
        color: #00ffb6;
        text-align: center;
        margin-bottom: 10px;
      }

      .btn {
        width: 133px;
        height: 31px;
        border: 1px solid #2d6cb0;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: "Source Han Sans SC", sans-serif;
        font-weight: bold;
        font-size: 15px;
        color: #ffffff;
        border-radius: 30px;
        margin-left: 7%;
      }
    }
  }

  .ltitle1 {
    margin-top: 10px;
  }

  .ltitle11 {
    margin-top: 30px;
  }

  .line1 {
    width: 2px;
    height: 823px;
    opacity: 0.64;
    background-color: #204964;
  }

  .all1 {
    flex: 462;

    .nenghao {
      width: 227px;
      height: 173px;
      background: url("../../assets/image/nenghao.png");
      background-size: 100% 100%;
      margin-left: 120px;
      margin-top: 21px;
      text-align: center;
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      font-size: 16px;
      color: #ffffff;
      line-height: 213px;
    }

    .nhp {
      text-align: center;
      font-family: Alibaba PuHuiTi;
      font-weight: 500;
      font-size: 30px;
      color: #2cc1ff;
      margin-top: 8px;
    }

    .nh {
      margin-left: 24px;
      margin-top: 5px;
      width: 423px;
      height: 93px;
      border: 1px solid #364d5a;
      background-size: 100% 100%;
      display: flex;
      flex-direction: row;
      align-items: center;
      padding-left: 42px;

      .nhimg {
        width: 96.6px;
        height: 70px;
      }

      .nhtit {
        width: 148px;
        margin-left: 10px;
        margin-top: 10px;

        .p11 {
          font-family: PangMenZhengDao;
          font-weight: 400;
          font-size: 30px;
          color: #7acfff;
        }

        .p12 {
          font-family: PangMenZhengDao;
          font-weight: 400;
          font-size: 30px;
          color: #ffa170;
        }

        .p2 {
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 17px;
          color: #ffffff;
        }
      }

      .nhtit1 {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-left: 35px;

        .nhimg1 {
          width: 16px;
          height: 20px;
        }

        .pp1 {
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 17px;
          color: #0df29b;
        }

        .pp2 {
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 17px;
          color: #ffa170;
        }
      }

      .nht {
        margin-top: 10px;
        display: flex;
        flex-direction: column;

        .pp {
          margin-left: 35px;
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 17px;

          color: #cccccc;
        }
      }
    }
  }

  .all2 {
    margin-left: 38px;
    flex: 667;
    display: flex;
    flex-direction: column;

    .shinei {
      .itemshei {
        display: flex;
        justify-content: space-around;

        .nenghaos {
          width: 227px;
          height: 173px;
          background: url("../../assets/image/nenghao.png");
          background-size: 100% 100%;
          text-align: center;
          margin-left: 10px;
          margin-top: 23px;
          text-align: center;
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 14px;
          color: #ffffff;
          line-height: 144px;
        }

        .nhps {
          text-align: center;
          font-family: Alibaba PuHuiTi;
          font-weight: 500;
          font-size: 21px;
          color: #2cc1ff;
          margin-top: 8px;
        }
      }
    }
  }

  .all3 {
    flex: 668;
    margin-left: 45px;
  }
}

.shinei {
  width: 100%;
  height: 100%;
}

.shuantitle {
  width: 100%;
  display: flex;
  margin-top: 10px;

  .title {
    width: 95%;
    background: url("../../assets/image/title.png");
    background-size: 100% 100%;

    height: 25px;
    font-family: Source Han Sans SC;
    font-weight: 400;
    font-size: 25px;
    color: #ffffff;
    text-shadow: 2px 2px 3px rgba(0, 0, 0, 0.47);
    font-style: italic;
    text-align: left;
    line-height: 4px;
    padding-left: 33px;
  }
}

.nenghao {
  width: 167px;
  height: 113px;
  background: url("../../assets/image/nenghao.png");
  background-size: 100% 100%;
  text-align: center;
  margin-left: 83px;
  margin-top: 63px;
  text-align: center;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  line-height: 144px;
}

.nhp {
  text-align: center;
  font-family: Alibaba PuHuiTi;
  font-weight: 500;
  font-size: 21px;
  color: #2cc1ff;
  margin-top: 8px;
}

.contents {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: url("../../assets/image/zichanbeijin.png");
  width: 1863px;
  height: 868px;
  z-index: 99999;
  padding-left: 34px;
  padding-right: 22px;
  padding-top: 21px;
}

.toubu {
  width: 100%;

  position: relative;
}

.el-select {
  margin-top: -1px;
  margin-left: 10px;
  background: #00203d;
  border-radius: 3px;
  border: 1px solid #3e89db;

  /deep/.el-select__wrapper {
    background: #00203d !important;
    box-shadow: none;
  }

  /deep/.el-select__wrapper .is-hovering:not {
    box-shadow: none;
  }

  /deep/.el-select__wrapper:hover {
    box-shadow: none;
  }

  /deep/.el-select__placeholder.is-transparent {
    color: #2cc1ff;
  }

  /deep/.el-select__placeholder {
    color: #2cc1ff;
  }

  /deep/.el-select-dropdown__item.is-hovering {
    background-color: #2cc1ff !important;
  }
}

.sp {
  margin-top: -5px;
  margin-left: 12px;
  font-family: Alibaba PuHuiTi;
  font-weight: bold;
  font-size: 21px;
  color: #2cc1ff;
}

.img1sss {
  cursor: pointer;
  width: 15px;
  height: 15px;
}
</style>