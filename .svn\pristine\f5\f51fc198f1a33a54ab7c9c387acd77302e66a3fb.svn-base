<template>
  <div class="echart" ref="echart"></div>
</template>
    
  <script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  methods: {
    initData() {},
    init() {
      const myChart = echarts.init(this.$refs.echart);
      var category = [
        {
          name: "湿度",
          value: 87,
          color: "#69DFC2", // 设置湿度柱子的颜色为蓝色
        },
        {
          name: "温度",
          value: 55,
          color: "#FBF956", // 设置温度柱子的颜色为橙色
        },

        // 可以继续添加其他类别，并为它们指定颜色
      ];
      var total = 100; // 数据总数
      var datas = [];
      category.forEach((value) => {
        datas.push(value.value);
      });
      const option = {
        xAxis: {
          max: total,
          splitLine: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            show: false,
          },
          axisTick: {
            show: false,
          },
        },
        grid: {
          left: 50,
          top: 0, // 设置条形图的边距
          right: 49,
          bottom: 0,
        },
        yAxis: [
          {
            type: "category",
            inverse: false,
            data: category,
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              show: false,
            },
          },
        ],
        series: [
          {
            // 内
            type: "bar",
            barWidth: 12,

            legendHoverLink: false,
            silent: true,
            itemStyle: {
              normal: {
                color: function (params) {
                  // 获取当前柱子对应的类别
                  var currentCategory = category[params.dataIndex];
                  // 返回该类别指定的颜色
                  return currentCategory.color;
                },
              },
            },
            label: {
              normal: {
                show: true,
                position: "left",
                formatter: "{b}",
                textStyle: {
                  color: "#fff",
                  fontSize: 14,
                },
              },
            },
            data: category,
            z: 1,
            animationEasing: "elasticOut",
          },
          {
            // 分隔
            type: "pictorialBar",
            itemStyle: {
              normal: {
                color: "#061348",
              },
            },
            symbolRepeat: "fixed",
            symbolMargin: 6,
            symbol: "rect",
            symbolClip: true,
            symbolSize: [1, 13],
            symbolPosition: "start",
            symbolOffset: [1, -1],
            symbolBoundingData: this.total,
            data: category,
            z: 2,
            animationEasing: "elasticOut",
          },
          {
            // 外边框
            type: "pictorialBar",
            symbol: "rect",
            symbolBoundingData: total,
            itemStyle: {
              normal: {
                color: "none",
              },
            },
            label: {
              normal: {
                formatter: (params) => {
                  var text;
                  if (params.dataIndex == 1) {
                    text = "{f|  " + params.data + "℃}";
                  } else if (params.dataIndex == 2) {
                    text = "{f|  " + params.data + "}";
                  } else if (params.dataIndex == 3) {
                    text = "{f|  " + params.data + "}";
                  } else {
                    text = "{a|  " + params.data + "%RH}";
                  }
                  return text;
                },
                rich: {
                  a: {
                    color: "#53B0A5",
                  },
                  b: {
                    color: "blue",
                  },
                  c: {
                    color: "yellow",
                  },
                  d: {
                    color: "green",
                  },
                  f: {
                    color: "yellow",
                  },
                },
                position: "right",
                distance: 0, // 向右偏移位置
                show: true,
              },
            },
            data: datas,
            z: 0,
            animationEasing: "elasticOut",
          },
          {
            name: "外框",
            type: "bar",
            barGap: "-120%", // 设置外框粗细
            data: [
              total,
              total,
              total,
              total,
              total,
              total,
              total,
              total,
              total,
              total,
              total,
              total,
              total,
              total,
              total,
              total,
              total,
              total,
              total,
              total,
            ],
            barWidth: 15,
            itemStyle: {
              normal: {
                color: "transparent", // 填充色
                barBorderColor: "#1C4B8E", // 边框色
                barBorderWidth: 0.2, // 边框宽度
                // barBorderRadius: 0, //圆角半径
                label: {
                  // 标签显示位置
                  show: false,
                  position: "top", // insideTop 或者横向的 insideLeft
                },
              },
            },
            z: 0,
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>
    
  <style lang="less" scoped>
.echart {
  width: 309px;
  height: 55px;
}

@media (max-height: 1080px) {
  .echart {
    width: 309px;
    height: 55px !important;
  }
}
</style>