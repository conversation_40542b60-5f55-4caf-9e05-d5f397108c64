<template>
  <div>
    <div class="zichanbeijin" v-if="ressss">
      <div class="title">
        <div>材料管理</div>
        <img class="img1" @click="close()" src="../../assets/image/table-x.png" alt="" />
      </div>
      <hr />
      <div class="titlecontent">
        <div class="xuan">
          <el-select v-model="value" placeholder="请选择材料品名">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </div>
        <div class="xuan">
          <el-input placeholder="请输入内容" v-model="input4">
            <i slot="prefix" class="el-input__icon el-icon-search"></i>
          </el-input>
        </div>
        <div class="xiang">
          <div class="item">
            <img src="../../assets/image/xiaoiconshousuo.png" alt="" />
            <div>搜索</div>
          </div>
          <div class="item">
            <img src="../../assets/image/xiaoiconzz.png" alt="" />
            <div>重置</div>
          </div>
          <div class="item item1">
            <img src="../../assets/image/xiaoiconschuan.png" alt="" />
            <div style="color: #ffaa3f">导出</div>
          </div>
        </div>
      </div>
      <div class="table-container">
        <div class="table-header">
          <div class="table-rowtitle">
            <div v-for="(item, index) in tableTitle" :key="index" class="table-cell">
              {{ item.key }}
            </div>
          </div>
        </div>

        <div class="table-body">
          <div v-for="(item, index) in tableDataItem" :key="index" class="table-row">
            <div class="table-cell">{{ item.userid }}</div>
            <div class="table-cell">{{ item.username }}</div>
            <div class="table-cell">{{ item.manufacturername }}</div>
            <div class="table-cell">{{ item.collegename }}</div>
            <div class="table-cell">{{ item.laboratoryname }}</div>
            <div class="table-cell">{{ item.roomid }}</div>
            <div class="table-cell">{{ item.ordertime }}</div>
            <div class="table-cell">{{ item.productname }}</div>
            <div class="table-cell">{{ item.casid }}</div>
            <div class="table-cell">{{ item.volume }}</div>
            <div class="table-cell">{{ item.number }}{{ item.specifications }}</div>
            <!-- <div class="table-cell">化学实验室常用</div> -->


            <!-- <div class="table-cell" style="color: #2ffff2">
              {{ item.status }}
            </div> -->

            <!-- <div class="table-cell">
              <img src="../../assets/image/imgiconxiaoc.png" alt="" />
            </div>

            <div class="table-cell">
              <img src="../../assets/image/xiaoviode.png" alt="" />
            </div>
            <div class="table-cell">
              <img src="../../assets/image/iconwenzi.png" alt="" />
            </div>
            <div class="table-cell">{{ item.responsible }}</div>
            <div class="table-cell">
              <img src="../../assets/image/zuochuoss.png" alt="" />
            </div> -->
          </div>
        </div>
      </div>
      <!-- <el-pagination
        class="fenye"
        small
        layout=" next, pager, prev"
        :total="50"
      >
      </el-pagination> -->
      <el-pagination small class="fenye" @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="currentPage4" :page-sizes="[16, 20, 30, 40]" :page-size="16" layout="total, next, pager,  prev,"
        :total=total>
      </el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  props: ["tableTitle", "tableDataItem", 'pageSize', 'total'],
  data() {
    return {
      ressss: true,
      tableData: [
        {
          loucheng: "",
          shebeibianhao: "12121",
          shebeimingcheng: "保证正常",
          roomid: "房间号",
          model: "model",
          sbtypess: "sbtype",
          tytaisming: "sbtype",
        },
      ],
      currentPage4: 1,

      value: "",
      input4: "",
      options: [],
    };
  },
  methods: {
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.$emit('child-event', val);
      this.currentPage4 = val
    },
    close() {
      this.$emit("hidedetails");
    },
    anniu() {
      this.ressss = false;
    },
  },
};
</script>

<style lang="less" scoped>
.zichanbeijin {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: url("../../assets/image/zichanbeijin.png");
  width: 1863px;
  height: 868px;
  z-index: 99999;
  padding-left: 34px;
  padding-right: 22px;
  padding-top: 21px;

  .title {
    margin-bottom: 19px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-family: Source Han Sans SC;
    font-weight: bold;
    font-size: 18px;
    color: #ffffff;
  }

  .img1 {
    cursor: pointer;
    width: 15px;
    height: 15px;
  }

  hr {
    margin-bottom: 17px;
    border: none;
    border-top: 2px solid #466873;
    /* 设置边框颜色为红色 */
  }

  .titlecontent {
    width: 100%;
    display: flex;
    align-items: center;
    margin-bottom: 18px;

    .xuan {
      margin-right: 25px;
      width: 230px;
      height: 37px;
      color: #ffffff;

      ::v-deep .el-select__wrapper {
        background-color: rgba(28, 41, 50, 0.2);
        box-sizing: none;
        box-shadow: 0 0 0 1px #608e9a inset;
      }

      ::v-deep .el-input__wrapper {
        background-color: rgba(28, 41, 50, 0.2);
        box-sizing: none;
        box-shadow: 0 0 0 1px #608e9a inset;
      }
    }

    .xiang {
      width: 252px;
      height: 37px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .item {
        cursor: pointer;
        gap: 3px;
        width: 77px;
        height: 37px;
        border: 1px solid #537b86;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: Source Han Sans SC;
        font-weight: 400;
        font-size: 15px;
        color: #3cccf9;
        border-radius: 5px;
      }

      .item1 {
        cursor: pointer;
        border: 1px solid #d28e3c;
      }
    }
  }

  .table-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    color: #fff;
  }

  .table-row {
    color: #fff;
    display: flex;
    width: 100%;
    background-color: #1c2932;
    border: 1px solid #56808d;
    margin-bottom: -1px;
  }

  .table-header,
  .table-rowtitle {
    border: 1px solid #56808d;
    color: #fff;
    display: flex;
    width: 100%;
    background-color: #1c4064;
    margin-bottom: -1px;
  }

  .table-cell {
    flex: 1;
    padding: 10px;
    border-bottom: 1px solid #ccc;
    text-align: center;
  }

  .table-header .table-cell {
    font-weight: bold;
  }

  .table-row:nth-child(even) {
    color: #fff;
    display: flex;
    width: 100%;
    background-color: #1c4064;
    border: 1px solid #56808d;
    margin-bottom: -1px;
  }

  .fenye {
    margin-top: 18px;
    display: flex;
    flex-direction: row-reverse;

    ::v-deep .el-pager li {
      background: url("../../assets/image/fenyebox.png");
      background-size: 100% 100%;
      color: #fff;
      margin: 3px !important;
    }

    /deep/.el-pager li.is-active {
      color: #66BAE2 !important;
    }
  }
}


::v-deep .el-pagination button {
  background: rgba(28, 41, 50, 0) !important;
  color: #fff !important;
}

::v-deep .el-pagination__jump {
  color: #fff !important;
}

::v-deep .el-pagination__total {
  color: #fff !important;
  margin-left: 10px !important;
}

::v-deep .el-select__wrapper {
  background-color: rgba(151, 173, 83, 0);
  box-sizing: none;
  box-shadow: 0 0 0 1px#694e31 inset;
  margin-right: 10px;
}

::v-deep .el-input__wrapper {
  background-color: rgba(28, 41, 50, 0);
  box-sizing: none;
  box-shadow: 0 0 0 1px #be8b34 inset;
}

.table-body {
  height: 625px;
  /* 设置容器的高度 */
  overflow-y: scroll;

  /* 设置垂直滚动条 */
  /* 默认情况下隐藏滚动条 */
  &::-webkit-scrollbar {
    width: 0;
    /* 设置滚动条的宽度为0，即隐藏滚动条 */
  }

  /* 鼠标悬停在容器上时显示滚动条 */
  &:hover::-webkit-scrollbar {
    width: 1px;
    /* 鼠标悬停时设置滚动条的宽度为10px */
  }

  /* 设置滚动条轨道的样式 */
  &::-webkit-scrollbar-track {
    background-color: #f1f1f1;
    /* 设置滚动条轨道的背景色 */
  }

  /* 设置滚动条滑块的样式 */
  &::-webkit-scrollbar-thumb {
    background-color: #888;
    /* 设置滚动条滑块的背景色 */
  }

  /* 鼠标悬停在滚动条上时的样式 */
  &::-webkit-scrollbar-thumb:hover {
    background-color: #555;
    /* 设置鼠标悬停时滚动条滑块的背景色 */
  }
}
</style>