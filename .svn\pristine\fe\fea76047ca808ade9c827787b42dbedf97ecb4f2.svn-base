<template>
  <div class="table-home">
    <img
      class="xxxx"
      src="../../assets/image/table-x.png"
      alt=""
      v-if="sssiframe"
      @click="yuyuequxiao"
    />
    <iframe
      v-if="sssiframe"
      class="sssiframe"
      src="http://yiqi.tju.edu.cn/genee/"
      frameborder="0"
    ></iframe>

    <div class="box">
      <div class="title">
        <div class="img">{{ selectedItem.name }}</div>
        <div class="wenzhixuanz">
          <!-- <div class="left">
            <div style="color: #08f9f9">使用中</div>
            /
            <div style="color: #a2abb0">停用</div>
          </div> -->
        </div>
        <!-- this.selectedItem = item; -->
        <img
          class="x"
          src="../../assets/image/table-x.png"
          alt=""
          @click="close()"
        />
      </div>
      <div class="content">
        <div class="iframe">
          <img
            v-if="selectedItem.type"
            class="tupianimg"
            :src="zengtiimg"
            alt=""
          />
          <iframe
            v-else="!selectedItem.type"
            :src="`https://qiye.3dzhanting.cn/share-model.html?ids=${ids}&res=1&isshow=1`"
            frameborder="0"
            class="iframess"
          ></iframe>
          <div class="beijinxians1">加载中....</div>
        </div>
        <div class="rigth">
          <div>
            <div class="titles">
              <div
                class="item"
                @click="qiehuna(1)"
                :class="{ item1: currentView === 1 }"
              >
                设备详情
              </div>
              <div
                class="item"
                @click="qiehuna(2)"
                :class="{ item1: currentView === 2 }"
              >
                预约详情
              </div>
            </div>

            <div v-if="currentView === 1">
              <div class="boxsttt">
                <div class="xiaoboxs" v-for="item in cgqlistss" :key="item.id">
                  <img
                    class="siqiu"
                    src="../../assets/image/kongxin.png"
                    alt=""
                  />
                  <div class="nihaowo">{{ item.name }}</div>
                  <div class="shuru">{{ item.value }}</div>
                </div>
              </div>
            </div>

            <div v-else>
              <div class="titless">
                <div>预约人</div>
                <div>预约时间</div>
                <div>预约时长</div>
                <div>门禁编号</div>
                <div>预约状态</div>
              </div>
              <div class="contents" v-for="item in tableDatass" :key="item">
                <div>{{ item.name }}</div>
                <div>{{ item.date }}</div>
                <div>{{ item.duration }}</div>
                <div>{{ item.roomNumber }}</div>
                <div>{{ item.status }}</div>
              </div>
              <div class="anniu" @click="yuyue">点击立即预约</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import echarts1 from "@/components/echarts/bingjifang/echarts4.vue";
export default {
  components: {
    echarts1,
  },
  props: ["selectedItem", "ids", "zengtiimg"],

  data() {
    return {
      sssiframe: false,
      tableDatass: [
        {
          name: "张三",
          date: "2024-09-01",
          duration: "2H (13:00-15:00)",
          roomNumber: "001",
          status: "已成功",
        },
        {
          name: "张三",
          date: "2024-09-01",
          duration: "2H (13:00-15:00)",
          roomNumber: "001",
          status: "已成功",
        },
        {
          name: "张三",
          date: "2024-09-01",
          duration: "2H (13:00-15:00)",
          roomNumber: "001",
          status: "已成功",
        },
        {
          name: "张三",
          date: "2024-09-01",
          duration: "2H (13:00-15:00)",
          roomNumber: "001",
          status: "未成功",
        },
        {
          name: "张三",
          date: "2024-09-01",
          duration: "2H (13:00-15:00)",
          roomNumber: "001",
          status: "未成功",
        },
      ],
      currentView: 1,
      cgqlistss: [
        { name: "设备状态:", value: "在线" },
        { name: "预警情况:", value: "正常" },
        { name: "湿度分辨率:", value: "0.04%" },
        { name: "湿度精度:", value: "±3%RH（20%RH~80%RH）" },
        { name: "湿度范围:", value: "-40℃~+125℃" },
        { name: "温度范围:", value: "0.01℃" },
        { name: "温度精度：", value: "±0.5℃（10℃~60℃）" },
      ],
      dist: false,
      items: ["T1", "T2"],
      backgroundClasses: ["bg-image-1", "bg-image-2", "bg-image-3"],
      // Array of background images using require
      backgrounds: [
        { backgroundImage: `url(${require("../../assets/image/image1.jpg")})` },
        { backgroundImage: `url(${require("../../assets/image/image2.jpg")})` },
        { backgroundImage: `url(${require("../../assets/image/image3.jpg")})` },
      ],
      currentIndex: 0, // Initial index for class and image

      selectedIndex: 0,
      inputs: [
        {
          name: "温度",
          value: "4℃",
          type: "温度",
          ztaqi: "4℃",
        },

        {
          name: "湿度",
          value: "56%",
          type: "门状态",
          ztaqi: "已开启",
        },
      ],
    };
  },
  computed: {
    // Get the current class name
    currentClass() {
      return this.backgroundClasses[this.currentIndex];
    },
    // Get the current background style
    currentBackground() {
      return this.backgrounds[this.currentIndex];
    },
  },
  methods: {
    yuyuequxiao() {
      this.sssiframe = false;
    },
    yuyue() {
      this.sssiframe = true;
    },
    qiehuna(view) {
      // 切换显示的视图，view可以是1或2
      this.currentView = view;
    },
    switchBackground() {
      this.currentIndex = (this.currentIndex + 1) % this.backgrounds.length;
    },
    toggleSelection(index) {
      if (index == 0) {
        this.inputs = [
          {
            name: "温度",
            value: "4℃",
          },
          // {
          //   name: "分辨率",
          //   value: "0.01℃",
          // },
          // {
          //   name: "精度",
          //   value: "±0.5℃(-100℃~100℃);±1.0℃(其他范围);",
          // },
          {
            name: "门状态",
            value: "关",
          },
        ];
      } else if (index == 1) {
        this.inputs = [
          {
            name: "温度",
            value: "4.1℃",
          },
          // {
          //   name: "分辨率",
          //   value: "0.01℃",
          // },
          // {
          //   name: "精度",
          //   value: "±0.5℃(-100℃~100℃);±1.0℃(其他范围);",
          // },
          {
            name: "门状态",
            value: "关",
          },
        ];
      } else if (index == 2) {
        this.inputs = [];
      } else if (index == 3) {
        this.inputs = [];
      }

      this.selectedIndex = index; // 否则选中当前的
    },
    close() {
      this.$emit("hidedetails");
    },
  },
};
</script>

<style lang="less" scoped >
.wenzhixuanz {
  position: relative;
  display: flex;
  align-items: center;

  .left {
    display: flex;
    font-family: Source Han Sans SC;
    font-weight: 400;
    font-size: 17px;
  }

  .right {
    width: 351px;
    margin-left: 35px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-family: Source Han Sans SC;
    font-weight: bold;
    font-size: 15px;
    color: #fff;

    .item {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 90px;
      height: 35px;
      background: url("../../assets/image/lanse.png");
      font-family: Source Han Sans SC;
      font-weight: bold;
      font-size: 15px;
      color: #64dbfa;
      // cursor: pointer;
      display: flex;
      align-items: center;
    }

    .item1 {
      width: 90px;
      height: 35px;
    }
  }
}

.table-home {
  .box {
    color: #fff;
    background: url("../../assets/image/table-beijing.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 1368px;
    height: 845px;
    padding-top: 34px;

    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-right: 28px;
      margin-left: 31px;

      .img {
        background: url("../../assets/image/table-title.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;

        height: 35px;
        text-align: left;
        padding-left: 42px;

        line-height: 28px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 17px;
        color: #ffffff;
      }

      .x {
        cursor: pointer;
        width: 20px;
        height: 20px;
        cursor: pointer;
      }
    }

    .content {
      display: flex;

      margin: 23px 29px 0 67px;

      .iframess {
        position: absolute;
        top: 92px;

        width: 645px;
        height: 697px;

        z-index: 2;
      }

      .rigth {
        margin-left: 660px;
        display: flex;

        .titles {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 11px;
          .item {
            color: #fff;
            font-size: 19px;

            background: url("../../assets/image/weixuanzhogn.png");

            width: 305px;
            height: 57px;
            background-repeat: no-repeat;
            background-size: 100% 100%;
            // display: flex;
            // align-items: center;
            // justify-content: center;
            text-align: center;
            font-family: Source Han Sans SC;
            font-weight: bold;
            font-size: 20px;
            line-height: 56px;
            color: #a09a97;
            cursor: pointer;
          }
          .item1 {
            background: url("../../assets/image/tablexuanzhong.png");
            cursor: pointer;
            width: 305px;
            height: 66px;
            line-height: 56px !important;
            background-repeat: no-repeat;
            background-size: 100% 100%;

            font-family: Source Han Sans SC;
            font-weight: bold;
            font-size: 20px;
            color: #ffffff;
            margin-top: 9px;

            text-align: center;
          }
        }
      }
    }
  }
}
.boxsttt {
  margin-top: 6px;
  padding-top: 5px;
  margin-bottom: 0.225rem;

  // height: 800px;

  overflow-y: scroll;

  /* 设置垂直滚动条 */
  /* 设置滚动条的样式 */
  &::-webkit-scrollbar {
    width: 0.1px;
    /* 设置滚动条的宽度 */
  }

  /* 设置滚动条轨道的样式 */
  &::-webkit-scrollbar-track {
    background-color: #f1f1f1;
    /* 设置滚动条轨道的背景色 */
  }

  /* 设置滚动条滑块的样式 */
  &::-webkit-scrollbar-thumb {
    background-color: #334f6e;
    /* 设置滚动条滑块的背景色 */
  }

  /* 鼠标悬停在滚动条上时的样式 */
  &::-webkit-scrollbar-thumb:hover {
    background-color: #555;
    /* 设置鼠标悬停时滚动条滑块的背景色 */
  }

  .el-input {
    width: 305px;
    height: 34px;
    color: #fff !important;

    ::v-deep .el-input__wrapper {
      background: url("../../assets/image/inputss.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
      box-shadow: none !important;
    }
  }

  .suosuo {
    position: absolute;
    top: 66px;
    left: 285px;
  }

  .xiaobox {
    margin-top: 20px;
    display: flex;
    align-items: center;

    .siqiu {
      width: 16px;
      height: 16px;
    }

    .shuru {
      display: flex;
      align-items: center;
      padding-left: 10px;
      background: url("../../assets/image/shebei12.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 396px;
      height: 37px;
      font-family: Source Han Sans SC;
      font-weight: bold;
      font-size: 12px;
      color: #ffffff;
    }
  }

  .xiaoboxs {
    cursor: pointer;
    margin-top: 16px;
    display: flex;
    margin-left: 5px;
    align-items: center;

    .nihaowo {
      width: 167px;
      font-family: Source Han Sans SC;
      font-weight: bold;
      font-size: 18px;
      color: #ffffff;

      display: flex;
    }

    .siqiu {
      width: 16px;
      height: 16px;
      margin-left: 10px;
      margin-right: 7px;
    }

    .shuru {
      display: flex;
      align-items: center;
      padding-left: 10px;
      background: url("../../assets/image/shebei12.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 396px;
      height: 37px;
      font-family: Source Han Sans SC;
      font-weight: bold;
      font-size: 12px;
      color: #ffffff;
    }
  }
}

.titless {
  margin-right: 10px;
  width: 100%;
  background: rgba(44, 59, 100, 0.5);
  height: 51px;
  margin-top: 8px;
  display: flex;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  font-size: 17px;
  color: #40d7ff;
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.contents {
  border-bottom: 1px solid #293e5e;
  margin-right: 10px;
  width: 100%;
  background: rgba(56, 78, 115, 0.2);
  height: 51px;

  display: flex;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  font-size: 17px;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.contents:nth-child(odd) {
  background: rgba(52, 76, 116, 0.4); /* 深色背景 */
}

.contents:nth-child(even) {
  background: rgba(55, 76, 117, 0.2); /* 浅色背景 */
}
.anniu {
  cursor: pointer;
  background: url("../../assets/image/dianjianniu.png");
  width: 159px;
  height: 48px;
  background-repeat: no-repeat;
  background-size: 100% 100%;

  font-family: Alibaba PuHuiTi;
  font-weight: bold;
  font-size: 19px;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  bottom: 46px;
  right: 44px;
  z-index: 999999999999999999;
}

.sssiframe {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 1500px;
  height: 850px;
  z-index: 9999;
}
.xxxx {
  cursor: pointer;
  position: absolute;
  top: -20px;
  right: -90px;

  width: 15px;
  height: 15px;
  z-index: 9999;
}
.beijinxians1 {
  position: absolute;
  top: 94px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 640px;
  height: 690px;
  background-color: #adc0d3;
  z-index: 1;
}
</style>