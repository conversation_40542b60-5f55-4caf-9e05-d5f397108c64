var rightBtn = new Vue({
  el: "#app",
  data() {
    return {
      isshowchange: "",
      isfw: "",
      iszk: "",
      leftBtnIndex: null,
      rightBtnIndex: null,
      rightBtnIndex1: null,
      rightBtnIndex2: null,
      rightBtn: [],
      roomData: [],
      submenuData: {
        父菜单1: ["1F", "2F", "3F", "地下室"],
        父菜单2: ["1F", "2F", "3F"],
        父菜单3: ["1F", "2F", "3F"],
      },
      floorlist: [
        {
          title: "实验楼",
          name: ["JZ1_Building_01", "JZ2_Building_01", 'dm_dx',"dm"],
          pos: {
            x: 48.12188990651303,
            y: 62.89422173478372,
            z: 30.265675265868214,
          },
          tar: {
            x: -44.10985242127187,
            y: 1.4901579019901197,
            z: 30.71627393509893,
          },
          floor: [
            {
              title: "楼顶",
              pos: {
                x: 9.223440558558195,
                y: 159.5516673445813,
                z: 8.399120573928856,
              },
              tar: {
                x: -43.35588586108212,
                y: -7.532907045957097,
                z: 8.893870862878108,
              },
              name: ["JZ1_Building_01", "JZ2_Building_01", 'dm_dx',"dm"],
            },
            {
              title: "5F",
              pos: {
                x: 9.223440558558195,
                y: 159.5516673445813,
                z: 8.399120573928856,
              },
              tar: {
                x: -43.35588586108212,
                y: -7.532907045957097,
                z: 8.893870862878108,
              },
              name: [
                'dm_dx',"dm",
                "JZ1_F2_SN_Model_Building_01",

                "JZ2_F2_SN_Model_Building_01",
                "JZ1_F1_SN_Model_Building_01",

                "JZ2_F1_SN_Model_Building_01",
                "JZ1_B1_SN_Model_Building_01",
                "JZ1_F3_SN_Model_Building_01",
                "JZ2_F3_SN_Model_Building_01",
                "JZ1_F4_SN_Model_Building_01",
                "JZ2_F4_SN_Model_Building_01",
                "JZ1_F5_SN_Model_Building_01",
                "JZ2_F5_SN_Model_Building_01",
              ],
            },
            {
              title: "4F",
              pos: {
                x: 9.223440558558195,
                y: 159.5516673445813,
                z: 8.399120573928856,
              },
              tar: {
                x: -43.35588586108212,
                y: -7.532907045957097,
                z: 8.893870862878108,
              },
              name: [
                'dm_dx',"dm",
                "JZ1_F2_SN_Model_Building_01",

                "JZ2_F2_SN_Model_Building_01",
                "JZ1_F1_SN_Model_Building_01",

                "JZ2_F1_SN_Model_Building_01",
                "JZ1_B1_SN_Model_Building_01",
                "JZ1_F3_SN_Model_Building_01",
                "JZ2_F3_SN_Model_Building_01",
                "JZ1_F4_SN_Model_Building_01",
                "JZ2_F4_SN_Model_Building_01",
              ],
            },
            {
              title: "3F",
              pos: {
                x: 9.223440558558195,
                y: 159.5516673445813,
                z: 8.399120573928856,
              },
              tar: {
                x: -43.35588586108212,
                y: -7.532907045957097,
                z: 8.893870862878108,
              },
              name: [
                'dm_dx',"dm",
                "JZ1_F2_SN_Model_Building_01",

                "JZ2_F2_SN_Model_Building_01",
                "JZ1_F1_SN_Model_Building_01",

                "JZ2_F1_SN_Model_Building_01",
                "JZ1_B1_SN_Model_Building_01",
                "JZ1_F3_SN_Model_Building_01",
                "JZ2_F3_SN_Model_Building_01",
              ],
            },
            {
              title: "2F",
              pos: {
                x: 9.223440558558195,
                y: 159.5516673445813,
                z: 8.399120573928856,
              },
              tar: {
                x: -43.35588586108212,
                y: -7.532907045957097,
                z: 8.893870862878108,
              },
              name: [
                'dm_dx',"dm",
                "JZ1_F2_SN_Model_Building_01",

                "JZ2_F2_SN_Model_Building_01",
                "JZ1_F1_SN_Model_Building_01",

                "JZ2_F1_SN_Model_Building_01",
                "JZ1_B1_SN_Model_Building_01",
              ],
            },
            {
              title: "1F",
              pos: {
                x: 9.223440558558195,
                y: 159.5516673445813,
                z: 8.399120573928856,
              },
              tar: {
                x: -43.35588586108212,
                y: -7.532907045957097,
                z: 8.893870862878108,
              },
              name: [
                "JZ1_F1_SN_Model_Building_01",

                "JZ2_F1_SN_Model_Building_01",
                "JZ1_B1_SN_Model_Building_01",
                'dm_dx',"dm",
              ],
            },
            {
              title: "B1F",
              pos: {
                x: -9.898794069500234,
                y: 79.58884772637656,
                z: 30.146650335501832,
              },
              tar: {
                x: -42.75308851051134,
                y: -9.75274042843543,
                z: 30.60458207854196,
              },
              name: ["dm", "JZ1_B1_SN_Model_Building_01"],
            },
          ],
        },
      ],
      selectedMenu: null,
      submenuItems: [],
      selectedSubmenuItem: null,
      activefloor: [],
    };
  },
  mounted() {
    var that = this;
    this.checkFloorList();
    window.addEventListener("message", function (event) {
      // console.log(event.data.data,'sbid');
      if (
        event.data &&
        event.data.data &&
        event.data.data.type == "setFloorRoomData"
      ) {
        let info = event.data.data.info;
        console.log(info.roomData);
        info.roomData.forEach((item) => {
          console.log(
            view.getObjCenterByNames(view.searchAllByName(item.roomId))[0]
              .center,
            11
          );
          item.center = view.getObjCenterByNames(
            view.searchAllByName(item.roomId)
          )[0].center;
        });
        that.roomData = info.roomData;

        const results = that
          .selectfloor(info.floorNum)[0]
          .floor.find(function (item) {
            return item.title == info.floorNum;
          });
        console.log(results, 11);
        that.rightBtnFun1(
          results.name,
          results.pos,
          results.tar,
          results.title
        );
      } else if (
        event.data &&
        event.data.data &&
        event.data.data.type == "setFloorData"
      ) {
        let data = event.data.data.info;
        let bid = data.buildNum;
        let fid = data.floorNum;
        console.log(data);
        // that.sendMessage(data.floorNum);
        const floorDetails = that.getFloorDetails(bid, fid, that.rightBtn);
        console.log(floorDetails, 1254);
        setfloor(floorDetails.name, floorDetails.pos, floorDetails.tar);

        setTimeout(() => {
          addImg(data.roomData);
        }, 200);
      } else if (
        event.data &&
        event.data.data &&
        event.data.data.type == "build"
      ) {
        console.log(floorlist);
        let info = event.data.data.info;
        console.log(info.roomData);
        info.roomData.forEach((item) => {
          console.log(
            view.getObjCenterByNames(view.searchAllByName(item.roomId))[0]
              .center,
            11
          );
          item.center = view.getObjCenterByNames(
            view.searchAllByName(item.roomId)
          )[0].center;
        });
        that.roomData = info.roomData;

        const results = that
          .selectfloor(info.floorNum)[0]
          .floor.find(function (item) {
            return item.title == info.floorNum;
          });
        console.log(results, 11);
        that.rightBtnFun1(
          results.name,
          results.pos,
          results.tar,
          results.title
        );
      } else if (
        event.data &&
        event.data.data &&
        event.data.data.type == "sbid"
      ) {
        console.log(event.data.data, "sbid");
      }
    });
  },
  methods: {
    getFloorDetails(buildingTitle, floorTitle, data) {
      for (let building of data) {
        if (building.title === buildingTitle) {
          for (let floor of building.floor) {
            if (floor.title === floorTitle) {
              return {
                pos: floor.pos,
                tar: floor.tar,
                name: floor.name,
              };
            }
          }
        }
      }
      return null;
    },

    changeSubmenu(parentMenu, items) {
      console.log(parentMenu, items, 185);
      this.selectedMenu = parentMenu;
      this.submenuItems = items.floor;
      this.selectedSubmenuItem = null;
    },
    selectSubmenuItem(item) {
      this.selectedSubmenuItem = item;
    },
    checkFloorList() {
      const checkInterval = setInterval(() => {
        if (window.floorlist) {
          this.rightBtn = window.floorlist;
          // document.getElementById("zhankai").style.display = rightBtn.rightBtn
          //   .length
          //   ? "block"
          //   : "none";

          clearInterval(checkInterval); // 数据获取完成后停止检查
        }
      }, 100); // 每100毫秒检查一次
    },
    selectfloor(floorName) {
      console.log(this.rightBtn);
      return this.rightBtn.filter((obj) =>
        obj.floor.some((f) => f.title === floorName)
      );
    },
    labelTitleFun(item, index) {
      // switchbd(index);
      console.log(item, index, 185);
      buildId = item.title;
      setfloor(item.name, item.pos, item.tar);
      this.rightBtnIndex2 = index;
      this.rightBtnIndex = null; // 重置楼层选择的状态
    },
    rightBtnFun(item, pos, tar, title, index, index2, devicename, flag) {
      let that = this;
      this.sendMessage(title);
      console.log(index, index2, item, pos, tar, title, devicename);
      // buildId = data && data.title ? data.title : 0;
      floorId = title;
      if (flag) {
        getmodeltableData(
          1,
          urlid,
          parkId,
          buildId,
          floorId,
          devicename ? devicename : ""
        );
        getmodeltableData(
          2,
          urlid,
          parkId,
          buildId,
          floorId,
          devicename ? devicename : ""
        );
      }

      setfloor(item, pos, tar);
      if (index == 0) {
      }
      if (index == 1) {
      } else if (index == 2) {
      } else if (index == 3) {
      }
      // that.rightBtnIndex1 = index;
      that.rightBtnIndex = item;
      that.rightBtnIndex2 = null;
      // if (item[0] == "f5") {
      setTimeout(() => {
        console.log(1111);
        addlableldimg(this.roomData);
      }, 100);
      // }
    },
    rightBtnFun1(item, pos, tar, title, index, index2) {
      let that = this;
      // this.sendMessage(title);
      console.log(index, index2, item, pos, tar, title);

      // setfloor(item, pos, tar);
      // if (index == 0) {
      // }
      // if (index == 1) {
      // } else if (index == 2) {
      // } else if (index == 3) {
      // }
      // that.rightBtnIndex1 = index;
      that.rightBtnIndex = item;
      that.rightBtnIndex2 = null;

      setTimeout(() => {
        console.log(1111);
        // addlable(this.roomData);
      }, 100);
    },
    sendMessage(id) {
      console.log("已发送", id);

      if (id == "1F" && projectname == "标签演示") {
        setTimeout(() => {
          addImg(lableimg);
        }, 200);
      }
      window.parent.postMessage(
        {
          type: "clickFloor",
          info: {
            floorNum: id, //当前点击楼层
          },
        },
        "*"
      );
    },
    leftBtnFun(index) {
      let that = this;
      that.leftBtnIndex = index;
    },
    imgToBase64(icon) {
      return icon.toString();
    },
    clickFun() {
      console.log("點擊");
    },
    scrollLeft() {
      const container = document.querySelector(".top");
      container.scrollBy({ left: -200, behavior: "smooth" });
    },
    scrollRight() {
      const container = document.querySelector(".top");
      container.scrollBy({ left: 200, behavior: "smooth" });
    },
  },
});

try {
} catch (e) {}

//切换楼宇
function switchbd(index) {}
