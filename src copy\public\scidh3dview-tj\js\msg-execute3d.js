var comdata;

window.addEventListener("message", function (event) {
  //event.data获取传过来的数据
  console.log(event);

  if (event.data.type == "function") {
    let name = event.data.name;
    let comdata = event.data.param;
    console.log(comdata, "收到了");
  } else {
    let comdata = event.data.param;
    console.log(comdata, "收到了");

    if (comdata.data == "zutai1") {
      view.setLayer([
        "floor",
        "xt_sf_xf_2",
        "柱体181",
        "柱体177",
        "柱体175",
        "柱体174",
        "柱体173",
        "柱体172",
        "柱体171",
        "柱体170",
        "柱体169",
        "柱体163",
        "柱体162",
        "柱体161",
        "柱体159",
        "柱体157",
        "柱体154",
        "柱体092",
        "柱体091",
        "柱体090",
        "柱体089",
        "柱体088",
        "柱体087",
        "柱体086",
        "柱体085",
        "柱体084",
        "柱体083",
        "柱体082",
        "柱体081",
        "柱体080",
        "柱体079",
        "柱体078",
        "柱体077",
        "柱体076",
        "柱体075",
        "柱体074",
        "柱体073",
        "柱体072",
        "柱体071",
        "柱体070",
        "柱体069",
        "柱体068",
        "柱体067",
        "柱体066",
        "柱体065",
        "柱体064",
        "柱体063",
        "柱体062",
        "柱体061",
        "柱体060",
        "柱体059",
        "柱体058",
        "柱体057",
        "柱体056",
        "柱体055",
        "柱体054",
        "柱体053",
        "柱体052",
        "柱体050",
        "柱体047",
        "柱体046",
        "柱体045",
        "柱体044",
        "柱体042",
        "柱体041",
        "柱体040",
        "柱体038",
        "柱体036",
        "柱体035",
        "柱体034",
        "柱体032",
        "柱体031",
        "柱体021",
        "柱体020",
        "柱体019",
        "柱体018",
        "柱体015",
        "柱体014",
        "柱体013",
        "柱体012",
        "柱体011",
        "柱体009",
        "柱体006",
        "柱体005",
        "柱体004",
        "柱体003",
        "柱体001",
        "柱体",
        "柱体002",
        "柱体007",
        "柱体008",
        "柱体010",
        "柱体016",
      ]);

      // view.animateCamera(
      //   {
      //     x: 19.86918084985126,
      //     y: 6.012298248396115,
      //     z: 11.386288472414392,
      //   },
      //   {
      //     x: 19.86585560501809,
      //     y: 0.18774235567265957,
      //     z: 7.154502216058601,
      //   },
      //   0
      // );
    } else if (comdata.data == "zutai2") {
      view.setLayer([
        "floor",
        "xt_pf",
        "柱体181",
        "柱体177",
        "柱体175",
        "柱体174",
        "柱体173",
        "柱体172",
        "柱体171",
        "柱体170",
        "柱体169",
        "柱体163",
        "柱体162",
        "柱体161",
        "柱体159",
        "柱体157",
        "柱体154",
        "柱体092",
        "柱体091",
        "柱体090",
        "柱体089",
        "柱体088",
        "柱体087",
        "柱体086",
        "柱体085",
        "柱体084",
        "柱体083",
        "柱体082",
        "柱体081",
        "柱体080",
        "柱体079",
        "柱体078",
        "柱体077",
        "柱体076",
        "柱体075",
        "柱体074",
        "柱体073",
        "柱体072",
        "柱体071",
        "柱体070",
        "柱体069",
        "柱体068",
        "柱体067",
        "柱体066",
        "柱体065",
        "柱体064",
        "柱体063",
        "柱体062",
        "柱体061",
        "柱体060",
        "柱体059",
        "柱体058",
        "柱体057",
        "柱体056",
        "柱体055",
        "柱体054",
        "柱体053",
        "柱体052",
        "柱体050",
        "柱体047",
        "柱体046",
        "柱体045",
        "柱体044",
        "柱体042",
        "柱体041",
        "柱体040",
        "柱体038",
        "柱体036",
        "柱体035",
        "柱体034",
        "柱体032",
        "柱体031",
        "柱体021",
        "柱体020",
        "柱体019",
        "柱体018",
        "柱体015",
        "柱体014",
        "柱体013",
        "柱体012",
        "柱体011",
        "柱体009",
        "柱体006",
        "柱体005",
        "柱体004",
        "柱体003",
        "柱体001",
        "柱体",
        "柱体002",
        "柱体007",
        "柱体008",
        "柱体010",
        "柱体016",
      ]);
    } else if (comdata.data == "zutai3") {
      view.setLayer([
        "floor",
        "xt_sf_xf",
        "柱体181",
        "柱体177",
        "柱体175",
        "柱体174",
        "柱体173",
        "柱体172",
        "柱体171",
        "柱体170",
        "柱体169",
        "柱体163",
        "柱体162",
        "柱体161",
        "柱体159",
        "柱体157",
        "柱体154",
        "柱体092",
        "柱体091",
        "柱体090",
        "柱体089",
        "柱体088",
        "柱体087",
        "柱体086",
        "柱体085",
        "柱体084",
        "柱体083",
        "柱体082",
        "柱体081",
        "柱体080",
        "柱体079",
        "柱体078",
        "柱体077",
        "柱体076",
        "柱体075",
        "柱体074",
        "柱体073",
        "柱体072",
        "柱体071",
        "柱体070",
        "柱体069",
        "柱体068",
        "柱体067",
        "柱体066",
        "柱体065",
        "柱体064",
        "柱体063",
        "柱体062",
        "柱体061",
        "柱体060",
        "柱体059",
        "柱体058",
        "柱体057",
        "柱体056",
        "柱体055",
        "柱体054",
        "柱体053",
        "柱体052",
        "柱体050",
        "柱体047",
        "柱体046",
        "柱体045",
        "柱体044",
        "柱体042",
        "柱体041",
        "柱体040",
        "柱体038",
        "柱体036",
        "柱体035",
        "柱体034",
        "柱体032",
        "柱体031",
        "柱体021",
        "柱体020",
        "柱体019",
        "柱体018",
        "柱体015",
        "柱体014",
        "柱体013",
        "柱体012",
        "柱体011",
        "柱体009",
        "柱体006",
        "柱体005",
        "柱体004",
        "柱体003",
        "柱体001",
        "柱体",
        "柱体002",
        "柱体007",
        "柱体008",
        "柱体010",
        "柱体016",
      ]);
    } else if (comdata.data == "zutai") {
      view.setLayer([
        "floor",
        "xt_sf_xf_2",
        "xt_pf",
        "xt_sf_xf",
        "柱体181",
        "柱体177",
        "柱体175",
        "柱体174",
        "柱体173",
        "柱体172",
        "柱体171",
        "柱体170",
        "柱体169",
        "柱体163",
        "柱体162",
        "柱体161",
        "柱体159",
        "柱体157",
        "柱体154",
        "柱体092",
        "柱体091",
        "柱体090",
        "柱体089",
        "柱体088",
        "柱体087",
        "柱体086",
        "柱体085",
        "柱体084",
        "柱体083",
        "柱体082",
        "柱体081",
        "柱体080",
        "柱体079",
        "柱体078",
        "柱体077",
        "柱体076",
        "柱体075",
        "柱体074",
        "柱体073",
        "柱体072",
        "柱体071",
        "柱体070",
        "柱体069",
        "柱体068",
        "柱体067",
        "柱体066",
        "柱体065",
        "柱体064",
        "柱体063",
        "柱体062",
        "柱体061",
        "柱体060",
        "柱体059",
        "柱体058",
        "柱体057",
        "柱体056",
        "柱体055",
        "柱体054",
        "柱体053",
        "柱体052",
        "柱体050",
        "柱体047",
        "柱体046",
        "柱体045",
        "柱体044",
        "柱体042",
        "柱体041",
        "柱体040",
        "柱体038",
        "柱体036",
        "柱体035",
        "柱体034",
        "柱体032",
        "柱体031",
        "柱体021",
        "柱体020",
        "柱体019",
        "柱体018",
        "柱体015",
        "柱体014",
        "柱体013",
        "柱体012",
        "柱体011",
        "柱体009",
        "柱体006",
        "柱体005",
        "柱体004",
        "柱体003",
        "柱体001",
        "柱体",
        "柱体002",
        "柱体007",
        "柱体008",
        "柱体010",
        "柱体016",
      ]);
    }
    view.clearAllLight(); // 清除所有灯光
    let lightConfig = [
      {
        type: "AmbientLight",
        color: "#aaaaff",
        intensity: 1,
      },
      {
        intensity: 3,
        type: "DirectionalLight",
        color: "#fff",
        position: [353.1440322709692, 32.118162337619367, 415.14587542705004],
      },
    ];
    view.setLight(lightConfig);
  }
});
//生成标签
function createLabel(imageSrc, width, className, position, scale, name) {
  const container = document.createElement("div");
  const img = document.createElement("img");
  img.src = imageSrc;
  img.style.width = width + "px";
  img.draggable = false;
  container.appendChild(img);
  container.className = className;
  view.add3dSprite(container, {
    position,
    scale,
    name,
  });
}

function addLabel() {
  const labels = view
    .getObjCenterByNames([
      "B1",
      "B2",
      "B3",
      "B4",
      "W1",
      "W2",
      "C1",
      "C2",
      "sushe_01",
      "sushe_02",
      "sushe_03",
      "sushe_04",
      "sushe_05",
      "sushe_06",
      "sushe_07",
      "sushe_08",
      "sushe_09",
      "sushe_10",
    ])
    .map((item) => {
      return {
        name: item.name,
        pos: item.center,
        src: item.name + ".png",
        scale: 0.6,
      };
    });
  console.log(labels);
  labels.forEach((label) => {
    createLabel(
      `./images/${label.src}`,
      95,
      "label",
      label.pos,
      label.scale,
      label.name
    );
  });
}
