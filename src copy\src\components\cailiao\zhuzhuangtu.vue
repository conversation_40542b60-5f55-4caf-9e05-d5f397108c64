<template>
  <div class="echart" ref="echart"></div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  props: {
    chartData: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  methods: {
    init() {
      const myChart = echarts.init(this.$refs.echart);
var types = [
    '库存管理',
    '采购记录',
    '领用情况',
    '维护保养',
    '报废处理'
];

var names = [
    '烧杯', '量筒', '移液管', '滴定管', '试管', 
    '培养皿', 'EP管', '离心管'
];

var data = [
    [0, 0, 5], [0, 1, 1], [0, 2, 6], [0, 3, 2], [0, 4, 4],
    [1, 0, 7], [1, 1, 2], [1, 2, 5], [1, 3, 7], [1, 4, 2],
    [2, 0, 1], [2, 1, 1], [2, 2, 4], [2, 3, 2], [2, 4, 3],
    [3, 0, 5], [3, 1, 4], [3, 2, 7], [3, 3, 4], [3, 4, 4],
    [4, 0, 1], [4, 1, 3], [4, 2, 4], [4, 3, 8], [4, 4, 5],
    [5, 0, 2], [5, 1, 1], [5, 2, 3], [5, 3, 2], [5, 4, 4],
    [6, 0, 1], [6, 1, 1], [6, 2, 5], [6, 3, 10], [6, 4, 5],
    [7, 0, 7], [7, 1, 6], [7, 2, 5], [7, 3, 3], [7, 4, 4]
];

data = data.map(function (item) {
    return [item[1], item[0], item[2] || '-'];
});

const option = {
    tooltip: {
        position: 'top',
    },
    animation: false,
    grid: {
        height: '50%',
        top: '10%',
    },
    xAxis: {
        type: 'category',
        data: types,
        splitArea: {
            show: true,
        },
        axisLabel: {
            textStyle: { fontSize: 12, color: '#fff' }
        },
    },
    yAxis: {
        type: 'category',
        data: names,
        splitArea: {
            show: true,
        },
        axisLabel: {
            textStyle: { fontSize: 12, color: '#fff' }
        },
    },
    visualMap: {
        type: 'continuous',
        min: 0,
        max: 10,
        calculable: true,
        orient: 'horizontal',
        left: 'center',
        bottom: '15%',
        inRange: {
            color: ['#ebedf0', '#c6e48b', '#7bc96f', '#239a3b', '#196027']
        },
    },
    series: [
        {
            name: '材料管理情况',
            type: 'heatmap',
            data: data,
            label: {
                show: true,
                formatter(params) {
                    return params.value[2] * 10 + '%';
                }
            },
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowColor: 'rgba(0, 0, 0, 0.5)',
                },
            },
            tooltip: {
                formatter: function (params) {
                    var title = params.seriesName;
                    var x = params.name;
                    var y = names[params.value[1]];
                    var point = params.value[2];
                    return `${title}: ${point * 10}%<br>材料：${y}<br>管理项目：${x}<br>`;
                }
            }
        }
    ],
};



      myChart.setOption(option);
    },
  },
};
</script>

<style lang="less" scoped>
.echart {
  width: 500px;
  height: 330px;
}
</style>
