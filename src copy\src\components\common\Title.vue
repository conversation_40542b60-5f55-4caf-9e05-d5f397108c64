<template>
  <div class="tt" :style="{ height: computedHeight }">
    <div class="title">
      <div>{{ tit }}</div>
      <!-- <div v-if="isshow" class="anniu" @click="open()">查看详情</div> -->
    </div>
    <div class="content">
      <slot></slot>
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';

export default {
  // import引入的组件需要注入到对象中才能使用
  components: {},
  props: ["tit", 'isshow'],
  data() {
    // 这里存放数据
    return {};
  },
  // 计算属性类似于data概念
  computed: {
    computedHeight() {
      return this.tit === "平台介绍" ? "560px" : "仪器状态" ? "320px" : "289px";
    },
  },
  // 监控data中的数据变化
  watch: {},
  // 方法集合
  methods: {
    open() {
      if (this.tit == "疾控中心介绍") {
        this.$emit("open-dialog", 0);
      } else if (this.tit == "疾控中心风采") {
        this.$emit("open-dialog", 1);
      }

    }
  },
  // 生命周期 - 创建完成（可以访问当前this实例）
  created() { },
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted() { },
  beforeCreate() { }, // 生命周期 - 创建之前
  beforeMount() { }, // 生命周期 - 挂载之前
  beforeUpdate() { }, // 生命周期 - 更新之前
  updated() { }, // 生命周期 - 更新之后
  beforeDestroy() { }, // 生命周期 - 销毁之前
  destroyed() { }, // 生命周期 - 销毁完成
  activated() { }, // 如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="less" scoped >
.tt {
  background: url("../../assets/image/titlebg.png");
  background-size: 100% 100%;
  width: 387px;
  padding-top: 10px;
  padding-left: 17px;
  height: 289px;
}

.title {
  letter-spacing: 0.5px;
  background: url("../../assets/image/title.png");
  background-size: 100% 100%;
  width: 354px;
  height: 34px;

  font-family: Alibaba PuHuiTi;
  font-weight: 500;
  font-style: italic;
  font-size: 24px;
  color: #FFFFFF;
  text-align: left;
  line-height: 12px;
  padding-left: 40px;

  // padding-top: 10px;

  justify-content: space-between;

  .anniu {
    background: url("../../assets/image/biaoqian.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    font-family: Source Han Sans SC;
    font-weight: bold;
    font-size: 12px;
    color: #ffffff;
    width: 78px;
    height: 35px;
    text-align: center;
    line-height: 35px;
    cursor: pointer;
  }
}

// .content{
//     background: url("../../assets/image/itembgc.png");
//     width: 330px;
//     height:265px;
//     background-repeat: no-repeat;
//     background-size: 100% 100%;
//   }
</style>