<template>
  <div class="table-home" v-if="isshow">
    <div class="box">
      <div class="title">
        <div class="img">报警系统</div>

        <img
          class="x"
          src="../../assets/image/table-x.png"
          alt=""
          @click="close"
        />
      </div>
      <div class="content1">
          <div class="value">
            <div class="warningMessage">
         
            </div>
            <div class="table">
              <div class="table_content">
                <div class="table_contentTitleContentValue">
                  <div class="table_contentTitle">报警时间：</div>
                  <div class="table_contentValue">
                    {{ warnlist[warnid].time }}
                  </div>
                </div>
                <div class="tableContentLine"></div>
                <div class="table_contentTitleContentValue">
                  <div class="table_contentTitle">报警类型：</div>
                  <div class="table_contentValue">
                    {{ warnlist[warnid].type }}
                  </div>
                </div>
                <div class="tableContentLine"></div>
                <div class="table_contentTitleContentValue">
                  <div class="table_contentTitle">报警级别：</div>
                  <div class="table_contentValue">
                    {{ warnlist[warnid].level }}
                  </div>
                </div>
                <div class="tableContentLine"></div>
                <div class="table_contentTitleContentValue">
                  <div class="table_contentTitle">楼栋：</div>
                  <div class="table_contentValue">
                    {{ warnlist[warnid].locallou }}
                  </div>
                </div>
                <div class="tableContentLine"></div>
                <div class="table_contentTitleContentValue">
                  <div class="table_contentTitle">楼层：</div>
                  <div class="table_contentValue">
                    {{ warnlist[warnid].localceng }}
                  </div>
                </div>
                <div class="tableContentLine"></div>
              </div>

              <div class="table_btn" @click="sendToUE41(warnlist[warnid].type + '查看详情')">查看详情</div>
            </div>
          </div>
          <div class="viode" v-if="warnlist[warnid].value">
            <video :src="warnlist[warnid].video" autoplay class="video" muted></video>
    
          </div>
        </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      isshow: true,
      
      warnid: 0,
      showdata11: false,
      showdata1: false,
      jhdata: '',
      title,
      time: "",
      showdata: 0,
      fullscreen: false,
      button1Enabled: true,
      floor0: [{ num: "1F" }, { num: "-1F" }, { num: "-2F" }],
      floor1: [{ num: "1F" }, { num: "2F" }, { num: "3F" }, { num: "4F" }],
      floor2: [
        { num: "1F" },
        { num: "2F" },
        { num: "3F" },
        { num: "4F" },
        { num: "5F" },
        { num: "6F" },
        { num: "7F" },
        { num: "8F" },
        { num: "9F" },
        { num: "10F" },
        { num: "11F" },
        { num: "12F" },
        { num: "13F" },
      ],
      floor3: [
        { num: "1F" },
        { num: "2F" },
        { num: "3F" },
        { num: "4F" },
        { num: "2F" },
      ],
      warnlist: [
        {
          type: "监控报警",
          locallou: "7号楼",
          localceng: "9F",
          level: "一级",
          time: "2023-06-02 09:02:25",
          video: require("../../assets/video/1.mp4"),
          value: true,
          // video1: require("../assets/video/2.mp4"),
          // video2: require("../assets/video/3.mp4"),
          // video3: require("../assets/video/2.mp4"),
        },
        // {
        //   type: "电梯报警",
        //   locallou: "1号楼",
        //   localceng: "1F",
        //   level: "一级",
        //   time: "2023-06-03 08:22:29",
        //   video: require("../assets/video/1.mp4"),
        //   value: true,
        //   video1: require("../assets/video/2.mp4"),
        //   video2: require("../assets/video/3.mp4"),
        //   video3: require("../assets/video/2.mp4"),
        //   value: true,
        // },
        // {
        //   type: "婴儿报警",
        //   locallou: "1号楼",
        //   localceng: "1F",
        //   level: "一级",
        //   time: "2023-06-12 12:25:16",
        //   video: require("../assets/video/1.mp4"),
        //   value: true,
        //   video1: require("../assets/video/2.mp4"),
        //   video2: require("../assets/video/3.mp4"),
        //   video3: require("../assets/video/4.mp4"),
        // },
        // {
        //   type: "设备报警",
        //   locallou: "1号楼",
        //   localceng: "1F",
        //   level: "一级",
        //   time: "2023-06-05 11:52:26",
        //   video: require("../assets/video/1.mp4"),
        //   value: true,
        //   video1: require("../assets/video/2.mp4"),
        //   video2: require("../assets/video/3.mp4"),
        //   video3: require("../assets/video/2.mp4"),
        // },
        // {
        //   type: "门禁告警",
        //   locallou: "1号楼",
        //   localceng: "楼顶",
        //   level: "一级",
        //   time: "2023-06-05 11:52:26",
        //   video: require("../assets/video/1.mp4"),
        //   value: true,
        //   video1: require("../assets/video/2.mp4"),
        //   video2: require("../assets/video/3.mp4"),
        //   video3: require("../assets/video/4.mp4"),

        // },
      ],
      lastClickedTitle: '',
    
    };
  },
  methods: {
    close() {
      console.log("guanbi");
      this.$emit("close");
      // this.isshow = false
    },
  },
};
</script>

<style lang="less" scoped>
.table-home {

  .box {
    color: #fff;
 
    // background: url("../../assets/image/table-beijing.png");
    background-color:rgba( 12, 24, 40, 0.8);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 100%;
    height: 100%;

    padding: 34px;
    padding-bottom: 44px;


    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-right: 28px;
      margin-left: 42px;

      .img {
        font-family: Source Han Sans SC;
        font-weight: 400;
        font-size: 18px;
        color: #d0d7de;
        line-height: 40px;
      }

      .x {
        cursor: pointer;
        width: 20px;
        height: 20px;
      }
    }

    .title2 {
      margin-left: 41px;
      display: flex;
      align-items: center;

      .x {
        width: 33px;
        height: 33px;
      }

      .img {
        margin-left: 11px;
        font-family: Alibaba PuHuiTi;
        font-weight: bold;
        font-size: 25px;
        color: #ffffff;
      }
    }

    .title3 {
      display: flex;
      margin-top: 25px;
      justify-content: space-between;
      margin-left: 42px;
      margin-right: 23px;

      .zonghe {
        display: flex;
        justify-content: space-between;
        width: 613px;
        height: 16px;

        font-family: Source Han Sans SC;
        font-weight: 400;
        font-size: 16px;
        color: #b6c9db;
        line-height: 40px;
      }

      .anniubeijing {
        background: url("../../assets/image/anniubeijing.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;
        width: 106px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .henxian {
        width: 1317px;
        height: 12px;
        background: url("../../assets/image/shganhaihenxian.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
    }

    .content {
      text-align: left;
      padding: 20px 50px 42px 50px;
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      font-size: 21px;
      color: #ffffff;

      p {
        text-indent: 2em;
        line-height: 1.5;
      }
    }

    .footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0px 50px 0px 50px;
      font-family: Source Han Sans SC;
      font-weight: 400;
      font-size: 16px;
      color: #b6c9db;

      .left {
        .shang {
          display: flex;
          margin-bottom: 10px;
        }

        .xia {
          display: flex;
        }
      }

      .right {
      }
    }
  }
}


.content1 {
  height: 308px;
  // background-color: red;
  margin: 16px 16px 0px 32px;
  display: flex;
  justify-content: center;

  align-items: center;

  .value {
    width: 227px;
    height: 100%;

    //   border: 1px solid #ccc;
    .table {
      height: 274px;
      width: 100%;

      // border: 1px solid #ccc;
      .table_content {
        height: 245px;
        width: 100%;

        .table_contentTitleContentValue {
          // text-align: center;
          line-height: 40px;
          height: 44px;

          .table_contentTitle {
            display: inline-block;
            width: 78px;
            font-size: 14px;
            font-family: Source Han Sans SC;
            font-weight: 500;
            color: #c3ced4;
          }

          .table_contentValue {
            padding-left: 5px;
            display: inline-block;
            font-size: 14px;
            font-family: Source Han Sans SC;
            font-weight: 500;
            color: #ffffff;
          }
        }

        .tableContentLine {
          width: 100%;

          border: 1px solid;

          border-image: linear-gradient(to right,
              transparent,
              #315c90,
              transparent) 1;
        }
      }

      .table_btn {
        width: 90px;
        height: 26px;
        border: 1px solid #03c7fb;
        margin: 0 auto;
        text-align: center;
        line-height: 26px;
        font-size: 14px;
        font-family: Source Han Sans SC;
        font-weight: 500;
        color: #00c6ff;
        cursor: pointer;
      }
    }

    .warningMessage {
      font-size: 13px;
      font-family: Source Han Sans SC;
      font-weight: 400;
      color: #fab40c;
    }
  }

  .viode {
    background-color: #3c3f46;
    width: 514px;
    // height: 308px;

    .video {
      width: 100%;
      height:100%;
    }
  }
}

</style>
