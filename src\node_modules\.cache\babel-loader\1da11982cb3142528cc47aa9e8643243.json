{"remainingRequest": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\babel-loader\\lib\\index.js!E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\src\\views\\nenghao.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\src\\views\\nenghao.vue", "mtime": 1751445995813}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\babel.config.js", "mtime": 1726672621424}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["XLSX", "Electricity", "biao1s", "biao1ss", "Titles", "Electricity2", "Electricity3", "Electricity4", "Electricity5", "Electricity6", "Electricity7", "Electricity8", "huanxing", "axios", "buildingEnergyDataList", "components", "data", "isshow", "pickerOptions", "shortcuts", "text", "onClick", "picker", "end", "Date", "start", "setTime", "getTime", "$emit", "options", "value", "label", "selectvalue2", "options2", "selectvalue3", "options3", "selectvalue1", "options1", "options4", "selectvalue4", "optionData", "name", "itemStyle", "color", "opacity", "optionData1", "token", "systemnum", "tokenvalue", "expiretime", "baseURL", "isTokenValid", "totalConsumption", "daily", "weekly", "monthly", "total", "curBuilding", "totalElectricityFee", "roomtag", "electricityFees", "yesterday", "yearly", "meterReadings", "electricityUsageData", "dialogVisible", "date<PERSON><PERSON><PERSON>", "detailData", "electricityFeeData", "methods", "anniu", "buildParams", "type", "formatDate", "date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "now", "today", "param", "buildingId", "deviceType", "displayType", "from", "to", "setDate", "weekAgo", "monthAgo", "tenYearsAgo", "setFullYear", "getEnergyDataByType", "res", "datas", "reduce", "sum", "d", "parseInt", "totalVal", "error", "console", "getToken", "response", "get", "params", "<PERSON><PERSON><PERSON>", "resultvalue", "tokenData", "timestamp", "localStorage", "setItem", "JSON", "stringify", "log", "errmsg", "checkTokenValidity", "storedToken", "getItem", "parse", "expireTime", "currentTime", "tokenTimestamp", "getElectricityUsage", "startTime", "endTime", "post", "starttime", "endtime", "item", "parseFloat", "ylvalue", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "getPaymentStatistics", "totalFee", "zdf", "updateConsumptionData", "id", "updateConsumptionDataFallback", "updateFeeData", "monthStart", "yearStart", "getMeterReadings", "sevenDaysAgo", "tag", "nodeid", "getElectricityUsageData", "map", "sort", "a", "b", "readtime", "showDetailDialog", "split", "searchData", "length", "$message", "warning", "zhaddress", "startcode", "toFixed", "endcode", "jfmx", "replace", "success", "message", "exportToExcel", "exportData", "房间标识", "住户地址", "起码", "止码", "缴费明细", "抄表时间", "ws", "utils", "json_to_sheet", "wb", "book_new", "book_append_sheet", "wch", "fileName", "writeFile", "getElectricityFeeData", "created", "setInterval"], "sources": ["E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\src\\views\\nenghao.vue"], "sourcesContent": ["<template>\n  <div class=\"contents\" v-if=\"isshow\">\n    <div class=\"toubu\">\n      <div\n        style=\"margin-left: 20px; display: flex; align-items: center\"\n        v-if=\"false\"\n      >\n        <div style=\"display: flex; width: 100%; align-items: center\">\n          <span class=\"sp\">当前位置：</span>\n          <el-select\n            class=\"el-select\"\n            v-model=\"selectvalue1\"\n            placeholder=\"selectvalue1\"\n            style=\"width: 64px; height: 35.1px\"\n            @change=\"handleChange\"\n          >\n            <el-option\n              v-for=\"item in options1\"\n              :key=\"item.value\"\n              :label=\"item.label\"\n              :value=\"item.value\"\n            />\n          </el-select>\n          <el-select\n            class=\"el-select\"\n            v-model=\"selectvalue2\"\n            placeholder=\"selectvalue2\"\n            style=\"width: 64px; height: 35.1px\"\n            @change=\"handleChange\"\n          >\n            <el-option\n              v-for=\"item in options2\"\n              :key=\"item.value\"\n              :label=\"item.label\"\n              :value=\"item.value\"\n            />\n          </el-select>\n          <el-select\n            class=\"el-select\"\n            v-model=\"selectvalue3\"\n            placeholder=\"selectvalue3\"\n            style=\"width: 78px; height: 35.1px\"\n            @change=\"handleChange\"\n          >\n            <el-option\n              v-for=\"item in options3\"\n              :key=\"item.value\"\n              :label=\"item.label\"\n              :value=\"item.value\"\n            />\n          </el-select>\n        </div>\n        <img\n          v-if=\"isshow\"\n          class=\"img1sss\"\n          @click=\"anniu()\"\n          src=\"../assets/image/table-x.png\"\n          alt=\"\"\n        />\n      </div>\n\n      <div class=\"all\">\n        <div class=\"all1\">\n          <Titles class=\"ltitle\" tit=\"大型仪器平台概况\">\n            <div class=\"nenghao\">累计总能耗:</div>\n            <p class=\"nhp\">{{ totalConsumption.total.toFixed(1) }} kwh</p>\n            <div class=\"nh\">\n              <img class=\"nhimg\" src=\"../assets/image/nenghao1.png\" alt=\"\" />\n              <div class=\"nhtit\">\n                <p class=\"p11\">{{ totalConsumption.daily.toFixed(1) }}kwh</p>\n                <p class=\"p2\">本日累计能耗</p>\n              </div>\n              <!-- <div class=\"nhtit1\">\n                <img class=\"nhimg1\" src=\"../assets/image/nhxia.png\" alt=\"\" />\n                <p class=\"pp1\">{{ ((totalConsumption.daily - totalConsumption.daily) / totalConsumption.daily * 100).toFixed(1) }}%</p>\n              </div> -->\n            </div>\n            <div class=\"nh\">\n              <img class=\"nhimg\" src=\"../assets/image/nenghao1.png\" alt=\"\" />\n              <div class=\"nhtit\">\n                <p class=\"p11\">{{ totalConsumption.weekly.toFixed(1) }}kwh</p>  \n                <p class=\"p2\">近7日累计能耗</p>\n              </div>\n              <!-- <div class=\"nhtit1\">\n                <img class=\"nhimg1\" src=\"../assets/image/nhxia.png\" alt=\"\" />\n                <p class=\"pp1\">{{ ((totalConsumption.weekly - totalConsumption.weekly) / totalConsumption.weekly * 100).toFixed(1) }}%</p>\n              </div> -->\n            </div>\n            <div class=\"nh\">\n              <img class=\"nhimg\" src=\"../assets/image/nenghao2.png\" alt=\"\" />\n              <div class=\"nhtit\">\n                <p class=\"p12\">{{ totalConsumption.monthly.toFixed(1) }}kwh</p>\n                <p class=\"p2\">近30日累计能耗</p>\n              </div>\n              <div class=\"nht\">\n                <!-- <div class=\"nhtit1\">\n                  <img\n                    class=\"nhimg1\"\n                    src=\"../assets/image/nhshang.png\"\n                    alt=\"\"\n                  />\n                  <p class=\"pp2\">{{ ((totalConsumption.monthly - totalConsumption.monthly) / totalConsumption.monthly * 100).toFixed(1) }}%</p>\n                </div> -->\n                <!-- <p class=\"pp\">环比</p> -->\n              </div>\n            </div>\n          </Titles>\n          <!-- <Titles class=\"ltitle\" style=\"margin-top: 20px\" tit=\"电耗费用\">\n            <div class=\"shinei\">\n              <Electricity2\n                :yesterday-fee=\"electricityFees.yesterday\"\n                :monthly-fee=\"electricityFees.monthly\"\n                :yearly-fee=\"electricityFees.yearly\"\n              ></Electricity2>\n            </div>\n          </Titles> -->\n        </div>\n        <!-- <div class=\"line1\"></div> -->\n        <div class=\"all2\">\n          <!-- <Titles class=\"ltitle1\" tit=\"峰平谷用电量\">\n            <div class=\"shinei\">\n              <Electricity8></Electricity8>\n            </div>\n          </Titles> -->\n\n          <Titles class=\"ltitle1\" tit=\"用电量排名\">\n            <div class=\"shinei\">\n              <Electricity3\n                :electricity-data=\"electricityUsageData\"\n              ></Electricity3>\n            </div>\n          </Titles>\n          <!-- <Titles class=\"ltitle1\" tit=\"分区用电量\">\n            <div class=\"shinei\">\n              <Electricity4 :fee-data=\"electricityFeeData\"></Electricity4>\n            </div>\n          </Titles> -->\n        </div>\n        <div class=\"all3\">\n          <div>\n            <Titles class=\"ltitle1\" tit=\"抄电表记录\">\n              <div class=\"shinei\">\n                <div class=\"table-container\">\n                  <el-table\n                    :data=\"meterReadings\"\n                    style=\"width: 100%; background: transparent\"\n                    :header-cell-style=\"{\n                      background: '#2A363F',\n                      color: '#fff',\n                      borderColor: '#1e415c',\n                    }\"\n                    :cell-style=\"{\n                      background: '#2A363F',\n                      color: '#fff',\n                      borderColor: '#1e415c',\n                    }\"\n                    height=\"320\"\n                  >\n                    <el-table-column\n                      prop=\"roomtag\"\n                      label=\"房间标识\"\n                      align=\"center\"\n                      width=\"120\"\n                    />\n                    <el-table-column\n                      prop=\"zhaddress\"\n                      label=\"住户地址\"\n                      align=\"center\"\n                      width=\"180\"\n                    />\n                    <el-table-column\n                      prop=\"readvalue\"\n                      label=\"抄表值\"\n                      align=\"center\"\n                      width=\"120\"\n                    />\n                    <el-table-column\n                      prop=\"readtime\"\n                      label=\"抄表时间\"\n                      align=\"center\"\n                      width=\"180\"\n                    />\n                  </el-table>\n                </div>\n              </div>\n            </Titles>\n          </div>\n          <Titles class=\"ltitle1\" style=\"margin-top:15px\" tit=\"用电量记录\">\n            <div class=\"shinei\">\n              <div class=\"title-container\">\n                <div class=\"more-btn\" @click=\"showDetailDialog\">\n                  <span>更多</span>\n                  <i class=\"el-icon-arrow-right\"></i>\n                </div>\n              </div>\n              <div class=\"table-container\">\n                <el-table\n                  :data=\"electricityUsageData\"\n                  style=\"width: 100%; background: transparent\"\n                  :header-cell-style=\"{\n                    background: '#2A363F',\n                    color: '#fff',\n                    borderColor: '#38444C',\n                  }\"\n                  :cell-style=\"{\n                    background: '#2A363F',\n                    color: '#fff',\n                    borderColor: '#1e415c',\n                  }\"\n                  height=\"400\"\n                >\n                  <el-table-column\n                    prop=\"roomtag\"\n                    label=\"房间标识\"\n                    align=\"center\"\n                    width=\"120\"\n                  />\n                  <el-table-column\n                    prop=\"zhaddress\"\n                    label=\"住户地址\"\n                    align=\"center\"\n                    width=\"180\"\n                  />\n                  <el-table-column\n                    prop=\"ylvalue\"\n                    label=\"用电量(kwh)\"\n                    align=\"center\"\n                    width=\"120\"\n                  >\n                    <!-- <template slot-scope=\"scope\">\n                      {{ parseFloat(scope.row.ylvalue).toFixed(2) }}\n                    </template> -->\n                  </el-table-column>\n                  <el-table-column\n                    prop=\"endtime\"\n                    label=\"抄表时间\"\n                    align=\"center\"\n                    width=\"180\"\n                  />\n                </el-table>\n              </div>\n            </div>\n          </Titles>\n        </div>\n      </div>\n    </div>\n\n    <!-- 自定义弹窗 -->\n    <div\n      v-if=\"dialogVisible\"\n      class=\"custom-modal-overlay\"\n      @click.self=\"dialogVisible = false\"\n    >\n      <div class=\"custom-modal\">\n        <div class=\"modal-header\">\n          <span class=\"modal-title\">用电量详细记录</span>\n          <div class=\"header-buttons\">\n            <el-button\n              type=\"text\"\n              class=\"close-text\"\n              @click=\"dialogVisible = false\"\n              >关闭</el-button\n            >\n            <i\n              class=\"el-icon-close close-btn\"\n              @click=\"dialogVisible = false\"\n            ></i>\n          </div>\n        </div>\n        <div class=\"modal-content\">\n          <!-- 搜索条件 -->\n          <div class=\"search-container\">\n            <el-date-picker\n              v-model=\"dateRange\"\n              type=\"daterange\"\n              range-separator=\"至\"\n              start-placeholder=\"开始日期\"\n              end-placeholder=\"结束日期\"\n              value-format=\"YYYY-MM-DD\"\n              style=\"width: 380px; margin-right: 15px\"\n            >\n            </el-date-picker>\n            <!-- <el-date-picker\n                v-model=\"dateRange\"\n                type=\"daterange\"\n                align=\"right\"\n                unlink-panels\n                range-separator=\"至\"\n                start-placeholder=\"开始日期\"\n                end-placeholder=\"结束日期\"\n                :picker-options=\"pickerOptions\"\n              > -->\n            <!-- </el-date-picker> -->\n\n            <el-button type=\"primary\" @click=\"searchData\">查询</el-button>\n            <!-- <el-button type=\"success\" @click=\"exportToExcel\">导出</el-button> -->\n          </div>\n\n          <!-- 详细数据表格 -->\n          <el-table\n            :data=\"detailData\"\n            style=\"width: 100%; margin-top: 20px\"\n            :header-cell-style=\"{\n              background: '#2C4255',\n              color: '#fff',\n              borderColor: '#1e415c',\n            }\"\n            :cell-style=\"{\n              background: '#2C4255',\n              color: '#fff',\n              borderColor: '#1e415c',\n            }\"\n            height=\"500\"\n          >\n            <el-table-column\n              prop=\"roomtag\"\n              label=\"房间标识\"\n              align=\"center\"\n              width=\"180\"\n            />\n            <el-table-column\n              prop=\"zhaddress\"\n              label=\"住户地址\"\n              align=\"center\"\n              width=\"180\"\n            />\n            <el-table-column\n              prop=\"startcode\"\n              label=\"起码\"\n              align=\"center\"\n              width=\"180\"\n            />\n            <el-table-column\n              prop=\"endcode\"\n              label=\"止码\"\n              align=\"center\"\n              width=\"180\"\n            />\n            <el-table-column\n              prop=\"ylvalue\"\n              label=\"用电量(kwh)\"\n              align=\"center\"\n              width=\"180\"\n            />\n            <el-table-column\n              prop=\"jfmx\"\n              label=\"缴费明细\"\n              align=\"center\"\n              width=\"180\"\n            />\n\n            <el-table-column\n              prop=\"endtime\"\n              label=\"抄表时间\"\n              align=\"center\"\n              width=\"181\"\n            />\n          </el-table>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as XLSX from \"xlsx\";\nimport Electricity from \"../components/echarts/dianbiao/biao1.vue\";\nimport biao1s from \"../components/echarts/dianbiao/biao1s.vue\";\nimport biao1ss from \"../components/echarts/dianbiao/biao1ss.vue\";\nimport Titles from \"../components/common/Titles.vue\";\n\nimport Electricity2 from \"../components/echarts/dianbiao/Electricity2.vue\";\nimport Electricity3 from \"../components/echarts/dianbiao/Electricity3.vue\";\nimport Electricity4 from \"../components/echarts/dianbiao/Electricity4.vue\";\nimport Electricity5 from \"../components/echarts/dianbiao/Electricity5.vue\";\nimport Electricity6 from \"../components/echarts/dianbiao/Electricity6.vue\";\nimport Electricity7 from \"../components/echarts/dianbiao/Electricity7.vue\";\nimport Electricity8 from \"../components/echarts/dianbiao/Electricity8.vue\";\nimport huanxing from \"@/components/echarts/xiaobingtu.vue\";\nimport axios from \"axios\";\nimport { buildingEnergyDataList } from \"@/api/device\";\n\nexport default {\n  components: {\n    Titles,\n    Electricity,\n    Electricity2,\n    Electricity3,\n    Electricity4,\n    Electricity5,\n    Electricity6,\n    Electricity7,\n    Electricity8,\n    huanxing,\n    biao1s,\n    biao1ss,\n  },\n  data() {\n    return {\n      isshow: true,\n      pickerOptions: {\n        shortcuts: [\n          {\n            text: \"最近一周\",\n            onClick(picker) {\n              const end = new Date();\n              const start = new Date();\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);\n              picker.$emit(\"pick\", [start, end]);\n            },\n          },\n          {\n            text: \"最近一个月\",\n            onClick(picker) {\n              const end = new Date();\n              const start = new Date();\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);\n              picker.$emit(\"pick\", [start, end]);\n            },\n          },\n          {\n            text: \"最近三个月\",\n            onClick(picker) {\n              const end = new Date();\n              const start = new Date();\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);\n              picker.$emit(\"pick\", [start, end]);\n            },\n          },\n        ],\n      },\n      options: [\n        {\n          value: \"总览\",\n          label: \"总览\",\n        },\n        {\n          value: \"能耗分析\",\n          label: \"能耗分析\",\n        },\n        {\n          value: \"能流分析\",\n          label: \"能流分析\",\n        },\n        {\n          value: \"设备状态\",\n          label: \"设备状态\",\n        },\n        {\n          value: \"一键抄表\",\n          label: \"一键抄表\",\n        },\n        {\n          value: \"费用管理\",\n          label: \"费用管理\",\n        },\n        {\n          value: \"碳排放管理\",\n          label: \"碳排放管理\",\n        },\n      ],\n      selectvalue2: \"B3\",\n      options2: [\n        {\n          value: \"B3\",\n          label: \"B3\",\n        },\n      ],\n      selectvalue3: \"B3\",\n      options3: [\n        {\n          value: \"B3\",\n          label: \"B3\",\n        },\n      ],\n      selectvalue1: \"B3\",\n      options1: [\n        {\n          value: \"B3\",\n          label: \"B3\",\n        },\n      ],\n      selectvalue3: \"B1栋\",\n      options4: [\n        {\n          value: \"B1栋\",\n          label: \"B1栋\",\n        },\n        {\n          value: \"B2栋\",\n          label: \"B2栋\",\n        },\n        {\n          value: \"B3栋\",\n          label: \"B3栋\",\n        },\n        {\n          value: \"B4栋\",\n          label: \"B4栋\",\n        },\n        {\n          value: \"W1栋\",\n          label: \"W1栋\",\n        },\n        {\n          value: \"W2栋\",\n          label: \"W2栋\",\n        },\n      ],\n      selectvalue4: \"B1栋\",\n      optionData: [\n        {\n          name: \"一级告警\",\n          value: 16,\n          itemStyle: { color: \"#EB6877\", opacity: 0.9 },\n        },\n        {\n          name: \"二级告警\",\n          value: 27,\n          itemStyle: { color: \"#F8B551\", opacity: 0.9 },\n        },\n        {\n          name: \"三级告警\",\n          value: 17,\n          itemStyle: { color: \"#B954E8\", opacity: 0.9 },\n        },\n        {\n          name: \"四级告警\",\n          value: 40,\n          itemStyle: { color: \"#0284F0\", opacity: 0.9 },\n        },\n      ],\n      optionData1: [\n        {\n          name: \"一级告警\",\n          value: 16,\n          itemStyle: { color: \"#EB6877\", opacity: 0.9 },\n        },\n        {\n          name: \"二级告警\",\n          value: 27,\n          itemStyle: { color: \"#F8B551\", opacity: 0.9 },\n        },\n        {\n          name: \"三级告警\",\n          value: 17,\n          itemStyle: { color: \"#B954E8\", opacity: 0.9 },\n        },\n        {\n          name: \"四级告警\",\n          value: 40,\n          itemStyle: { color: \"#0284F0\", opacity: 0.9 },\n        },\n      ],\n      token: {\n        systemnum: \"\",\n        tokenvalue: \"\",\n        expiretime: \"\",\n      },\n      baseURL: \"/power\", // Replace with actual server address\n      // baseURL: 'http://*************:8080',\n      isTokenValid: false,\n      totalConsumption: {\n        daily: 0,\n        weekly: 0,\n        monthly: 0,\n        total: 0, // 累计用量\n      },\n      curBuilding: null, // 当前建筑信息\n      totalElectricityFee: 0,\n      roomtag: \"\", // Add your roomtag here if you want to query specific user\n      electricityFees: {\n        yesterday: 0,\n        monthly: 0,\n        yearly: 0,\n      },\n      meterReadings: [], // 新增抄表记录数据\n      electricityUsageData: [], // 新增用电量数据\n      \n      dialogVisible: false, // 修改为 false，默认关闭\n      dateRange: \"\",\n      detailData: [],\n      electricityFeeData: [], // 新增电费数据\n    };\n  },\n  methods: {\n    anniu() {\n      this.isshow = false;\n    },\n\n    // 构建API参数 - 参考 BimEnergyOverview.vue 的实现\n    buildParams(type) {\n      // 格式化日期为 YYYY-MM-DD 格式\n      const formatDate = (date) => {\n        const year = date.getFullYear();\n        const month = String(date.getMonth() + 1).padStart(2, '0');\n        const day = String(date.getDate()).padStart(2, '0');\n        return `${year}-${month}-${day}`;\n      };\n\n      const now = new Date();\n      const today = formatDate(now);\n\n      const param = {\n        buildingId: 1, // 直接写死为1\n        deviceType: 'electricity',\n        type: 'electricity',\n        displayType: \"day\",\n        from: today,\n        to: today,\n      };\n\n      // 根据类型调整参数\n      switch (type) {\n        case 'daily':\n          // 本日用量：昨天到今天\n          param.displayType = 'day';\n          const yesterday = new Date(now);\n          yesterday.setDate(yesterday.getDate() - 1);\n          param.from = formatDate(yesterday);\n          param.to = today;\n          break;\n        case 'weekly':\n          // 近7日用量：7天前到今天\n          param.displayType = 'day';\n          const weekAgo = new Date(now);\n          weekAgo.setDate(weekAgo.getDate() - 7);\n          param.from = formatDate(weekAgo);\n          param.to = today;\n          break;\n        case 'monthly':\n          // 近30日用量：30天前到今天\n          param.displayType = 'day';\n          const monthAgo = new Date(now);\n          monthAgo.setDate(monthAgo.getDate() - 30);\n          param.from = formatDate(monthAgo);\n          param.to = today;\n          break;\n        case 'total':\n          // 累计用量：从很久以前到今天\n          param.displayType = 'total';\n          const tenYearsAgo = new Date(now);\n          tenYearsAgo.setFullYear(tenYearsAgo.getFullYear() - 10);\n          param.from = formatDate(tenYearsAgo);\n          param.to = today;\n          break;\n      }\n\n      return param;\n    },\n\n    // 使用新API获取能耗数据\n    async getEnergyDataByType(type) {\n      const param = this.buildParams(type);\n      if (!param) {\n        return 0;\n      }\n\n      try {\n        const res = await buildingEnergyDataList(param);\n        let total = 0;\n        if (res.data && res.data.datas) {\n          total = res.data.datas.reduce((sum, d) => sum + parseInt(d.totalVal || 0), 0);\n        }\n        return total;\n      } catch (error) {\n        console.error(`获取${type}数据失败:`, error);\n        return 0;\n      }\n    },\n    async getToken() {\n      try {\n        const response = await axios.get(\n          `${this.baseURL}/api/ztwyPower/getToken`,\n          {\n            params: {\n              systemnum: \"346E473FD1EF46E3A2EE43F393BCAF7C\",\n            },\n          }\n        );\n\n        if (response.data.errcode === 0) {\n          const { systemnum, tokenvalue, expiretime } =\n            response.data.resultvalue;\n          this.token = {\n            systemnum,\n            tokenvalue,\n            expiretime,\n          };\n          this.isTokenValid = true;\n\n          // 存储 token 和获取时间\n          const tokenData = {\n            ...this.token,\n            timestamp: new Date().getTime(),\n          };\n          localStorage.setItem(\"powerToken\", JSON.stringify(tokenData));\n\n          console.log(\"Token updated successfully:\", this.token);\n        } else {\n          console.error(\"Failed to get token:\", response.data.errmsg);\n          this.isTokenValid = false;\n        }\n      } catch (error) {\n        console.error(\"Error getting token:\", error);\n        this.isTokenValid = false;\n      }\n    },\n    checkTokenValidity() {\n      const storedToken = localStorage.getItem(\"powerToken\");\n      if (storedToken) {\n        const tokenData = JSON.parse(storedToken);\n        const expireTime = new Date(tokenData.expiretime).getTime();\n        const currentTime = new Date().getTime();\n        const tokenTimestamp = tokenData.timestamp;\n\n        // 检查 token 是否过期或距离上次获取是否超过5分钟\n        if (\n          currentTime < expireTime &&\n          currentTime - tokenTimestamp < 5 * 60 * 1000\n        ) {\n          this.token = {\n            systemnum: tokenData.systemnum,\n            tokenvalue: tokenData.tokenvalue,\n            expiretime: tokenData.expiretime,\n          };\n          this.isTokenValid = true;\n          return true;\n        }\n      }\n      return false;\n    },\n    async getElectricityUsage(startTime, endTime) {\n      if (!this.isTokenValid) {\n        await this.getToken();\n      }\n\n      try {\n        const response = await axios.post(\n          `${this.baseURL}/api/ztwyPower/getYdlByTime`,\n          {\n            tokenvalue: this.token.tokenvalue,\n            starttime: startTime,\n            endtime: endTime,\n            roomtag: this.roomtag, // Optional: if empty, will return all users' data\n          }\n        );\n\n        if (response.data.errcode === 0) {\n          // Calculate total consumption by summing ylvalue\n          const totalConsumption = response.data.resultvalue.reduce(\n            (sum, item) => {\n              return sum + parseFloat(item.ylvalue || 0);\n            },\n            0\n          );\n          return totalConsumption;\n        } else {\n          console.error(\n            \"Failed to get electricity usage:\",\n            response.data.errmsg\n          );\n          return 0;\n        }\n      } catch (error) {\n        console.error(\"Error getting electricity usage:\", error);\n        return 0;\n      }\n    },\n    formatDate(date) {\n      const year = date.getFullYear();\n      const month = String(date.getMonth() + 1).padStart(2, \"0\");\n      const day = String(date.getDate()).padStart(2, \"0\");\n      const hours = String(date.getHours()).padStart(2, \"0\");\n      const minutes = String(date.getMinutes()).padStart(2, \"0\");\n      const seconds = String(date.getSeconds()).padStart(2, \"0\");\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\n    },\n    async getPaymentStatistics(startTime, endTime) {\n      if (!this.isTokenValid) {\n        await this.getToken();\n      }\n\n      try {\n        const response = await axios.post(\n          `${this.baseURL}/api/ztwyPower/getJfAllInfos`,\n          {\n            tokenvalue: this.token.tokenvalue,\n            starttime: startTime,\n            endtime: endTime,\n            roomtag: this.roomtag,\n          }\n        );\n\n        if (response.data.errcode === 0) {\n          const totalFee = response.data.resultvalue.reduce((sum, item) => {\n            return sum + parseFloat(item.zdf || 0);\n          }, 0);\n          return totalFee;\n        } else {\n          console.error(\n            \"Failed to get payment statistics:\",\n            response.data.errmsg\n          );\n          return 0;\n        }\n      } catch (error) {\n        console.error(\"Error getting payment statistics:\", error);\n        return 0;\n      }\n    },\n    async updateConsumptionData() {\n      console.log(5685);\n      \n      // 检查 curBuilding 是否已初始化\n      if (!this.curBuilding || !this.curBuilding.id) {\n        console.error('curBuilding 未初始化，无法获取能耗数据');\n        return;\n      }\n\n      try {\n        // 使用新的API获取四个数据项\n        // 累计用量\n        this.totalConsumption.total = await this.getEnergyDataByType('total');\n\n        // 本日用量\n        this.totalConsumption.daily = await this.getEnergyDataByType('daily');\n\n        // 近7日用量\n        this.totalConsumption.weekly = await this.getEnergyDataByType('weekly');\n\n        // 近30日用量\n        this.totalConsumption.monthly = await this.getEnergyDataByType('monthly');\n\n        console.log('能耗数据更新成功:', this.totalConsumption);\n      } catch (error) {\n        console.error('更新能耗数据失败:', error);\n        // 如果新API失败，回退到原来的方法\n        await this.updateConsumptionDataFallback();\n      }\n    },\n\n    // 原来的数据获取方法作为备用\n    async updateConsumptionDataFallback() {\n      const now = new Date();\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n      const yesterday = new Date(today);\n      yesterday.setDate(yesterday.getDate() - 1);\n      const weekAgo = new Date(today);\n      weekAgo.setDate(weekAgo.getDate() - 7);\n      const monthAgo = new Date(today);\n      monthAgo.setDate(monthAgo.getDate() - 30);\n\n      // Get daily consumption\n      this.totalConsumption.daily = await this.getElectricityUsage(\n        this.formatDate(yesterday),\n        this.formatDate(now)\n      );\n\n      // Get weekly consumption\n      this.totalConsumption.weekly = await this.getElectricityUsage(\n        this.formatDate(weekAgo),\n        this.formatDate(now)\n      );\n\n      // Get monthly consumption\n      this.totalConsumption.monthly = await this.getElectricityUsage(\n        this.formatDate(monthAgo),\n        this.formatDate(now)\n      );\n\n      // 累计用量使用近30日数据作为近似值\n      this.totalConsumption.total = this.totalConsumption.monthly;\n\n      // Get total electricity fee\n      this.totalElectricityFee = await this.getPaymentStatistics(\n        this.formatDate(monthAgo),\n        this.formatDate(now)\n      );\n    },\n    async updateFeeData() {\n      const now = new Date();\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n      const yesterday = new Date(today);\n      yesterday.setDate(yesterday.getDate() - 1);\n      const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);\n      const yearStart = new Date(today.getFullYear(), 0, 1);\n\n      // Get yesterday's fee\n      this.electricityFees.yesterday = await this.getPaymentStatistics(\n        this.formatDate(yesterday),\n        this.formatDate(today)\n      );\n\n      // Get monthly fee\n      this.electricityFees.monthly = await this.getPaymentStatistics(\n        this.formatDate(monthStart),\n        this.formatDate(now)\n      );\n\n      // Get yearly fee\n      this.electricityFees.yearly = await this.getPaymentStatistics(\n        this.formatDate(yearStart),\n        this.formatDate(now)\n      );\n    },\n    async getMeterReadings() {\n      if (!this.isTokenValid) {\n        await this.getToken();\n      }\n\n      try {\n        const now = new Date();\n        const sevenDaysAgo = new Date(now);\n        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);\n\n        const response = await axios.post(\n          `${this.baseURL}/api/ztwyPower/getAllDbValue`,\n          {\n            tokenvalue: this.token.tokenvalue,\n            starttime: this.formatDate(sevenDaysAgo),\n            endtime: this.formatDate(now),\n            tag: 0, // 返回最后一次抄表记录\n            nodeid: 0, // 默认为0，返回全部\n            roomtag: this.roomtag,\n          }\n        );\n\n        if (response.data.errcode === 0) {\n          this.meterReadings = response.data.resultvalue;\n          console.log(\n            \"Meter readings retrieved successfully:\",\n            this.meterReadings\n          );\n        } else {\n          console.error(\"Failed to get meter readings:\", response.data.errmsg);\n        }\n      } catch (error) {\n        console.error(\"Error getting meter readings:\", error);\n      }\n    },\n    async getElectricityUsageData() {\n      if (!this.isTokenValid) {\n        await this.getToken();\n      }\n\n      try {\n        const now = new Date();\n        const sevenDaysAgo = new Date(now);\n        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);\n\n        const response = await axios.post(\n          `${this.baseURL}/api/ztwyPower/getYdlByTime`,\n          {\n            tokenvalue: this.token.tokenvalue,\n            starttime: this.formatDate(sevenDaysAgo),\n            endtime: this.formatDate(now),\n            roomtag: this.roomtag,\n          }\n        );\n\n        if (response.data.errcode === 0) {\n          this.electricityUsageData = response.data.resultvalue\n            .map((item) => ({\n              ...item,\n              ylvalue: parseFloat(item.ylvalue || 0),\n            }))\n            .sort((a, b) => new Date(a.readtime) - new Date(b.readtime));\n          console.log(\n            \"Electricity usage data retrieved successfully:\",\n            this.electricityUsageData\n          );\n        } else {\n          console.error(\n            \"Failed to get electricity usage:\",\n            response.data.errmsg\n          );\n        }\n      } catch (error) {\n        console.error(\"Error getting electricity usage:\", error);\n      }\n    },\n    showDetailDialog() {\n      this.dialogVisible = true;\n      this.detailData = []; // 清空数据\n      // 默认显示最近一天的数据\n      const end = new Date();\n      const start = new Date();\n      start.setTime(start.getTime() - 3600 * 1000 * 24); // 最近一天\n      this.dateRange = [\n        this.formatDate(start).split(\" \")[0],\n        this.formatDate(end).split(\" \")[0],\n      ];\n      this.searchData(); // 自动查询最近一天的数据\n    },\n    async searchData() {\n      if (!this.dateRange || this.dateRange.length !== 2) {\n        this.$message.warning(\"请选择日期范围\");\n        return;\n      }\n\n      try {\n        if (!this.isTokenValid) {\n          await this.getToken();\n        }\n\n        const response = await axios.post(\n          `${this.baseURL}/api/ztwyPower/getYdlByTime`,\n          {\n            tokenvalue: this.token.tokenvalue,\n            starttime: `${this.dateRange[0]} 00:00:00`,\n            endtime: `${this.dateRange[1]} 23:59:59`,\n            roomtag: this.roomtag,\n          }\n        );\n\n        if (response.data.errcode === 0) {\n          if (\n            response.data.resultvalue &&\n            response.data.resultvalue.length > 0\n          ) {\n            this.detailData = response.data.resultvalue\n              .map((item) => ({\n                roomtag: item.roomtag || \"\",\n                zhaddress: item.zhaddress || \"\",\n                startcode: item.startcode\n                  ? parseFloat(item.startcode).toFixed(1)\n                  : \"0.0\",\n                endcode: item.endcode\n                  ? parseFloat(item.endcode).toFixed(1)\n                  : \"0.0\",\n                ylvalue: item.ylvalue\n                  ? parseFloat(item.ylvalue).toFixed(2)\n                  : \"0.00\",\n                jfmx: item.jfmx || \"\",\n                endtime: item.endtime ? item.endtime.replace(/-/g, \"/\") : \"\",\n              }))\n              .sort((a, b) => new Date(b.endtime) - new Date(a.endtime));\n            this.$message.success(\"查询成功\");\n          } else {\n            this.detailData = [];\n            this.$message.warning(\"所选时间范围内无数据\");\n          }\n        } else {\n          this.$message.error(\"获取数据失败：\" + response.data.errmsg);\n          this.detailData = [];\n        }\n      } catch (error) {\n        this.$message.error(\"获取数据失败：\" + (error.message || \"未知错误\"));\n        this.detailData = [];\n      }\n    },\n    exportToExcel() {\n      if (!this.detailData || !this.detailData.length) {\n        this.$message.warning(\"暂无数据可导出\");\n        return;\n      }\n\n      try {\n        // 准备要导出的数据\n        const exportData = this.detailData.map((item) => ({\n          房间标识: item.roomtag || \"\",\n          住户地址: item.zhaddress || \"\",\n          起码: item.startcode ? parseFloat(item.startcode).toFixed(1) : \"0.0\",\n          止码: item.endcode ? parseFloat(item.endcode).toFixed(1) : \"0.0\",\n          \"用电量(kwh)\": item.ylvalue\n            ? parseFloat(item.ylvalue).toFixed(2)\n            : \"0.00\",\n          缴费明细: item.jfmx || \"\",\n          抄表时间: item.endtime ? item.endtime.replace(/-/g, \"/\") : \"\",\n        }));\n\n        // 创建工作簿并设置数据\n        const ws = XLSX.utils.json_to_sheet(exportData);\n        const wb = XLSX.utils.book_new();\n        XLSX.utils.book_append_sheet(wb, ws, \"用电量记录\");\n\n        // 设置列宽\n        ws[\"!cols\"] = [\n          { wch: 15 }, // 房间标识\n          { wch: 20 }, // 住户地址\n          { wch: 12 }, // 起码\n          { wch: 12 }, // 止码\n          { wch: 15 }, // 用电量\n          { wch: 15 }, // 缴费明细\n          { wch: 20 }, // 抄表时间\n        ];\n\n        // 直接使用 XLSX.writeFile 导出文件\n        const fileName = `用电量记录_${this.dateRange[0]}_${this.dateRange[1]}.xlsx`;\n        XLSX.writeFile(wb, fileName);\n\n        this.$message.success(\"导出成功\");\n      } catch (error) {\n        console.error(\"Export error:\", error);\n        this.$message.error(\"导出失败：\" + (error.message || \"未知错误\"));\n      }\n    },\n    async getElectricityFeeData() {\n      if (!this.isTokenValid) {\n        await this.getToken();\n      }\n\n      try {\n        const now = new Date();\n        const sevenDaysAgo = new Date(now);\n        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);\n\n        const response = await axios.post(\n          `${this.baseURL}/api/ztwyPower/getJfAllInfos`,\n          {\n            tokenvalue: this.token.tokenvalue,\n            starttime: this.formatDate(sevenDaysAgo),\n            endtime: this.formatDate(now),\n            roomtag: this.roomtag,\n          }\n        );\n\n        if (response.data.errcode === 0) {\n          this.electricityFeeData = response.data.resultvalue\n            .map((item) => ({\n              ...item,\n              zdf: parseFloat(item.zdf || 0),\n            }))\n            .sort((a, b) => new Date(a.readtime) - new Date(b.readtime));\n          console.log(\n            \"Electricity fee data retrieved successfully:\",\n            this.electricityFeeData\n          );\n        } else {\n          console.error(\n            \"Failed to get electricity fee data:\",\n            response.data.errmsg\n          );\n        }\n      } catch (error) {\n        console.error(\"Error getting electricity fee data:\", error);\n      }\n    },\n  },\n  async created() {\n    // 初始化建筑信息\n    console.log(5685);\n\n    // 直接设置建筑ID为1\n    this.curBuilding = { id: 1 };\n    console.log('curBuilding 已初始化:', this.curBuilding);\n\n    // 初始化获取 token\n    if (!this.checkTokenValidity()) {\n      await this.getToken();\n    }\n\n    // 更新数据\n    await this.updateConsumptionData();\n    await this.updateFeeData();\n    await this.getMeterReadings();\n    await this.getElectricityUsageData();\n    await this.getElectricityFeeData();\n\n    // 每5分钟更新一次 token\n    setInterval(async () => {\n      await this.getToken();\n    }, 5 * 60 * 1000);\n\n    // 每5分钟更新一次数据\n    setInterval(async () => {\n      await this.updateConsumptionData();\n      await this.updateFeeData();\n      await this.getMeterReadings();\n      await this.getElectricityUsageData();\n      await this.getElectricityFeeData();\n    }, 5 * 60 * 1000);\n  },\n};\n</script>\n\n<style lang=\"less\" scoped>\n.all {\n  display: flex;\n  flex-direction: row;\n  margin-top: 5px;\n\n  .zong {\n    display: flex;\n    flex-direction: row;\n    margin-top: 10px;\n    .echart1,\n    .echart2 {\n      flex: 1;\n\n      .center {\n        margin-top: -24px;\n        font-family: \"Source Han Sans SC\", sans-serif;\n        font-weight: 400;\n        font-size: 17px;\n        color: #00ffb6;\n        text-align: center;\n        margin-bottom: 10px;\n      }\n\n      .btn {\n        width: 133px;\n        height: 31px;\n        border: 1px solid #2d6cb0;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-family: \"Source Han Sans SC\", sans-serif;\n        font-weight: bold;\n        font-size: 15px;\n        color: #ffffff;\n        border-radius: 30px;\n        margin-left: 7%;\n      }\n    }\n  }\n\n  .ltitle1 {\n    margin-top: 10px;\n    position: relative;\n  }\n\n  .line1 {\n    width: 2px;\n    height: 823px;\n    opacity: 0.64;\n    background-color: #204964;\n  }\n\n  .all1 {\n    flex: 557;\n\n    .nenghao {\n      width: 257px;\n      height: 183px;\n      background: url(\"../assets/image/nenghao.png\");\n      background-size: 100% 100%;\n      margin-left: 100px;\n      margin-top: 45px;\n      text-align: center;\n      font-family: Alibaba PuHuiTi;\n      font-weight: 400;\n      font-size: 20px;\n      color: #ffffff;\n      line-height: 213px;\n    }\n\n    .nhp {\n      text-align: center;\n      font-family: Alibaba PuHuiTi;\n      font-weight: 500;\n      font-size: 52px;\n      color: #2cc1ff;\n      margin-top: 8px;\n    }\n\n    .nh {\n      margin-left: 24px;\n      margin-top: 32px;\n      width: 423px;\n      height: 105px;\n      border: 1px solid #364d5a;\n      background-size: 100% 100%;\n      display: flex;\n      flex-direction: row;\n      align-items: center;\n      padding-left: 72px;\n      margin-bottom: 5px;\n      // justify-content: space-evenly;\n\n      .nhimg {\n        width: 107px;\n        height: 90px;\n        margin-right: 35px;\n      }\n\n      .nhtit {\n        width: 148px;\n        margin-left: 10px;\n        margin-top: 3px;\n\n        .p11 {\n          font-family: Alibaba PuHuiTi;\n          font-weight: 400;\n          font-size: 30px;\n          color: #7acfff;\n        }\n\n        .p12 {\n          font-family: Alibaba PuHuiTi;\n          font-weight: 400;\n          font-size: 30px;\n          color: #ffa170;\n        }\n\n        .p2 {\n          font-family: Alibaba PuHuiTi;\n          font-weight: 400;\n          font-size: 20px;\n          color: #ffffff;\n        }\n      }\n\n      .nhtit1 {\n        display: flex;\n        flex-direction: row;\n        align-items: center;\n        margin-left: 35px;\n\n        .nhimg1 {\n          width: 16px;\n          height: 20px;\n        }\n\n        .pp1 {\n          font-family: Alibaba PuHuiTi;\n          font-weight: 400;\n          font-size: 17px;\n          color: #0df29b;\n        }\n\n        .pp2 {\n          font-family: Alibaba PuHuiTi;\n          font-weight: 400;\n          font-size: 17px;\n          color: #ffa170;\n        }\n      }\n\n      .nht {\n        margin-top: 10px;\n        display: flex;\n        flex-direction: column;\n\n        .pp {\n          margin-left: 35px;\n          font-family: Alibaba PuHuiTi;\n          font-weight: 400;\n          font-size: 17px;\n\n          color: #cccccc;\n        }\n      }\n    }\n  }\n\n  .all2 {\n    margin-left: -52px;\n    flex: 627;\n    display: flex;\n    flex-direction: column;\n    .shinei {\n      .itemshei {\n        display: flex;\n        justify-content: space-around;\n        .nenghaos {\n          width: 227px;\n          height: 173px;\n          background: url(\"../assets/image/nenghao.png\");\n          background-size: 100% 100%;\n          text-align: center;\n          margin-left: 10px;\n          margin-top: 33px;\n          text-align: center;\n          font-family: Alibaba PuHuiTi;\n          font-weight: 400;\n          font-size: 14px;\n          color: #ffffff;\n          line-height: 144px;\n        }\n        .nhps {\n          text-align: center;\n          font-family: Alibaba PuHuiTi;\n          font-weight: 500;\n          font-size: 21px;\n          color: #2cc1ff;\n          margin-top: 8px;\n        }\n      }\n    }\n  }\n\n  .all3 {\n    flex: 658;\n    margin-left: 15px;\n  }\n}\n\n.shinei {\n  width: 100%;\n  height: 100%;\n}\n.shuantitle {\n  width: 100%;\n  display: flex;\n  margin-top: 10px;\n  .title {\n    width: 95%;\n    background: url(\"../assets/image/title.png\");\n    background-size: 100% 100%;\n\n    height: 25px;\n    font-family: Source Han Sans SC;\n    font-weight: 400;\n    font-size: 25px;\n    color: #ffffff;\n    text-shadow: 2px 2px 3px rgba(0, 0, 0, 0.47);\n    font-style: italic;\n    text-align: left;\n    line-height: 4px;\n    padding-left: 33px;\n  }\n}\n.nenghao {\n  width: 167px;\n  height: 113px;\n  background: url(\"../assets/image/nenghao.png\");\n  background-size: 100% 100%;\n  text-align: center;\n  margin-left: 83px;\n  // margin-top: 63px;\n  text-align: center;\n  font-family: Alibaba PuHuiTi;\n  font-weight: 400;\n  font-size: 14px;\n  color: #ffffff;\n  line-height: 144px;\n}\n.nhp {\n  text-align: center;\n  font-family: Alibaba PuHuiTi;\n  font-weight: 500;\n  font-size: 25px;\n  color: #2cc1ff;\n  margin-top: 8px;\n  width: 79%;\n}\n\n.contents {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  background: url(\"../assets/image/zichanbeijin.png\");\n  width: 1863px;\n  height: 868px;\n  z-index: 99999;\n  padding-left: 34px;\n  padding-right: 22px;\n  padding-top: 21px;\n}\n.toubu {\n  width: 100%;\n\n  position: relative;\n}\n.el-select {\n  margin-top: -1px;\n  margin-left: 10px;\n  background: #00203d;\n  border-radius: 3px;\n  border: 1px solid #3e89db;\n\n  /deep/.el-select__wrapper {\n    background: #00203d !important;\n    box-shadow: none;\n  }\n\n  /deep/.el-select__wrapper .is-hovering:not {\n    box-shadow: none;\n  }\n\n  /deep/.el-select__wrapper:hover {\n    box-shadow: none;\n  }\n\n  /deep/.el-select__placeholder.is-transparent {\n    color: #2cc1ff;\n  }\n\n  /deep/.el-select__placeholder {\n    color: #2cc1ff;\n  }\n\n  /deep/.el-select-dropdown__item.is-hovering {\n    background-color: #2cc1ff !important;\n  }\n}\n.sp {\n  margin-top: -5px;\n  margin-left: 12px;\n  font-family: Alibaba PuHuiTi;\n  font-weight: bold;\n  font-size: 21px;\n  color: #2cc1ff;\n}\n.img1sss {\n  cursor: pointer;\n  width: 15px;\n  height: 15px;\n}\n\n.table-container {\n  cursor: pointer;\n  .el-table {\n    background-color: transparent !important;\n\n    // 设置滚动条样式\n    ::-webkit-scrollbar {\n      width: 6px;\n      height: 6px;\n    }\n\n    ::-webkit-scrollbar-thumb {\n      background: #0a3054;\n      border-radius: 3px;\n    }\n\n    ::-webkit-scrollbar-track {\n      background: #1e415c;\n      border-radius: 3px;\n    }\n\n    // 设置表格背景透明\n    ::v-deep .el-table__body-wrapper {\n      background-color: transparent;\n\n      &::-webkit-scrollbar {\n        width: 6px;\n        height: 6px;\n      }\n\n      &::-webkit-scrollbar-thumb {\n        background: #0a3054;\n        border-radius: 3px;\n      }\n\n      &::-webkit-scrollbar-track {\n        background: #1e415c;\n        border-radius: 3px;\n      }\n    }\n  }\n}\n\n.custom-modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 9999;\n}\n\n.custom-modal {\n  width: 1300px;\n  background: #1B2A47;\n  border: 1px solid #00E4FF;\n  border-radius: 8px;\n  padding: 0;\n  \n  .modal-header {\n    background: #1B2A47;\n    border-bottom: 1px solid #00E4FF;\n    padding: 15px 20px;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n\n    .modal-title {\n      color: #00E4FF;\n      font-size: 18px;\n      font-weight: bold;\n    }\n\n    .header-buttons {\n      display: flex;\n      align-items: center;\n      \n      .close-text {\n        color: #00E4FF;\n        margin-right: 15px;\n      }\n\n      .close-btn {\n        color: #00E4FF;\n        font-size: 20px;\n        cursor: pointer;\n        &:hover {\n          opacity: 0.8;\n        }\n      }\n    }\n  }\n\n  .modal-content {\n    padding: 20px;\n    background: #1B2A47;\n\n    .search-container {\n      margin-bottom: 20px;\n      \n      .el-button--primary {\n        background: #1B2A47;\n        border: 1px solid #00E4FF;\n        color: #00E4FF;\n        \n        &:hover {\n          opacity: 0.8;\n        }\n      }\n    }\n\n    .el-table {\n      background: #1B2A47 !important;\n      border: 1px solid #00E4FF;\n      \n      &::before {\n        display: none;\n      }\n\n      th {\n        background: #162442 !important;\n        border-bottom: 1px solid #00E4FF !important;\n        color: #00E4FF !important;\n        font-weight: bold;\n      }\n\n      td {\n        background: #1B2A47 !important;\n        border-bottom: 1px solid rgba(0, 228, 255, 0.2) !important;\n        color: #fff !important;\n      }\n\n      .el-table__row:hover > td {\n        background: #243B6B !important;\n      }\n    }\n\n    .el-table--border::after {\n      display: none;\n    }\n  }\n}\n\n// 修改日期选择器样式\n:deep(.el-date-editor) {\n  background: #1B2A47;\n  border: 1px solid #00E4FF;\n  \n  .el-range-input {\n    background: #1B2A47;\n    color: #fff;\n  }\n  \n  .el-range-separator {\n    color: #00E4FF;\n  }\n}\n\n// 修改滚动条样式\n:deep(.el-table__body-wrapper::-webkit-scrollbar) {\n  width: 6px;\n  height: 6px;\n}\n\n:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {\n  background: #00E4FF;\n  border-radius: 3px;\n}\n\n:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {\n  background: #1B2A47;\n}\n\n.title-container {\n  position: absolute;\n  top: -9px;\n  left: 57.5%;\n  width: 100%;\n  z-index: 1000;\n\n  .more-btn {\n    display: flex;\n    align-items: center;\n    cursor: pointer;\n    color: #2cc1ff;\n    font-size: 20px;\n\n    &:hover {\n      opacity: 0.8;\n    }\n\n    i {\n      margin-left: 5px;\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;AA6WA,OAAO,KAAKA,IAAG,MAAO,MAAM;AAC5B,OAAOC,WAAU,MAAO,0CAA0C;AAClE,OAAOC,MAAK,MAAO,2CAA2C;AAC9D,OAAOC,OAAM,MAAO,4CAA4C;AAChE,OAAOC,MAAK,MAAO,iCAAiC;AAEpD,OAAOC,YAAW,MAAO,iDAAiD;AAC1E,OAAOC,YAAW,MAAO,iDAAiD;AAC1E,OAAOC,YAAW,MAAO,iDAAiD;AAC1E,OAAOC,YAAW,MAAO,iDAAiD;AAC1E,OAAOC,YAAW,MAAO,iDAAiD;AAC1E,OAAOC,YAAW,MAAO,iDAAiD;AAC1E,OAAOC,YAAW,MAAO,iDAAiD;AAC1E,OAAOC,QAAO,MAAO,qCAAqC;AAC1D,OAAOC,KAAI,MAAO,OAAO;AACzB,SAASC,sBAAqB,QAAS,cAAc;AAErD,eAAe;EACbC,UAAU,EAAE;IACVX,MAAM;IACNH,WAAW;IACXI,YAAY;IACZC,YAAY;IACZC,YAAY;IACZC,YAAY;IACZC,YAAY;IACZC,YAAY;IACZC,YAAY;IACZC,QAAQ;IACRV,MAAM;IACNC;EACF,CAAC;EACDa,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,MAAM,EAAE,IAAI;MACZC,aAAa,EAAE;QACbC,SAAS,EAAE,CACT;UACEC,IAAI,EAAE,MAAM;UACZC,OAAOA,CAACC,MAAM,EAAE;YACd,MAAMC,GAAE,GAAI,IAAIC,IAAI,CAAC,CAAC;YACtB,MAAMC,KAAI,GAAI,IAAID,IAAI,CAAC,CAAC;YACxBC,KAAK,CAACC,OAAO,CAACD,KAAK,CAACE,OAAO,CAAC,IAAI,IAAG,GAAI,IAAG,GAAI,EAAC,GAAI,CAAC,CAAC;YACrDL,MAAM,CAACM,KAAK,CAAC,MAAM,EAAE,CAACH,KAAK,EAAEF,GAAG,CAAC,CAAC;UACpC;QACF,CAAC,EACD;UACEH,IAAI,EAAE,OAAO;UACbC,OAAOA,CAACC,MAAM,EAAE;YACd,MAAMC,GAAE,GAAI,IAAIC,IAAI,CAAC,CAAC;YACtB,MAAMC,KAAI,GAAI,IAAID,IAAI,CAAC,CAAC;YACxBC,KAAK,CAACC,OAAO,CAACD,KAAK,CAACE,OAAO,CAAC,IAAI,IAAG,GAAI,IAAG,GAAI,EAAC,GAAI,EAAE,CAAC;YACtDL,MAAM,CAACM,KAAK,CAAC,MAAM,EAAE,CAACH,KAAK,EAAEF,GAAG,CAAC,CAAC;UACpC;QACF,CAAC,EACD;UACEH,IAAI,EAAE,OAAO;UACbC,OAAOA,CAACC,MAAM,EAAE;YACd,MAAMC,GAAE,GAAI,IAAIC,IAAI,CAAC,CAAC;YACtB,MAAMC,KAAI,GAAI,IAAID,IAAI,CAAC,CAAC;YACxBC,KAAK,CAACC,OAAO,CAACD,KAAK,CAACE,OAAO,CAAC,IAAI,IAAG,GAAI,IAAG,GAAI,EAAC,GAAI,EAAE,CAAC;YACtDL,MAAM,CAACM,KAAK,CAAC,MAAM,EAAE,CAACH,KAAK,EAAEF,GAAG,CAAC,CAAC;UACpC;QACF,CAAC;MAEL,CAAC;MACDM,OAAO,EAAE,CACP;QACEC,KAAK,EAAE,IAAI;QACXC,KAAK,EAAE;MACT,CAAC,EACD;QACED,KAAK,EAAE,MAAM;QACbC,KAAK,EAAE;MACT,CAAC,EACD;QACED,KAAK,EAAE,MAAM;QACbC,KAAK,EAAE;MACT,CAAC,EACD;QACED,KAAK,EAAE,MAAM;QACbC,KAAK,EAAE;MACT,CAAC,EACD;QACED,KAAK,EAAE,MAAM;QACbC,KAAK,EAAE;MACT,CAAC,EACD;QACED,KAAK,EAAE,MAAM;QACbC,KAAK,EAAE;MACT,CAAC,EACD;QACED,KAAK,EAAE,OAAO;QACdC,KAAK,EAAE;MACT,CAAC,CACF;MACDC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE,CACR;QACEH,KAAK,EAAE,IAAI;QACXC,KAAK,EAAE;MACT,CAAC,CACF;MACDG,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE,CACR;QACEL,KAAK,EAAE,IAAI;QACXC,KAAK,EAAE;MACT,CAAC,CACF;MACDK,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE,CACR;QACEP,KAAK,EAAE,IAAI;QACXC,KAAK,EAAE;MACT,CAAC,CACF;MACDG,YAAY,EAAE,KAAK;MACnBI,QAAQ,EAAE,CACR;QACER,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE;MACT,CAAC,EACD;QACED,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE;MACT,CAAC,EACD;QACED,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE;MACT,CAAC,EACD;QACED,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE;MACT,CAAC,EACD;QACED,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE;MACT,CAAC,EACD;QACED,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE;MACT,CAAC,CACF;MACDQ,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE,CACV;QACEC,IAAI,EAAE,MAAM;QACZX,KAAK,EAAE,EAAE;QACTY,SAAS,EAAE;UAAEC,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAI;MAC9C,CAAC,EACD;QACEH,IAAI,EAAE,MAAM;QACZX,KAAK,EAAE,EAAE;QACTY,SAAS,EAAE;UAAEC,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAI;MAC9C,CAAC,EACD;QACEH,IAAI,EAAE,MAAM;QACZX,KAAK,EAAE,EAAE;QACTY,SAAS,EAAE;UAAEC,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAI;MAC9C,CAAC,EACD;QACEH,IAAI,EAAE,MAAM;QACZX,KAAK,EAAE,EAAE;QACTY,SAAS,EAAE;UAAEC,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAI;MAC9C,CAAC,CACF;MACDC,WAAW,EAAE,CACX;QACEJ,IAAI,EAAE,MAAM;QACZX,KAAK,EAAE,EAAE;QACTY,SAAS,EAAE;UAAEC,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAI;MAC9C,CAAC,EACD;QACEH,IAAI,EAAE,MAAM;QACZX,KAAK,EAAE,EAAE;QACTY,SAAS,EAAE;UAAEC,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAI;MAC9C,CAAC,EACD;QACEH,IAAI,EAAE,MAAM;QACZX,KAAK,EAAE,EAAE;QACTY,SAAS,EAAE;UAAEC,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAI;MAC9C,CAAC,EACD;QACEH,IAAI,EAAE,MAAM;QACZX,KAAK,EAAE,EAAE;QACTY,SAAS,EAAE;UAAEC,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAI;MAC9C,CAAC,CACF;MACDE,KAAK,EAAE;QACLC,SAAS,EAAE,EAAE;QACbC,UAAU,EAAE,EAAE;QACdC,UAAU,EAAE;MACd,CAAC;MACDC,OAAO,EAAE,QAAQ;MAAE;MACnB;MACAC,YAAY,EAAE,KAAK;MACnBC,gBAAgB,EAAE;QAChBC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTC,OAAO,EAAE,CAAC;QACVC,KAAK,EAAE,CAAC,CAAE;MACZ,CAAC;MACDC,WAAW,EAAE,IAAI;MAAE;MACnBC,mBAAmB,EAAE,CAAC;MACtBC,OAAO,EAAE,EAAE;MAAE;MACbC,eAAe,EAAE;QACfC,SAAS,EAAE,CAAC;QACZN,OAAO,EAAE,CAAC;QACVO,MAAM,EAAE;MACV,CAAC;MACDC,aAAa,EAAE,EAAE;MAAE;MACnBC,oBAAoB,EAAE,EAAE;MAAE;;MAE1BC,aAAa,EAAE,KAAK;MAAE;MACtBC,SAAS,EAAE,EAAE;MACbC,UAAU,EAAE,EAAE;MACdC,kBAAkB,EAAE,EAAE,CAAE;IAC1B,CAAC;EACH,CAAC;EACDC,OAAO,EAAE;IACPC,KAAKA,CAAA,EAAG;MACN,IAAI,CAACrD,MAAK,GAAI,KAAK;IACrB,CAAC;IAED;IACAsD,WAAWA,CAACC,IAAI,EAAE;MAChB;MACA,MAAMC,UAAS,GAAKC,IAAI,IAAK;QAC3B,MAAMC,IAAG,GAAID,IAAI,CAACE,WAAW,CAAC,CAAC;QAC/B,MAAMC,KAAI,GAAIC,MAAM,CAACJ,IAAI,CAACK,QAAQ,CAAC,IAAI,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;QAC1D,MAAMC,GAAE,GAAIH,MAAM,CAACJ,IAAI,CAACQ,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;QACnD,OAAO,GAAGL,IAAI,IAAIE,KAAK,IAAII,GAAG,EAAE;MAClC,CAAC;MAED,MAAME,GAAE,GAAI,IAAI3D,IAAI,CAAC,CAAC;MACtB,MAAM4D,KAAI,GAAIX,UAAU,CAACU,GAAG,CAAC;MAE7B,MAAME,KAAI,GAAI;QACZC,UAAU,EAAE,CAAC;QAAE;QACfC,UAAU,EAAE,aAAa;QACzBf,IAAI,EAAE,aAAa;QACnBgB,WAAW,EAAE,KAAK;QAClBC,IAAI,EAAEL,KAAK;QACXM,EAAE,EAAEN;MACN,CAAC;;MAED;MACA,QAAQZ,IAAI;QACV,KAAK,OAAO;UACV;UACAa,KAAK,CAACG,WAAU,GAAI,KAAK;UACzB,MAAM3B,SAAQ,GAAI,IAAIrC,IAAI,CAAC2D,GAAG,CAAC;UAC/BtB,SAAS,CAAC8B,OAAO,CAAC9B,SAAS,CAACqB,OAAO,CAAC,IAAI,CAAC,CAAC;UAC1CG,KAAK,CAACI,IAAG,GAAIhB,UAAU,CAACZ,SAAS,CAAC;UAClCwB,KAAK,CAACK,EAAC,GAAIN,KAAK;UAChB;QACF,KAAK,QAAQ;UACX;UACAC,KAAK,CAACG,WAAU,GAAI,KAAK;UACzB,MAAMI,OAAM,GAAI,IAAIpE,IAAI,CAAC2D,GAAG,CAAC;UAC7BS,OAAO,CAACD,OAAO,CAACC,OAAO,CAACV,OAAO,CAAC,IAAI,CAAC,CAAC;UACtCG,KAAK,CAACI,IAAG,GAAIhB,UAAU,CAACmB,OAAO,CAAC;UAChCP,KAAK,CAACK,EAAC,GAAIN,KAAK;UAChB;QACF,KAAK,SAAS;UACZ;UACAC,KAAK,CAACG,WAAU,GAAI,KAAK;UACzB,MAAMK,QAAO,GAAI,IAAIrE,IAAI,CAAC2D,GAAG,CAAC;UAC9BU,QAAQ,CAACF,OAAO,CAACE,QAAQ,CAACX,OAAO,CAAC,IAAI,EAAE,CAAC;UACzCG,KAAK,CAACI,IAAG,GAAIhB,UAAU,CAACoB,QAAQ,CAAC;UACjCR,KAAK,CAACK,EAAC,GAAIN,KAAK;UAChB;QACF,KAAK,OAAO;UACV;UACAC,KAAK,CAACG,WAAU,GAAI,OAAO;UAC3B,MAAMM,WAAU,GAAI,IAAItE,IAAI,CAAC2D,GAAG,CAAC;UACjCW,WAAW,CAACC,WAAW,CAACD,WAAW,CAAClB,WAAW,CAAC,IAAI,EAAE,CAAC;UACvDS,KAAK,CAACI,IAAG,GAAIhB,UAAU,CAACqB,WAAW,CAAC;UACpCT,KAAK,CAACK,EAAC,GAAIN,KAAK;UAChB;MACJ;MAEA,OAAOC,KAAK;IACd,CAAC;IAED;IACA,MAAMW,mBAAmBA,CAACxB,IAAI,EAAE;MAC9B,MAAMa,KAAI,GAAI,IAAI,CAACd,WAAW,CAACC,IAAI,CAAC;MACpC,IAAI,CAACa,KAAK,EAAE;QACV,OAAO,CAAC;MACV;MAEA,IAAI;QACF,MAAMY,GAAE,GAAI,MAAMnF,sBAAsB,CAACuE,KAAK,CAAC;QAC/C,IAAI7B,KAAI,GAAI,CAAC;QACb,IAAIyC,GAAG,CAACjF,IAAG,IAAKiF,GAAG,CAACjF,IAAI,CAACkF,KAAK,EAAE;UAC9B1C,KAAI,GAAIyC,GAAG,CAACjF,IAAI,CAACkF,KAAK,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAE,GAAIE,QAAQ,CAACD,CAAC,CAACE,QAAO,IAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAC/E;QACA,OAAO/C,KAAK;MACd,EAAE,OAAOgD,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,KAAKhC,IAAI,OAAO,EAAEgC,KAAK,CAAC;QACtC,OAAO,CAAC;MACV;IACF,CAAC;IACD,MAAME,QAAQA,CAAA,EAAG;MACf,IAAI;QACF,MAAMC,QAAO,GAAI,MAAM9F,KAAK,CAAC+F,GAAG,CAC9B,GAAG,IAAI,CAAC1D,OAAO,yBAAyB,EACxC;UACE2D,MAAM,EAAE;YACN9D,SAAS,EAAE;UACb;QACF,CACF,CAAC;QAED,IAAI4D,QAAQ,CAAC3F,IAAI,CAAC8F,OAAM,KAAM,CAAC,EAAE;UAC/B,MAAM;YAAE/D,SAAS;YAAEC,UAAU;YAAEC;UAAW,IACxC0D,QAAQ,CAAC3F,IAAI,CAAC+F,WAAW;UAC3B,IAAI,CAACjE,KAAI,GAAI;YACXC,SAAS;YACTC,UAAU;YACVC;UACF,CAAC;UACD,IAAI,CAACE,YAAW,GAAI,IAAI;;UAExB;UACA,MAAM6D,SAAQ,GAAI;YAChB,GAAG,IAAI,CAAClE,KAAK;YACbmE,SAAS,EAAE,IAAIzF,IAAI,CAAC,CAAC,CAACG,OAAO,CAAC;UAChC,CAAC;UACDuF,YAAY,CAACC,OAAO,CAAC,YAAY,EAAEC,IAAI,CAACC,SAAS,CAACL,SAAS,CAAC,CAAC;UAE7DP,OAAO,CAACa,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAACxE,KAAK,CAAC;QACxD,OAAO;UACL2D,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEG,QAAQ,CAAC3F,IAAI,CAACuG,MAAM,CAAC;UAC3D,IAAI,CAACpE,YAAW,GAAI,KAAK;QAC3B;MACF,EAAE,OAAOqD,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,IAAI,CAACrD,YAAW,GAAI,KAAK;MAC3B;IACF,CAAC;IACDqE,kBAAkBA,CAAA,EAAG;MACnB,MAAMC,WAAU,GAAIP,YAAY,CAACQ,OAAO,CAAC,YAAY,CAAC;MACtD,IAAID,WAAW,EAAE;QACf,MAAMT,SAAQ,GAAII,IAAI,CAACO,KAAK,CAACF,WAAW,CAAC;QACzC,MAAMG,UAAS,GAAI,IAAIpG,IAAI,CAACwF,SAAS,CAAC/D,UAAU,CAAC,CAACtB,OAAO,CAAC,CAAC;QAC3D,MAAMkG,WAAU,GAAI,IAAIrG,IAAI,CAAC,CAAC,CAACG,OAAO,CAAC,CAAC;QACxC,MAAMmG,cAAa,GAAId,SAAS,CAACC,SAAS;;QAE1C;QACA,IACEY,WAAU,GAAID,UAAS,IACvBC,WAAU,GAAIC,cAAa,GAAI,IAAI,EAAC,GAAI,IAAG,EAC3C;UACA,IAAI,CAAChF,KAAI,GAAI;YACXC,SAAS,EAAEiE,SAAS,CAACjE,SAAS;YAC9BC,UAAU,EAAEgE,SAAS,CAAChE,UAAU;YAChCC,UAAU,EAAE+D,SAAS,CAAC/D;UACxB,CAAC;UACD,IAAI,CAACE,YAAW,GAAI,IAAI;UACxB,OAAO,IAAI;QACb;MACF;MACA,OAAO,KAAK;IACd,CAAC;IACD,MAAM4E,mBAAmBA,CAACC,SAAS,EAAEC,OAAO,EAAE;MAC5C,IAAI,CAAC,IAAI,CAAC9E,YAAY,EAAE;QACtB,MAAM,IAAI,CAACuD,QAAQ,CAAC,CAAC;MACvB;MAEA,IAAI;QACF,MAAMC,QAAO,GAAI,MAAM9F,KAAK,CAACqH,IAAI,CAC/B,GAAG,IAAI,CAAChF,OAAO,6BAA6B,EAC5C;UACEF,UAAU,EAAE,IAAI,CAACF,KAAK,CAACE,UAAU;UACjCmF,SAAS,EAAEH,SAAS;UACpBI,OAAO,EAAEH,OAAO;UAChBtE,OAAO,EAAE,IAAI,CAACA,OAAO,CAAE;QACzB,CACF,CAAC;QAED,IAAIgD,QAAQ,CAAC3F,IAAI,CAAC8F,OAAM,KAAM,CAAC,EAAE;UAC/B;UACA,MAAM1D,gBAAe,GAAIuD,QAAQ,CAAC3F,IAAI,CAAC+F,WAAW,CAACZ,MAAM,CACvD,CAACC,GAAG,EAAEiC,IAAI,KAAK;YACb,OAAOjC,GAAE,GAAIkC,UAAU,CAACD,IAAI,CAACE,OAAM,IAAK,CAAC,CAAC;UAC5C,CAAC,EACD,CACF,CAAC;UACD,OAAOnF,gBAAgB;QACzB,OAAO;UACLqD,OAAO,CAACD,KAAK,CACX,kCAAkC,EAClCG,QAAQ,CAAC3F,IAAI,CAACuG,MAChB,CAAC;UACD,OAAO,CAAC;QACV;MACF,EAAE,OAAOf,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,OAAO,CAAC;MACV;IACF,CAAC;IACD/B,UAAUA,CAACC,IAAI,EAAE;MACf,MAAMC,IAAG,GAAID,IAAI,CAACE,WAAW,CAAC,CAAC;MAC/B,MAAMC,KAAI,GAAIC,MAAM,CAACJ,IAAI,CAACK,QAAQ,CAAC,IAAI,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAC1D,MAAMC,GAAE,GAAIH,MAAM,CAACJ,IAAI,CAACQ,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACnD,MAAMwD,KAAI,GAAI1D,MAAM,CAACJ,IAAI,CAAC+D,QAAQ,CAAC,CAAC,CAAC,CAACzD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACtD,MAAM0D,OAAM,GAAI5D,MAAM,CAACJ,IAAI,CAACiE,UAAU,CAAC,CAAC,CAAC,CAAC3D,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAC1D,MAAM4D,OAAM,GAAI9D,MAAM,CAACJ,IAAI,CAACmE,UAAU,CAAC,CAAC,CAAC,CAAC7D,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAC1D,OAAO,GAAGL,IAAI,IAAIE,KAAK,IAAII,GAAG,IAAIuD,KAAK,IAAIE,OAAO,IAAIE,OAAO,EAAE;IACjE,CAAC;IACD,MAAME,oBAAoBA,CAACd,SAAS,EAAEC,OAAO,EAAE;MAC7C,IAAI,CAAC,IAAI,CAAC9E,YAAY,EAAE;QACtB,MAAM,IAAI,CAACuD,QAAQ,CAAC,CAAC;MACvB;MAEA,IAAI;QACF,MAAMC,QAAO,GAAI,MAAM9F,KAAK,CAACqH,IAAI,CAC/B,GAAG,IAAI,CAAChF,OAAO,8BAA8B,EAC7C;UACEF,UAAU,EAAE,IAAI,CAACF,KAAK,CAACE,UAAU;UACjCmF,SAAS,EAAEH,SAAS;UACpBI,OAAO,EAAEH,OAAO;UAChBtE,OAAO,EAAE,IAAI,CAACA;QAChB,CACF,CAAC;QAED,IAAIgD,QAAQ,CAAC3F,IAAI,CAAC8F,OAAM,KAAM,CAAC,EAAE;UAC/B,MAAMiC,QAAO,GAAIpC,QAAQ,CAAC3F,IAAI,CAAC+F,WAAW,CAACZ,MAAM,CAAC,CAACC,GAAG,EAAEiC,IAAI,KAAK;YAC/D,OAAOjC,GAAE,GAAIkC,UAAU,CAACD,IAAI,CAACW,GAAE,IAAK,CAAC,CAAC;UACxC,CAAC,EAAE,CAAC,CAAC;UACL,OAAOD,QAAQ;QACjB,OAAO;UACLtC,OAAO,CAACD,KAAK,CACX,mCAAmC,EACnCG,QAAQ,CAAC3F,IAAI,CAACuG,MAChB,CAAC;UACD,OAAO,CAAC;QACV;MACF,EAAE,OAAOf,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD,OAAO,CAAC;MACV;IACF,CAAC;IACD,MAAMyC,qBAAqBA,CAAA,EAAG;MAC5BxC,OAAO,CAACa,GAAG,CAAC,IAAI,CAAC;;MAEjB;MACA,IAAI,CAAC,IAAI,CAAC7D,WAAU,IAAK,CAAC,IAAI,CAACA,WAAW,CAACyF,EAAE,EAAE;QAC7CzC,OAAO,CAACD,KAAK,CAAC,2BAA2B,CAAC;QAC1C;MACF;MAEA,IAAI;QACF;QACA;QACA,IAAI,CAACpD,gBAAgB,CAACI,KAAI,GAAI,MAAM,IAAI,CAACwC,mBAAmB,CAAC,OAAO,CAAC;;QAErE;QACA,IAAI,CAAC5C,gBAAgB,CAACC,KAAI,GAAI,MAAM,IAAI,CAAC2C,mBAAmB,CAAC,OAAO,CAAC;;QAErE;QACA,IAAI,CAAC5C,gBAAgB,CAACE,MAAK,GAAI,MAAM,IAAI,CAAC0C,mBAAmB,CAAC,QAAQ,CAAC;;QAEvE;QACA,IAAI,CAAC5C,gBAAgB,CAACG,OAAM,GAAI,MAAM,IAAI,CAACyC,mBAAmB,CAAC,SAAS,CAAC;QAEzES,OAAO,CAACa,GAAG,CAAC,WAAW,EAAE,IAAI,CAAClE,gBAAgB,CAAC;MACjD,EAAE,OAAOoD,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC;QACA,MAAM,IAAI,CAAC2C,6BAA6B,CAAC,CAAC;MAC5C;IACF,CAAC;IAED;IACA,MAAMA,6BAA6BA,CAAA,EAAG;MACpC,MAAMhE,GAAE,GAAI,IAAI3D,IAAI,CAAC,CAAC;MACtB,MAAM4D,KAAI,GAAI,IAAI5D,IAAI,CAAC2D,GAAG,CAACP,WAAW,CAAC,CAAC,EAAEO,GAAG,CAACJ,QAAQ,CAAC,CAAC,EAAEI,GAAG,CAACD,OAAO,CAAC,CAAC,CAAC;MACxE,MAAMrB,SAAQ,GAAI,IAAIrC,IAAI,CAAC4D,KAAK,CAAC;MACjCvB,SAAS,CAAC8B,OAAO,CAAC9B,SAAS,CAACqB,OAAO,CAAC,IAAI,CAAC,CAAC;MAC1C,MAAMU,OAAM,GAAI,IAAIpE,IAAI,CAAC4D,KAAK,CAAC;MAC/BQ,OAAO,CAACD,OAAO,CAACC,OAAO,CAACV,OAAO,CAAC,IAAI,CAAC,CAAC;MACtC,MAAMW,QAAO,GAAI,IAAIrE,IAAI,CAAC4D,KAAK,CAAC;MAChCS,QAAQ,CAACF,OAAO,CAACE,QAAQ,CAACX,OAAO,CAAC,IAAI,EAAE,CAAC;;MAEzC;MACA,IAAI,CAAC9B,gBAAgB,CAACC,KAAI,GAAI,MAAM,IAAI,CAAC0E,mBAAmB,CAC1D,IAAI,CAACtD,UAAU,CAACZ,SAAS,CAAC,EAC1B,IAAI,CAACY,UAAU,CAACU,GAAG,CACrB,CAAC;;MAED;MACA,IAAI,CAAC/B,gBAAgB,CAACE,MAAK,GAAI,MAAM,IAAI,CAACyE,mBAAmB,CAC3D,IAAI,CAACtD,UAAU,CAACmB,OAAO,CAAC,EACxB,IAAI,CAACnB,UAAU,CAACU,GAAG,CACrB,CAAC;;MAED;MACA,IAAI,CAAC/B,gBAAgB,CAACG,OAAM,GAAI,MAAM,IAAI,CAACwE,mBAAmB,CAC5D,IAAI,CAACtD,UAAU,CAACoB,QAAQ,CAAC,EACzB,IAAI,CAACpB,UAAU,CAACU,GAAG,CACrB,CAAC;;MAED;MACA,IAAI,CAAC/B,gBAAgB,CAACI,KAAI,GAAI,IAAI,CAACJ,gBAAgB,CAACG,OAAO;;MAE3D;MACA,IAAI,CAACG,mBAAkB,GAAI,MAAM,IAAI,CAACoF,oBAAoB,CACxD,IAAI,CAACrE,UAAU,CAACoB,QAAQ,CAAC,EACzB,IAAI,CAACpB,UAAU,CAACU,GAAG,CACrB,CAAC;IACH,CAAC;IACD,MAAMiE,aAAaA,CAAA,EAAG;MACpB,MAAMjE,GAAE,GAAI,IAAI3D,IAAI,CAAC,CAAC;MACtB,MAAM4D,KAAI,GAAI,IAAI5D,IAAI,CAAC2D,GAAG,CAACP,WAAW,CAAC,CAAC,EAAEO,GAAG,CAACJ,QAAQ,CAAC,CAAC,EAAEI,GAAG,CAACD,OAAO,CAAC,CAAC,CAAC;MACxE,MAAMrB,SAAQ,GAAI,IAAIrC,IAAI,CAAC4D,KAAK,CAAC;MACjCvB,SAAS,CAAC8B,OAAO,CAAC9B,SAAS,CAACqB,OAAO,CAAC,IAAI,CAAC,CAAC;MAC1C,MAAMmE,UAAS,GAAI,IAAI7H,IAAI,CAAC4D,KAAK,CAACR,WAAW,CAAC,CAAC,EAAEQ,KAAK,CAACL,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;MACrE,MAAMuE,SAAQ,GAAI,IAAI9H,IAAI,CAAC4D,KAAK,CAACR,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAErD;MACA,IAAI,CAAChB,eAAe,CAACC,SAAQ,GAAI,MAAM,IAAI,CAACiF,oBAAoB,CAC9D,IAAI,CAACrE,UAAU,CAACZ,SAAS,CAAC,EAC1B,IAAI,CAACY,UAAU,CAACW,KAAK,CACvB,CAAC;;MAED;MACA,IAAI,CAACxB,eAAe,CAACL,OAAM,GAAI,MAAM,IAAI,CAACuF,oBAAoB,CAC5D,IAAI,CAACrE,UAAU,CAAC4E,UAAU,CAAC,EAC3B,IAAI,CAAC5E,UAAU,CAACU,GAAG,CACrB,CAAC;;MAED;MACA,IAAI,CAACvB,eAAe,CAACE,MAAK,GAAI,MAAM,IAAI,CAACgF,oBAAoB,CAC3D,IAAI,CAACrE,UAAU,CAAC6E,SAAS,CAAC,EAC1B,IAAI,CAAC7E,UAAU,CAACU,GAAG,CACrB,CAAC;IACH,CAAC;IACD,MAAMoE,gBAAgBA,CAAA,EAAG;MACvB,IAAI,CAAC,IAAI,CAACpG,YAAY,EAAE;QACtB,MAAM,IAAI,CAACuD,QAAQ,CAAC,CAAC;MACvB;MAEA,IAAI;QACF,MAAMvB,GAAE,GAAI,IAAI3D,IAAI,CAAC,CAAC;QACtB,MAAMgI,YAAW,GAAI,IAAIhI,IAAI,CAAC2D,GAAG,CAAC;QAClCqE,YAAY,CAAC7D,OAAO,CAAC6D,YAAY,CAACtE,OAAO,CAAC,IAAI,CAAC,CAAC;QAEhD,MAAMyB,QAAO,GAAI,MAAM9F,KAAK,CAACqH,IAAI,CAC/B,GAAG,IAAI,CAAChF,OAAO,8BAA8B,EAC7C;UACEF,UAAU,EAAE,IAAI,CAACF,KAAK,CAACE,UAAU;UACjCmF,SAAS,EAAE,IAAI,CAAC1D,UAAU,CAAC+E,YAAY,CAAC;UACxCpB,OAAO,EAAE,IAAI,CAAC3D,UAAU,CAACU,GAAG,CAAC;UAC7BsE,GAAG,EAAE,CAAC;UAAE;UACRC,MAAM,EAAE,CAAC;UAAE;UACX/F,OAAO,EAAE,IAAI,CAACA;QAChB,CACF,CAAC;QAED,IAAIgD,QAAQ,CAAC3F,IAAI,CAAC8F,OAAM,KAAM,CAAC,EAAE;UAC/B,IAAI,CAAC/C,aAAY,GAAI4C,QAAQ,CAAC3F,IAAI,CAAC+F,WAAW;UAC9CN,OAAO,CAACa,GAAG,CACT,wCAAwC,EACxC,IAAI,CAACvD,aACP,CAAC;QACH,OAAO;UACL0C,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEG,QAAQ,CAAC3F,IAAI,CAACuG,MAAM,CAAC;QACtE;MACF,EAAE,OAAOf,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACvD;IACF,CAAC;IACD,MAAMmD,uBAAuBA,CAAA,EAAG;MAC9B,IAAI,CAAC,IAAI,CAACxG,YAAY,EAAE;QACtB,MAAM,IAAI,CAACuD,QAAQ,CAAC,CAAC;MACvB;MAEA,IAAI;QACF,MAAMvB,GAAE,GAAI,IAAI3D,IAAI,CAAC,CAAC;QACtB,MAAMgI,YAAW,GAAI,IAAIhI,IAAI,CAAC2D,GAAG,CAAC;QAClCqE,YAAY,CAAC7D,OAAO,CAAC6D,YAAY,CAACtE,OAAO,CAAC,IAAI,CAAC,CAAC;QAEhD,MAAMyB,QAAO,GAAI,MAAM9F,KAAK,CAACqH,IAAI,CAC/B,GAAG,IAAI,CAAChF,OAAO,6BAA6B,EAC5C;UACEF,UAAU,EAAE,IAAI,CAACF,KAAK,CAACE,UAAU;UACjCmF,SAAS,EAAE,IAAI,CAAC1D,UAAU,CAAC+E,YAAY,CAAC;UACxCpB,OAAO,EAAE,IAAI,CAAC3D,UAAU,CAACU,GAAG,CAAC;UAC7BxB,OAAO,EAAE,IAAI,CAACA;QAChB,CACF,CAAC;QAED,IAAIgD,QAAQ,CAAC3F,IAAI,CAAC8F,OAAM,KAAM,CAAC,EAAE;UAC/B,IAAI,CAAC9C,oBAAmB,GAAI2C,QAAQ,CAAC3F,IAAI,CAAC+F,WAAU,CACjD6C,GAAG,CAAEvB,IAAI,KAAM;YACd,GAAGA,IAAI;YACPE,OAAO,EAAED,UAAU,CAACD,IAAI,CAACE,OAAM,IAAK,CAAC;UACvC,CAAC,CAAC,EACDsB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIvI,IAAI,CAACsI,CAAC,CAACE,QAAQ,IAAI,IAAIxI,IAAI,CAACuI,CAAC,CAACC,QAAQ,CAAC,CAAC;UAC9DvD,OAAO,CAACa,GAAG,CACT,gDAAgD,EAChD,IAAI,CAACtD,oBACP,CAAC;QACH,OAAO;UACLyC,OAAO,CAACD,KAAK,CACX,kCAAkC,EAClCG,QAAQ,CAAC3F,IAAI,CAACuG,MAChB,CAAC;QACH;MACF,EAAE,OAAOf,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC1D;IACF,CAAC;IACDyD,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAAChG,aAAY,GAAI,IAAI;MACzB,IAAI,CAACE,UAAS,GAAI,EAAE,EAAE;MACtB;MACA,MAAM5C,GAAE,GAAI,IAAIC,IAAI,CAAC,CAAC;MACtB,MAAMC,KAAI,GAAI,IAAID,IAAI,CAAC,CAAC;MACxBC,KAAK,CAACC,OAAO,CAACD,KAAK,CAACE,OAAO,CAAC,IAAI,IAAG,GAAI,IAAG,GAAI,EAAE,CAAC,EAAE;MACnD,IAAI,CAACuC,SAAQ,GAAI,CACf,IAAI,CAACO,UAAU,CAAChD,KAAK,CAAC,CAACyI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EACpC,IAAI,CAACzF,UAAU,CAAClD,GAAG,CAAC,CAAC2I,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACnC;MACD,IAAI,CAACC,UAAU,CAAC,CAAC,EAAE;IACrB,CAAC;IACD,MAAMA,UAAUA,CAAA,EAAG;MACjB,IAAI,CAAC,IAAI,CAACjG,SAAQ,IAAK,IAAI,CAACA,SAAS,CAACkG,MAAK,KAAM,CAAC,EAAE;QAClD,IAAI,CAACC,QAAQ,CAACC,OAAO,CAAC,SAAS,CAAC;QAChC;MACF;MAEA,IAAI;QACF,IAAI,CAAC,IAAI,CAACnH,YAAY,EAAE;UACtB,MAAM,IAAI,CAACuD,QAAQ,CAAC,CAAC;QACvB;QAEA,MAAMC,QAAO,GAAI,MAAM9F,KAAK,CAACqH,IAAI,CAC/B,GAAG,IAAI,CAAChF,OAAO,6BAA6B,EAC5C;UACEF,UAAU,EAAE,IAAI,CAACF,KAAK,CAACE,UAAU;UACjCmF,SAAS,EAAE,GAAG,IAAI,CAACjE,SAAS,CAAC,CAAC,CAAC,WAAW;UAC1CkE,OAAO,EAAE,GAAG,IAAI,CAAClE,SAAS,CAAC,CAAC,CAAC,WAAW;UACxCP,OAAO,EAAE,IAAI,CAACA;QAChB,CACF,CAAC;QAED,IAAIgD,QAAQ,CAAC3F,IAAI,CAAC8F,OAAM,KAAM,CAAC,EAAE;UAC/B,IACEH,QAAQ,CAAC3F,IAAI,CAAC+F,WAAU,IACxBJ,QAAQ,CAAC3F,IAAI,CAAC+F,WAAW,CAACqD,MAAK,GAAI,GACnC;YACA,IAAI,CAACjG,UAAS,GAAIwC,QAAQ,CAAC3F,IAAI,CAAC+F,WAAU,CACvC6C,GAAG,CAAEvB,IAAI,KAAM;cACd1E,OAAO,EAAE0E,IAAI,CAAC1E,OAAM,IAAK,EAAE;cAC3B4G,SAAS,EAAElC,IAAI,CAACkC,SAAQ,IAAK,EAAE;cAC/BC,SAAS,EAAEnC,IAAI,CAACmC,SAAQ,GACpBlC,UAAU,CAACD,IAAI,CAACmC,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,IACpC,KAAK;cACTC,OAAO,EAAErC,IAAI,CAACqC,OAAM,GAChBpC,UAAU,CAACD,IAAI,CAACqC,OAAO,CAAC,CAACD,OAAO,CAAC,CAAC,IAClC,KAAK;cACTlC,OAAO,EAAEF,IAAI,CAACE,OAAM,GAChBD,UAAU,CAACD,IAAI,CAACE,OAAO,CAAC,CAACkC,OAAO,CAAC,CAAC,IAClC,MAAM;cACVE,IAAI,EAAEtC,IAAI,CAACsC,IAAG,IAAK,EAAE;cACrBvC,OAAO,EAAEC,IAAI,CAACD,OAAM,GAAIC,IAAI,CAACD,OAAO,CAACwC,OAAO,CAAC,IAAI,EAAE,GAAG,IAAI;YAC5D,CAAC,CAAC,EACDf,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIvI,IAAI,CAACuI,CAAC,CAAC3B,OAAO,IAAI,IAAI5G,IAAI,CAACsI,CAAC,CAAC1B,OAAO,CAAC,CAAC;YAC5D,IAAI,CAACiC,QAAQ,CAACQ,OAAO,CAAC,MAAM,CAAC;UAC/B,OAAO;YACL,IAAI,CAAC1G,UAAS,GAAI,EAAE;YACpB,IAAI,CAACkG,QAAQ,CAACC,OAAO,CAAC,YAAY,CAAC;UACrC;QACF,OAAO;UACL,IAAI,CAACD,QAAQ,CAAC7D,KAAK,CAAC,SAAQ,GAAIG,QAAQ,CAAC3F,IAAI,CAACuG,MAAM,CAAC;UACrD,IAAI,CAACpD,UAAS,GAAI,EAAE;QACtB;MACF,EAAE,OAAOqC,KAAK,EAAE;QACd,IAAI,CAAC6D,QAAQ,CAAC7D,KAAK,CAAC,SAAQ,IAAKA,KAAK,CAACsE,OAAM,IAAK,MAAM,CAAC,CAAC;QAC1D,IAAI,CAAC3G,UAAS,GAAI,EAAE;MACtB;IACF,CAAC;IACD4G,aAAaA,CAAA,EAAG;MACd,IAAI,CAAC,IAAI,CAAC5G,UAAS,IAAK,CAAC,IAAI,CAACA,UAAU,CAACiG,MAAM,EAAE;QAC/C,IAAI,CAACC,QAAQ,CAACC,OAAO,CAAC,SAAS,CAAC;QAChC;MACF;MAEA,IAAI;QACF;QACA,MAAMU,UAAS,GAAI,IAAI,CAAC7G,UAAU,CAACyF,GAAG,CAAEvB,IAAI,KAAM;UAChD4C,IAAI,EAAE5C,IAAI,CAAC1E,OAAM,IAAK,EAAE;UACxBuH,IAAI,EAAE7C,IAAI,CAACkC,SAAQ,IAAK,EAAE;UAC1BY,EAAE,EAAE9C,IAAI,CAACmC,SAAQ,GAAIlC,UAAU,CAACD,IAAI,CAACmC,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,IAAI,KAAK;UAClEW,EAAE,EAAE/C,IAAI,CAACqC,OAAM,GAAIpC,UAAU,CAACD,IAAI,CAACqC,OAAO,CAAC,CAACD,OAAO,CAAC,CAAC,IAAI,KAAK;UAC9D,UAAU,EAAEpC,IAAI,CAACE,OAAM,GACnBD,UAAU,CAACD,IAAI,CAACE,OAAO,CAAC,CAACkC,OAAO,CAAC,CAAC,IAClC,MAAM;UACVY,IAAI,EAAEhD,IAAI,CAACsC,IAAG,IAAK,EAAE;UACrBW,IAAI,EAAEjD,IAAI,CAACD,OAAM,GAAIC,IAAI,CAACD,OAAO,CAACwC,OAAO,CAAC,IAAI,EAAE,GAAG,IAAI;QACzD,CAAC,CAAC,CAAC;;QAEH;QACA,MAAMW,EAAC,GAAIvL,IAAI,CAACwL,KAAK,CAACC,aAAa,CAACT,UAAU,CAAC;QAC/C,MAAMU,EAAC,GAAI1L,IAAI,CAACwL,KAAK,CAACG,QAAQ,CAAC,CAAC;QAChC3L,IAAI,CAACwL,KAAK,CAACI,iBAAiB,CAACF,EAAE,EAAEH,EAAE,EAAE,OAAO,CAAC;;QAE7C;QACAA,EAAE,CAAC,OAAO,IAAI,CACZ;UAAEM,GAAG,EAAE;QAAG,CAAC;QAAE;QACb;UAAEA,GAAG,EAAE;QAAG,CAAC;QAAE;QACb;UAAEA,GAAG,EAAE;QAAG,CAAC;QAAE;QACb;UAAEA,GAAG,EAAE;QAAG,CAAC;QAAE;QACb;UAAEA,GAAG,EAAE;QAAG,CAAC;QAAE;QACb;UAAEA,GAAG,EAAE;QAAG,CAAC;QAAE;QACb;UAAEA,GAAG,EAAE;QAAG,CAAC,CAAE;QAAA,CACd;;QAED;QACA,MAAMC,QAAO,GAAI,SAAS,IAAI,CAAC5H,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAACA,SAAS,CAAC,CAAC,CAAC,OAAO;QACvElE,IAAI,CAAC+L,SAAS,CAACL,EAAE,EAAEI,QAAQ,CAAC;QAE5B,IAAI,CAACzB,QAAQ,CAACQ,OAAO,CAAC,MAAM,CAAC;MAC/B,EAAE,OAAOrE,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;QACrC,IAAI,CAAC6D,QAAQ,CAAC7D,KAAK,CAAC,OAAM,IAAKA,KAAK,CAACsE,OAAM,IAAK,MAAM,CAAC,CAAC;MAC1D;IACF,CAAC;IACD,MAAMkB,qBAAqBA,CAAA,EAAG;MAC5B,IAAI,CAAC,IAAI,CAAC7I,YAAY,EAAE;QACtB,MAAM,IAAI,CAACuD,QAAQ,CAAC,CAAC;MACvB;MAEA,IAAI;QACF,MAAMvB,GAAE,GAAI,IAAI3D,IAAI,CAAC,CAAC;QACtB,MAAMgI,YAAW,GAAI,IAAIhI,IAAI,CAAC2D,GAAG,CAAC;QAClCqE,YAAY,CAAC7D,OAAO,CAAC6D,YAAY,CAACtE,OAAO,CAAC,IAAI,CAAC,CAAC;QAEhD,MAAMyB,QAAO,GAAI,MAAM9F,KAAK,CAACqH,IAAI,CAC/B,GAAG,IAAI,CAAChF,OAAO,8BAA8B,EAC7C;UACEF,UAAU,EAAE,IAAI,CAACF,KAAK,CAACE,UAAU;UACjCmF,SAAS,EAAE,IAAI,CAAC1D,UAAU,CAAC+E,YAAY,CAAC;UACxCpB,OAAO,EAAE,IAAI,CAAC3D,UAAU,CAACU,GAAG,CAAC;UAC7BxB,OAAO,EAAE,IAAI,CAACA;QAChB,CACF,CAAC;QAED,IAAIgD,QAAQ,CAAC3F,IAAI,CAAC8F,OAAM,KAAM,CAAC,EAAE;UAC/B,IAAI,CAAC1C,kBAAiB,GAAIuC,QAAQ,CAAC3F,IAAI,CAAC+F,WAAU,CAC/C6C,GAAG,CAAEvB,IAAI,KAAM;YACd,GAAGA,IAAI;YACPW,GAAG,EAAEV,UAAU,CAACD,IAAI,CAACW,GAAE,IAAK,CAAC;UAC/B,CAAC,CAAC,EACDa,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIvI,IAAI,CAACsI,CAAC,CAACE,QAAQ,IAAI,IAAIxI,IAAI,CAACuI,CAAC,CAACC,QAAQ,CAAC,CAAC;UAC9DvD,OAAO,CAACa,GAAG,CACT,8CAA8C,EAC9C,IAAI,CAAClD,kBACP,CAAC;QACH,OAAO;UACLqC,OAAO,CAACD,KAAK,CACX,qCAAqC,EACrCG,QAAQ,CAAC3F,IAAI,CAACuG,MAChB,CAAC;QACH;MACF,EAAE,OAAOf,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC7D;IACF;EACF,CAAC;EACD,MAAMyF,OAAOA,CAAA,EAAG;IACd;IACAxF,OAAO,CAACa,GAAG,CAAC,IAAI,CAAC;;IAEjB;IACA,IAAI,CAAC7D,WAAU,GAAI;MAAEyF,EAAE,EAAE;IAAE,CAAC;IAC5BzC,OAAO,CAACa,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC7D,WAAW,CAAC;;IAElD;IACA,IAAI,CAAC,IAAI,CAAC+D,kBAAkB,CAAC,CAAC,EAAE;MAC9B,MAAM,IAAI,CAACd,QAAQ,CAAC,CAAC;IACvB;;IAEA;IACA,MAAM,IAAI,CAACuC,qBAAqB,CAAC,CAAC;IAClC,MAAM,IAAI,CAACG,aAAa,CAAC,CAAC;IAC1B,MAAM,IAAI,CAACG,gBAAgB,CAAC,CAAC;IAC7B,MAAM,IAAI,CAACI,uBAAuB,CAAC,CAAC;IACpC,MAAM,IAAI,CAACqC,qBAAqB,CAAC,CAAC;;IAElC;IACAE,WAAW,CAAC,YAAY;MACtB,MAAM,IAAI,CAACxF,QAAQ,CAAC,CAAC;IACvB,CAAC,EAAE,IAAI,EAAC,GAAI,IAAI,CAAC;;IAEjB;IACAwF,WAAW,CAAC,YAAY;MACtB,MAAM,IAAI,CAACjD,qBAAqB,CAAC,CAAC;MAClC,MAAM,IAAI,CAACG,aAAa,CAAC,CAAC;MAC1B,MAAM,IAAI,CAACG,gBAAgB,CAAC,CAAC;MAC7B,MAAM,IAAI,CAACI,uBAAuB,CAAC,CAAC;MACpC,MAAM,IAAI,CAACqC,qBAAqB,CAAC,CAAC;IACpC,CAAC,EAAE,IAAI,EAAC,GAAI,IAAI,CAAC;EACnB;AACF,CAAC", "ignoreList": []}]}