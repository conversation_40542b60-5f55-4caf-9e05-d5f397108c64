{"remainingRequest": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\babel-loader\\lib\\index.js!E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\views\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\views\\index.vue", "mtime": 1751448706819}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\babel.config.js", "mtime": 1726672621424}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["huanxing", "zhexian", "zhexian1", "SystemDete", "echarts1", "echarts2", "table2", "shuang<PERSON>ng", "shebei", "resourceDeviceList", "biao1", "biao1ss", "axios", "getDeviceData", "getDevicedetails", "getDeviceWarningList", "components", "props", "data", "jlURL", "dstime", "responseData", "error", "todayReservations", "allReservations", "allTableData", "allTableData1", "currentPage", "pageSize", "total", "yytotal", "pageSizes", "opentable2", "hoveredRoom", "scrollPosition", "flag", "localtitle", "dianlist", "name", "value", "img", "require", "tablelist", "<PERSON><PERSON><PERSON><PERSON>", "activef", "isshow", "isactive", "tabledata", "zeng<PERSON><PERSON><PERSON>", "lrdata", "title1", "title2", "title3", "deviceTypes", "activeTab", "botlist", "code", "listst", "showdh", "showdh1", "noAnimation", "localTitle", "title", "nhlist", "status", "unit", "warnlist1", "type", "time", "isButton2Active", "status1", "status2", "selectedIndex", "componentTag", "dectid", "showPopup", "isLoading", "isAllLoading", "warningData", "unfixed", "fixed", "unfixedtotal", "fixedtotal", "baseURL", "token", "localStorage", "getItem", "warningStats", "computed", "formattedTitle", "formattedTitle1", "formattedTitle2", "formattedTitle3", "formattedTitle4", "formatted1Title", "formatted1Title1", "formatted1Title2", "formatted1Title3", "formatted1Title4", "unfixedCount", "fixedCount", "watch", "newVal", "resItems", "console", "log", "methods", "getStartOfDayTimestamp", "now", "Date", "setHours", "Math", "floor", "getTime", "getEndOfDayTimestamp", "formatReservationData", "item", "equipment_name", "date", "start", "toLocaleDateString", "date1", "toLocaleString", "duration", "getHours", "end", "duration1", "roomNumber", "user_name", "is_using", "equipment_group", "equipment_location", "fetchTodayReservations", "headers", "clientid", "clients<PERSON>ret", "requestBody", "method", "params", "dtstart", "dtend", "location", "limit", "response", "post", "map", "$nextTick", "uniqueEquipmentCount", "err", "fetchAllReservations", "sortedData", "sort", "a", "b", "length", "handleCurrentChange", "showLargeTable", "<PERSON><PERSON>", "openbj", "handleOpenDialog", "$emit", "scrollUp", "content", "$refs", "scrollTop", "scrollDown", "returnhome", "switchactivef", "index", "id", "res", "resourceId", "roomid", "slideUp", "contentHeight", "scrollHeight", "position", "containerHeight", "step", "style", "transform", "slideDown", "switchTab1", "switchTab", "tab", "qeihuan", "qiehuanyans", "currentIndex", "getClassForStatus", "getClassForS<PERSON>uss", "oc", "closePopup", "page", "tableDatass", "slice", "handleSizeChange", "size", "getWarningList", "hasFixed", "$auth", "checkAndRefreshToken", "rows", "msg", "removeItem", "$router", "push", "fetchAllWarningData", "Promise", "all", "formatDate", "timestamp", "getFullYear", "String", "getMonth", "padStart", "getDate", "getMinutes", "fetchWarningStats", "allWarningTypes", "压力", "氧气", "温度", "湿度", "stats", "Object", "keys", "for<PERSON>ach", "unresolved", "matchedType", "find", "warningCategory", "includes", "entries", "category", "count", "label", "created", "setInterval", "mounted", "setTimeout", "ue", "interface", "setSliderV<PERSON>ue", "isNaN", "Number", "beforeCreate", "beforeMount", "beforeUpdate", "updated", "beforeUnmount", "unmounted", "destroyed", "activated"], "sources": ["E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\views\\index.vue"], "sourcesContent": ["<template>\n  <div>\n    <component :is=\"componentTag\" :tabledata=\"tabledata\" :zengtiimg=\"zengtiimg\" @fatherMethoddd=\"fatherMethoddd\"\n      ref=\"child\"></component>\n    <div class=\"container\" v-if=\"isshow\">\n      <div class=\"left-panel\" :class=\"{\n        'left-panel-active': showdh,\n        'no-animation': noAnimation,\n        'left-panel-active1': showdh1,\n      }\">\n        <Title class=\"ltitle1\" tit=\"平台介绍\" :isshow=\"true\">\n          <div class=\"zonghe\">\n            <div class=\"boxsty\" v-for=\"item in tablelist\" :key=\"item\">\n              <div class=\"mianji\">\n                <img :src=\"item.img\" class=\"img\" alt=\"\" />\n                <div class=\"wenzi\">\n                  <div class=\"top\">{{ item.name }}</div>\n                  <div class=\"bottom\">{{ item.value }}</div>\n                </div>\n              </div>\n            </div>\n          </div>\n          <div class=\"gongneng\" v-for=\"item in wenzilist\" :key=\"item\">\n            <div style=\"display: flex; align-items: center\">\n              <div class=\"yuan\"></div>\n              <div class=\"name\">{{ item.name }}</div>\n            </div>\n            <div class=\"value\">{{ item.value }}</div>\n          </div>\n        </Title>\n        <Title class=\"ltitle1\" tit=\"报警统计\" :isshow=\"true\">\n          <div class=\"boxxx\">\n            <huanxing :warningData=\"warningStats\"></huanxing>\n          </div>\n        </Title>\n        <!-- <Title class=\"ltitle1\" tit=\"能耗统计\" :isshow=\"true\">\n          <div class=\"box\">\n            <div class=\"zongheqt\">\n              <div class=\"left1\">\n                <div class=\"mianji\" v-for=\"item in dianlist\" :key=\"item\">\n                  <img :src=\"item.img\" class=\"img\" alt=\"\" />\n                  <div class=\"wenzis\">\n                    <div class=\"top\">12346</div>\n                    <div class=\"bottom\">\n                      <div style=\"\n                          font-family: Alibaba PuHuiTi;\n                          font-weight: 400;\n                          font-size: 13px;\n                          color: #3ba1f4;\n                        \">\n                        本日\n                      </div>\n                      /Kwh\n                    </div>\n                  </div>\n                </div>\n              </div>\n              <biao1></biao1>\n            </div>\n          </div>\n        </Title> -->\n      </div>\n\n      <!-- 弹出层 -->\n      <div class=\"popup-overlay\" v-if=\"showPopup\" @click=\"closePopup\">\n        <div class=\"popup-content\" @click.stop>\n          <div class=\"popup-header\">\n            <div class=\"popup-title\">历史预约详情</div>\n            <div class=\"close-btn\" @click=\"closePopup\">×</div>\n          </div>\n          <div class=\"popup-body\" v-loading=\"isAllLoading\" element-loading-text=\"加载中...\"\n            element-loading-background=\"rgba( 28, 37, 56, 0.8)\">\n            <div class=\"popup-table\">\n              <div class=\"table-header\">\n                <div class=\"col-2\">仪器名</div>\n                <div class=\"col-2\">组织机构</div>\n                <div class=\"col-2\">仪器位置</div>\n                <div class=\"col-2\">预约时长</div>\n                <div class=\"col-1\">预约人</div>\n                <div class=\"col-1\">预约状态</div>\n              </div>\n              <template v-if=\"tableDatass && tableDatass.length > 0\">\n                <div class=\"table-row\" v-for=\"item in tableDatass\" :key=\"item\">\n                  <div class=\"col-2\" :title=\"item.name\">{{ item.name }}</div>\n                  <div class=\"col-2\" :title=\"item.equipment_group\">\n                    {{ item.equipment_group }}\n                  </div>\n                  <div class=\"col-2\" :title=\"item.equipment_location\">\n                    {{ item.equipment_location }}\n                  </div>\n                  <div class=\"col-2\">{{ item.duration1 }}</div>\n                  <div class=\"col-1\">{{ item.roomNumber }}</div>\n                  <div class=\"col-1\">{{ item.status }}</div>\n                </div>\n              </template>\n              <template v-else>\n                <div class=\"empty-message\">暂无预约记录</div>\n              </template>\n            </div>\n            <!-- 分页组件 -->\n            <div class=\"pagination-container\">\n              <el-pagination :current-page=\"currentPage\" :page-size=\"pageSize\" :page-sizes=\"pageSizes\" :total=\"total\"\n                @size-change=\"handleSizeChange\" @current-change=\"handleCurrentChange\"\n                layout=\"total, sizes, prev, pager, next, jumper\" background />\n            </div>\n          </div>\n        </div>\n      </div>\n      <!-- 右侧内容 -->\n\n      <div class=\"right-panel\" :class=\"{\n        'right-panel-active': showdh,\n        'no-animation': noAnimation,\n        'right-panel-active1': showdh1,\n      }\">\n        <Title1 class=\"rtitle\" tit=\"今日预约\">\n          <div class=\"boxswq\" @click=\"showLargeTable\">\n            <div class=\"titleimgs\">\n              <div class=\"bgu\">\n                <div>预约仪器数</div>\n                <div>{{ yytotal }}</div>\n              </div>\n              <div class=\"bgu1\">\n                <div>预约总数</div>\n                <div>{{ todayReservations.length }}</div>\n              </div>\n            </div>\n            <div class=\"titless\">\n              <div class=\"item1\">仪器名</div>\n              <div class=\"item1\">预约时长</div>\n              <div class=\"item\">预约人</div>\n              <div class=\"item1\">预约状态</div>\n            </div>\n            <div class=\"titlesscontents\" v-loading=\"isLoading\" element-loading-text=\"加载中...\"\n              element-loading-spinner=\"el-icon-loading\" element-loading-background=\"rgba(0, 0, 0, 0.8)\">\n              <div class=\"contents\" v-for=\"item in todayReservations\" :key=\"item\">\n                <div class=\"item1\" :title=\"item.name\">{{ item.name }}</div>\n                <div class=\"item1\">{{ item.duration }}</div>\n                <div class=\"item\">{{ item.roomNumber }}</div>\n                <div class=\"item1\">{{ item.status }}</div>\n              </div>\n              <div v-if=\"!todayReservations.length\" class=\"empty-message\">\n                暂无预约记录\n              </div>\n            </div>\n          </div>\n        </Title1>\n\n        <Title1 class=\"rtitle\" tit=\"仪器状态\">\n          <div class=\"huangxing\">\n            <SystemDete></SystemDete>\n            <!-- <SystemDete></SystemDete> -->\n          </div>\n        </Title1>\n        <Title1 class=\"rtitle\" tit=\"异常跟踪处理\" :isshow=\"true\">\n          <div class=\"boxxxs\" @click=\"openbj()\">\n            <div class=\"ql-center\">\n              <div class=\"ql-Box\">\n                <div class=\"ql-box\">\n                  <div class=\"left_ql\">\n                    <div class=\"yuan status\" style=\"color: #5c9dee\"></div>\n                    <div class=\"pp\">未修复</div>\n                  </div>\n                </div>\n                <div class=\"ql-box1\" style=\"color: #b93851\">\n                  {{ unfixedCount }}\n                </div>\n              </div>\n              <div class=\"ql-Box\">\n                <div class=\"ql-box\">\n                  <div class=\"left_ql\">\n                    <div class=\"yuan1 status\" style=\"color: #89f6c1\"></div>\n                    <div class=\"pp\">已修复</div>\n                  </div>\n                </div>\n                <div class=\"ql-box1 status\" style=\"color: #89f6c1\">\n                  {{ fixedCount }}\n                </div>\n              </div>\n            </div>\n            <div class=\"unfixed-warnings\">\n              <!-- 未修复警告列表 -->\n              <div v-for=\"(warning, index) in warningData.unfixed\" :key=\"'unfixed-' + index\" class=\"warning12\">\n                <div class=\"info\">\n                  <div>\n                    <div class=\"zongduan\">\n                      <div class=\"yuan\" style=\"background-color: #b93851\"></div>\n                      <div class=\"cjhulizhong\" style=\"color: #b93851\">\n                        未修复\n                      </div>\n                    </div>\n                    <p class=\"info2\">{{ warning.warningCategory }}</p>\n                  </div>\n\n                  <div class=\"info1\">\n                    <p class=\"time\">{{ formatDate(warning.createdAt) }}</p>\n                    <p class=\"location\">{{ warning.errMsg }}</p>\n                  </div>\n                  <p class=\"info2\">\n                    {{ warning.deviceName }}\n                  </p>\n                </div>\n              </div>\n            </div>\n            <!-- 已修复警告列表 -->\n            <!-- <div v-for=\"(warning, index) in warningData.fixed\" :key=\"'fixed-'+index\" class=\"warning12\">\n              <div class=\"info\">\n                <div class=\"zongduan\">\n                  <div class=\"yuan\" style=\"background-color: #64f8bb\"></div>\n                  <div class=\"cjhulizhong\" style=\"color: #64f8bb\">已处理</div>\n                </div>\n                <div class=\"info1\">\n                  <p class=\"time\">{{ formatDate(warning.createdAt) }}</p>\n                  <p class=\"location\">{{ warning.errMsg }}</p>\n                </div>\n                <p class=\"info2\" style=\"color: #64f8bb\" @click=\"openbj()\">{{ warning.deviceName }}</p>\n              </div>\n            </div> -->\n          </div>\n        </Title1>\n      </div>\n    </div>\n    <table2 @close=\"closetan\" v-if=\"opentable2\"></table2>\n    <!-- <table-2 class=\"table2\" @close=\"closetan\" v-if=\"opentable2\"></table-2> -->\n    <!-- <div\n      class=\"center_container\"\n      :class=\"{\n        'right-panel-active11': showdh,\n        'no-animation': noAnimation,\n        'right-panel-active12': showdh1,\n      }\"\n    >\n      <img\n        class=\"btn\"\n        src=\"../assets/image/shang.png\"\n        @click=\"scrollUp\"\n        alt=\"向上\"\n      />\n      <div class=\"content\" ref=\"content\">\n        <div\n          :class=\"activef == index ? 'itema' : 'item'\"\n          v-for=\"(item, index) in resItems\"\n          :key=\"index\"\n          @click=\"switchactivef(item, index)\"\n          @mouseover=\"hoveredRoom = item\"\n          @mouseleave=\"hoveredRoom = null\"\n        >\n          {{\n            index === 0\n              ? title + \"F-\" + \"整体\"\n              : title + \"F-\" + (index < 10 ? \"10\" + index : \"1\" + index)\n          }}\n\n          <div class=\"tooltip\" v-if=\"hoveredRoom === item\">{{ item.name }}</div>\n        </div>\n      </div>\n      <img\n        class=\"btn\"\n        src=\"../assets/image/xia.png\"\n        @click=\"scrollDown\"\n        alt=\"向下\"\n      />\n    </div>\n    <div\n      @click=\"returnhome()\"\n      class=\"return\"\n      :class=\"{\n        'right-panel-active11': showdh,\n        'no-animation': noAnimation,\n        'right-panel-active12': showdh1,\n      }\"\n    >\n      返回\n    </div> -->\n  </div>\n</template>\n\n<script>\n// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）\n// 例如：import 《组件名称》 from '《组件路径》';\nimport huanxing from \"@/components/echarts/huanxing.vue\";\nimport zhexian from \"@/components/echarts/zhexian.vue\";\nimport zhexian1 from \"@/components/echarts/zhexian1.vue\";\nimport SystemDete from \"@/components/echarts/SystemDete.vue\";\nimport echarts1 from \"@/components/echarts/bingjifang/echarts3.vue\";\nimport echarts2 from \"@/components/echarts/bingjifang/echarts5.vue\";\nimport table2 from \"@/components/common/table2.vue\";\nimport shuangxiang from \"@/components/echarts/shuangxiang.vue\";\nimport shebei from \"@/views/shebei.vue\";\nimport { resourceDeviceList } from \"@/api/admin.js\";\nimport biao1 from \"../components/echarts/biao1.vue\";\nimport biao1ss from \"../components/echarts/biao1ss.vue\";\nimport axios from \"axios\";\nimport {\n  getDeviceData,\n  getDevicedetails,\n  getDeviceWarningList,\n} from \"@/api/device.js\";\n\n// resourceDeviceList\nexport default {\n  // import引入的组件需要注入到对象中才能使用\n  components: {\n    table2,\n    huanxing,\n    zhexian,\n    zhexian1,\n    SystemDete,\n    echarts1,\n    echarts2,\n    shuangxiang,\n    shebei,\n    biao1ss,\n    biao1,\n  },\n  props: [\"title\", \"resItems\"],\n  data() {\n    // 这里存放数据\n    return {\n      jlURL,\n      dstime,\n      responseData: null, // 存储返回的数据\n      error: null, // 存储错误信息\n      todayReservations: [], // 今日预约数据\n      allReservations: [], // 所有预约数据\n      allTableData: [], // 存储所有数据\n      allTableData1: [], // 存储所有数据\n      currentPage: 1, // 当前页码\n      pageSize: 10, // 每页显示条数\n      total: 0, // 总数据条数\n      yytotal: 0,\n      pageSizes: [10, 20, 50, 100], // 每页显示条数选项\n      opentable2: false,\n      hoveredRoom: null,\n      scrollPosition: 0,\n      flag: true,\n      localtitle: \"\",\n      dianlist: [\n        {\n          name: \"总用地面积\",\n          value: \"57874.1㎡\",\n          img: require(\"../assets/image/ri.png\"),\n        },\n        {\n          name: \"总建筑面积\",\n          value: \"7802.54㎡\",\n          img: require(\"../assets/image/zhou.png\"),\n        },\n        {\n          name: \"地上建筑面积\",\n          value: \"14085㎡\",\n          img: require(\"../assets/image/yue.png\"),\n        },\n      ],\n      tablelist: [\n        {\n          name: \"总用地面积\",\n          value: \"4423.8㎡\",\n          img: require(\"../assets/image/mianji1.png\"),\n        },\n        {\n          name: \"总建筑面积\",\n          value: \"16845㎡\",\n          img: require(\"../assets/image/mianji2.png\"),\n        },\n        {\n          name: \"地上建筑面积\",\n          value: \"14085㎡\",\n          img: require(\"../assets/image/mianji3.png\"),\n        },\n        {\n          name: \"地下建筑面积\",\n          value: \"2760㎡\",\n          img: require(\"../assets/image/mianji4.png\"),\n        },\n      ],\n      wenzilist: [\n        {\n          name: \"平台概述\",\n          value: \"天津大学大型仪器平台是天津大学批准设立的校级公共技术服务平台，聚焦兼顾多学科需求的保障学校基础能力，促进跨平台和交叉新兴学科能力的建设,以'专管共用'的管理模式，为科学研究提供高质量的开放式测试服务，开展仪器设备创新性功能开发与技术研发。\",\n        },\n      ],\n\n      activef: 0,\n      isshow: true,\n      isactive: 0,\n      tabledata: [],\n      zengtiimg: \"\",\n      lrdata: [\n        {\n          title1: \"温度\",\n          title2: \"22℃\",\n          title3: \"2022-04-01 12:00:00\",\n        },\n      ],\n      deviceTypes: \"CQQ11\",\n      activeTab: \"today\",\n      botlist: [\n        { name: \"总览\", code: \"\" },\n        { name: \"设备列表\", code: \"\" },\n        {\n          name: \"环境温湿度\",\n          code: \"CGQ11\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/wensiduchuanganqi.png\",\n        },\n        {\n          name: \"防爆温湿度\",\n          code: \"CGQ10\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/fangbao.png\",\n        },\n        {\n          name: \"冰箱状态\",\n          code: \"LRY193\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/bingxiang.png\",\n        },\n        {\n          name: \"培养箱状态\",\n          code: \"CGQ13\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/bingxiang.png\",\n        },\n        {\n          name: \"乙炔气体\",\n          code: \"CGQ7\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/fangbao.png\",\n        },\n        {\n          name: \"环境CO2\",\n          code: \"CGQ9\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/huanjingco2.png\",\n        },\n        {\n          name: \"环境O2\",\n          code: \"CGQ3\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/huanjingco2.png\",\n        },\n        {\n          name: \"甲烷气体\",\n          code: \"CGQ8\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/jiawan.png\",\n        },\n        {\n          name: \"房间压差\",\n          code: \"CGQ2\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/kqyc.png\",\n        },\n      ],\n      listst: [\n        {\n          name: \"广东质检中诚认证有限公司到中广...\",\n        },\n        { name: \"材料科学、化学工程及医药研发成...\" },\n        { name: \"酒精代谢能力检测：您的酒量，基...\" },\n        { name: \"植酸检测方法及作用\" },\n        { name: \"兔拉检测：行业中芯片造假技术...\" },\n        { name: \"酒精代谢能力检测：您的酒量，基...\" },\n      ],\n      showdh: true,\n      showdh1: false,\n      noAnimation: false,\n      localTitle: this.title, // 初始化本地数据属性\n      nhlist: [\n        {\n          title: \"供气压力\",\n          status: \"0.3Mpa\",\n          unit: \"℃\",\n        },\n\n        {\n          title: \"供气流量\",\n          status: \"6M3/min\",\n          unit: \"㎡\",\n        },\n        {\n          title: \"露点温度\",\n          status: \"6℃\",\n          unit: \"℃\",\n        },\n        {\n          title: \"含氧量\",\n          status: \"6PPM\",\n          unit: \"㎡\",\n        },\n      ],\n      warnlist1: [\n        {\n          type: 1,\n          time: \"视频监控报警-3号楼-3F-101\",\n          value: \"\",\n          name: \"2024-06-16   12:34:09\",\n        },\n        {\n          type: 2,\n          time: \"视频监控报警-3号楼-3F-101\",\n          value: \"\",\n          name: \"2024-06-16   12:34:09\",\n        },\n        {\n          type: 3,\n          name: \"2024-06-16   12:34:09\",\n          value: \"\",\n          time: \"视频监控报警-3号楼-3F-101\",\n        },\n      ],\n      isButton2Active: false,\n      status: \"巡检中\",\n      status1: \"已完成\",\n      status2: \"待巡检\",\n      selectedIndex: 0,\n      componentTag: \"\",\n      dectid: \"\",\n      showPopup: false,\n      isLoading: false, // 今日预约加载状态\n      isAllLoading: false, // 全部预约加载状态\n      warningData: {\n        unfixed: [],\n        fixed: [],\n        unfixedtotal: 0,\n        fixedtotal: 0,\n      },\n      baseURL: \"https://tjdx.yuankong.org.cn\",\n      token: localStorage.getItem(\"token\") || \"\",\n      warningStats: [],\n    };\n  },\n  // 计算属性类似于data概念\n  computed: {\n    formattedTitle() {\n      return {\n        title: `${this.localTitle}F实验室介绍`,\n        img: require(`../assets/img/floor/1Fbig.png`),\n      };\n    },\n    formattedTitle1() {\n      return `${this.localTitle}F实验室总览`;\n    },\n    formattedTitle2() {\n      return `实验室${this.localTitle}F环境信息`;\n    },\n    formattedTitle3() {\n      return `实验室${this.localTitle}F设备信息`;\n    },\n    formattedTitle4() {\n      return `实验室${this.localTitle}F事件详情`;\n    },\n    formatted1Title() {\n      return {\n        title: `${this.localTitle}实验室介绍`,\n        img: require(`../assets/img/floor/${this.title}Fbig.png`),\n      };\n    },\n    formatted1Title1() {\n      return `${this.localTitle}实验室总览`;\n    },\n    formatted1Title2() {\n      return `${this.localTitle}环境信息`;\n    },\n    formatted1Title3() {\n      return `${this.localTitle}设备信息`;\n    },\n    formatted1Title4() {\n      return `${this.localTitle}事件详情`;\n    },\n    unfixedCount() {\n      return this.warningData.unfixedtotal;\n    },\n    fixedCount() {\n      return this.warningData.fixedtotal;\n    },\n  },\n  // 监控data中的数据变化\n  watch: {\n    title(newVal) {\n      this.localTitle = newVal;\n    },\n    resItems(newVal) {\n      console.log(newVal);\n\n      // this.resItems = newVal;\n    },\n  },\n  // 方法集合\n  methods: {\n    // 获取当前日期的00:00:01的时间戳\n    getStartOfDayTimestamp() {\n      const now = new Date();\n      now.setHours(0, 0, 1, 0); // 设置时间为当天的 00:00:01\n      return Math.floor(now.getTime() / 1000); // 转换为 Unix 时间戳（秒）\n    },\n\n    // 获取当前日期的23:59:59的时间戳\n    getEndOfDayTimestamp() {\n      const now = new Date();\n      now.setHours(23, 59, 59, 0);\n      return Math.floor(now.getTime() / 1000);\n    },\n\n    // 格式化预约数据\n    formatReservationData(item) {\n      return {\n        name: item.equipment_name,\n        date: new Date(item.start * 1000).toLocaleDateString(),\n        date1: new Date(item.start * 1000).toLocaleString(),\n        duration: `${new Date(item.start * 1000).getHours()}:00-${new Date(\n          item.end * 1000\n        ).getHours()}:00`,\n        duration1: `${new Date(\n          item.start * 1000\n        ).toLocaleDateString()} ${new Date(\n          item.start * 1000\n        ).getHours()}:00 - ${new Date(\n          item.end * 1000\n        ).toLocaleDateString()} ${new Date(item.end * 1000).getHours()}:00`,\n        roomNumber: item.user_name,\n        status: item.is_using === \"1\" ? \"已预约\" : \"已预约\",\n        equipment_group: item.equipment_group || \"未设置\", // 添加组织机构字段\n        equipment_location: item.equipment_location || \"未设置\", // 添加仪器位置字段\n      };\n    },\n\n    // 获取今日预约数据\n    async fetchTodayReservations() {\n      this.isLoading = true;\n      const headers = {\n        clientid: \"5a298e93-158d-4e22-83cf-6ceb62e9b4f1\",\n        clientsecret: \"2c8ec39e-9887-482a-b28b-e64c496b601c\",\n      };\n\n      const requestBody = {\n        method: \"gpui/eq_reserv/reservList\",\n        params: {\n          dtstart: this.getStartOfDayTimestamp(),\n          dtend: this.getEndOfDayTimestamp(),\n          params: {\n            location: \"58\",\n            limit: [0, 10000],\n          },\n        },\n      };\n\n      try {\n        const response = await axios.post(\n          jlURL,\n          requestBody,\n          { headers }\n        );\n\n        this.todayReservations = response.data.response.map(\n          this.formatReservationData\n        );\n        // 使用去重后的仪器个数\n        this.$nextTick(() => {\n          this.yytotal = this.uniqueEquipmentCount;\n        });\n      } catch (err) {\n        console.error(\"获取今日预约数据失败:\", err);\n        this.error = err;\n      } finally {\n        this.isLoading = false;\n      }\n    },\n\n    // 获取所有预约数据\n    async fetchAllReservations() {\n      this.isAllLoading = true;\n      const headers = {\n        clientid: \"5a298e93-158d-4e22-83cf-6ceb62e9b4f1\",\n        clientsecret: \"2c8ec39e-9887-482a-b28b-e64c496b601c\",\n      };\n\n      const requestBody = {\n        method: \"gpui/eq_reserv/reservList\",\n        params: {\n          dtstart: 1704844800,\n          dtend: this.getEndOfDayTimestamp(),\n          params: {\n            location: \"58\",\n            limit: [0, 10000],\n          },\n        },\n      };\n\n      try {\n        const response = await axios.post(\n          jlURL,\n          requestBody,\n          { headers }\n        );\n\n        // 对数据进行时间倒序排序\n        const sortedData = response.data.response.sort(\n          (a, b) => b.start - a.start\n        );\n        this.allReservations = sortedData.map(this.formatReservationData);\n        this.total = this.allReservations.length;\n        this.handleCurrentChange(1);\n      } catch (err) {\n        console.error(\"获取所有预约数据失败:\", err);\n        this.error = err;\n      } finally {\n        this.isAllLoading = false;\n      }\n    },\n\n    // 显示大表格\n    async showLargeTable() {\n      this.showPopup = true;\n      // 只在没有数据或数据过期的情况下重新获取\n      if (!this.allReservations.length) {\n        await this.fetchAllReservations();\n      } else {\n        // 如果已有数据，直接更新分页\n        this.handleCurrentChange(1);\n      }\n    },\n\n    closetan() {\n      this.opentable2 = false;\n    },\n    openbj() {\n      this.opentable2 = true;\n    },\n    handleOpenDialog() {\n      console.log(1111);\n      this.$emit(\"open-bj\");\n    },\n    scrollUp() {\n      const content = this.$refs.content;\n      content.scrollTop -= 38; // 每次向上滑动25px\n    },\n    scrollDown() {\n      const content = this.$refs.content;\n      content.scrollTop += 38; // 每次向下滑动25px\n    },\n    returnhome() {\n      this.$emit(\"returnhome\");\n    },\n    async switchactivef(item, index) {\n      this.dectid = item.id;\n      const res = await resourceDeviceList({\n        resourceId: item.id,\n        deviceTypes: this.deviceTypes,\n      });\n\n      this.tabledata = res.data;\n\n      if (index) {\n        this.flag = false;\n        this.localTitle = item.roomid;\n      } else {\n        this.localTitle = this.title;\n        this.flag = true;\n      }\n      console.log(item);\n      this.activef = index;\n      // this.$emit(\"childEvent\", title, index);\n    },\n    slideUp() {\n      const contentHeight = this.$refs.content.scrollHeight;\n      if (this.position > -contentHeight + this.containerHeight) {\n        this.position -= this.step;\n        this.$refs.content.style.transform = `translateY(${this.position}px)`;\n      }\n    },\n    slideDown() {\n      if (this.position < 0) {\n        this.position += this.step;\n        this.$refs.content.style.transform = `translateY(${this.position}px)`;\n      }\n    },\n\n    //  this.dectid = item.id;\n    //     const res = await resourceDeviceList({\n    //       resourceId: item.id,\n    //       deviceTypes: this.deviceTypes,\n    //     });\n    //     console.log(res.data, \"qilei\");\n    //     this.tabledata = res.data;\n\n    async switchTab1(item, index) {\n      console.log(item.img);\n      this.zengtiimg = item.img;\n\n      this.deviceTypes = item.code;\n      const res = await resourceDeviceList({\n        resourceId: this.dectid,\n        deviceTypes: this.deviceTypes,\n      });\n\n      this.tabledata = res.data;\n\n      // this.switchactivef(item, item.code);\n      this.isactive = index;\n      if (index) {\n        this.componentTag = \"shebei\";\n        this.isshow = false;\n        this.showdh = true;\n        this.showdh1 = false;\n      } else {\n        this.componentTag = \"\";\n        this.isshow = true;\n        this.showdh = false;\n        this.showdh1 = true;\n      }\n    },\n    switchTab(tab) {\n      this.activeTab = tab;\n    },\n    qeihuan(index) {\n      console.log(index, \"123123\");\n    },\n\n    qiehuanyans(index) {\n      console.log(index, \"123123\");\n      this.currentIndex = index;\n    },\n    getClassForStatus(status) {\n      if (status === \"告警总数\") {\n        return \"completed\";\n      } else if (status === \"处理完\") {\n        return \"incomplete\";\n      } else if (status === \"未处理\") {\n        return \"warning\";\n      } else {\n        return \"default\"; // 没有匹配的状态时可以添加一个默认类\n      }\n    },\n    getClassForStatuss(status) {\n      if (status === \"告警总数\") {\n        return \"completeds\";\n      } else if (status === \"处理完\") {\n        return \"incompletes\";\n      } else if (status === \"未处理\") {\n        return \"warnings\";\n      } else {\n        return \"default\"; // 没有匹配的状态时可以添加一个默认类\n      }\n    },\n    qiehuanyans(index) {\n      console.log(index, \"123123\");\n      this.currentIndex = index;\n    },\n    oc(value) {\n      console.log(value, \"floor收到的值\");\n      this.showdh = value;\n    },\n    getClassForStatus(status) {\n      if (status === \"巡检中\") {\n        return \"completed\";\n      } else if (status === \"待巡检\") {\n        return \"incomplete\";\n      } else if (status === \"已完成\") {\n        return \"warning\";\n      } else {\n        return \"default\"; // 没有匹配的状态时可以添加一个默认类\n      }\n    },\n    getClassForStatuss(status) {\n      if (status === \"巡检中\") {\n        return \"completeds\";\n      } else if (status === \"待巡检\") {\n        return \"incompletes\";\n      } else if (status === \"已完成\") {\n        return \"warnings\";\n      } else {\n        return \"default\"; // 没有匹配的状态时可以添加一个默认类\n      }\n    },\n    closePopup() {\n      this.showPopup = false;\n    },\n    // 处理页码改变\n    handleCurrentChange(page) {\n      this.currentPage = page;\n      const start = (page - 1) * this.pageSize;\n      const end = start + this.pageSize;\n      this.tableDatass = this.allReservations.slice(start, end);\n      console.log(this.tableDatass, \"tableDatass\");\n    },\n\n    // 处理每页显示条数改变\n    handleSizeChange(size) {\n      this.pageSize = size;\n      this.handleCurrentChange(1);\n    },\n    async getWarningList(hasFixed) {\n      try {\n        // 在关键请求前检查token是否需要刷新\n        if (this.$auth && this.$auth.checkAndRefreshToken) {\n          await this.$auth.checkAndRefreshToken();\n        }\n\n        const response = await getDeviceWarningList({\n          hasFixed: hasFixed,\n        });\n\n        if (response.code === 200) {\n          if (hasFixed === \"N\") {\n            this.warningData.unfixed = response.rows;\n            this.warningData.unfixedtotal = response.total;\n          } else {\n            this.warningData.fixed = response.rows;\n            this.warningData.fixedtotal = response.total;\n          }\n        } else {\n          console.error(\"获取警告数据失败:\", response.msg);\n          // 只有在明确的认证错误时才清除token并跳转\n          if (response.code === 401) {\n            localStorage.removeItem(\"token\");\n            this.$router.push(\"/\");\n          }\n        }\n      } catch (error) {\n        console.error(\"请求警告数据出错:\", error);\n        // 请求错误时不要立即清除token，让拦截器处理\n      }\n    },\n    async fetchAllWarningData() {\n      await Promise.all([this.getWarningList(\"N\"), this.getWarningList(\"Y\")]);\n    },\n    formatDate(timestamp) {\n      const date = new Date(timestamp);\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(\n        2,\n        \"0\"\n      )}-${String(date.getDate()).padStart(2, \"0\")} ${String(\n        date.getHours()\n      ).padStart(2, \"0\")}:${String(date.getMinutes()).padStart(2, \"0\")}`;\n    },\n    async fetchWarningStats() {\n      try {\n        const res = await getDeviceWarningList({\n          pageSize: 9999,\n          currentPage: 1,\n          hasFixed: \"N\",\n        });\n\n        if (res.code === 200 && res.rows) {\n          // 定义所有可能的报警类型及其阈值（简化后的名称）\n          const allWarningTypes = {\n            压力: 14,\n            氧气: 48,\n            温度: 67,\n            湿度: 67,\n            // '气体泄漏': 50\n          };\n\n          // 统计各类型报警数量\n          const stats = {};\n          // 初始化所有报警类型的计数为0\n          Object.keys(allWarningTypes).forEach((type) => {\n            stats[type] = {\n              total: 0,\n              unresolved: 0,\n            };\n          });\n\n          // 统计实际数据（使用包含匹配）\n          res.rows.forEach((item) => {\n            // 查找匹配的报警类型（只要包含关键字就匹配）\n            const matchedType = Object.keys(allWarningTypes).find(type =>\n              item.warningCategory && item.warningCategory.includes(type)\n            );\n\n            if (matchedType) {\n              stats[matchedType].total++;\n              if (item.status === \"N\") {\n                stats[matchedType].unresolved++;\n              }\n            }\n          });\n\n          // 转换为图表所需格式\n          this.warningStats = Object.entries(stats).map(\n            ([category, count]) => ({\n              label: `${category}(${count.total}/${allWarningTypes[category]})`,\n              value: count.total,\n            })\n          );\n\n          console.log(this.warningStats, \"报警统计数据\");\n        }\n      } catch (error) {\n        console.error(\"获取报警统计数据失败:\", error);\n      }\n    },\n  },\n  // 生命周期 - 创建完成（可以访问当前this实例）\n  created() {\n    this.fetchAllWarningData();\n    // 每5分钟刷新一次数据\n    setInterval(this.fetchAllWarningData, 5 * 60 * 1000);\n  },\n  // 生命周期 - 挂载完成（可以访问DOM元素）\n  mounted() {\n    this.fetchTodayReservations();\n    this.showdh1 = true;\n    setTimeout(() => {\n      this.showdh1 = false;\n      this.noAnimation = false;\n    }, 1000);\n\n    // 定时刷新今日预约数据\n    setInterval(() => {\n      this.fetchTodayReservations();\n    }, 1000 * this.dstime);\n    ue.interface.setSliderValue = (value) => {\n      console.log(value, \"ue点击拿到的值\");\n      if (!isNaN(Number(value.data))) {\n        // let did = value.data; // 如果是数字，则赋值\n        // const result = this.sblist.filter(item => item.id == did);\n        // this.deviceId = result[0].deviceId\n        // console.log(this.deviceId, 'ue点击拿到的id');\n      }\n      // this.deid = JSON.parse(value.data) - 43846\n      // console.log(this.deid);\n      // if (!isNaN(parseInt(value.data, 10))) {\n      //   var dtdata1 = JSON.parse(JSON.stringify(this.dtdata))\n      //   console.log(dtdata1);\n      //   this.showdet = false\n      //   // this.did = dtdata1.find(item => item.id == value.data)?.deviceid;\n      //   // console.log(this.did);\n      //   var didata = JSON.parse(dtdata1.find(item => item.id == value.data).channelCode);\n      //   let data1 = dtdata1.find(item => item.id == value.data)\n      //   // this.details = didata\n      //   this.bid = data1.bid\n      //   this.fid = data1.fid\n      //   // this.hlsurl\n      //   // this.bm = data1.note\n      //   console.log(data1, 1111111);\n      //   // this.getCameraData(did)\n      // }\n    };\n    this.fetchWarningStats();\n    // 每30秒更新一次数据\n    setInterval(() => {\n      this.fetchWarningStats();\n    }, 30000);\n  },\n  beforeCreate() { }, // 生命周期 - 创建之前\n  beforeMount() { }, // 生命周期 - 挂载之前\n  beforeUpdate() { }, // 生命周期 - 更新之前\n  updated() { }, // 生命周期 - 更新之后\n  beforeUnmount() {\n    // 在组件销毁之前清除定时器\n    console.log(1111);\n  },\n\n  unmounted() {\n    console.log(2222);\n  }, // 生命周期 - 销毁之前\n  destroyed() {\n    console.log(1221);\n  }, // 生命周期 - 销毁完成\n  activated() { }, // 如果页面有keep-alive缓存功能，这个函数会触发\n};\n</script>\n<style lang=\"less\" scoped>\n.table2 {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  z-index: 99999;\n}\n\n.return {\n  position: fixed;\n  right: 373px;\n  top: 100px;\n  height: 44px;\n  width: 46px;\n  // overflow: hidden;\n  transform: translate(720%);\n  transition: transform 0.5s ease-in-out;\n\n  z-index: 999;\n  cursor: pointer;\n  text-align: center;\n  line-height: 67px;\n  font-family: Source Han Sans SC;\n  font-weight: 400;\n  font-size: 11px;\n  color: #ffffff;\n  background: url(\"../assets/image/return.png\");\n  background-size: 100% 100%;\n}\n\n.center_container {\n  position: fixed;\n  right: 359px;\n  top: 352px;\n  height: 401px;\n  width: 70px;\n  // overflow: hidden;\n  transform: translate(470%);\n  transition: transform 0.5s ease-in-out;\n  z-index: 999;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  background: url(\"../assets/image/louceng.png\");\n  background-size: 100% 100%;\n\n  .content::-webkit-scrollbar {\n    width: 0px;\n    display: none;\n    /* 设置滚动条的宽度 */\n  }\n\n  /* 设置滚动条轨道的样式 */\n  .content::-webkit-scrollbar-track {\n    background-color: #f1f1f1;\n    /* 设置滚动条轨道的背景色 */\n  }\n\n  /* 设置滚动条滑块的样式 */\n  .content::-webkit-scrollbar-thumb {\n    background-color: #888;\n    /* 设置滚动条滑块的背景色 */\n  }\n\n  /* 鼠标悬停在滚动条上时的样式 */\n  .content::-webkit-scrollbar-thumb:hover {\n    background-color: #555;\n    /* 设置鼠标悬停时滚动条滑块的背景色 */\n  }\n\n  .content {\n    height: 330px;\n    /* 内容区的总高度，视实际内容而定 */\n    transition: transform 0.5s ease;\n    overflow-y: auto;\n    text-align: center;\n\n    /* 设置滚动条的样式 */\n\n    .item {\n      cursor: pointer;\n      width: 75px;\n      height: 25px;\n      font-family: YouSheBiaoTiHei;\n      font-weight: 400;\n      font-size: 14px;\n      color: #86a6b7;\n      line-height: 25px;\n      margin-top: 12px;\n    }\n\n    .itema {\n      background: url(\"../assets/image/lcactive.png\");\n      background-size: 100% 100%;\n      cursor: pointer;\n      width: 66px;\n      height: 25px;\n      font-family: YouSheBiaoTiHei;\n      font-weight: 400;\n      font-size: 14px;\n      color: #ffffff;\n      line-height: 25px;\n      margin-top: 12px;\n    }\n\n    .tooltip {\n      position: absolute;\n      left: 80%;\n      // top: 15px;\n      background-color: #1a3867;\n      border: 1px solid #7ba6eb;\n      color: #fff;\n      padding: 5px;\n      z-index: 1;\n      white-space: nowrap;\n      font-size: 12px;\n      visibility: hidden;\n\n      opacity: 0;\n      transition: opacity 0.5s, visibility 0.5s;\n      z-index: 999;\n      font-family: Source Han Sans SC;\n    }\n\n    .item:hover .tooltip {\n      visibility: visible;\n      /* 当鼠标悬停时显示 */\n      opacity: 1;\n    }\n\n    .itema:hover .tooltip {\n      visibility: visible;\n      /* 当鼠标悬停时显示 */\n      opacity: 1;\n    }\n  }\n}\n\n.btn {\n  margin-top: 13px;\n  width: 27px;\n  height: 14px;\n  cursor: pointer;\n}\n\n.echart2 {\n  height: 180px;\n}\n\n.bott {\n  position: fixed;\n  z-index: 1;\n  bottom: 4px;\n  // left: 6px;\n  width: 1920px;\n  height: 50px;\n  display: flex;\n  flex-direction: row;\n  cursor: pointer;\n  text-align: center;\n\n  .bottit {\n    width: 153px;\n    height: 45px;\n    background: url(\"../assets/image/bot_b.png\");\n    background-size: 100% 100%;\n    margin-left: 19.496px;\n    font-family: Source Han Sans SC;\n    font-weight: bold;\n    font-size: 17px;\n    color: #ffffff;\n    line-height: 43px;\n    cursor: pointer;\n  }\n\n  .bottit1 {\n    width: 153px;\n    height: 69px;\n    background: url(\"../assets/image/bot_a.png\");\n    background-size: 100% 100%;\n    margin-left: 19.496px;\n    font-family: Source Han Sans SC;\n    font-weight: bold;\n    font-size: 17px;\n    color: #ffffff;\n    line-height: 87px;\n    cursor: pointer;\n    margin-top: -23px;\n  }\n}\n\n.container {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: space-between;\n  align-items: stretch;\n  height: 1080px;\n  text-align: center;\n\n  .left-panel {\n    position: fixed;\n    z-index: 1;\n    top: 75px;\n    left: 22px;\n    width: 387px;\n    height: 937px;\n    background-size: 100% 100%;\n    transform: translate(-122%);\n    transition: transform 0.5s ease-in-out;\n\n    .box {\n      // margin-top: 6px;\n      margin-bottom: 18px;\n      // background: url(\"../assets/image/zuoshang1.png\");\n      background-size: 100% 100%;\n      background-repeat: no-repeat;\n\n      // width: 330px;\n      // height: 404px;\n\n      .loudong {\n        margin-top: 21px;\n        margin-bottom: 25px;\n        width: 296px;\n        height: 178px;\n      }\n\n      .wenzi {\n        font-family: Microsoft YaHei;\n        font-weight: 400;\n        font-size: 10px;\n        color: #bdecf9;\n        text-align: left;\n        margin-left: 20px;\n        margin-right: 20px;\n      }\n\n      .p {\n        text-indent: 2em;\n        margin-bottom: 1em;\n        letter-spacing: 0.05em;\n      }\n    }\n  }\n\n  .left-panel-active {\n    transform: translate(0%);\n  }\n\n  .left-panel-active1 {\n    // transform: translate(0%);\n    animation: slideOut 1s ease-in-out forwards;\n  }\n\n  @keyframes slideOut {\n    100% {\n      transform: translateX(0%);\n    }\n\n    // 85% {\n    //   transform: translateX(-25%);\n    // }\n\n    // 65% {\n    //   transform: translateX(-15%);\n    // }\n\n    // 40% {\n    //   transform: translateX(-55%);\n    // }\n\n    // 30% {\n    //   transform: translateX(-40%);\n    // }\n\n    0% {\n      transform: translateX(-100%);\n    }\n  }\n\n  .rtitle {\n    margin-top: 16px;\n  }\n\n  .ltitle1 {\n    margin-top: 16px;\n  }\n\n  .right-panel {\n    position: fixed;\n    z-index: 1;\n    right: 22px;\n    width: 387px;\n    top: 75px;\n    height: 937px;\n\n    background-size: 100% 100%;\n    transform: translate(122%);\n    transition: transform 0.5s ease-in-out;\n\n    .box {\n      // margin-top: 6px;\n      margin-bottom: 18px;\n      // background: url(\"../assets/image/zuoshang1.png\");\n      background-size: 100% 100%;\n      background-repeat: no-repeat;\n      font-family: Source Han Sans SC;\n      font-weight: 400;\n      font-size: 12px;\n      color: #ffffff;\n      width: 330px;\n      height: 224px;\n\n      .titlest {\n        display: flex;\n\n        // shiyansimg.png\n        .itm {\n          cursor: pointer;\n          margin: 16px 9px 0 10px;\n          background: url(\"../assets/image/shiyansimg.png\");\n          background-size: 100% 100%;\n          background-repeat: no-repeat;\n          width: 100px;\n          height: 32px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n        }\n\n        .itms {\n          background: url(\"../assets/image/xuanzexuanzhong.png\") !important;\n          background-size: 100% 100%;\n          background-repeat: no-repeat;\n          width: 100px;\n          height: 41px !important;\n          padding-bottom: 10px;\n        }\n      }\n\n      .contentss {\n        display: flex;\n        align-items: center;\n        flex-wrap: wrap;\n        justify-content: space-around;\n        align-items: center;\n\n        .itm {\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          width: 112px;\n          height: 70px;\n          background: url(\"../assets/image/wendupng.png\");\n          background-size: 100% 100%;\n          background-repeat: no-repeat;\n          font-family: DIN;\n          font-weight: bold;\n          font-size: 22px;\n          color: #ffffff;\n\n          .danwei {\n            font-family: DIN;\n            font-weight: bold;\n            font-size: 12px;\n            color: #ffffff;\n          }\n        }\n\n        .wendyu {\n          font-family: Source Han Sans SC;\n          font-weight: 400;\n          font-size: 13px;\n          color: #ffffff;\n          margin-top: -7px;\n        }\n      }\n\n      .loudong {\n        margin-top: 21px;\n        margin-bottom: 25px;\n        width: 296px;\n        height: 178px;\n      }\n    }\n\n    .boxxxs {\n      margin-left: -10px;\n      margin-top: 1px;\n      margin-bottom: 18px;\n      // background: url(\"../assets/image/zuoshang1.png\");\n      background-size: 100% 100%;\n      background-repeat: no-repeat;\n\n      width: 366px;\n      cursor: pointer;\n      // height: 254px;\n    }\n  }\n\n  .boxxx {\n    // margin-top: 6px;\n    margin-bottom: 18px;\n    // background: url(\"../assets/image/zuoshang1.png\");\n    background-size: 100% 100%;\n    background-repeat: no-repeat;\n\n    width: 350px;\n    height: 284px;\n  }\n\n  .no-animation {\n    transition: none;\n  }\n\n  .right-panel-active {\n    transform: translate(0%);\n    // animation: slideIn 1s ease-in-out ;\n  }\n\n  .right-panel-active1 {\n    // transform: translate(0%);\n    animation: slideIn 1s ease-in-out forwards;\n  }\n\n  .right-panel-active11 {\n    transform: translate(0%) !important;\n    // animation: slideIn 1s ease-in-out ;\n  }\n\n  .right-panel-active12 {\n    // transform: translate(0%);\n    animation: slideIn 1s ease-in-out forwards !important;\n  }\n\n  @keyframes slideIn {\n    0% {\n      transform: translateX(100%);\n    }\n\n    // 30% {\n    //   transform: translateX(65%);\n    // }\n\n    // 40% {\n    //   transform: translateX(40%);\n    // }\n\n    // 65% {\n    //   transform: translateX(15%);\n    // }\n\n    // 85% {\n    //   transform: translateX(25%);\n    // }\n\n    100% {\n      transform: translateX(0%);\n    }\n  }\n\n  .completed {\n    background: #7ad0ff;\n  }\n\n  .incomplete {\n    background: #ff6041;\n  }\n\n  .warning {\n    background: #00ffc0;\n  }\n\n  .completeds {\n    color: #7ad0ff;\n  }\n\n  .incompletes {\n    color: #ff6041;\n  }\n\n  .warnings {\n    color: #00ffc0;\n  }\n}\n\n.ql-center {\n  display: flex;\n  // margin-top: 20px;\n  justify-content: space-around;\n  margin-top: 4px;\n  margin-bottom: 4px;\n\n  .ql-Box {\n    width: 46%;\n    height: 49px;\n    border: 1px solid #7ad0ff;\n    // opacity: 0.6;\n    border-radius: 2px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n\n    .ql-box1 {\n      font-family: Alibaba PuHuiTi;\n      font-weight: bold;\n      font-size: 19px;\n      color: #7ad0ff;\n    }\n\n    .ql-box {\n      display: flex;\n      // padding-left: 23px;\n      padding-right: 9px;\n      flex-direction: row;\n      justify-content: space-between;\n      align-items: center;\n      // width: 100%;\n      height: 24px;\n\n      .left_ql {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        color: #ffffff;\n\n        .yuan {\n          width: 8px;\n          height: 8px;\n          border-radius: 50%;\n          background-color: #b93851;\n          margin-right: 5px;\n        }\n\n        .yuan1 {\n          width: 8px;\n          height: 8px;\n          border-radius: 50%;\n          background-color: #84edc3;\n          margin-right: 5px;\n        }\n\n        .pp {\n          margin-left: 5px;\n          color: #fff;\n          font-size: 18px;\n        }\n      }\n\n      img {\n        height: 12px;\n        width: 8px;\n      }\n    }\n  }\n}\n\n.warn1 {\n  // background: url(\"../assets/image/warnred.png\");\n}\n\n.warn2 {\n  // background: url(\"../assets/image/warnyellow.png\");\n}\n\n.warn3 {\n  // background: url(\"../assets/image/warngreen.png\");\n}\n\n.unfixed-warnings {\n  height: 180px;\n  overflow-y: auto;\n}\n\n.warning12 {\n  background-size: 100% 100%;\n  height: 47px;\n  margin-bottom: 8px;\n\n  .info {\n    display: flex;\n    justify-content: space-between;\n    align-items: flex-start;\n    text-align: left;\n    font-size: 13px;\n    padding: 8px 12px;\n    background: rgba(25, 37, 60, 0.1);\n    border-radius: 4px;\n\n    .zongduan {\n      display: flex;\n      align-items: center;\n      min-width: 80px;\n\n      .yuan {\n        width: 8px;\n        height: 8px;\n        border-radius: 50%;\n        margin-right: 6px;\n      }\n\n      .cjhulizhong {\n        font-family: Microsoft YaHei;\n        font-weight: bold;\n        font-size: 14px;\n      }\n    }\n\n    .info1 {\n      flex: 1;\n      // margin: 0 12px;\n\n      .time {\n        font-family: Microsoft YaHei;\n        font-size: 14px;\n        color: #ffffff;\n        margin-bottom: 4px;\n      }\n\n      .location {\n        font-family: Microsoft YaHei;\n        font-size: 14px;\n        color: #ffffff;\n        white-space: nowrap;\n        overflow: hidden;\n        text-overflow: ellipsis;\n      }\n    }\n\n    .info2 {\n      cursor: pointer;\n      font-size: 14px;\n      font-family: Microsoft YaHei;\n      font-weight: 400;\n      color: #b93851;\n      // white-space: nowrap;\n      margin-left: 10px;\n    }\n  }\n}\n\n.zonghe {\n  // margin-bottom: 10px;\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center;\n  justify-content: center;\n\n  .boxsty {\n    width: 50%;\n    margin-top: 12px;\n\n    .mianji {\n      display: flex;\n      align-items: center;\n\n      .img {\n        width: 50px;\n        height: 49px;\n      }\n\n      .wenzi {\n        text-align: left;\n        margin-left: 5px;\n\n        .top {\n          // margin-bottom: 9px;\n          font-family: Alibaba PuHuiTi;\n          font-weight: 500;\n          font-size: 20px;\n          color: #ffffff;\n        }\n\n        .bottom {\n          font-family: Source Han Sans SC;\n          font-weight: 500;\n          font-size: 21px;\n          color: #59ffc4;\n        }\n      }\n    }\n  }\n}\n\n.gongneng {\n  margin-top: 12px;\n\n  display: flex;\n  flex-direction: column;\n  // align-items: center;\n  // font-family: Source Han Sans SC;\n  font-family: Alibaba PuHuiTi;\n  // font-weight: bold;\n  font-size: 22px;\n  color: #59ffc4;\n  text-align: left;\n\n  .yuan {\n    margin-right: 7px;\n    width: 16px;\n    height: 16px;\n    border-radius: 50%;\n    background-color: #85fdca;\n  }\n\n  .value {\n    font-family: Alibaba PuHuiTi;\n    font-weight: 500;\n    font-size: 20px;\n    color: #fff;\n    width: 100%;\n    margin-right: 3px;\n    text-indent: 40px;\n  }\n\n  .name {\n    // width: 58px;\n    font-size: 22px;\n  }\n}\n\n.zongheqt {\n  .left1 {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    margin-right: 20px;\n    margin-top: 7px;\n\n    .mianji {\n      background: url(\"../assets/image/zengfangti.png\");\n      background-repeat: no-repeat;\n      background-size: 100% 100%;\n      width: 106px;\n      height: 58px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n    .img {\n      width: 50px;\n      height: 49px;\n    }\n\n    .wenzis {\n      .top {\n        font-family: Alibaba PuHuiTi;\n        font-weight: 400;\n        font-size: 18px;\n        color: #ffffff;\n      }\n\n      .bottom {\n        display: flex;\n        align-items: flex-end;\n        font-family: Alibaba PuHuiTi;\n        font-weight: 400;\n        font-size: 13px;\n        color: #fff;\n        margin-left: 7px;\n      }\n    }\n  }\n}\n\n.boxswq {\n  width: 365px;\n  height: 242px;\n}\n\n.huangxing {\n  width: 359px;\n  height: 238px;\n}\n\n.cjhulizhong {\n  font-family: Microsoft YaHei;\n  font-weight: bold;\n  font-size: 14px;\n  color: #64f8bb;\n  margin-left: 8px;\n}\n\n.yuan {\n  width: 10px;\n  height: 10px;\n  background-color: #518acd;\n  border-radius: 50%;\n}\n\n.zongduan {\n  display: flex;\n  align-items: center;\n  font-size: 13px;\n}\n\n.titleimgs {\n  display: flex;\n  align-items: center;\n  justify-content: space-around;\n  margin-right: 10px;\n\n  .bgu {\n    background-color: #95871cbf !important;\n\n    // background: url(\"../assets/image/titlessimg.png\");\n    background-repeat: no-repeat;\n    background-size: 100% 100%;\n    width: 171px;\n    height: 38px;\n    font-family: Alibaba PuHuiTi;\n    font-weight: 400;\n    font-size: 22px;\n    color: #ffffff;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 0 12px 0 14px;\n  }\n\n  .bgu1 {\n    background-color: rgb(28, 128, 149) !important;\n\n    // background: url(\"../assets/image/titlessimg.png\");\n    background-repeat: no-repeat;\n    background-size: 100% 100%;\n    width: 171px;\n    height: 38px;\n    font-family: Alibaba PuHuiTi;\n    font-weight: 400;\n    font-size: 22px;\n    color: #ffffff;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 0 12px 0 14px;\n  }\n}\n\n.titlesscontents {\n  overflow: auto;\n  height: 154px;\n}\n\n/* 设置滚动条的样式 */\n.titlesscontents::-webkit-scrollbar {\n  width: 5px;\n  /* 设置滚动条的宽度 */\n}\n\n/* 设置滚动条轨道的样式 */\n.titlesscontents::-webkit-scrollbar-track {\n  background-color: #454f5d;\n  /* 设置滚动条轨道的背景色 */\n}\n\n/* 设置滚动条的样式 */\n.unfixed-warnings::-webkit-scrollbar {\n  width: 5px;\n  /* 设置滚动条的宽度 */\n}\n\n/* 设置滚动条轨道的样式 */\n.unfixed-warnings::-webkit-scrollbar-track {\n  background-color: #454f5d;\n  /* 设置滚动条轨道的背景色 */\n}\n\n/* 设置滚动条滑块的样式 */\n.unfixed-warnings::-webkit-scrollbar-thumb {\n  background-color: #f1f1f1;\n  /* 设置滚动条滑块的背景色 */\n}\n\n.pagination-container {\n  margin-top: 20px;\n  display: flex;\n  justify-content: flex-end;\n  padding: 10px;\n}\n\n:deep(.el-pagination) {\n\n  .el-pagination__total,\n  .el-pagination__sizes,\n  .el-pagination__jump,\n  .btn-prev,\n  .btn-next,\n  .el-pager li {\n    background-color: transparent;\n    color: #fff;\n  }\n\n  .el-pagination__total,\n  .el-pagination__jump {\n    color: #fff;\n  }\n\n  .el-select .el-input .el-input__inner {\n    color: #fff;\n    background-color: transparent;\n  }\n\n  .el-pager li.active {\n    background-color: #409eff;\n    color: #fff;\n  }\n}\n\n.loading-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100%;\n  width: 100%;\n}\n\n:deep(.el-loading-spinner) {\n  .el-loading-text {\n    color: #fff;\n    margin: 3px 0;\n    font-size: 14px;\n  }\n\n  .circular {\n    .path {\n      stroke: #3ba1f4;\n    }\n  }\n}\n\n/* 设置滚动条滑块的样式 */\n.titlesscontents::-webkit-scrollbar-thumb {\n  background-color: #f1f1f1;\n  /* 设置滚动条滑块的背景色 */\n}\n\n.titless {\n  margin-right: 10px;\n  width: 96%;\n  background: rgba(25, 37, 60, 0.5);\n  height: 32px;\n  margin-top: 8px;\n  display: flex;\n  font-family: Alibaba PuHuiTi;\n  font-weight: 400;\n  font-size: 17px;\n  color: #40d7ff;\n  display: flex;\n  align-items: center;\n  justify-content: space-around;\n\n  .item {\n    width: 100%;\n    flex: 1.1;\n  }\n\n  .item1 {\n    width: 100%;\n    flex: 1.9;\n  }\n}\n\n.contents {\n  border-bottom: 1px solid #3b5471;\n  margin-right: 10px;\n  width: 96%;\n  background: rgba(45, 58, 79, 0.2);\n  height: 32px;\n  display: flex;\n  font-family: Alibaba PuHuiTi;\n  font-weight: 400;\n  font-size: 15px;\n  color: #fff;\n  display: flex;\n  align-items: center;\n\n  .item {\n    width: 100%;\n    flex: 1;\n  }\n\n  .item1 {\n    width: 100%;\n    flex: 1.9;\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    position: relative;\n    cursor: pointer;\n\n    &:hover::after {\n      content: attr(title);\n      position: absolute;\n      left: 0;\n      top: 100%;\n      background: rgba(0, 0, 0, 0.8);\n      color: #fff;\n      padding: 4px 8px;\n      border-radius: 4px;\n      z-index: 999;\n      white-space: normal;\n    }\n  }\n}\n\n.contents:nth-child(odd) {\n  background: rgba(46, 61, 83, 0.4);\n}\n\n.contents:nth-child(even) {\n  background: rgba(37, 50, 69, 0.2);\n}\n\n.popup-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.7);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 9999;\n}\n\n.popup-content {\n  background: rgba(25, 37, 60, 0.95);\n  border: 1px solid #3ba1f4;\n  border-radius: 8px;\n  width: 80%;\n  max-width: 1000px;\n  max-height: 80vh;\n  overflow-y: auto;\n  padding: 20px;\n}\n\n.popup-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid #3ba1f4;\n}\n\n.popup-title {\n  font-family: Alibaba PuHuiTi;\n  font-size: 24px;\n  color: #40d7ff;\n}\n\n.close-btn {\n  font-size: 28px;\n  color: #fff;\n  cursor: pointer;\n  padding: 0 10px;\n\n  &:hover {\n    color: #40d7ff;\n  }\n}\n\n.popup-table {\n  .table-header {\n    display: flex;\n    background: rgba(25, 37, 60, 0.8);\n    padding: 12px;\n    color: #40d7ff;\n    font-family: Alibaba PuHuiTi;\n    font-size: 20px;\n\n    .col-1 {\n      flex: 1;\n      padding: 0 2px;\n      text-align: center;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n\n    .col-2 {\n      flex: 2.3;\n      padding: 0 2px;\n      text-align: center;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n  }\n\n  .table-row {\n    display: flex;\n    padding: 12px;\n    font-size: 12px;\n    border-bottom: 1px solid rgba(59, 161, 244, 0.2);\n    color: #fff;\n    font-family: Alibaba PuHuiTi;\n\n    &:hover {\n      background: rgba(59, 161, 244, 0.1);\n    }\n\n    .col-1 {\n      flex: 1;\n      padding: 0 2px;\n      text-align: center;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n\n    .col-2 {\n      flex: 2.3;\n      padding: 0 2px;\n      text-align: center;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n  }\n}\n\n.pagination-container {\n  margin-top: 20px;\n  display: flex;\n  justify-content: flex-end;\n  padding: 10px;\n}\n\n:deep(.el-pagination) {\n\n  .el-pagination__total,\n  .el-pagination__sizes,\n  .el-pagination__jump {\n    color: #fff !important;\n  }\n\n  &.is-background {\n\n    .btn-prev,\n    .btn-next,\n    .el-pager li {\n      background-color: rgba(25, 37, 60, 0.8) !important;\n      color: #fff !important;\n      border: 1px solid #3ba1f4;\n      margin: 0 3px;\n\n      &:hover {\n        color: #409eff !important;\n        background-color: rgba(37, 50, 69, 0.4) !important;\n      }\n\n      &.is-active {\n        background-color: #409eff !important;\n        color: #fff !important;\n        border-color: #409eff;\n      }\n\n      &:disabled {\n        background-color: rgba(25, 37, 60, 0.4) !important;\n        color: #606266 !important;\n      }\n    }\n  }\n}\n\n.loading-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100%;\n  width: 100%;\n}\n\n:deep(.el-loading-spinner) {\n  .el-loading-text {\n    color: #fff;\n    margin: 3px 0;\n    font-size: 14px;\n  }\n\n  .circular {\n    .path {\n      stroke: #3ba1f4;\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;AAsRA;AACA;AACA,OAAOA,QAAO,MAAO,mCAAmC;AACxD,OAAOC,OAAM,MAAO,kCAAkC;AACtD,OAAOC,QAAO,MAAO,mCAAmC;AACxD,OAAOC,UAAS,MAAO,qCAAqC;AAC5D,OAAOC,QAAO,MAAO,8CAA8C;AACnE,OAAOC,QAAO,MAAO,8CAA8C;AACnE,OAAOC,MAAK,MAAO,gCAAgC;AACnD,OAAOC,WAAU,MAAO,sCAAsC;AAC9D,OAAOC,MAAK,MAAO,oBAAoB;AACvC,SAASC,kBAAiB,QAAS,gBAAgB;AACnD,OAAOC,KAAI,MAAO,iCAAiC;AACnD,OAAOC,OAAM,MAAO,mCAAmC;AACvD,OAAOC,KAAI,MAAO,OAAO;AACzB,SACEC,aAAa,EACbC,gBAAgB,EAChBC,oBAAoB,QACf,iBAAiB;;AAExB;AACA,eAAe;EACb;EACAC,UAAU,EAAE;IACVV,MAAM;IACNN,QAAQ;IACRC,OAAO;IACPC,QAAQ;IACRC,UAAU;IACVC,QAAQ;IACRC,QAAQ;IACRE,WAAW;IACXC,MAAM;IACNG,OAAO;IACPD;EACF,CAAC;EACDO,KAAK,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC;EAC5BC,IAAIA,CAAA,EAAG;IACL;IACA,OAAO;MACLC,KAAK;MACLC,MAAM;MACNC,YAAY,EAAE,IAAI;MAAE;MACpBC,KAAK,EAAE,IAAI;MAAE;MACbC,iBAAiB,EAAE,EAAE;MAAE;MACvBC,eAAe,EAAE,EAAE;MAAE;MACrBC,YAAY,EAAE,EAAE;MAAE;MAClBC,aAAa,EAAE,EAAE;MAAE;MACnBC,WAAW,EAAE,CAAC;MAAE;MAChBC,QAAQ,EAAE,EAAE;MAAE;MACdC,KAAK,EAAE,CAAC;MAAE;MACVC,OAAO,EAAE,CAAC;MACVC,SAAS,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;MAAE;MAC9BC,UAAU,EAAE,KAAK;MACjBC,WAAW,EAAE,IAAI;MACjBC,cAAc,EAAE,CAAC;MACjBC,IAAI,EAAE,IAAI;MACVC,UAAU,EAAE,EAAE;MACdC,QAAQ,EAAE,CACR;QACEC,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,UAAU;QACjBC,GAAG,EAAEC,OAAO,CAAC,wBAAwB;MACvC,CAAC,EACD;QACEH,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,UAAU;QACjBC,GAAG,EAAEC,OAAO,CAAC,0BAA0B;MACzC,CAAC,EACD;QACEH,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,QAAQ;QACfC,GAAG,EAAEC,OAAO,CAAC,yBAAyB;MACxC,CAAC,CACF;MACDC,SAAS,EAAE,CACT;QACEJ,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,SAAS;QAChBC,GAAG,EAAEC,OAAO,CAAC,6BAA6B;MAC5C,CAAC,EACD;QACEH,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,QAAQ;QACfC,GAAG,EAAEC,OAAO,CAAC,6BAA6B;MAC5C,CAAC,EACD;QACEH,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,QAAQ;QACfC,GAAG,EAAEC,OAAO,CAAC,6BAA6B;MAC5C,CAAC,EACD;QACEH,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,OAAO;QACdC,GAAG,EAAEC,OAAO,CAAC,6BAA6B;MAC5C,CAAC,CACF;MACDE,SAAS,EAAE,CACT;QACEL,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE;MACT,CAAC,CACF;MAEDK,OAAO,EAAE,CAAC;MACVC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE,CAAC;MACXC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE,EAAE;MACbC,MAAM,EAAE,CACN;QACEC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE,KAAK;QACbC,MAAM,EAAE;MACV,CAAC,CACF;MACDC,WAAW,EAAE,OAAO;MACpBC,SAAS,EAAE,OAAO;MAClBC,OAAO,EAAE,CACP;QAAEjB,IAAI,EAAE,IAAI;QAAEkB,IAAI,EAAE;MAAG,CAAC,EACxB;QAAElB,IAAI,EAAE,MAAM;QAAEkB,IAAI,EAAE;MAAG,CAAC,EAC1B;QACElB,IAAI,EAAE,OAAO;QACbkB,IAAI,EAAE,OAAO;QACbhB,GAAG,EAAE;MACP,CAAC,EACD;QACEF,IAAI,EAAE,OAAO;QACbkB,IAAI,EAAE,OAAO;QACbhB,GAAG,EAAE;MACP,CAAC,EACD;QACEF,IAAI,EAAE,MAAM;QACZkB,IAAI,EAAE,QAAQ;QACdhB,GAAG,EAAE;MACP,CAAC,EACD;QACEF,IAAI,EAAE,OAAO;QACbkB,IAAI,EAAE,OAAO;QACbhB,GAAG,EAAE;MACP,CAAC,EACD;QACEF,IAAI,EAAE,MAAM;QACZkB,IAAI,EAAE,MAAM;QACZhB,GAAG,EAAE;MACP,CAAC,EACD;QACEF,IAAI,EAAE,OAAO;QACbkB,IAAI,EAAE,MAAM;QACZhB,GAAG,EAAE;MACP,CAAC,EACD;QACEF,IAAI,EAAE,MAAM;QACZkB,IAAI,EAAE,MAAM;QACZhB,GAAG,EAAE;MACP,CAAC,EACD;QACEF,IAAI,EAAE,MAAM;QACZkB,IAAI,EAAE,MAAM;QACZhB,GAAG,EAAE;MACP,CAAC,EACD;QACEF,IAAI,EAAE,MAAM;QACZkB,IAAI,EAAE,MAAM;QACZhB,GAAG,EAAE;MACP,CAAC,CACF;MACDiB,MAAM,EAAE,CACN;QACEnB,IAAI,EAAE;MACR,CAAC,EACD;QAAEA,IAAI,EAAE;MAAqB,CAAC,EAC9B;QAAEA,IAAI,EAAE;MAAqB,CAAC,EAC9B;QAAEA,IAAI,EAAE;MAAY,CAAC,EACrB;QAAEA,IAAI,EAAE;MAAoB,CAAC,EAC7B;QAAEA,IAAI,EAAE;MAAqB,CAAC,CAC/B;MACDoB,MAAM,EAAE,IAAI;MACZC,OAAO,EAAE,KAAK;MACdC,WAAW,EAAE,KAAK;MAClBC,UAAU,EAAE,IAAI,CAACC,KAAK;MAAE;MACxBC,MAAM,EAAE,CACN;QACED,KAAK,EAAE,MAAM;QACbE,MAAM,EAAE,QAAQ;QAChBC,IAAI,EAAE;MACR,CAAC,EAED;QACEH,KAAK,EAAE,MAAM;QACbE,MAAM,EAAE,SAAS;QACjBC,IAAI,EAAE;MACR,CAAC,EACD;QACEH,KAAK,EAAE,MAAM;QACbE,MAAM,EAAE,IAAI;QACZC,IAAI,EAAE;MACR,CAAC,EACD;QACEH,KAAK,EAAE,KAAK;QACZE,MAAM,EAAE,MAAM;QACdC,IAAI,EAAE;MACR,CAAC,CACF;MACDC,SAAS,EAAE,CACT;QACEC,IAAI,EAAE,CAAC;QACPC,IAAI,EAAE,mBAAmB;QACzB7B,KAAK,EAAE,EAAE;QACTD,IAAI,EAAE;MACR,CAAC,EACD;QACE6B,IAAI,EAAE,CAAC;QACPC,IAAI,EAAE,mBAAmB;QACzB7B,KAAK,EAAE,EAAE;QACTD,IAAI,EAAE;MACR,CAAC,EACD;QACE6B,IAAI,EAAE,CAAC;QACP7B,IAAI,EAAE,uBAAuB;QAC7BC,KAAK,EAAE,EAAE;QACT6B,IAAI,EAAE;MACR,CAAC,CACF;MACDC,eAAe,EAAE,KAAK;MACtBL,MAAM,EAAE,KAAK;MACbM,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE,KAAK;MACdC,aAAa,EAAE,CAAC;MAChBC,YAAY,EAAE,EAAE;MAChBC,MAAM,EAAE,EAAE;MACVC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE,KAAK;MAAE;MAClBC,YAAY,EAAE,KAAK;MAAE;MACrBC,WAAW,EAAE;QACXC,OAAO,EAAE,EAAE;QACXC,KAAK,EAAE,EAAE;QACTC,YAAY,EAAE,CAAC;QACfC,UAAU,EAAE;MACd,CAAC;MACDC,OAAO,EAAE,8BAA8B;MACvCC,KAAK,EAAEC,YAAY,CAACC,OAAO,CAAC,OAAO,KAAK,EAAE;MAC1CC,YAAY,EAAE;IAChB,CAAC;EACH,CAAC;EACD;EACAC,QAAQ,EAAE;IACRC,cAAcA,CAAA,EAAG;MACf,OAAO;QACL3B,KAAK,EAAE,GAAG,IAAI,CAACD,UAAU,QAAQ;QACjCrB,GAAG,EAAEC,OAAO,CAAC,+BAA+B;MAC9C,CAAC;IACH,CAAC;IACDiD,eAAeA,CAAA,EAAG;MAChB,OAAO,GAAG,IAAI,CAAC7B,UAAU,QAAQ;IACnC,CAAC;IACD8B,eAAeA,CAAA,EAAG;MAChB,OAAO,MAAM,IAAI,CAAC9B,UAAU,OAAO;IACrC,CAAC;IACD+B,eAAeA,CAAA,EAAG;MAChB,OAAO,MAAM,IAAI,CAAC/B,UAAU,OAAO;IACrC,CAAC;IACDgC,eAAeA,CAAA,EAAG;MAChB,OAAO,MAAM,IAAI,CAAChC,UAAU,OAAO;IACrC,CAAC;IACDiC,eAAeA,CAAA,EAAG;MAChB,OAAO;QACLhC,KAAK,EAAE,GAAG,IAAI,CAACD,UAAU,OAAO;QAChCrB,GAAG,EAAEC,OAAO,CAAC,uBAAuB,IAAI,CAACqB,KAAK,UAAU;MAC1D,CAAC;IACH,CAAC;IACDiC,gBAAgBA,CAAA,EAAG;MACjB,OAAO,GAAG,IAAI,CAAClC,UAAU,OAAO;IAClC,CAAC;IACDmC,gBAAgBA,CAAA,EAAG;MACjB,OAAO,GAAG,IAAI,CAACnC,UAAU,MAAM;IACjC,CAAC;IACDoC,gBAAgBA,CAAA,EAAG;MACjB,OAAO,GAAG,IAAI,CAACpC,UAAU,MAAM;IACjC,CAAC;IACDqC,gBAAgBA,CAAA,EAAG;MACjB,OAAO,GAAG,IAAI,CAACrC,UAAU,MAAM;IACjC,CAAC;IACDsC,YAAYA,CAAA,EAAG;MACb,OAAO,IAAI,CAACrB,WAAW,CAACG,YAAY;IACtC,CAAC;IACDmB,UAAUA,CAAA,EAAG;MACX,OAAO,IAAI,CAACtB,WAAW,CAACI,UAAU;IACpC;EACF,CAAC;EACD;EACAmB,KAAK,EAAE;IACLvC,KAAKA,CAACwC,MAAM,EAAE;MACZ,IAAI,CAACzC,UAAS,GAAIyC,MAAM;IAC1B,CAAC;IACDC,QAAQA,CAACD,MAAM,EAAE;MACfE,OAAO,CAACC,GAAG,CAACH,MAAM,CAAC;;MAEnB;IACF;EACF,CAAC;EACD;EACAI,OAAO,EAAE;IACP;IACAC,sBAAsBA,CAAA,EAAG;MACvB,MAAMC,GAAE,GAAI,IAAIC,IAAI,CAAC,CAAC;MACtBD,GAAG,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;MAC1B,OAAOC,IAAI,CAACC,KAAK,CAACJ,GAAG,CAACK,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE;IAC3C,CAAC;IAED;IACAC,oBAAoBA,CAAA,EAAG;MACrB,MAAMN,GAAE,GAAI,IAAIC,IAAI,CAAC,CAAC;MACtBD,GAAG,CAACE,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;MAC3B,OAAOC,IAAI,CAACC,KAAK,CAACJ,GAAG,CAACK,OAAO,CAAC,IAAI,IAAI,CAAC;IACzC,CAAC;IAED;IACAE,qBAAqBA,CAACC,IAAI,EAAE;MAC1B,OAAO;QACL9E,IAAI,EAAE8E,IAAI,CAACC,cAAc;QACzBC,IAAI,EAAE,IAAIT,IAAI,CAACO,IAAI,CAACG,KAAI,GAAI,IAAI,CAAC,CAACC,kBAAkB,CAAC,CAAC;QACtDC,KAAK,EAAE,IAAIZ,IAAI,CAACO,IAAI,CAACG,KAAI,GAAI,IAAI,CAAC,CAACG,cAAc,CAAC,CAAC;QACnDC,QAAQ,EAAE,GAAG,IAAId,IAAI,CAACO,IAAI,CAACG,KAAI,GAAI,IAAI,CAAC,CAACK,QAAQ,CAAC,CAAC,OAAO,IAAIf,IAAI,CAChEO,IAAI,CAACS,GAAE,GAAI,IACb,CAAC,CAACD,QAAQ,CAAC,CAAC,KAAK;QACjBE,SAAS,EAAE,GAAG,IAAIjB,IAAI,CACpBO,IAAI,CAACG,KAAI,GAAI,IACf,CAAC,CAACC,kBAAkB,CAAC,CAAC,IAAI,IAAIX,IAAI,CAChCO,IAAI,CAACG,KAAI,GAAI,IACf,CAAC,CAACK,QAAQ,CAAC,CAAC,SAAS,IAAIf,IAAI,CAC3BO,IAAI,CAACS,GAAE,GAAI,IACb,CAAC,CAACL,kBAAkB,CAAC,CAAC,IAAI,IAAIX,IAAI,CAACO,IAAI,CAACS,GAAE,GAAI,IAAI,CAAC,CAACD,QAAQ,CAAC,CAAC,KAAK;QACnEG,UAAU,EAAEX,IAAI,CAACY,SAAS;QAC1BhE,MAAM,EAAEoD,IAAI,CAACa,QAAO,KAAM,GAAE,GAAI,KAAI,GAAI,KAAK;QAC7CC,eAAe,EAAEd,IAAI,CAACc,eAAc,IAAK,KAAK;QAAE;QAChDC,kBAAkB,EAAEf,IAAI,CAACe,kBAAiB,IAAK,KAAK,CAAE;MACxD,CAAC;IACH,CAAC;IAED;IACA,MAAMC,sBAAsBA,CAAA,EAAG;MAC7B,IAAI,CAACxD,SAAQ,GAAI,IAAI;MACrB,MAAMyD,OAAM,GAAI;QACdC,QAAQ,EAAE,sCAAsC;QAChDC,YAAY,EAAE;MAChB,CAAC;MAED,MAAMC,WAAU,GAAI;QAClBC,MAAM,EAAE,2BAA2B;QACnCC,MAAM,EAAE;UACNC,OAAO,EAAE,IAAI,CAAChC,sBAAsB,CAAC,CAAC;UACtCiC,KAAK,EAAE,IAAI,CAAC1B,oBAAoB,CAAC,CAAC;UAClCwB,MAAM,EAAE;YACNG,QAAQ,EAAE,IAAI;YACdC,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK;UAClB;QACF;MACF,CAAC;MAED,IAAI;QACF,MAAMC,QAAO,GAAI,MAAMnI,KAAK,CAACoI,IAAI,CAC/B7H,KAAK,EACLqH,WAAW,EACX;UAAEH;QAAQ,CACZ,CAAC;QAED,IAAI,CAAC9G,iBAAgB,GAAIwH,QAAQ,CAAC7H,IAAI,CAAC6H,QAAQ,CAACE,GAAG,CACjD,IAAI,CAAC9B,qBACP,CAAC;QACD;QACA,IAAI,CAAC+B,SAAS,CAAC,MAAM;UACnB,IAAI,CAACpH,OAAM,GAAI,IAAI,CAACqH,oBAAoB;QAC1C,CAAC,CAAC;MACJ,EAAE,OAAOC,GAAG,EAAE;QACZ5C,OAAO,CAAClF,KAAK,CAAC,aAAa,EAAE8H,GAAG,CAAC;QACjC,IAAI,CAAC9H,KAAI,GAAI8H,GAAG;MAClB,UAAU;QACR,IAAI,CAACxE,SAAQ,GAAI,KAAK;MACxB;IACF,CAAC;IAED;IACA,MAAMyE,oBAAoBA,CAAA,EAAG;MAC3B,IAAI,CAACxE,YAAW,GAAI,IAAI;MACxB,MAAMwD,OAAM,GAAI;QACdC,QAAQ,EAAE,sCAAsC;QAChDC,YAAY,EAAE;MAChB,CAAC;MAED,MAAMC,WAAU,GAAI;QAClBC,MAAM,EAAE,2BAA2B;QACnCC,MAAM,EAAE;UACNC,OAAO,EAAE,UAAU;UACnBC,KAAK,EAAE,IAAI,CAAC1B,oBAAoB,CAAC,CAAC;UAClCwB,MAAM,EAAE;YACNG,QAAQ,EAAE,IAAI;YACdC,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK;UAClB;QACF;MACF,CAAC;MAED,IAAI;QACF,MAAMC,QAAO,GAAI,MAAMnI,KAAK,CAACoI,IAAI,CAC/B7H,KAAK,EACLqH,WAAW,EACX;UAAEH;QAAQ,CACZ,CAAC;;QAED;QACA,MAAMiB,UAAS,GAAIP,QAAQ,CAAC7H,IAAI,CAAC6H,QAAQ,CAACQ,IAAI,CAC5C,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAClC,KAAI,GAAIiC,CAAC,CAACjC,KACxB,CAAC;QACD,IAAI,CAAC/F,eAAc,GAAI8H,UAAU,CAACL,GAAG,CAAC,IAAI,CAAC9B,qBAAqB,CAAC;QACjE,IAAI,CAACtF,KAAI,GAAI,IAAI,CAACL,eAAe,CAACkI,MAAM;QACxC,IAAI,CAACC,mBAAmB,CAAC,CAAC,CAAC;MAC7B,EAAE,OAAOP,GAAG,EAAE;QACZ5C,OAAO,CAAClF,KAAK,CAAC,aAAa,EAAE8H,GAAG,CAAC;QACjC,IAAI,CAAC9H,KAAI,GAAI8H,GAAG;MAClB,UAAU;QACR,IAAI,CAACvE,YAAW,GAAI,KAAK;MAC3B;IACF,CAAC;IAED;IACA,MAAM+E,cAAcA,CAAA,EAAG;MACrB,IAAI,CAACjF,SAAQ,GAAI,IAAI;MACrB;MACA,IAAI,CAAC,IAAI,CAACnD,eAAe,CAACkI,MAAM,EAAE;QAChC,MAAM,IAAI,CAACL,oBAAoB,CAAC,CAAC;MACnC,OAAO;QACL;QACA,IAAI,CAACM,mBAAmB,CAAC,CAAC,CAAC;MAC7B;IACF,CAAC;IAEDE,QAAQA,CAAA,EAAG;MACT,IAAI,CAAC7H,UAAS,GAAI,KAAK;IACzB,CAAC;IACD8H,MAAMA,CAAA,EAAG;MACP,IAAI,CAAC9H,UAAS,GAAI,IAAI;IACxB,CAAC;IACD+H,gBAAgBA,CAAA,EAAG;MACjBvD,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC;MACjB,IAAI,CAACuD,KAAK,CAAC,SAAS,CAAC;IACvB,CAAC;IACDC,QAAQA,CAAA,EAAG;MACT,MAAMC,OAAM,GAAI,IAAI,CAACC,KAAK,CAACD,OAAO;MAClCA,OAAO,CAACE,SAAQ,IAAK,EAAE,EAAE;IAC3B,CAAC;IACDC,UAAUA,CAAA,EAAG;MACX,MAAMH,OAAM,GAAI,IAAI,CAACC,KAAK,CAACD,OAAO;MAClCA,OAAO,CAACE,SAAQ,IAAK,EAAE,EAAE;IAC3B,CAAC;IACDE,UAAUA,CAAA,EAAG;MACX,IAAI,CAACN,KAAK,CAAC,YAAY,CAAC;IAC1B,CAAC;IACD,MAAMO,aAAaA,CAACnD,IAAI,EAAEoD,KAAK,EAAE;MAC/B,IAAI,CAAC9F,MAAK,GAAI0C,IAAI,CAACqD,EAAE;MACrB,MAAMC,GAAE,GAAI,MAAMjK,kBAAkB,CAAC;QACnCkK,UAAU,EAAEvD,IAAI,CAACqD,EAAE;QACnBpH,WAAW,EAAE,IAAI,CAACA;MACpB,CAAC,CAAC;MAEF,IAAI,CAACN,SAAQ,GAAI2H,GAAG,CAACxJ,IAAI;MAEzB,IAAIsJ,KAAK,EAAE;QACT,IAAI,CAACrI,IAAG,GAAI,KAAK;QACjB,IAAI,CAAC0B,UAAS,GAAIuD,IAAI,CAACwD,MAAM;MAC/B,OAAO;QACL,IAAI,CAAC/G,UAAS,GAAI,IAAI,CAACC,KAAK;QAC5B,IAAI,CAAC3B,IAAG,GAAI,IAAI;MAClB;MACAqE,OAAO,CAACC,GAAG,CAACW,IAAI,CAAC;MACjB,IAAI,CAACxE,OAAM,GAAI4H,KAAK;MACpB;IACF,CAAC;IACDK,OAAOA,CAAA,EAAG;MACR,MAAMC,aAAY,GAAI,IAAI,CAACX,KAAK,CAACD,OAAO,CAACa,YAAY;MACrD,IAAI,IAAI,CAACC,QAAO,GAAI,CAACF,aAAY,GAAI,IAAI,CAACG,eAAe,EAAE;QACzD,IAAI,CAACD,QAAO,IAAK,IAAI,CAACE,IAAI;QAC1B,IAAI,CAACf,KAAK,CAACD,OAAO,CAACiB,KAAK,CAACC,SAAQ,GAAI,cAAc,IAAI,CAACJ,QAAQ,KAAK;MACvE;IACF,CAAC;IACDK,SAASA,CAAA,EAAG;MACV,IAAI,IAAI,CAACL,QAAO,GAAI,CAAC,EAAE;QACrB,IAAI,CAACA,QAAO,IAAK,IAAI,CAACE,IAAI;QAC1B,IAAI,CAACf,KAAK,CAACD,OAAO,CAACiB,KAAK,CAACC,SAAQ,GAAI,cAAc,IAAI,CAACJ,QAAQ,KAAK;MACvE;IACF,CAAC;IAED;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA,MAAMM,UAAUA,CAAClE,IAAI,EAAEoD,KAAK,EAAE;MAC5BhE,OAAO,CAACC,GAAG,CAACW,IAAI,CAAC5E,GAAG,CAAC;MACrB,IAAI,CAACQ,SAAQ,GAAIoE,IAAI,CAAC5E,GAAG;MAEzB,IAAI,CAACa,WAAU,GAAI+D,IAAI,CAAC5D,IAAI;MAC5B,MAAMkH,GAAE,GAAI,MAAMjK,kBAAkB,CAAC;QACnCkK,UAAU,EAAE,IAAI,CAACjG,MAAM;QACvBrB,WAAW,EAAE,IAAI,CAACA;MACpB,CAAC,CAAC;MAEF,IAAI,CAACN,SAAQ,GAAI2H,GAAG,CAACxJ,IAAI;;MAEzB;MACA,IAAI,CAAC4B,QAAO,GAAI0H,KAAK;MACrB,IAAIA,KAAK,EAAE;QACT,IAAI,CAAC/F,YAAW,GAAI,QAAQ;QAC5B,IAAI,CAAC5B,MAAK,GAAI,KAAK;QACnB,IAAI,CAACa,MAAK,GAAI,IAAI;QAClB,IAAI,CAACC,OAAM,GAAI,KAAK;MACtB,OAAO;QACL,IAAI,CAACc,YAAW,GAAI,EAAE;QACtB,IAAI,CAAC5B,MAAK,GAAI,IAAI;QAClB,IAAI,CAACa,MAAK,GAAI,KAAK;QACnB,IAAI,CAACC,OAAM,GAAI,IAAI;MACrB;IACF,CAAC;IACD4H,SAASA,CAACC,GAAG,EAAE;MACb,IAAI,CAAClI,SAAQ,GAAIkI,GAAG;IACtB,CAAC;IACDC,OAAOA,CAACjB,KAAK,EAAE;MACbhE,OAAO,CAACC,GAAG,CAAC+D,KAAK,EAAE,QAAQ,CAAC;IAC9B,CAAC;IAEDkB,WAAWA,CAAClB,KAAK,EAAE;MACjBhE,OAAO,CAACC,GAAG,CAAC+D,KAAK,EAAE,QAAQ,CAAC;MAC5B,IAAI,CAACmB,YAAW,GAAInB,KAAK;IAC3B,CAAC;IACDoB,iBAAiBA,CAAC5H,MAAM,EAAE;MACxB,IAAIA,MAAK,KAAM,MAAM,EAAE;QACrB,OAAO,WAAW;MACpB,OAAO,IAAIA,MAAK,KAAM,KAAK,EAAE;QAC3B,OAAO,YAAY;MACrB,OAAO,IAAIA,MAAK,KAAM,KAAK,EAAE;QAC3B,OAAO,SAAS;MAClB,OAAO;QACL,OAAO,SAAS,EAAE;MACpB;IACF,CAAC;IACD6H,kBAAkBA,CAAC7H,MAAM,EAAE;MACzB,IAAIA,MAAK,KAAM,MAAM,EAAE;QACrB,OAAO,YAAY;MACrB,OAAO,IAAIA,MAAK,KAAM,KAAK,EAAE;QAC3B,OAAO,aAAa;MACtB,OAAO,IAAIA,MAAK,KAAM,KAAK,EAAE;QAC3B,OAAO,UAAU;MACnB,OAAO;QACL,OAAO,SAAS,EAAE;MACpB;IACF,CAAC;IACD0H,WAAWA,CAAClB,KAAK,EAAE;MACjBhE,OAAO,CAACC,GAAG,CAAC+D,KAAK,EAAE,QAAQ,CAAC;MAC5B,IAAI,CAACmB,YAAW,GAAInB,KAAK;IAC3B,CAAC;IACDsB,EAAEA,CAACvJ,KAAK,EAAE;MACRiE,OAAO,CAACC,GAAG,CAAClE,KAAK,EAAE,WAAW,CAAC;MAC/B,IAAI,CAACmB,MAAK,GAAInB,KAAK;IACrB,CAAC;IACDqJ,iBAAiBA,CAAC5H,MAAM,EAAE;MACxB,IAAIA,MAAK,KAAM,KAAK,EAAE;QACpB,OAAO,WAAW;MACpB,OAAO,IAAIA,MAAK,KAAM,KAAK,EAAE;QAC3B,OAAO,YAAY;MACrB,OAAO,IAAIA,MAAK,KAAM,KAAK,EAAE;QAC3B,OAAO,SAAS;MAClB,OAAO;QACL,OAAO,SAAS,EAAE;MACpB;IACF,CAAC;IACD6H,kBAAkBA,CAAC7H,MAAM,EAAE;MACzB,IAAIA,MAAK,KAAM,KAAK,EAAE;QACpB,OAAO,YAAY;MACrB,OAAO,IAAIA,MAAK,KAAM,KAAK,EAAE;QAC3B,OAAO,aAAa;MACtB,OAAO,IAAIA,MAAK,KAAM,KAAK,EAAE;QAC3B,OAAO,UAAU;MACnB,OAAO;QACL,OAAO,SAAS,EAAE;MACpB;IACF,CAAC;IACD+H,UAAUA,CAAA,EAAG;MACX,IAAI,CAACpH,SAAQ,GAAI,KAAK;IACxB,CAAC;IACD;IACAgF,mBAAmBA,CAACqC,IAAI,EAAE;MACxB,IAAI,CAACrK,WAAU,GAAIqK,IAAI;MACvB,MAAMzE,KAAI,GAAI,CAACyE,IAAG,GAAI,CAAC,IAAI,IAAI,CAACpK,QAAQ;MACxC,MAAMiG,GAAE,GAAIN,KAAI,GAAI,IAAI,CAAC3F,QAAQ;MACjC,IAAI,CAACqK,WAAU,GAAI,IAAI,CAACzK,eAAe,CAAC0K,KAAK,CAAC3E,KAAK,EAAEM,GAAG,CAAC;MACzDrB,OAAO,CAACC,GAAG,CAAC,IAAI,CAACwF,WAAW,EAAE,aAAa,CAAC;IAC9C,CAAC;IAED;IACAE,gBAAgBA,CAACC,IAAI,EAAE;MACrB,IAAI,CAACxK,QAAO,GAAIwK,IAAI;MACpB,IAAI,CAACzC,mBAAmB,CAAC,CAAC,CAAC;IAC7B,CAAC;IACD,MAAM0C,cAAcA,CAACC,QAAQ,EAAE;MAC7B,IAAI;QACF;QACA,IAAI,IAAI,CAACC,KAAI,IAAK,IAAI,CAACA,KAAK,CAACC,oBAAoB,EAAE;UACjD,MAAM,IAAI,CAACD,KAAK,CAACC,oBAAoB,CAAC,CAAC;QACzC;QAEA,MAAMzD,QAAO,GAAI,MAAMhI,oBAAoB,CAAC;UAC1CuL,QAAQ,EAAEA;QACZ,CAAC,CAAC;QAEF,IAAIvD,QAAQ,CAACvF,IAAG,KAAM,GAAG,EAAE;UACzB,IAAI8I,QAAO,KAAM,GAAG,EAAE;YACpB,IAAI,CAACxH,WAAW,CAACC,OAAM,GAAIgE,QAAQ,CAAC0D,IAAI;YACxC,IAAI,CAAC3H,WAAW,CAACG,YAAW,GAAI8D,QAAQ,CAAClH,KAAK;UAChD,OAAO;YACL,IAAI,CAACiD,WAAW,CAACE,KAAI,GAAI+D,QAAQ,CAAC0D,IAAI;YACtC,IAAI,CAAC3H,WAAW,CAACI,UAAS,GAAI6D,QAAQ,CAAClH,KAAK;UAC9C;QACF,OAAO;UACL2E,OAAO,CAAClF,KAAK,CAAC,WAAW,EAAEyH,QAAQ,CAAC2D,GAAG,CAAC;UACxC;UACA,IAAI3D,QAAQ,CAACvF,IAAG,KAAM,GAAG,EAAE;YACzB6B,YAAY,CAACsH,UAAU,CAAC,OAAO,CAAC;YAChC,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,GAAG,CAAC;UACxB;QACF;MACF,EAAE,OAAOvL,KAAK,EAAE;QACdkF,OAAO,CAAClF,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC;MACF;IACF,CAAC;IACD,MAAMwL,mBAAmBA,CAAA,EAAG;MAC1B,MAAMC,OAAO,CAACC,GAAG,CAAC,CAAC,IAAI,CAACX,cAAc,CAAC,GAAG,CAAC,EAAE,IAAI,CAACA,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC;IACzE,CAAC;IACDY,UAAUA,CAACC,SAAS,EAAE;MACpB,MAAM5F,IAAG,GAAI,IAAIT,IAAI,CAACqG,SAAS,CAAC;MAChC,OAAO,GAAG5F,IAAI,CAAC6F,WAAW,CAAC,CAAC,IAAIC,MAAM,CAAC9F,IAAI,CAAC+F,QAAQ,CAAC,IAAI,CAAC,CAAC,CAACC,QAAQ,CAClE,CAAC,EACD,GACF,CAAC,IAAIF,MAAM,CAAC9F,IAAI,CAACiG,OAAO,CAAC,CAAC,CAAC,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,MAAM,CACpD9F,IAAI,CAACM,QAAQ,CAAC,CAChB,CAAC,CAAC0F,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,MAAM,CAAC9F,IAAI,CAACkG,UAAU,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;IACpE,CAAC;IACD,MAAMG,iBAAiBA,CAAA,EAAG;MACxB,IAAI;QACF,MAAM/C,GAAE,GAAI,MAAM3J,oBAAoB,CAAC;UACrCa,QAAQ,EAAE,IAAI;UACdD,WAAW,EAAE,CAAC;UACd2K,QAAQ,EAAE;QACZ,CAAC,CAAC;QAEF,IAAI5B,GAAG,CAAClH,IAAG,KAAM,GAAE,IAAKkH,GAAG,CAAC+B,IAAI,EAAE;UAChC;UACA,MAAMiB,eAAc,GAAI;YACtBC,EAAE,EAAE,EAAE;YACNC,EAAE,EAAE,EAAE;YACNC,EAAE,EAAE,EAAE;YACNC,EAAE,EAAE;YACJ;UACF,CAAC;;UAED;UACA,MAAMC,KAAI,GAAI,CAAC,CAAC;UAChB;UACAC,MAAM,CAACC,IAAI,CAACP,eAAe,CAAC,CAACQ,OAAO,CAAE/J,IAAI,IAAK;YAC7C4J,KAAK,CAAC5J,IAAI,IAAI;cACZtC,KAAK,EAAE,CAAC;cACRsM,UAAU,EAAE;YACd,CAAC;UACH,CAAC,CAAC;;UAEF;UACAzD,GAAG,CAAC+B,IAAI,CAACyB,OAAO,CAAE9G,IAAI,IAAK;YACzB;YACA,MAAMgH,WAAU,GAAIJ,MAAM,CAACC,IAAI,CAACP,eAAe,CAAC,CAACW,IAAI,CAAClK,IAAG,IACvDiD,IAAI,CAACkH,eAAc,IAAKlH,IAAI,CAACkH,eAAe,CAACC,QAAQ,CAACpK,IAAI,CAC5D,CAAC;YAED,IAAIiK,WAAW,EAAE;cACfL,KAAK,CAACK,WAAW,CAAC,CAACvM,KAAK,EAAE;cAC1B,IAAIuF,IAAI,CAACpD,MAAK,KAAM,GAAG,EAAE;gBACvB+J,KAAK,CAACK,WAAW,CAAC,CAACD,UAAU,EAAE;cACjC;YACF;UACF,CAAC,CAAC;;UAEF;UACA,IAAI,CAAC5I,YAAW,GAAIyI,MAAM,CAACQ,OAAO,CAACT,KAAK,CAAC,CAAC9E,GAAG,CAC3C,CAAC,CAACwF,QAAQ,EAAEC,KAAK,CAAC,MAAM;YACtBC,KAAK,EAAE,GAAGF,QAAQ,IAAIC,KAAK,CAAC7M,KAAK,IAAI6L,eAAe,CAACe,QAAQ,CAAC,GAAG;YACjElM,KAAK,EAAEmM,KAAK,CAAC7M;UACf,CAAC,CACH,CAAC;UAED2E,OAAO,CAACC,GAAG,CAAC,IAAI,CAAClB,YAAY,EAAE,QAAQ,CAAC;QAC1C;MACF,EAAE,OAAOjE,KAAK,EAAE;QACdkF,OAAO,CAAClF,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACrC;IACF;EACF,CAAC;EACD;EACAsN,OAAOA,CAAA,EAAG;IACR,IAAI,CAAC9B,mBAAmB,CAAC,CAAC;IAC1B;IACA+B,WAAW,CAAC,IAAI,CAAC/B,mBAAmB,EAAE,IAAI,EAAC,GAAI,IAAI,CAAC;EACtD,CAAC;EACD;EACAgC,OAAOA,CAAA,EAAG;IACR,IAAI,CAAC1G,sBAAsB,CAAC,CAAC;IAC7B,IAAI,CAACzE,OAAM,GAAI,IAAI;IACnBoL,UAAU,CAAC,MAAM;MACf,IAAI,CAACpL,OAAM,GAAI,KAAK;MACpB,IAAI,CAACC,WAAU,GAAI,KAAK;IAC1B,CAAC,EAAE,IAAI,CAAC;;IAER;IACAiL,WAAW,CAAC,MAAM;MAChB,IAAI,CAACzG,sBAAsB,CAAC,CAAC;IAC/B,CAAC,EAAE,IAAG,GAAI,IAAI,CAAChH,MAAM,CAAC;IACtB4N,EAAE,CAACC,SAAS,CAACC,cAAa,GAAK3M,KAAK,IAAK;MACvCiE,OAAO,CAACC,GAAG,CAAClE,KAAK,EAAE,UAAU,CAAC;MAC9B,IAAI,CAAC4M,KAAK,CAACC,MAAM,CAAC7M,KAAK,CAACrB,IAAI,CAAC,CAAC,EAAE;QAC9B;QACA;QACA;QACA;MAAA;MAEF;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACF,CAAC;IACD,IAAI,CAACuM,iBAAiB,CAAC,CAAC;IACxB;IACAoB,WAAW,CAAC,MAAM;MAChB,IAAI,CAACpB,iBAAiB,CAAC,CAAC;IAC1B,CAAC,EAAE,KAAK,CAAC;EACX,CAAC;EACD4B,YAAYA,CAAA,EAAG,CAAE,CAAC;EAAE;EACpBC,WAAWA,CAAA,EAAG,CAAE,CAAC;EAAE;EACnBC,YAAYA,CAAA,EAAG,CAAE,CAAC;EAAE;EACpBC,OAAOA,CAAA,EAAG,CAAE,CAAC;EAAE;EACfC,aAAaA,CAAA,EAAG;IACd;IACAjJ,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC;EACnB,CAAC;EAEDiJ,SAASA,CAAA,EAAG;IACVlJ,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC;EACnB,CAAC;EAAE;EACHkJ,SAASA,CAAA,EAAG;IACVnJ,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC;EACnB,CAAC;EAAE;EACHmJ,SAASA,CAAA,EAAG,CAAE,CAAC,CAAE;AACnB,CAAC", "ignoreList": []}]}