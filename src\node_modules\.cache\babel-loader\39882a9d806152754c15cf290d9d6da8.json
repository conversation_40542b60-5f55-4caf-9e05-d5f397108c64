{"remainingRequest": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\babel-loader\\lib\\index.js!E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\views\\index.vue?vue&type=template&id=a83bd3b0&scoped=true", "dependencies": [{"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\views\\index.vue", "mtime": 1751448706819}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\babel.config.js", "mtime": 1726672621424}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "style", "_createElementBlock", "_createBlock", "_resolveDynamicComponent", "$data", "componentTag", "tabledata", "zeng<PERSON><PERSON><PERSON>", "onFatherMethoddd", "_ctx", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ref", "isshow", "_hoisted_1", "_createElementVNode", "_normalizeClass", "showdh", "noAnimation", "showdh1", "_createVNode", "_component_Title", "tit", "_hoisted_2", "_Fragment", "_renderList", "tablelist", "item", "key", "_hoisted_3", "src", "img", "alt", "_hoisted_5", "_hoisted_6", "_toDisplayString", "name", "_hoisted_7", "value", "<PERSON><PERSON><PERSON><PERSON>", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_component_huanxing", "warningData", "warningStats", "_createCommentVNode", "showPopup", "onClick", "_cache", "args", "$options", "closePopup", "_withModifiers", "_hoisted_12", "_hoisted_13", "_hoisted_14", "tableDatass", "length", "title", "_hoisted_15", "equipment_group", "_hoisted_16", "equipment_location", "_hoisted_17", "_hoisted_18", "duration1", "_hoisted_19", "roomNumber", "_hoisted_20", "status", "_hoisted_21", "_hoisted_22", "_component_el_pagination", "currentPage", "pageSize", "pageSizes", "total", "onSizeChange", "handleSizeChange", "onCurrentChange", "handleCurrentChange", "layout", "background", "isAllLoading", "_component_Title1", "showLargeTable", "_hoisted_23", "_hoisted_24", "yytotal", "_hoisted_25", "todayReservations", "_hoisted_26", "_hoisted_27", "_hoisted_28", "duration", "_hoisted_29", "_hoisted_30", "_hoisted_31", "isLoading", "_hoisted_32", "_component_SystemDete", "$event", "openbj", "_hoisted_33", "_hoisted_34", "_hoisted_35", "unfixedCount", "_hoisted_36", "_hoisted_37", "fixedCount", "_hoisted_38", "unfixed", "warning", "index", "_hoisted_39", "_hoisted_40", "warningCategory", "_hoisted_41", "_hoisted_42", "formatDate", "createdAt", "_hoisted_43", "errMsg", "_hoisted_44", "deviceName", "opentable2", "_component_table2", "onClose", "<PERSON><PERSON>"], "sources": ["E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\views\\index.vue"], "sourcesContent": ["<template>\n  <div>\n    <component :is=\"componentTag\" :tabledata=\"tabledata\" :zengtiimg=\"zengtiimg\" @fatherMethoddd=\"fatherMethoddd\"\n      ref=\"child\"></component>\n    <div class=\"container\" v-if=\"isshow\">\n      <div class=\"left-panel\" :class=\"{\n        'left-panel-active': showdh,\n        'no-animation': noAnimation,\n        'left-panel-active1': showdh1,\n      }\">\n        <Title class=\"ltitle1\" tit=\"平台介绍\" :isshow=\"true\">\n          <div class=\"zonghe\">\n            <div class=\"boxsty\" v-for=\"item in tablelist\" :key=\"item\">\n              <div class=\"mianji\">\n                <img :src=\"item.img\" class=\"img\" alt=\"\" />\n                <div class=\"wenzi\">\n                  <div class=\"top\">{{ item.name }}</div>\n                  <div class=\"bottom\">{{ item.value }}</div>\n                </div>\n              </div>\n            </div>\n          </div>\n          <div class=\"gongneng\" v-for=\"item in wenzilist\" :key=\"item\">\n            <div style=\"display: flex; align-items: center\">\n              <div class=\"yuan\"></div>\n              <div class=\"name\">{{ item.name }}</div>\n            </div>\n            <div class=\"value\">{{ item.value }}</div>\n          </div>\n        </Title>\n        <Title class=\"ltitle1\" tit=\"报警统计\" :isshow=\"true\">\n          <div class=\"boxxx\">\n            <huanxing :warningData=\"warningStats\"></huanxing>\n          </div>\n        </Title>\n        <!-- <Title class=\"ltitle1\" tit=\"能耗统计\" :isshow=\"true\">\n          <div class=\"box\">\n            <div class=\"zongheqt\">\n              <div class=\"left1\">\n                <div class=\"mianji\" v-for=\"item in dianlist\" :key=\"item\">\n                  <img :src=\"item.img\" class=\"img\" alt=\"\" />\n                  <div class=\"wenzis\">\n                    <div class=\"top\">12346</div>\n                    <div class=\"bottom\">\n                      <div style=\"\n                          font-family: Alibaba PuHuiTi;\n                          font-weight: 400;\n                          font-size: 13px;\n                          color: #3ba1f4;\n                        \">\n                        本日\n                      </div>\n                      /Kwh\n                    </div>\n                  </div>\n                </div>\n              </div>\n              <biao1></biao1>\n            </div>\n          </div>\n        </Title> -->\n      </div>\n\n      <!-- 弹出层 -->\n      <div class=\"popup-overlay\" v-if=\"showPopup\" @click=\"closePopup\">\n        <div class=\"popup-content\" @click.stop>\n          <div class=\"popup-header\">\n            <div class=\"popup-title\">历史预约详情</div>\n            <div class=\"close-btn\" @click=\"closePopup\">×</div>\n          </div>\n          <div class=\"popup-body\" v-loading=\"isAllLoading\" element-loading-text=\"加载中...\"\n            element-loading-background=\"rgba( 28, 37, 56, 0.8)\">\n            <div class=\"popup-table\">\n              <div class=\"table-header\">\n                <div class=\"col-2\">仪器名</div>\n                <div class=\"col-2\">组织机构</div>\n                <div class=\"col-2\">仪器位置</div>\n                <div class=\"col-2\">预约时长</div>\n                <div class=\"col-1\">预约人</div>\n                <div class=\"col-1\">预约状态</div>\n              </div>\n              <template v-if=\"tableDatass && tableDatass.length > 0\">\n                <div class=\"table-row\" v-for=\"item in tableDatass\" :key=\"item\">\n                  <div class=\"col-2\" :title=\"item.name\">{{ item.name }}</div>\n                  <div class=\"col-2\" :title=\"item.equipment_group\">\n                    {{ item.equipment_group }}\n                  </div>\n                  <div class=\"col-2\" :title=\"item.equipment_location\">\n                    {{ item.equipment_location }}\n                  </div>\n                  <div class=\"col-2\">{{ item.duration1 }}</div>\n                  <div class=\"col-1\">{{ item.roomNumber }}</div>\n                  <div class=\"col-1\">{{ item.status }}</div>\n                </div>\n              </template>\n              <template v-else>\n                <div class=\"empty-message\">暂无预约记录</div>\n              </template>\n            </div>\n            <!-- 分页组件 -->\n            <div class=\"pagination-container\">\n              <el-pagination :current-page=\"currentPage\" :page-size=\"pageSize\" :page-sizes=\"pageSizes\" :total=\"total\"\n                @size-change=\"handleSizeChange\" @current-change=\"handleCurrentChange\"\n                layout=\"total, sizes, prev, pager, next, jumper\" background />\n            </div>\n          </div>\n        </div>\n      </div>\n      <!-- 右侧内容 -->\n\n      <div class=\"right-panel\" :class=\"{\n        'right-panel-active': showdh,\n        'no-animation': noAnimation,\n        'right-panel-active1': showdh1,\n      }\">\n        <Title1 class=\"rtitle\" tit=\"今日预约\">\n          <div class=\"boxswq\" @click=\"showLargeTable\">\n            <div class=\"titleimgs\">\n              <div class=\"bgu\">\n                <div>预约仪器数</div>\n                <div>{{ yytotal }}</div>\n              </div>\n              <div class=\"bgu1\">\n                <div>预约总数</div>\n                <div>{{ todayReservations.length }}</div>\n              </div>\n            </div>\n            <div class=\"titless\">\n              <div class=\"item1\">仪器名</div>\n              <div class=\"item1\">预约时长</div>\n              <div class=\"item\">预约人</div>\n              <div class=\"item1\">预约状态</div>\n            </div>\n            <div class=\"titlesscontents\" v-loading=\"isLoading\" element-loading-text=\"加载中...\"\n              element-loading-spinner=\"el-icon-loading\" element-loading-background=\"rgba(0, 0, 0, 0.8)\">\n              <div class=\"contents\" v-for=\"item in todayReservations\" :key=\"item\">\n                <div class=\"item1\" :title=\"item.name\">{{ item.name }}</div>\n                <div class=\"item1\">{{ item.duration }}</div>\n                <div class=\"item\">{{ item.roomNumber }}</div>\n                <div class=\"item1\">{{ item.status }}</div>\n              </div>\n              <div v-if=\"!todayReservations.length\" class=\"empty-message\">\n                暂无预约记录\n              </div>\n            </div>\n          </div>\n        </Title1>\n\n        <Title1 class=\"rtitle\" tit=\"仪器状态\">\n          <div class=\"huangxing\">\n            <SystemDete></SystemDete>\n            <!-- <SystemDete></SystemDete> -->\n          </div>\n        </Title1>\n        <Title1 class=\"rtitle\" tit=\"异常跟踪处理\" :isshow=\"true\">\n          <div class=\"boxxxs\" @click=\"openbj()\">\n            <div class=\"ql-center\">\n              <div class=\"ql-Box\">\n                <div class=\"ql-box\">\n                  <div class=\"left_ql\">\n                    <div class=\"yuan status\" style=\"color: #5c9dee\"></div>\n                    <div class=\"pp\">未修复</div>\n                  </div>\n                </div>\n                <div class=\"ql-box1\" style=\"color: #b93851\">\n                  {{ unfixedCount }}\n                </div>\n              </div>\n              <div class=\"ql-Box\">\n                <div class=\"ql-box\">\n                  <div class=\"left_ql\">\n                    <div class=\"yuan1 status\" style=\"color: #89f6c1\"></div>\n                    <div class=\"pp\">已修复</div>\n                  </div>\n                </div>\n                <div class=\"ql-box1 status\" style=\"color: #89f6c1\">\n                  {{ fixedCount }}\n                </div>\n              </div>\n            </div>\n            <div class=\"unfixed-warnings\">\n              <!-- 未修复警告列表 -->\n              <div v-for=\"(warning, index) in warningData.unfixed\" :key=\"'unfixed-' + index\" class=\"warning12\">\n                <div class=\"info\">\n                  <div>\n                    <div class=\"zongduan\">\n                      <div class=\"yuan\" style=\"background-color: #b93851\"></div>\n                      <div class=\"cjhulizhong\" style=\"color: #b93851\">\n                        未修复\n                      </div>\n                    </div>\n                    <p class=\"info2\">{{ warning.warningCategory }}</p>\n                  </div>\n\n                  <div class=\"info1\">\n                    <p class=\"time\">{{ formatDate(warning.createdAt) }}</p>\n                    <p class=\"location\">{{ warning.errMsg }}</p>\n                  </div>\n                  <p class=\"info2\">\n                    {{ warning.deviceName }}\n                  </p>\n                </div>\n              </div>\n            </div>\n            <!-- 已修复警告列表 -->\n            <!-- <div v-for=\"(warning, index) in warningData.fixed\" :key=\"'fixed-'+index\" class=\"warning12\">\n              <div class=\"info\">\n                <div class=\"zongduan\">\n                  <div class=\"yuan\" style=\"background-color: #64f8bb\"></div>\n                  <div class=\"cjhulizhong\" style=\"color: #64f8bb\">已处理</div>\n                </div>\n                <div class=\"info1\">\n                  <p class=\"time\">{{ formatDate(warning.createdAt) }}</p>\n                  <p class=\"location\">{{ warning.errMsg }}</p>\n                </div>\n                <p class=\"info2\" style=\"color: #64f8bb\" @click=\"openbj()\">{{ warning.deviceName }}</p>\n              </div>\n            </div> -->\n          </div>\n        </Title1>\n      </div>\n    </div>\n    <table2 @close=\"closetan\" v-if=\"opentable2\"></table2>\n    <!-- <table-2 class=\"table2\" @close=\"closetan\" v-if=\"opentable2\"></table-2> -->\n    <!-- <div\n      class=\"center_container\"\n      :class=\"{\n        'right-panel-active11': showdh,\n        'no-animation': noAnimation,\n        'right-panel-active12': showdh1,\n      }\"\n    >\n      <img\n        class=\"btn\"\n        src=\"../assets/image/shang.png\"\n        @click=\"scrollUp\"\n        alt=\"向上\"\n      />\n      <div class=\"content\" ref=\"content\">\n        <div\n          :class=\"activef == index ? 'itema' : 'item'\"\n          v-for=\"(item, index) in resItems\"\n          :key=\"index\"\n          @click=\"switchactivef(item, index)\"\n          @mouseover=\"hoveredRoom = item\"\n          @mouseleave=\"hoveredRoom = null\"\n        >\n          {{\n            index === 0\n              ? title + \"F-\" + \"整体\"\n              : title + \"F-\" + (index < 10 ? \"10\" + index : \"1\" + index)\n          }}\n\n          <div class=\"tooltip\" v-if=\"hoveredRoom === item\">{{ item.name }}</div>\n        </div>\n      </div>\n      <img\n        class=\"btn\"\n        src=\"../assets/image/xia.png\"\n        @click=\"scrollDown\"\n        alt=\"向下\"\n      />\n    </div>\n    <div\n      @click=\"returnhome()\"\n      class=\"return\"\n      :class=\"{\n        'right-panel-active11': showdh,\n        'no-animation': noAnimation,\n        'right-panel-active12': showdh1,\n      }\"\n    >\n      返回\n    </div> -->\n  </div>\n</template>\n\n<script>\n// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）\n// 例如：import 《组件名称》 from '《组件路径》';\nimport huanxing from \"@/components/echarts/huanxing.vue\";\nimport zhexian from \"@/components/echarts/zhexian.vue\";\nimport zhexian1 from \"@/components/echarts/zhexian1.vue\";\nimport SystemDete from \"@/components/echarts/SystemDete.vue\";\nimport echarts1 from \"@/components/echarts/bingjifang/echarts3.vue\";\nimport echarts2 from \"@/components/echarts/bingjifang/echarts5.vue\";\nimport table2 from \"@/components/common/table2.vue\";\nimport shuangxiang from \"@/components/echarts/shuangxiang.vue\";\nimport shebei from \"@/views/shebei.vue\";\nimport { resourceDeviceList } from \"@/api/admin.js\";\nimport biao1 from \"../components/echarts/biao1.vue\";\nimport biao1ss from \"../components/echarts/biao1ss.vue\";\nimport axios from \"axios\";\nimport {\n  getDeviceData,\n  getDevicedetails,\n  getDeviceWarningList,\n} from \"@/api/device.js\";\n\n// resourceDeviceList\nexport default {\n  // import引入的组件需要注入到对象中才能使用\n  components: {\n    table2,\n    huanxing,\n    zhexian,\n    zhexian1,\n    SystemDete,\n    echarts1,\n    echarts2,\n    shuangxiang,\n    shebei,\n    biao1ss,\n    biao1,\n  },\n  props: [\"title\", \"resItems\"],\n  data() {\n    // 这里存放数据\n    return {\n      jlURL,\n      dstime,\n      responseData: null, // 存储返回的数据\n      error: null, // 存储错误信息\n      todayReservations: [], // 今日预约数据\n      allReservations: [], // 所有预约数据\n      allTableData: [], // 存储所有数据\n      allTableData1: [], // 存储所有数据\n      currentPage: 1, // 当前页码\n      pageSize: 10, // 每页显示条数\n      total: 0, // 总数据条数\n      yytotal: 0,\n      pageSizes: [10, 20, 50, 100], // 每页显示条数选项\n      opentable2: false,\n      hoveredRoom: null,\n      scrollPosition: 0,\n      flag: true,\n      localtitle: \"\",\n      dianlist: [\n        {\n          name: \"总用地面积\",\n          value: \"57874.1㎡\",\n          img: require(\"../assets/image/ri.png\"),\n        },\n        {\n          name: \"总建筑面积\",\n          value: \"7802.54㎡\",\n          img: require(\"../assets/image/zhou.png\"),\n        },\n        {\n          name: \"地上建筑面积\",\n          value: \"14085㎡\",\n          img: require(\"../assets/image/yue.png\"),\n        },\n      ],\n      tablelist: [\n        {\n          name: \"总用地面积\",\n          value: \"4423.8㎡\",\n          img: require(\"../assets/image/mianji1.png\"),\n        },\n        {\n          name: \"总建筑面积\",\n          value: \"16845㎡\",\n          img: require(\"../assets/image/mianji2.png\"),\n        },\n        {\n          name: \"地上建筑面积\",\n          value: \"14085㎡\",\n          img: require(\"../assets/image/mianji3.png\"),\n        },\n        {\n          name: \"地下建筑面积\",\n          value: \"2760㎡\",\n          img: require(\"../assets/image/mianji4.png\"),\n        },\n      ],\n      wenzilist: [\n        {\n          name: \"平台概述\",\n          value: \"天津大学大型仪器平台是天津大学批准设立的校级公共技术服务平台，聚焦兼顾多学科需求的保障学校基础能力，促进跨平台和交叉新兴学科能力的建设,以'专管共用'的管理模式，为科学研究提供高质量的开放式测试服务，开展仪器设备创新性功能开发与技术研发。\",\n        },\n      ],\n\n      activef: 0,\n      isshow: true,\n      isactive: 0,\n      tabledata: [],\n      zengtiimg: \"\",\n      lrdata: [\n        {\n          title1: \"温度\",\n          title2: \"22℃\",\n          title3: \"2022-04-01 12:00:00\",\n        },\n      ],\n      deviceTypes: \"CQQ11\",\n      activeTab: \"today\",\n      botlist: [\n        { name: \"总览\", code: \"\" },\n        { name: \"设备列表\", code: \"\" },\n        {\n          name: \"环境温湿度\",\n          code: \"CGQ11\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/wensiduchuanganqi.png\",\n        },\n        {\n          name: \"防爆温湿度\",\n          code: \"CGQ10\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/fangbao.png\",\n        },\n        {\n          name: \"冰箱状态\",\n          code: \"LRY193\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/bingxiang.png\",\n        },\n        {\n          name: \"培养箱状态\",\n          code: \"CGQ13\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/bingxiang.png\",\n        },\n        {\n          name: \"乙炔气体\",\n          code: \"CGQ7\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/fangbao.png\",\n        },\n        {\n          name: \"环境CO2\",\n          code: \"CGQ9\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/huanjingco2.png\",\n        },\n        {\n          name: \"环境O2\",\n          code: \"CGQ3\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/huanjingco2.png\",\n        },\n        {\n          name: \"甲烷气体\",\n          code: \"CGQ8\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/jiawan.png\",\n        },\n        {\n          name: \"房间压差\",\n          code: \"CGQ2\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/kqyc.png\",\n        },\n      ],\n      listst: [\n        {\n          name: \"广东质检中诚认证有限公司到中广...\",\n        },\n        { name: \"材料科学、化学工程及医药研发成...\" },\n        { name: \"酒精代谢能力检测：您的酒量，基...\" },\n        { name: \"植酸检测方法及作用\" },\n        { name: \"兔拉检测：行业中芯片造假技术...\" },\n        { name: \"酒精代谢能力检测：您的酒量，基...\" },\n      ],\n      showdh: true,\n      showdh1: false,\n      noAnimation: false,\n      localTitle: this.title, // 初始化本地数据属性\n      nhlist: [\n        {\n          title: \"供气压力\",\n          status: \"0.3Mpa\",\n          unit: \"℃\",\n        },\n\n        {\n          title: \"供气流量\",\n          status: \"6M3/min\",\n          unit: \"㎡\",\n        },\n        {\n          title: \"露点温度\",\n          status: \"6℃\",\n          unit: \"℃\",\n        },\n        {\n          title: \"含氧量\",\n          status: \"6PPM\",\n          unit: \"㎡\",\n        },\n      ],\n      warnlist1: [\n        {\n          type: 1,\n          time: \"视频监控报警-3号楼-3F-101\",\n          value: \"\",\n          name: \"2024-06-16   12:34:09\",\n        },\n        {\n          type: 2,\n          time: \"视频监控报警-3号楼-3F-101\",\n          value: \"\",\n          name: \"2024-06-16   12:34:09\",\n        },\n        {\n          type: 3,\n          name: \"2024-06-16   12:34:09\",\n          value: \"\",\n          time: \"视频监控报警-3号楼-3F-101\",\n        },\n      ],\n      isButton2Active: false,\n      status: \"巡检中\",\n      status1: \"已完成\",\n      status2: \"待巡检\",\n      selectedIndex: 0,\n      componentTag: \"\",\n      dectid: \"\",\n      showPopup: false,\n      isLoading: false, // 今日预约加载状态\n      isAllLoading: false, // 全部预约加载状态\n      warningData: {\n        unfixed: [],\n        fixed: [],\n        unfixedtotal: 0,\n        fixedtotal: 0,\n      },\n      baseURL: \"https://tjdx.yuankong.org.cn\",\n      token: localStorage.getItem(\"token\") || \"\",\n      warningStats: [],\n    };\n  },\n  // 计算属性类似于data概念\n  computed: {\n    formattedTitle() {\n      return {\n        title: `${this.localTitle}F实验室介绍`,\n        img: require(`../assets/img/floor/1Fbig.png`),\n      };\n    },\n    formattedTitle1() {\n      return `${this.localTitle}F实验室总览`;\n    },\n    formattedTitle2() {\n      return `实验室${this.localTitle}F环境信息`;\n    },\n    formattedTitle3() {\n      return `实验室${this.localTitle}F设备信息`;\n    },\n    formattedTitle4() {\n      return `实验室${this.localTitle}F事件详情`;\n    },\n    formatted1Title() {\n      return {\n        title: `${this.localTitle}实验室介绍`,\n        img: require(`../assets/img/floor/${this.title}Fbig.png`),\n      };\n    },\n    formatted1Title1() {\n      return `${this.localTitle}实验室总览`;\n    },\n    formatted1Title2() {\n      return `${this.localTitle}环境信息`;\n    },\n    formatted1Title3() {\n      return `${this.localTitle}设备信息`;\n    },\n    formatted1Title4() {\n      return `${this.localTitle}事件详情`;\n    },\n    unfixedCount() {\n      return this.warningData.unfixedtotal;\n    },\n    fixedCount() {\n      return this.warningData.fixedtotal;\n    },\n  },\n  // 监控data中的数据变化\n  watch: {\n    title(newVal) {\n      this.localTitle = newVal;\n    },\n    resItems(newVal) {\n      console.log(newVal);\n\n      // this.resItems = newVal;\n    },\n  },\n  // 方法集合\n  methods: {\n    // 获取当前日期的00:00:01的时间戳\n    getStartOfDayTimestamp() {\n      const now = new Date();\n      now.setHours(0, 0, 1, 0); // 设置时间为当天的 00:00:01\n      return Math.floor(now.getTime() / 1000); // 转换为 Unix 时间戳（秒）\n    },\n\n    // 获取当前日期的23:59:59的时间戳\n    getEndOfDayTimestamp() {\n      const now = new Date();\n      now.setHours(23, 59, 59, 0);\n      return Math.floor(now.getTime() / 1000);\n    },\n\n    // 格式化预约数据\n    formatReservationData(item) {\n      return {\n        name: item.equipment_name,\n        date: new Date(item.start * 1000).toLocaleDateString(),\n        date1: new Date(item.start * 1000).toLocaleString(),\n        duration: `${new Date(item.start * 1000).getHours()}:00-${new Date(\n          item.end * 1000\n        ).getHours()}:00`,\n        duration1: `${new Date(\n          item.start * 1000\n        ).toLocaleDateString()} ${new Date(\n          item.start * 1000\n        ).getHours()}:00 - ${new Date(\n          item.end * 1000\n        ).toLocaleDateString()} ${new Date(item.end * 1000).getHours()}:00`,\n        roomNumber: item.user_name,\n        status: item.is_using === \"1\" ? \"已预约\" : \"已预约\",\n        equipment_group: item.equipment_group || \"未设置\", // 添加组织机构字段\n        equipment_location: item.equipment_location || \"未设置\", // 添加仪器位置字段\n      };\n    },\n\n    // 获取今日预约数据\n    async fetchTodayReservations() {\n      this.isLoading = true;\n      const headers = {\n        clientid: \"5a298e93-158d-4e22-83cf-6ceb62e9b4f1\",\n        clientsecret: \"2c8ec39e-9887-482a-b28b-e64c496b601c\",\n      };\n\n      const requestBody = {\n        method: \"gpui/eq_reserv/reservList\",\n        params: {\n          dtstart: this.getStartOfDayTimestamp(),\n          dtend: this.getEndOfDayTimestamp(),\n          params: {\n            location: \"58\",\n            limit: [0, 10000],\n          },\n        },\n      };\n\n      try {\n        const response = await axios.post(\n          jlURL,\n          requestBody,\n          { headers }\n        );\n\n        this.todayReservations = response.data.response.map(\n          this.formatReservationData\n        );\n        // 使用去重后的仪器个数\n        this.$nextTick(() => {\n          this.yytotal = this.uniqueEquipmentCount;\n        });\n      } catch (err) {\n        console.error(\"获取今日预约数据失败:\", err);\n        this.error = err;\n      } finally {\n        this.isLoading = false;\n      }\n    },\n\n    // 获取所有预约数据\n    async fetchAllReservations() {\n      this.isAllLoading = true;\n      const headers = {\n        clientid: \"5a298e93-158d-4e22-83cf-6ceb62e9b4f1\",\n        clientsecret: \"2c8ec39e-9887-482a-b28b-e64c496b601c\",\n      };\n\n      const requestBody = {\n        method: \"gpui/eq_reserv/reservList\",\n        params: {\n          dtstart: 1704844800,\n          dtend: this.getEndOfDayTimestamp(),\n          params: {\n            location: \"58\",\n            limit: [0, 10000],\n          },\n        },\n      };\n\n      try {\n        const response = await axios.post(\n          jlURL,\n          requestBody,\n          { headers }\n        );\n\n        // 对数据进行时间倒序排序\n        const sortedData = response.data.response.sort(\n          (a, b) => b.start - a.start\n        );\n        this.allReservations = sortedData.map(this.formatReservationData);\n        this.total = this.allReservations.length;\n        this.handleCurrentChange(1);\n      } catch (err) {\n        console.error(\"获取所有预约数据失败:\", err);\n        this.error = err;\n      } finally {\n        this.isAllLoading = false;\n      }\n    },\n\n    // 显示大表格\n    async showLargeTable() {\n      this.showPopup = true;\n      // 只在没有数据或数据过期的情况下重新获取\n      if (!this.allReservations.length) {\n        await this.fetchAllReservations();\n      } else {\n        // 如果已有数据，直接更新分页\n        this.handleCurrentChange(1);\n      }\n    },\n\n    closetan() {\n      this.opentable2 = false;\n    },\n    openbj() {\n      this.opentable2 = true;\n    },\n    handleOpenDialog() {\n      console.log(1111);\n      this.$emit(\"open-bj\");\n    },\n    scrollUp() {\n      const content = this.$refs.content;\n      content.scrollTop -= 38; // 每次向上滑动25px\n    },\n    scrollDown() {\n      const content = this.$refs.content;\n      content.scrollTop += 38; // 每次向下滑动25px\n    },\n    returnhome() {\n      this.$emit(\"returnhome\");\n    },\n    async switchactivef(item, index) {\n      this.dectid = item.id;\n      const res = await resourceDeviceList({\n        resourceId: item.id,\n        deviceTypes: this.deviceTypes,\n      });\n\n      this.tabledata = res.data;\n\n      if (index) {\n        this.flag = false;\n        this.localTitle = item.roomid;\n      } else {\n        this.localTitle = this.title;\n        this.flag = true;\n      }\n      console.log(item);\n      this.activef = index;\n      // this.$emit(\"childEvent\", title, index);\n    },\n    slideUp() {\n      const contentHeight = this.$refs.content.scrollHeight;\n      if (this.position > -contentHeight + this.containerHeight) {\n        this.position -= this.step;\n        this.$refs.content.style.transform = `translateY(${this.position}px)`;\n      }\n    },\n    slideDown() {\n      if (this.position < 0) {\n        this.position += this.step;\n        this.$refs.content.style.transform = `translateY(${this.position}px)`;\n      }\n    },\n\n    //  this.dectid = item.id;\n    //     const res = await resourceDeviceList({\n    //       resourceId: item.id,\n    //       deviceTypes: this.deviceTypes,\n    //     });\n    //     console.log(res.data, \"qilei\");\n    //     this.tabledata = res.data;\n\n    async switchTab1(item, index) {\n      console.log(item.img);\n      this.zengtiimg = item.img;\n\n      this.deviceTypes = item.code;\n      const res = await resourceDeviceList({\n        resourceId: this.dectid,\n        deviceTypes: this.deviceTypes,\n      });\n\n      this.tabledata = res.data;\n\n      // this.switchactivef(item, item.code);\n      this.isactive = index;\n      if (index) {\n        this.componentTag = \"shebei\";\n        this.isshow = false;\n        this.showdh = true;\n        this.showdh1 = false;\n      } else {\n        this.componentTag = \"\";\n        this.isshow = true;\n        this.showdh = false;\n        this.showdh1 = true;\n      }\n    },\n    switchTab(tab) {\n      this.activeTab = tab;\n    },\n    qeihuan(index) {\n      console.log(index, \"123123\");\n    },\n\n    qiehuanyans(index) {\n      console.log(index, \"123123\");\n      this.currentIndex = index;\n    },\n    getClassForStatus(status) {\n      if (status === \"告警总数\") {\n        return \"completed\";\n      } else if (status === \"处理完\") {\n        return \"incomplete\";\n      } else if (status === \"未处理\") {\n        return \"warning\";\n      } else {\n        return \"default\"; // 没有匹配的状态时可以添加一个默认类\n      }\n    },\n    getClassForStatuss(status) {\n      if (status === \"告警总数\") {\n        return \"completeds\";\n      } else if (status === \"处理完\") {\n        return \"incompletes\";\n      } else if (status === \"未处理\") {\n        return \"warnings\";\n      } else {\n        return \"default\"; // 没有匹配的状态时可以添加一个默认类\n      }\n    },\n    qiehuanyans(index) {\n      console.log(index, \"123123\");\n      this.currentIndex = index;\n    },\n    oc(value) {\n      console.log(value, \"floor收到的值\");\n      this.showdh = value;\n    },\n    getClassForStatus(status) {\n      if (status === \"巡检中\") {\n        return \"completed\";\n      } else if (status === \"待巡检\") {\n        return \"incomplete\";\n      } else if (status === \"已完成\") {\n        return \"warning\";\n      } else {\n        return \"default\"; // 没有匹配的状态时可以添加一个默认类\n      }\n    },\n    getClassForStatuss(status) {\n      if (status === \"巡检中\") {\n        return \"completeds\";\n      } else if (status === \"待巡检\") {\n        return \"incompletes\";\n      } else if (status === \"已完成\") {\n        return \"warnings\";\n      } else {\n        return \"default\"; // 没有匹配的状态时可以添加一个默认类\n      }\n    },\n    closePopup() {\n      this.showPopup = false;\n    },\n    // 处理页码改变\n    handleCurrentChange(page) {\n      this.currentPage = page;\n      const start = (page - 1) * this.pageSize;\n      const end = start + this.pageSize;\n      this.tableDatass = this.allReservations.slice(start, end);\n      console.log(this.tableDatass, \"tableDatass\");\n    },\n\n    // 处理每页显示条数改变\n    handleSizeChange(size) {\n      this.pageSize = size;\n      this.handleCurrentChange(1);\n    },\n    async getWarningList(hasFixed) {\n      try {\n        // 在关键请求前检查token是否需要刷新\n        if (this.$auth && this.$auth.checkAndRefreshToken) {\n          await this.$auth.checkAndRefreshToken();\n        }\n\n        const response = await getDeviceWarningList({\n          hasFixed: hasFixed,\n        });\n\n        if (response.code === 200) {\n          if (hasFixed === \"N\") {\n            this.warningData.unfixed = response.rows;\n            this.warningData.unfixedtotal = response.total;\n          } else {\n            this.warningData.fixed = response.rows;\n            this.warningData.fixedtotal = response.total;\n          }\n        } else {\n          console.error(\"获取警告数据失败:\", response.msg);\n          // 只有在明确的认证错误时才清除token并跳转\n          if (response.code === 401) {\n            localStorage.removeItem(\"token\");\n            this.$router.push(\"/\");\n          }\n        }\n      } catch (error) {\n        console.error(\"请求警告数据出错:\", error);\n        // 请求错误时不要立即清除token，让拦截器处理\n      }\n    },\n    async fetchAllWarningData() {\n      await Promise.all([this.getWarningList(\"N\"), this.getWarningList(\"Y\")]);\n    },\n    formatDate(timestamp) {\n      const date = new Date(timestamp);\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(\n        2,\n        \"0\"\n      )}-${String(date.getDate()).padStart(2, \"0\")} ${String(\n        date.getHours()\n      ).padStart(2, \"0\")}:${String(date.getMinutes()).padStart(2, \"0\")}`;\n    },\n    async fetchWarningStats() {\n      try {\n        const res = await getDeviceWarningList({\n          pageSize: 9999,\n          currentPage: 1,\n          hasFixed: \"N\",\n        });\n\n        if (res.code === 200 && res.rows) {\n          // 定义所有可能的报警类型及其阈值（简化后的名称）\n          const allWarningTypes = {\n            压力: 14,\n            氧气: 48,\n            温度: 67,\n            湿度: 67,\n            // '气体泄漏': 50\n          };\n\n          // 统计各类型报警数量\n          const stats = {};\n          // 初始化所有报警类型的计数为0\n          Object.keys(allWarningTypes).forEach((type) => {\n            stats[type] = {\n              total: 0,\n              unresolved: 0,\n            };\n          });\n\n          // 统计实际数据（使用包含匹配）\n          res.rows.forEach((item) => {\n            // 查找匹配的报警类型（只要包含关键字就匹配）\n            const matchedType = Object.keys(allWarningTypes).find(type =>\n              item.warningCategory && item.warningCategory.includes(type)\n            );\n\n            if (matchedType) {\n              stats[matchedType].total++;\n              if (item.status === \"N\") {\n                stats[matchedType].unresolved++;\n              }\n            }\n          });\n\n          // 转换为图表所需格式\n          this.warningStats = Object.entries(stats).map(\n            ([category, count]) => ({\n              label: `${category}(${count.total}/${allWarningTypes[category]})`,\n              value: count.total,\n            })\n          );\n\n          console.log(this.warningStats, \"报警统计数据\");\n        }\n      } catch (error) {\n        console.error(\"获取报警统计数据失败:\", error);\n      }\n    },\n  },\n  // 生命周期 - 创建完成（可以访问当前this实例）\n  created() {\n    this.fetchAllWarningData();\n    // 每5分钟刷新一次数据\n    setInterval(this.fetchAllWarningData, 5 * 60 * 1000);\n  },\n  // 生命周期 - 挂载完成（可以访问DOM元素）\n  mounted() {\n    this.fetchTodayReservations();\n    this.showdh1 = true;\n    setTimeout(() => {\n      this.showdh1 = false;\n      this.noAnimation = false;\n    }, 1000);\n\n    // 定时刷新今日预约数据\n    setInterval(() => {\n      this.fetchTodayReservations();\n    }, 1000 * this.dstime);\n    ue.interface.setSliderValue = (value) => {\n      console.log(value, \"ue点击拿到的值\");\n      if (!isNaN(Number(value.data))) {\n        // let did = value.data; // 如果是数字，则赋值\n        // const result = this.sblist.filter(item => item.id == did);\n        // this.deviceId = result[0].deviceId\n        // console.log(this.deviceId, 'ue点击拿到的id');\n      }\n      // this.deid = JSON.parse(value.data) - 43846\n      // console.log(this.deid);\n      // if (!isNaN(parseInt(value.data, 10))) {\n      //   var dtdata1 = JSON.parse(JSON.stringify(this.dtdata))\n      //   console.log(dtdata1);\n      //   this.showdet = false\n      //   // this.did = dtdata1.find(item => item.id == value.data)?.deviceid;\n      //   // console.log(this.did);\n      //   var didata = JSON.parse(dtdata1.find(item => item.id == value.data).channelCode);\n      //   let data1 = dtdata1.find(item => item.id == value.data)\n      //   // this.details = didata\n      //   this.bid = data1.bid\n      //   this.fid = data1.fid\n      //   // this.hlsurl\n      //   // this.bm = data1.note\n      //   console.log(data1, 1111111);\n      //   // this.getCameraData(did)\n      // }\n    };\n    this.fetchWarningStats();\n    // 每30秒更新一次数据\n    setInterval(() => {\n      this.fetchWarningStats();\n    }, 30000);\n  },\n  beforeCreate() { }, // 生命周期 - 创建之前\n  beforeMount() { }, // 生命周期 - 挂载之前\n  beforeUpdate() { }, // 生命周期 - 更新之前\n  updated() { }, // 生命周期 - 更新之后\n  beforeUnmount() {\n    // 在组件销毁之前清除定时器\n    console.log(1111);\n  },\n\n  unmounted() {\n    console.log(2222);\n  }, // 生命周期 - 销毁之前\n  destroyed() {\n    console.log(1221);\n  }, // 生命周期 - 销毁完成\n  activated() { }, // 如果页面有keep-alive缓存功能，这个函数会触发\n};\n</script>\n<style lang=\"less\" scoped>\n.table2 {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  z-index: 99999;\n}\n\n.return {\n  position: fixed;\n  right: 373px;\n  top: 100px;\n  height: 44px;\n  width: 46px;\n  // overflow: hidden;\n  transform: translate(720%);\n  transition: transform 0.5s ease-in-out;\n\n  z-index: 999;\n  cursor: pointer;\n  text-align: center;\n  line-height: 67px;\n  font-family: Source Han Sans SC;\n  font-weight: 400;\n  font-size: 11px;\n  color: #ffffff;\n  background: url(\"../assets/image/return.png\");\n  background-size: 100% 100%;\n}\n\n.center_container {\n  position: fixed;\n  right: 359px;\n  top: 352px;\n  height: 401px;\n  width: 70px;\n  // overflow: hidden;\n  transform: translate(470%);\n  transition: transform 0.5s ease-in-out;\n  z-index: 999;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  background: url(\"../assets/image/louceng.png\");\n  background-size: 100% 100%;\n\n  .content::-webkit-scrollbar {\n    width: 0px;\n    display: none;\n    /* 设置滚动条的宽度 */\n  }\n\n  /* 设置滚动条轨道的样式 */\n  .content::-webkit-scrollbar-track {\n    background-color: #f1f1f1;\n    /* 设置滚动条轨道的背景色 */\n  }\n\n  /* 设置滚动条滑块的样式 */\n  .content::-webkit-scrollbar-thumb {\n    background-color: #888;\n    /* 设置滚动条滑块的背景色 */\n  }\n\n  /* 鼠标悬停在滚动条上时的样式 */\n  .content::-webkit-scrollbar-thumb:hover {\n    background-color: #555;\n    /* 设置鼠标悬停时滚动条滑块的背景色 */\n  }\n\n  .content {\n    height: 330px;\n    /* 内容区的总高度，视实际内容而定 */\n    transition: transform 0.5s ease;\n    overflow-y: auto;\n    text-align: center;\n\n    /* 设置滚动条的样式 */\n\n    .item {\n      cursor: pointer;\n      width: 75px;\n      height: 25px;\n      font-family: YouSheBiaoTiHei;\n      font-weight: 400;\n      font-size: 14px;\n      color: #86a6b7;\n      line-height: 25px;\n      margin-top: 12px;\n    }\n\n    .itema {\n      background: url(\"../assets/image/lcactive.png\");\n      background-size: 100% 100%;\n      cursor: pointer;\n      width: 66px;\n      height: 25px;\n      font-family: YouSheBiaoTiHei;\n      font-weight: 400;\n      font-size: 14px;\n      color: #ffffff;\n      line-height: 25px;\n      margin-top: 12px;\n    }\n\n    .tooltip {\n      position: absolute;\n      left: 80%;\n      // top: 15px;\n      background-color: #1a3867;\n      border: 1px solid #7ba6eb;\n      color: #fff;\n      padding: 5px;\n      z-index: 1;\n      white-space: nowrap;\n      font-size: 12px;\n      visibility: hidden;\n\n      opacity: 0;\n      transition: opacity 0.5s, visibility 0.5s;\n      z-index: 999;\n      font-family: Source Han Sans SC;\n    }\n\n    .item:hover .tooltip {\n      visibility: visible;\n      /* 当鼠标悬停时显示 */\n      opacity: 1;\n    }\n\n    .itema:hover .tooltip {\n      visibility: visible;\n      /* 当鼠标悬停时显示 */\n      opacity: 1;\n    }\n  }\n}\n\n.btn {\n  margin-top: 13px;\n  width: 27px;\n  height: 14px;\n  cursor: pointer;\n}\n\n.echart2 {\n  height: 180px;\n}\n\n.bott {\n  position: fixed;\n  z-index: 1;\n  bottom: 4px;\n  // left: 6px;\n  width: 1920px;\n  height: 50px;\n  display: flex;\n  flex-direction: row;\n  cursor: pointer;\n  text-align: center;\n\n  .bottit {\n    width: 153px;\n    height: 45px;\n    background: url(\"../assets/image/bot_b.png\");\n    background-size: 100% 100%;\n    margin-left: 19.496px;\n    font-family: Source Han Sans SC;\n    font-weight: bold;\n    font-size: 17px;\n    color: #ffffff;\n    line-height: 43px;\n    cursor: pointer;\n  }\n\n  .bottit1 {\n    width: 153px;\n    height: 69px;\n    background: url(\"../assets/image/bot_a.png\");\n    background-size: 100% 100%;\n    margin-left: 19.496px;\n    font-family: Source Han Sans SC;\n    font-weight: bold;\n    font-size: 17px;\n    color: #ffffff;\n    line-height: 87px;\n    cursor: pointer;\n    margin-top: -23px;\n  }\n}\n\n.container {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: space-between;\n  align-items: stretch;\n  height: 1080px;\n  text-align: center;\n\n  .left-panel {\n    position: fixed;\n    z-index: 1;\n    top: 75px;\n    left: 22px;\n    width: 387px;\n    height: 937px;\n    background-size: 100% 100%;\n    transform: translate(-122%);\n    transition: transform 0.5s ease-in-out;\n\n    .box {\n      // margin-top: 6px;\n      margin-bottom: 18px;\n      // background: url(\"../assets/image/zuoshang1.png\");\n      background-size: 100% 100%;\n      background-repeat: no-repeat;\n\n      // width: 330px;\n      // height: 404px;\n\n      .loudong {\n        margin-top: 21px;\n        margin-bottom: 25px;\n        width: 296px;\n        height: 178px;\n      }\n\n      .wenzi {\n        font-family: Microsoft YaHei;\n        font-weight: 400;\n        font-size: 10px;\n        color: #bdecf9;\n        text-align: left;\n        margin-left: 20px;\n        margin-right: 20px;\n      }\n\n      .p {\n        text-indent: 2em;\n        margin-bottom: 1em;\n        letter-spacing: 0.05em;\n      }\n    }\n  }\n\n  .left-panel-active {\n    transform: translate(0%);\n  }\n\n  .left-panel-active1 {\n    // transform: translate(0%);\n    animation: slideOut 1s ease-in-out forwards;\n  }\n\n  @keyframes slideOut {\n    100% {\n      transform: translateX(0%);\n    }\n\n    // 85% {\n    //   transform: translateX(-25%);\n    // }\n\n    // 65% {\n    //   transform: translateX(-15%);\n    // }\n\n    // 40% {\n    //   transform: translateX(-55%);\n    // }\n\n    // 30% {\n    //   transform: translateX(-40%);\n    // }\n\n    0% {\n      transform: translateX(-100%);\n    }\n  }\n\n  .rtitle {\n    margin-top: 16px;\n  }\n\n  .ltitle1 {\n    margin-top: 16px;\n  }\n\n  .right-panel {\n    position: fixed;\n    z-index: 1;\n    right: 22px;\n    width: 387px;\n    top: 75px;\n    height: 937px;\n\n    background-size: 100% 100%;\n    transform: translate(122%);\n    transition: transform 0.5s ease-in-out;\n\n    .box {\n      // margin-top: 6px;\n      margin-bottom: 18px;\n      // background: url(\"../assets/image/zuoshang1.png\");\n      background-size: 100% 100%;\n      background-repeat: no-repeat;\n      font-family: Source Han Sans SC;\n      font-weight: 400;\n      font-size: 12px;\n      color: #ffffff;\n      width: 330px;\n      height: 224px;\n\n      .titlest {\n        display: flex;\n\n        // shiyansimg.png\n        .itm {\n          cursor: pointer;\n          margin: 16px 9px 0 10px;\n          background: url(\"../assets/image/shiyansimg.png\");\n          background-size: 100% 100%;\n          background-repeat: no-repeat;\n          width: 100px;\n          height: 32px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n        }\n\n        .itms {\n          background: url(\"../assets/image/xuanzexuanzhong.png\") !important;\n          background-size: 100% 100%;\n          background-repeat: no-repeat;\n          width: 100px;\n          height: 41px !important;\n          padding-bottom: 10px;\n        }\n      }\n\n      .contentss {\n        display: flex;\n        align-items: center;\n        flex-wrap: wrap;\n        justify-content: space-around;\n        align-items: center;\n\n        .itm {\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          width: 112px;\n          height: 70px;\n          background: url(\"../assets/image/wendupng.png\");\n          background-size: 100% 100%;\n          background-repeat: no-repeat;\n          font-family: DIN;\n          font-weight: bold;\n          font-size: 22px;\n          color: #ffffff;\n\n          .danwei {\n            font-family: DIN;\n            font-weight: bold;\n            font-size: 12px;\n            color: #ffffff;\n          }\n        }\n\n        .wendyu {\n          font-family: Source Han Sans SC;\n          font-weight: 400;\n          font-size: 13px;\n          color: #ffffff;\n          margin-top: -7px;\n        }\n      }\n\n      .loudong {\n        margin-top: 21px;\n        margin-bottom: 25px;\n        width: 296px;\n        height: 178px;\n      }\n    }\n\n    .boxxxs {\n      margin-left: -10px;\n      margin-top: 1px;\n      margin-bottom: 18px;\n      // background: url(\"../assets/image/zuoshang1.png\");\n      background-size: 100% 100%;\n      background-repeat: no-repeat;\n\n      width: 366px;\n      cursor: pointer;\n      // height: 254px;\n    }\n  }\n\n  .boxxx {\n    // margin-top: 6px;\n    margin-bottom: 18px;\n    // background: url(\"../assets/image/zuoshang1.png\");\n    background-size: 100% 100%;\n    background-repeat: no-repeat;\n\n    width: 350px;\n    height: 284px;\n  }\n\n  .no-animation {\n    transition: none;\n  }\n\n  .right-panel-active {\n    transform: translate(0%);\n    // animation: slideIn 1s ease-in-out ;\n  }\n\n  .right-panel-active1 {\n    // transform: translate(0%);\n    animation: slideIn 1s ease-in-out forwards;\n  }\n\n  .right-panel-active11 {\n    transform: translate(0%) !important;\n    // animation: slideIn 1s ease-in-out ;\n  }\n\n  .right-panel-active12 {\n    // transform: translate(0%);\n    animation: slideIn 1s ease-in-out forwards !important;\n  }\n\n  @keyframes slideIn {\n    0% {\n      transform: translateX(100%);\n    }\n\n    // 30% {\n    //   transform: translateX(65%);\n    // }\n\n    // 40% {\n    //   transform: translateX(40%);\n    // }\n\n    // 65% {\n    //   transform: translateX(15%);\n    // }\n\n    // 85% {\n    //   transform: translateX(25%);\n    // }\n\n    100% {\n      transform: translateX(0%);\n    }\n  }\n\n  .completed {\n    background: #7ad0ff;\n  }\n\n  .incomplete {\n    background: #ff6041;\n  }\n\n  .warning {\n    background: #00ffc0;\n  }\n\n  .completeds {\n    color: #7ad0ff;\n  }\n\n  .incompletes {\n    color: #ff6041;\n  }\n\n  .warnings {\n    color: #00ffc0;\n  }\n}\n\n.ql-center {\n  display: flex;\n  // margin-top: 20px;\n  justify-content: space-around;\n  margin-top: 4px;\n  margin-bottom: 4px;\n\n  .ql-Box {\n    width: 46%;\n    height: 49px;\n    border: 1px solid #7ad0ff;\n    // opacity: 0.6;\n    border-radius: 2px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n\n    .ql-box1 {\n      font-family: Alibaba PuHuiTi;\n      font-weight: bold;\n      font-size: 19px;\n      color: #7ad0ff;\n    }\n\n    .ql-box {\n      display: flex;\n      // padding-left: 23px;\n      padding-right: 9px;\n      flex-direction: row;\n      justify-content: space-between;\n      align-items: center;\n      // width: 100%;\n      height: 24px;\n\n      .left_ql {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        color: #ffffff;\n\n        .yuan {\n          width: 8px;\n          height: 8px;\n          border-radius: 50%;\n          background-color: #b93851;\n          margin-right: 5px;\n        }\n\n        .yuan1 {\n          width: 8px;\n          height: 8px;\n          border-radius: 50%;\n          background-color: #84edc3;\n          margin-right: 5px;\n        }\n\n        .pp {\n          margin-left: 5px;\n          color: #fff;\n          font-size: 18px;\n        }\n      }\n\n      img {\n        height: 12px;\n        width: 8px;\n      }\n    }\n  }\n}\n\n.warn1 {\n  // background: url(\"../assets/image/warnred.png\");\n}\n\n.warn2 {\n  // background: url(\"../assets/image/warnyellow.png\");\n}\n\n.warn3 {\n  // background: url(\"../assets/image/warngreen.png\");\n}\n\n.unfixed-warnings {\n  height: 180px;\n  overflow-y: auto;\n}\n\n.warning12 {\n  background-size: 100% 100%;\n  height: 47px;\n  margin-bottom: 8px;\n\n  .info {\n    display: flex;\n    justify-content: space-between;\n    align-items: flex-start;\n    text-align: left;\n    font-size: 13px;\n    padding: 8px 12px;\n    background: rgba(25, 37, 60, 0.1);\n    border-radius: 4px;\n\n    .zongduan {\n      display: flex;\n      align-items: center;\n      min-width: 80px;\n\n      .yuan {\n        width: 8px;\n        height: 8px;\n        border-radius: 50%;\n        margin-right: 6px;\n      }\n\n      .cjhulizhong {\n        font-family: Microsoft YaHei;\n        font-weight: bold;\n        font-size: 14px;\n      }\n    }\n\n    .info1 {\n      flex: 1;\n      // margin: 0 12px;\n\n      .time {\n        font-family: Microsoft YaHei;\n        font-size: 14px;\n        color: #ffffff;\n        margin-bottom: 4px;\n      }\n\n      .location {\n        font-family: Microsoft YaHei;\n        font-size: 14px;\n        color: #ffffff;\n        white-space: nowrap;\n        overflow: hidden;\n        text-overflow: ellipsis;\n      }\n    }\n\n    .info2 {\n      cursor: pointer;\n      font-size: 14px;\n      font-family: Microsoft YaHei;\n      font-weight: 400;\n      color: #b93851;\n      // white-space: nowrap;\n      margin-left: 10px;\n    }\n  }\n}\n\n.zonghe {\n  // margin-bottom: 10px;\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center;\n  justify-content: center;\n\n  .boxsty {\n    width: 50%;\n    margin-top: 12px;\n\n    .mianji {\n      display: flex;\n      align-items: center;\n\n      .img {\n        width: 50px;\n        height: 49px;\n      }\n\n      .wenzi {\n        text-align: left;\n        margin-left: 5px;\n\n        .top {\n          // margin-bottom: 9px;\n          font-family: Alibaba PuHuiTi;\n          font-weight: 500;\n          font-size: 20px;\n          color: #ffffff;\n        }\n\n        .bottom {\n          font-family: Source Han Sans SC;\n          font-weight: 500;\n          font-size: 21px;\n          color: #59ffc4;\n        }\n      }\n    }\n  }\n}\n\n.gongneng {\n  margin-top: 12px;\n\n  display: flex;\n  flex-direction: column;\n  // align-items: center;\n  // font-family: Source Han Sans SC;\n  font-family: Alibaba PuHuiTi;\n  // font-weight: bold;\n  font-size: 22px;\n  color: #59ffc4;\n  text-align: left;\n\n  .yuan {\n    margin-right: 7px;\n    width: 16px;\n    height: 16px;\n    border-radius: 50%;\n    background-color: #85fdca;\n  }\n\n  .value {\n    font-family: Alibaba PuHuiTi;\n    font-weight: 500;\n    font-size: 20px;\n    color: #fff;\n    width: 100%;\n    margin-right: 3px;\n    text-indent: 40px;\n  }\n\n  .name {\n    // width: 58px;\n    font-size: 22px;\n  }\n}\n\n.zongheqt {\n  .left1 {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    margin-right: 20px;\n    margin-top: 7px;\n\n    .mianji {\n      background: url(\"../assets/image/zengfangti.png\");\n      background-repeat: no-repeat;\n      background-size: 100% 100%;\n      width: 106px;\n      height: 58px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n    .img {\n      width: 50px;\n      height: 49px;\n    }\n\n    .wenzis {\n      .top {\n        font-family: Alibaba PuHuiTi;\n        font-weight: 400;\n        font-size: 18px;\n        color: #ffffff;\n      }\n\n      .bottom {\n        display: flex;\n        align-items: flex-end;\n        font-family: Alibaba PuHuiTi;\n        font-weight: 400;\n        font-size: 13px;\n        color: #fff;\n        margin-left: 7px;\n      }\n    }\n  }\n}\n\n.boxswq {\n  width: 365px;\n  height: 242px;\n}\n\n.huangxing {\n  width: 359px;\n  height: 238px;\n}\n\n.cjhulizhong {\n  font-family: Microsoft YaHei;\n  font-weight: bold;\n  font-size: 14px;\n  color: #64f8bb;\n  margin-left: 8px;\n}\n\n.yuan {\n  width: 10px;\n  height: 10px;\n  background-color: #518acd;\n  border-radius: 50%;\n}\n\n.zongduan {\n  display: flex;\n  align-items: center;\n  font-size: 13px;\n}\n\n.titleimgs {\n  display: flex;\n  align-items: center;\n  justify-content: space-around;\n  margin-right: 10px;\n\n  .bgu {\n    background-color: #95871cbf !important;\n\n    // background: url(\"../assets/image/titlessimg.png\");\n    background-repeat: no-repeat;\n    background-size: 100% 100%;\n    width: 171px;\n    height: 38px;\n    font-family: Alibaba PuHuiTi;\n    font-weight: 400;\n    font-size: 22px;\n    color: #ffffff;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 0 12px 0 14px;\n  }\n\n  .bgu1 {\n    background-color: rgb(28, 128, 149) !important;\n\n    // background: url(\"../assets/image/titlessimg.png\");\n    background-repeat: no-repeat;\n    background-size: 100% 100%;\n    width: 171px;\n    height: 38px;\n    font-family: Alibaba PuHuiTi;\n    font-weight: 400;\n    font-size: 22px;\n    color: #ffffff;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 0 12px 0 14px;\n  }\n}\n\n.titlesscontents {\n  overflow: auto;\n  height: 154px;\n}\n\n/* 设置滚动条的样式 */\n.titlesscontents::-webkit-scrollbar {\n  width: 5px;\n  /* 设置滚动条的宽度 */\n}\n\n/* 设置滚动条轨道的样式 */\n.titlesscontents::-webkit-scrollbar-track {\n  background-color: #454f5d;\n  /* 设置滚动条轨道的背景色 */\n}\n\n/* 设置滚动条的样式 */\n.unfixed-warnings::-webkit-scrollbar {\n  width: 5px;\n  /* 设置滚动条的宽度 */\n}\n\n/* 设置滚动条轨道的样式 */\n.unfixed-warnings::-webkit-scrollbar-track {\n  background-color: #454f5d;\n  /* 设置滚动条轨道的背景色 */\n}\n\n/* 设置滚动条滑块的样式 */\n.unfixed-warnings::-webkit-scrollbar-thumb {\n  background-color: #f1f1f1;\n  /* 设置滚动条滑块的背景色 */\n}\n\n.pagination-container {\n  margin-top: 20px;\n  display: flex;\n  justify-content: flex-end;\n  padding: 10px;\n}\n\n:deep(.el-pagination) {\n\n  .el-pagination__total,\n  .el-pagination__sizes,\n  .el-pagination__jump,\n  .btn-prev,\n  .btn-next,\n  .el-pager li {\n    background-color: transparent;\n    color: #fff;\n  }\n\n  .el-pagination__total,\n  .el-pagination__jump {\n    color: #fff;\n  }\n\n  .el-select .el-input .el-input__inner {\n    color: #fff;\n    background-color: transparent;\n  }\n\n  .el-pager li.active {\n    background-color: #409eff;\n    color: #fff;\n  }\n}\n\n.loading-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100%;\n  width: 100%;\n}\n\n:deep(.el-loading-spinner) {\n  .el-loading-text {\n    color: #fff;\n    margin: 3px 0;\n    font-size: 14px;\n  }\n\n  .circular {\n    .path {\n      stroke: #3ba1f4;\n    }\n  }\n}\n\n/* 设置滚动条滑块的样式 */\n.titlesscontents::-webkit-scrollbar-thumb {\n  background-color: #f1f1f1;\n  /* 设置滚动条滑块的背景色 */\n}\n\n.titless {\n  margin-right: 10px;\n  width: 96%;\n  background: rgba(25, 37, 60, 0.5);\n  height: 32px;\n  margin-top: 8px;\n  display: flex;\n  font-family: Alibaba PuHuiTi;\n  font-weight: 400;\n  font-size: 17px;\n  color: #40d7ff;\n  display: flex;\n  align-items: center;\n  justify-content: space-around;\n\n  .item {\n    width: 100%;\n    flex: 1.1;\n  }\n\n  .item1 {\n    width: 100%;\n    flex: 1.9;\n  }\n}\n\n.contents {\n  border-bottom: 1px solid #3b5471;\n  margin-right: 10px;\n  width: 96%;\n  background: rgba(45, 58, 79, 0.2);\n  height: 32px;\n  display: flex;\n  font-family: Alibaba PuHuiTi;\n  font-weight: 400;\n  font-size: 15px;\n  color: #fff;\n  display: flex;\n  align-items: center;\n\n  .item {\n    width: 100%;\n    flex: 1;\n  }\n\n  .item1 {\n    width: 100%;\n    flex: 1.9;\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    position: relative;\n    cursor: pointer;\n\n    &:hover::after {\n      content: attr(title);\n      position: absolute;\n      left: 0;\n      top: 100%;\n      background: rgba(0, 0, 0, 0.8);\n      color: #fff;\n      padding: 4px 8px;\n      border-radius: 4px;\n      z-index: 999;\n      white-space: normal;\n    }\n  }\n}\n\n.contents:nth-child(odd) {\n  background: rgba(46, 61, 83, 0.4);\n}\n\n.contents:nth-child(even) {\n  background: rgba(37, 50, 69, 0.2);\n}\n\n.popup-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.7);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 9999;\n}\n\n.popup-content {\n  background: rgba(25, 37, 60, 0.95);\n  border: 1px solid #3ba1f4;\n  border-radius: 8px;\n  width: 80%;\n  max-width: 1000px;\n  max-height: 80vh;\n  overflow-y: auto;\n  padding: 20px;\n}\n\n.popup-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid #3ba1f4;\n}\n\n.popup-title {\n  font-family: Alibaba PuHuiTi;\n  font-size: 24px;\n  color: #40d7ff;\n}\n\n.close-btn {\n  font-size: 28px;\n  color: #fff;\n  cursor: pointer;\n  padding: 0 10px;\n\n  &:hover {\n    color: #40d7ff;\n  }\n}\n\n.popup-table {\n  .table-header {\n    display: flex;\n    background: rgba(25, 37, 60, 0.8);\n    padding: 12px;\n    color: #40d7ff;\n    font-family: Alibaba PuHuiTi;\n    font-size: 20px;\n\n    .col-1 {\n      flex: 1;\n      padding: 0 2px;\n      text-align: center;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n\n    .col-2 {\n      flex: 2.3;\n      padding: 0 2px;\n      text-align: center;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n  }\n\n  .table-row {\n    display: flex;\n    padding: 12px;\n    font-size: 12px;\n    border-bottom: 1px solid rgba(59, 161, 244, 0.2);\n    color: #fff;\n    font-family: Alibaba PuHuiTi;\n\n    &:hover {\n      background: rgba(59, 161, 244, 0.1);\n    }\n\n    .col-1 {\n      flex: 1;\n      padding: 0 2px;\n      text-align: center;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n\n    .col-2 {\n      flex: 2.3;\n      padding: 0 2px;\n      text-align: center;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n  }\n}\n\n.pagination-container {\n  margin-top: 20px;\n  display: flex;\n  justify-content: flex-end;\n  padding: 10px;\n}\n\n:deep(.el-pagination) {\n\n  .el-pagination__total,\n  .el-pagination__sizes,\n  .el-pagination__jump {\n    color: #fff !important;\n  }\n\n  &.is-background {\n\n    .btn-prev,\n    .btn-next,\n    .el-pager li {\n      background-color: rgba(25, 37, 60, 0.8) !important;\n      color: #fff !important;\n      border: 1px solid #3ba1f4;\n      margin: 0 3px;\n\n      &:hover {\n        color: #409eff !important;\n        background-color: rgba(37, 50, 69, 0.4) !important;\n      }\n\n      &.is-active {\n        background-color: #409eff !important;\n        color: #fff !important;\n        border-color: #409eff;\n      }\n\n      &:disabled {\n        background-color: rgba(25, 37, 60, 0.4) !important;\n        color: #606266 !important;\n      }\n    }\n  }\n}\n\n.loading-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100%;\n  width: 100%;\n}\n\n:deep(.el-loading-spinner) {\n  .el-loading-text {\n    color: #fff;\n    margin: 3px 0;\n    font-size: 14px;\n  }\n\n  .circular {\n    .path {\n      stroke: #3ba1f4;\n    }\n  }\n}\n</style>\n"], "mappings": ";;;EAISA,KAAK,EAAC;;;EAOAA,KAAK,EAAC;AAAQ;;EAEVA,KAAK,EAAC;AAAQ;;;EAEZA,KAAK,EAAC;AAAO;;EACXA,KAAK,EAAC;AAAK;;EACXA,KAAK,EAAC;AAAQ;;EAMpBC,KAA0C,EAA1C;IAAA;IAAA;EAAA;AAA0C;;EAExCD,KAAK,EAAC;AAAM;;EAEdA,KAAK,EAAC;AAAO;;EAIfA,KAAK,EAAC;AAAO;;EAmCbA,KAAK,EAAC;AAAc;;EAIpBA,KAAK,EAAC,YAAY;EAA0B,sBAAoB,EAAC,QAAQ;EAC5E,4BAA0B,EAAC;;;EACtBA,KAAK,EAAC;AAAa;;;;;EAkBbA,KAAK,EAAC;AAAO;;EACbA,KAAK,EAAC;AAAO;;EACbA,KAAK,EAAC;AAAO;;;EAIfA,KAAK,EAAC;;;EAIVA,KAAK,EAAC;AAAsB;;EAiB5BA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAK;;EAIXA,KAAK,EAAC;AAAM;;EAWdA,KAAK,EAAC,iBAAiB;EAAuB,sBAAoB,EAAC,QAAQ;EAC9E,yBAAuB,EAAC,iBAAiB;EAAC,4BAA0B,EAAC;;;;EAG9DA,KAAK,EAAC;AAAO;;EACbA,KAAK,EAAC;AAAM;;EACZA,KAAK,EAAC;AAAO;;;EAEkBA,KAAK,EAAC;;;EAQ3CA,KAAK,EAAC;AAAW;;EAOfA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAQ;;EAOZA,KAAK,EAAC,SAAS;EAACC,KAAsB,EAAtB;IAAA;EAAA;;;EAIlBD,KAAK,EAAC;AAAQ;;EAOZA,KAAK,EAAC,gBAAgB;EAACC,KAAsB,EAAtB;IAAA;EAAA;;;EAK3BD,KAAK,EAAC;AAAkB;;EAGpBA,KAAK,EAAC;AAAM;;EAQVA,KAAK,EAAC;AAAO;;EAGbA,KAAK,EAAC;AAAO;;EACbA,KAAK,EAAC;AAAM;;EACZA,KAAK,EAAC;AAAU;;EAElBA,KAAK,EAAC;AAAO;;;;;;;;;uBArMhCE,mBAAA,CAiRM,e,cAhRJC,YAAA,CAC0BC,wBAAA,CADVC,KAAA,CAAAC,YAAY;IAAGC,SAAS,EAAEF,KAAA,CAAAE,SAAS;IAAGC,SAAS,EAAEH,KAAA,CAAAG,SAAS;IAAGC,gBAAc,EAAEC,IAAA,CAAAC,cAAc;IACzGC,GAAG,EAAC;6FACuBP,KAAA,CAAAQ,MAAM,I,cAAnCX,mBAAA,CAyNM,OAzNNY,UAyNM,GAxNJC,mBAAA,CAwDM;IAxDDf,KAAK,EAAAgB,eAAA,EAAC,YAAY;2BAAwCX,KAAA,CAAAY,MAAM;sBAA0BZ,KAAA,CAAAa,WAAW;4BAAgCb,KAAA,CAAAc;;MAKxIC,YAAA,CAmBQC,gBAAA;IAnBDrB,KAAK,EAAC,SAAS;IAACsB,GAAG,EAAC,MAAM;IAAET,MAAM,EAAE;;sBACzC,MAUM,CAVNE,mBAAA,CAUM,OAVNQ,UAUM,I,kBATJrB,mBAAA,CAQMsB,SAAA,QAAAC,WAAA,CAR6BpB,KAAA,CAAAqB,SAAS,EAAjBC,IAAI;2BAA/BzB,mBAAA,CAQM;QARDF,KAAK,EAAC,QAAQ;QAA4B4B,GAAG,EAAED;UAClDZ,mBAAA,CAMM,OANNc,UAMM,GALJd,mBAAA,CAA0C;QAApCe,GAAG,EAAEH,IAAI,CAACI,GAAG;QAAE/B,KAAK,EAAC,KAAK;QAACgC,GAAG,EAAC;2CACrCjB,mBAAA,CAGM,OAHNkB,UAGM,GAFJlB,mBAAA,CAAsC,OAAtCmB,UAAsC,EAAAC,gBAAA,CAAlBR,IAAI,CAACS,IAAI,kBAC7BrB,mBAAA,CAA0C,OAA1CsB,UAA0C,EAAAF,gBAAA,CAAnBR,IAAI,CAACW,KAAK,iB;yDAKzCpC,mBAAA,CAMMsB,SAAA,QAAAC,WAAA,CAN+BpB,KAAA,CAAAkC,SAAS,EAAjBZ,IAAI;2BAAjCzB,mBAAA,CAMM;QANDF,KAAK,EAAC,UAAU;QAA4B4B,GAAG,EAAED;UACpDZ,mBAAA,CAGM,OAHNyB,UAGM,G,0BAFJzB,mBAAA,CAAwB;QAAnBf,KAAK,EAAC;MAAM,6BACjBe,mBAAA,CAAuC,OAAvC0B,UAAuC,EAAAN,gBAAA,CAAlBR,IAAI,CAACS,IAAI,iB,GAEhCrB,mBAAA,CAAyC,OAAzC2B,WAAyC,EAAAP,gBAAA,CAAnBR,IAAI,CAACW,KAAK,iB;;;MAGpClB,YAAA,CAIQC,gBAAA;IAJDrB,KAAK,EAAC,SAAS;IAACsB,GAAG,EAAC,MAAM;IAAET,MAAM,EAAE;;sBACzC,MAEM,CAFNE,mBAAA,CAEM,OAFN4B,WAEM,GADJvB,YAAA,CAAiDwB,mBAAA;MAAtCC,WAAW,EAAExC,KAAA,CAAAyC;IAAY,yC;;MAGxCC,mBAAA,s9BAyBY,C,kBAGdA,mBAAA,SAAY,EACqB1C,KAAA,CAAA2C,SAAS,I,cAA1C9C,mBAAA,CA2CM;;IA3CDF,KAAK,EAAC,eAAe;IAAmBiD,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,UAAA,IAAAD,QAAA,CAAAC,UAAA,IAAAF,IAAA,CAAU;MAC5DpC,mBAAA,CAyCM;IAzCDf,KAAK,EAAC,eAAe;IAAEiD,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAI,cAAA,CAAN,QAAW;MACpCvC,mBAAA,CAGM,OAHNwC,WAGM,G,0BAFJxC,mBAAA,CAAqC;IAAhCf,KAAK,EAAC;EAAa,GAAC,QAAM,sBAC/Be,mBAAA,CAAkD;IAA7Cf,KAAK,EAAC,WAAW;IAAEiD,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,UAAA,IAAAD,QAAA,CAAAC,UAAA,IAAAF,IAAA,CAAU;KAAE,GAAC,E,kCAE9CjD,mBAAA,CAmCM,OAnCNsD,WAmCM,GAjCJzC,mBAAA,CA0BM,OA1BN0C,WA0BM,G,gYAjBY/C,IAAA,CAAAgD,WAAW,IAAIhD,IAAA,CAAAgD,WAAW,CAACC,MAAM,Q,kBAC/CzD,mBAAA,CAWMsB,SAAA;IAAAI,GAAA;EAAA,GAAAH,WAAA,CAXgCf,IAAA,CAAAgD,WAAW,EAAnB/B,IAAI;yBAAlCzB,mBAAA,CAWM;MAXDF,KAAK,EAAC,WAAW;MAA8B4B,GAAG,EAAED;QACvDZ,mBAAA,CAA2D;MAAtDf,KAAK,EAAC,OAAO;MAAE4D,KAAK,EAAEjC,IAAI,CAACS;wBAAST,IAAI,CAACS,IAAI,wBAAAyB,WAAA,GAClD9C,mBAAA,CAEM;MAFDf,KAAK,EAAC,OAAO;MAAE4D,KAAK,EAAEjC,IAAI,CAACmC;wBAC3BnC,IAAI,CAACmC,eAAe,wBAAAC,WAAA,GAEzBhD,mBAAA,CAEM;MAFDf,KAAK,EAAC,OAAO;MAAE4D,KAAK,EAAEjC,IAAI,CAACqC;wBAC3BrC,IAAI,CAACqC,kBAAkB,wBAAAC,WAAA,GAE5BlD,mBAAA,CAA6C,OAA7CmD,WAA6C,EAAA/B,gBAAA,CAAvBR,IAAI,CAACwC,SAAS,kBACpCpD,mBAAA,CAA8C,OAA9CqD,WAA8C,EAAAjC,gBAAA,CAAxBR,IAAI,CAAC0C,UAAU,kBACrCtD,mBAAA,CAA0C,OAA1CuD,WAA0C,EAAAnC,gBAAA,CAApBR,IAAI,CAAC4C,MAAM,iB;kDAInCrE,mBAAA,CAAuC,OAAvCsE,WAAuC,EAAZ,QAAM,G,GAGrCzB,mBAAA,UAAa,EACbhC,mBAAA,CAIM,OAJN0D,WAIM,GAHJrD,YAAA,CAEgEsD,wBAAA;IAFhD,cAAY,EAAErE,KAAA,CAAAsE,WAAW;IAAG,WAAS,EAAEtE,KAAA,CAAAuE,QAAQ;IAAG,YAAU,EAAEvE,KAAA,CAAAwE,SAAS;IAAGC,KAAK,EAAEzE,KAAA,CAAAyE,KAAK;IACnGC,YAAW,EAAE3B,QAAA,CAAA4B,gBAAgB;IAAGC,eAAc,EAAE7B,QAAA,CAAA8B,mBAAmB;IACpEC,MAAM,EAAC,yCAAyC;IAACC,UAAU,EAAV;+IAjCpB/E,KAAA,CAAAgF,YAAY,E,4CAsCnDtC,mBAAA,UAAa,EAEbhC,mBAAA,CA8GM;IA9GDf,KAAK,EAAAgB,eAAA,EAAC,aAAa;4BAAyCX,KAAA,CAAAY,MAAM;sBAA0BZ,KAAA,CAAAa,WAAW;6BAAiCb,KAAA,CAAAc;;MAK3IC,YAAA,CA+BSkE,iBAAA;IA/BDtF,KAAK,EAAC,QAAQ;IAACsB,GAAG,EAAC;;sBACzB,MA6BM,CA7BNP,mBAAA,CA6BM;MA7BDf,KAAK,EAAC,QAAQ;MAAEiD,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAmC,cAAA,IAAAnC,QAAA,CAAAmC,cAAA,IAAApC,IAAA,CAAc;QACxCpC,mBAAA,CASM,OATNyE,WASM,GARJzE,mBAAA,CAGM,OAHN0E,WAGM,G,0BAFJ1E,mBAAA,CAAgB,aAAX,OAAK,sBACVA,mBAAA,CAAwB,aAAAoB,gBAAA,CAAhB9B,KAAA,CAAAqF,OAAO,iB,GAEjB3E,mBAAA,CAGM,OAHN4E,WAGM,G,0BAFJ5E,mBAAA,CAAe,aAAV,MAAI,sBACTA,mBAAA,CAAyC,aAAAoB,gBAAA,CAAjC9B,KAAA,CAAAuF,iBAAiB,CAACjC,MAAM,iB,iCAGpC5C,mBAAA,CAKM;MALDf,KAAK,EAAC;IAAS,IAClBe,mBAAA,CAA4B;MAAvBf,KAAK,EAAC;IAAO,GAAC,KAAG,GACtBe,mBAAA,CAA6B;MAAxBf,KAAK,EAAC;IAAO,GAAC,MAAI,GACvBe,mBAAA,CAA2B;MAAtBf,KAAK,EAAC;IAAM,GAAC,KAAG,GACrBe,mBAAA,CAA6B;MAAxBf,KAAK,EAAC;IAAO,GAAC,MAAI,E,qDAEzBE,mBAAA,CAWM,OAXN2F,WAWM,I,kBATJ3F,mBAAA,CAKMsB,SAAA,QAAAC,WAAA,CAL+BpB,KAAA,CAAAuF,iBAAiB,EAAzBjE,IAAI;2BAAjCzB,mBAAA,CAKM;QALDF,KAAK,EAAC,UAAU;QAAoC4B,GAAG,EAAED;UAC5DZ,mBAAA,CAA2D;QAAtDf,KAAK,EAAC,OAAO;QAAE4D,KAAK,EAAEjC,IAAI,CAACS;0BAAST,IAAI,CAACS,IAAI,wBAAA0D,WAAA,GAClD/E,mBAAA,CAA4C,OAA5CgF,WAA4C,EAAA5D,gBAAA,CAAtBR,IAAI,CAACqE,QAAQ,kBACnCjF,mBAAA,CAA6C,OAA7CkF,WAA6C,EAAA9D,gBAAA,CAAxBR,IAAI,CAAC0C,UAAU,kBACpCtD,mBAAA,CAA0C,OAA1CmF,WAA0C,EAAA/D,gBAAA,CAApBR,IAAI,CAAC4C,MAAM,iB;qCAEvBlE,KAAA,CAAAuF,iBAAiB,CAACjC,MAAM,I,cAApCzD,mBAAA,CAEM,OAFNiG,WAEM,EAFsD,UAE5D,K,4DAVsC9F,KAAA,CAAA+F,SAAS,E;;MAerDhF,YAAA,CAKSkE,iBAAA;IALDtF,KAAK,EAAC,QAAQ;IAACsB,GAAG,EAAC;;sBACzB,MAGM,CAHNP,mBAAA,CAGM,OAHNsF,WAGM,GAFJjF,YAAA,CAAyBkF,qBAAA,GACzBvD,mBAAA,+BAAkC,C;;MAGtC3B,YAAA,CAiESkE,iBAAA;IAjEDtF,KAAK,EAAC,QAAQ;IAACsB,GAAG,EAAC,QAAQ;IAAET,MAAM,EAAE;;sBAC3C,MA+DM,CA/DNE,mBAAA,CA+DM;MA/DDf,KAAK,EAAC,QAAQ;MAAEiD,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAqD,MAAA,IAAEnD,QAAA,CAAAoD,MAAM;QAChCzF,mBAAA,CAuBM,OAvBN0F,WAuBM,GAtBJ1F,mBAAA,CAUM,OAVN2F,WAUM,G,4BATJ3F,mBAAA,CAKM;MALDf,KAAK,EAAC;IAAQ,IACjBe,mBAAA,CAGM;MAHDf,KAAK,EAAC;IAAS,IAClBe,mBAAA,CAAsD;MAAjDf,KAAK,EAAC,aAAa;MAACC,KAAsB,EAAtB;QAAA;MAAA;QACzBc,mBAAA,CAAyB;MAApBf,KAAK,EAAC;IAAI,GAAC,KAAG,E,wBAGvBe,mBAAA,CAEM,OAFN4F,WAEM,EAAAxE,gBAAA,CADDiB,QAAA,CAAAwD,YAAY,iB,GAGnB7F,mBAAA,CAUM,OAVN8F,WAUM,G,4BATJ9F,mBAAA,CAKM;MALDf,KAAK,EAAC;IAAQ,IACjBe,mBAAA,CAGM;MAHDf,KAAK,EAAC;IAAS,IAClBe,mBAAA,CAAuD;MAAlDf,KAAK,EAAC,cAAc;MAACC,KAAsB,EAAtB;QAAA;MAAA;QAC1Bc,mBAAA,CAAyB;MAApBf,KAAK,EAAC;IAAI,GAAC,KAAG,E,wBAGvBe,mBAAA,CAEM,OAFN+F,WAEM,EAAA3E,gBAAA,CADDiB,QAAA,CAAA2D,UAAU,iB,KAInBhG,mBAAA,CAuBM,OAvBNiG,WAuBM,GAtBJjE,mBAAA,aAAgB,G,kBAChB7C,mBAAA,CAoBMsB,SAAA,QAAAC,WAAA,CApB0BpB,KAAA,CAAAwC,WAAW,CAACoE,OAAO,GAAtCC,OAAO,EAAEC,KAAK;2BAA3BjH,mBAAA,CAoBM;QApBgD0B,GAAG,eAAeuF,KAAK;QAAEnH,KAAK,EAAC;UACnFe,mBAAA,CAkBM,OAlBNqG,WAkBM,GAjBJrG,mBAAA,CAQM,c,4BAPJA,mBAAA,CAKM;QALDf,KAAK,EAAC;MAAU,IACnBe,mBAAA,CAA0D;QAArDf,KAAK,EAAC,MAAM;QAACC,KAAiC,EAAjC;UAAA;QAAA;UAClBc,mBAAA,CAEM;QAFDf,KAAK,EAAC,aAAa;QAACC,KAAsB,EAAtB;UAAA;QAAA;SAAuB,OAEhD,E,sBAEFc,mBAAA,CAAkD,KAAlDsG,WAAkD,EAAAlF,gBAAA,CAA9B+E,OAAO,CAACI,eAAe,iB,GAG7CvG,mBAAA,CAGM,OAHNwG,WAGM,GAFJxG,mBAAA,CAAuD,KAAvDyG,WAAuD,EAAArF,gBAAA,CAApCiB,QAAA,CAAAqE,UAAU,CAACP,OAAO,CAACQ,SAAS,mBAC/C3G,mBAAA,CAA4C,KAA5C4G,WAA4C,EAAAxF,gBAAA,CAArB+E,OAAO,CAACU,MAAM,iB,GAEvC7G,mBAAA,CAEI,KAFJ8G,WAEI,EAAA1F,gBAAA,CADC+E,OAAO,CAACY,UAAU,iB;sCAK7B/E,mBAAA,aAAgB,EAChBA,mBAAA,stBAYU,C;;8DAKc1C,KAAA,CAAA0H,UAAU,I,cAA1C5H,YAAA,CAAqD6H,iBAAA;;IAA5CC,OAAK,EAAE7E,QAAA,CAAA8E;6EAChBnF,mBAAA,kFAA+E,EAC/EA,mBAAA,s1CAiDU,C", "ignoreList": []}]}