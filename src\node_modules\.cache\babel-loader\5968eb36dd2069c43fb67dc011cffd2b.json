{"remainingRequest": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\babel-loader\\lib\\index.js!E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\views\\floor.vue?vue&type=template&id=75f2e462&scoped=true", "dependencies": [{"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\views\\floor.vue", "mtime": 1751448014712}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\babel.config.js", "mtime": 1726672621424}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_imports_0", "_imports_1", "class", "_createElementBlock", "$data", "isshowwhat", "_createBlock", "_resolveDynamicComponent", "componentTag", "onFatherMethoddd", "_ctx", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_createElementVNode", "_hoisted_1", "_Fragment", "_renderList", "changeTitle", "item", "index", "key", "_normalizeClass", "titactive", "onClick", "$event", "$options", "changetit", "_hoisted_2", "isshowsss", "_component_tedai", "ids", "selectedItem", "zeng<PERSON><PERSON><PERSON>", "$props", "onHidedetails", "hidedetailsss", "isshow", "_component_biaoGe", "Title", "onXuanzeDialog", "xuanzedialog", "hidedetails", "tableTitle", "tableDataItem", "devicedata", "_hoisted_3", "showdh", "noAnimation", "showdh1", "_createVNode", "_component_Title2", "onOpenDialog", "opendialog", "tit", "_component_el_cascader", "placeholder", "options", "equipmentTags", "onChange", "handleCascaderChange", "_component_el_input", "input", "src", "alt", "_hoisted_4", "_createCommentVNode", "_hoisted_5", "_hoisted_6", "menu", "_hoisted_7", "toggleSubMenu", "name", "_hoisted_8", "loading", "_component_el_pagination", "onSizeChange", "handleSizeChange", "onCurrentChange", "handleCurrentChange", "layout", "total"], "sources": ["E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\views\\floor.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <component\r\n      :is=\"componentTag\"\r\n      @fatherMethoddd=\"fatherMethoddd\"\r\n      v-if=\"isshowwhat\"\r\n    ></component>\r\n    <div class=\"botbtn\">\r\n      <div\r\n        v-for=\"(item, index) in changeTitle\"\r\n        :key=\"index\"\r\n        :class=\"titactive == index ? 'btt1' : 'btt'\"\r\n        @click=\"changetit(index)\"\r\n      >\r\n        {{ item }}\r\n      </div>\r\n    </div>\r\n    <tedai\r\n      :ids=\"ids\"\r\n      :selectedItem=\"selectedItem\"\r\n      class=\"sbdetails\"\r\n      :zengtiimg=\"zengtiimg\"\r\n      v-if=\"isshowsss\"\r\n      @hidedetails=\"hidedetailsss\"\r\n    ></tedai>\r\n    <biaoGe\r\n      :Title=\"Title\"\r\n      @xuanze-dialog=\"xuanzedialog\"\r\n      v-if=\"isshow\"\r\n      @hidedetails=\"hidedetails\"\r\n      :tableTitle=\"tableTitle\"\r\n      :tableDataItem=\"devicedata\"\r\n    ></biaoGe>\r\n    <div class=\"container\" v-if=\"!isshowwhat\">\r\n      <div\r\n        class=\"left-panel\"\r\n        :class=\"{\r\n          'left-panel-active': showdh,\r\n          'no-animation': noAnimation,\r\n          'left-panel-active1': showdh1,\r\n        }\"\r\n      >\r\n        <Title2 @open-dialog=\"opendialog\" class=\"ltitle1\" tit=\"大仪管理\">\r\n          <el-cascader\r\n            class=\"sect\"\r\n            placeholder=\"请选择类别\"\r\n            :options=\"equipmentTags\"\r\n            :show-all-levels=\"true\"\r\n            @change=\"handleCascaderChange\"\r\n          ></el-cascader>\r\n\r\n          <el-input\r\n            class=\"el-input\"\r\n            v-model=\"input\"\r\n            placeholder=\"请输入关键字\"\r\n          ></el-input>\r\n          <img class=\"suosuo\" src=\"../assets/image/suosuo.png\" alt=\"\" />\r\n          <div class=\"box\">\r\n            <!-- <div class=\"xiaobox\">\r\n              <img class=\"siqiu\" src=\"../assets/image/shixinqiu.png\" alt=\"\" />\r\n              <div class=\"shuru\">全部设备</div>\r\n            </div> -->\r\n            <div\r\n              class=\"menu-container\"\r\n              v-loading=\"loading\"\r\n              element-loading-text=\"拼命加载中\"\r\n              element-loading-background=\"rgba(0, 0, 0, 0)\"\r\n            >\r\n              <!-- 动态生成菜单 -->\r\n              <div class=\"menu\">\r\n                <div\r\n                  v-for=\"(menu, index) in devicedata\"\r\n                  :key=\"index\"\r\n                  class=\"menu-group\"\r\n                >\r\n                  <div class=\"qiuqiu\">\r\n                    <img\r\n                      class=\"siqiu\"\r\n                      src=\"../assets/image/shixinqiu.png\"\r\n                      alt=\"\"\r\n                    />\r\n                    <div class=\"menu-item\" @click=\"toggleSubMenu(menu)\">\r\n                      {{ menu.name }}\r\n                    </div>\r\n                    <!-- <div class=\"listtypes\" @click=\"showdetails(menu)\">详情</div> -->\r\n                  </div>\r\n\r\n                  <!-- <div v-show=\"activeSubmenu === menu.id\" class=\"submenu\">\r\n                    <div v-for=\"(item, subIndex) in menu.items\" :key=\"subIndex\" class=\"submenu-item\"\r\n                      @click=\"setContent(item)\">\r\n                      <p class=\"ellipsis\" :title=\"item.name\">{{ item.name }}</p>\r\n                    </div>\r\n                  </div> -->\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <el-pagination\r\n            @size-change=\"handleSizeChange\"\r\n            hide-on-single-page=\"true\"\r\n            @current-change=\"handleCurrentChange\"\r\n            :page-size=\"16\"\r\n            :pager-count=\"4\"\r\n            layout=\"prev, pager, next,total\"\r\n            :total=\"total\"\r\n          >\r\n          </el-pagination>\r\n          <!-- shebei12.png -->\r\n        </Title2>\r\n      </div>\r\n      <!-- shebeibgc.png -->\r\n      <!-- 右侧内容 -->\r\n      <!-- 右侧内容 -->\r\n\r\n      <div\r\n        class=\"right-panel\"\r\n        :class=\"{\r\n          'right-panel-active': showdh,\r\n          'no-animation': noAnimation,\r\n          'right-panel-active1': showdh1,\r\n        }\"\r\n      ></div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）\r\n// 例如：import 《组件名称》 from '《组件路径》';\r\nimport { mapActions, mapGetters } from \"vuex\";\r\nimport component0 from \"@/views/dayi/zichan.vue\";\r\nimport huanxing from \"@/components/echarts/huanxing.vue\";\r\nimport zhexian from \"@/components/echarts/zhexian.vue\";\r\nimport zhexian1 from \"@/components/echarts/zhexian1.vue\";\r\nimport SystemDete from \"@/components/echarts/SystemDete.vue\";\r\nimport echarts1 from \"@/components/echarts/bingjifang/echarts3.vue\";\r\nimport tedai from \"@/components/common/cl_details.vue\";\r\nimport biaoGe from \"@/components/common/biaoGes.vue\";\r\nimport shuangxiang from \"@/components/echarts/shuangxiang.vue\";\r\nimport axios from \"axios\";\r\n// import details from \"@/components/common/details.vue\";\r\n// 使用环境变量设置基础 API 地址\r\nconst baseURL = process.env.VUE_APP_BASE_API || \"/lims/api\";\r\n\r\nconst api = axios.create({\r\n  baseURL,\r\n});\r\nexport default {\r\n  // import引入的组件需要注入到对象中才能使用\r\n  components: {\r\n    tedai,\r\n    huanxing,\r\n    zhexian,\r\n    zhexian1,\r\n    SystemDete,\r\n    echarts1,\r\n    shuangxiang,\r\n    biaoGe,\r\n    component0,\r\n  },\r\n  props: [\"tabledata\", \"zengtiimg\"],\r\n\r\n  data() {\r\n    // 这里存放数据\r\n    return {\r\n      loading: true,\r\n      currentPage1: 5,\r\n      isshowwhat: false,\r\n      isshowsss: false,\r\n      titactive: 0,\r\n      total: null,\r\n      changeTitle: [\"数据列表\", \"数据统计\"],\r\n      activeSubmenu: null, // 当前激活的子菜单\r\n      activeContent: null, // 当前显示的内容\r\n      newArr: [],\r\n      isshow: false,\r\n      xxxx: false,\r\n      cgqlist: [],\r\n      listtable: [],\r\n      options: [\r\n        {\r\n          value: \"zhinan\",\r\n          label: \"指南\",\r\n          children: [\r\n            {\r\n              value: \"shejiyuanze\",\r\n              label: \"设计原则\",\r\n            },\r\n            {\r\n              value: \"daohang\",\r\n              label: \"导航\",\r\n            },\r\n          ],\r\n        },\r\n      ],\r\n      devicedata: [],\r\n      input: \"\",\r\n      activeTab: \"today\",\r\n      listst: [\r\n        {\r\n          name: \"广东质检中诚认证有限公司到中广...\",\r\n        },\r\n        { name: \"材料科学、化学工程及医药研发成...\" },\r\n        { name: \"酒精代谢能力检测：您的酒量，基...\" },\r\n        { name: \"植酸检测方法及作用\" },\r\n        { name: \"兔拉检测：行业中芯片造假技术...\" },\r\n        { name: \"酒精代谢能力检测：您的酒量，基...\" },\r\n      ],\r\n      showdh: true,\r\n      showdh1: false,\r\n      noAnimation: false,\r\n      selectedItem: {\r\n        name: \"离子溅射仪\", //产品名称\r\n        imgurl: \"https://3d.dddtask.cn/enginner-xufeng/image/tianjin/lizi.png\",\r\n        location: \"北洋园校区54楼, E105\", //位置\r\n        status: \"已领用\", //仪器状态  可领用\r\n        details: [\r\n          {\r\n            name: \"产品名称\",\r\n            value: \"离子溅射仪\",\r\n          },\r\n          {\r\n            name: \"原值\",\r\n            value: \"--\",\r\n          },\r\n          {\r\n            name: \"购入时间\",\r\n            value: \"2020/09/02\",\r\n          },\r\n          {\r\n            name: \"品牌\",\r\n            value: \"--\",\r\n          },\r\n          {\r\n            name: \"生产厂家\",\r\n            value: \"--\",\r\n          },\r\n          {\r\n            name: \"供应商名称\",\r\n            value: \"--\",\r\n          },\r\n          {\r\n            name: \"供应商联系信息\",\r\n            value: \"--\",\r\n          },\r\n        ],\r\n        maintenance_records: {\r\n          maintenance_content: \"校准和系统升级\", //维护内容\r\n          date: \"2024-01-10\", //维护时间\r\n          next_maintenance_date: \"2022-01-10\", //下次维护时间\r\n        },\r\n        management_name: \"王工\",\r\n        management_contact_info: \"15698567542\",\r\n      },\r\n      tableDataItem: [],\r\n      jlURL,\r\n      Title: \"资产管理\",\r\n      tableTitle: [\r\n        { key: \"楼层\" },\r\n        { key: \"设备编号\" },\r\n        { key: \"设备名称\" },\r\n        { key: \"房间号\" },\r\n        { key: \"模型\" },\r\n        { key: \"设备状态\" },\r\n        { key: \"状态说明\" },\r\n      ],\r\n      ids: null,\r\n      nhlist: [\r\n        {\r\n          title: \"供气压力\",\r\n          status: \"0.3Mpa\",\r\n          unit: \"℃\",\r\n        },\r\n\r\n        {\r\n          title: \"供气流量\",\r\n          status: \"6M3/min\",\r\n          unit: \"㎡\",\r\n        },\r\n        {\r\n          title: \"露点温度\",\r\n          status: \"6℃\",\r\n          unit: \"℃\",\r\n        },\r\n        {\r\n          title: \"含氧量\",\r\n          status: \"6PPM\",\r\n          unit: \"㎡\",\r\n        },\r\n      ],\r\n      warnlist1: [\r\n        {\r\n          type: 1,\r\n          name: \"检测到烟雾，可能有着火灾的发生...\",\r\n          value: \"\",\r\n          time: \"09:13:59  2023-06-07\",\r\n        },\r\n        {\r\n          type: 2,\r\n          name: \"检测到烟雾，可能预示着火灾的发生..\",\r\n          value: \"\",\r\n          time: \"10:13:18  2023-06-12\",\r\n        },\r\n        {\r\n          type: 3,\r\n          name: \"实验室内检测到漏水，可能来自冷凝水..\",\r\n          value: \"\",\r\n          time: \"10:13:18  2023-06-12\",\r\n        },\r\n      ],\r\n      isButton2Active: false,\r\n      status: \"巡检中\",\r\n      status1: \"已完成\",\r\n      status2: \"待巡检\",\r\n      selectedIndex: 0,\r\n      componentTag: \"component0\",\r\n      token: \"\",\r\n    };\r\n  },\r\n  // 计算属性类似于data概念\r\n  computed: {\r\n    ...mapGetters(\"equipment\", [\"equipmentTags\"]),\r\n  },\r\n  // 监控data中的数据变化\r\n  watch: {},\r\n  // 方法集合\r\n  methods: {\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.getyiqidetails(this.token, val - 1);\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`);\r\n      this.getyiqidetails(this.token, val - 1);\r\n    },\r\n    async gettoken(id) {\r\n      try {\r\n        const response = await api.post(\"\", {\r\n          method: \"equipment/searchEquipments\",\r\n          params: {\r\n            criteria: {\r\n              cat: id,\r\n              location:'58楼ACE区',\r\n              // \"group\": 1,\r\n              // \"searchtext\": \"搜索内容\"\r\n            },\r\n          },\r\n        });\r\n\r\n        // 检查是否成功拿到 token\r\n        if (response.data && response.data.response.token) {\r\n          const token = response.data.response.token;\r\n          this.token = token;\r\n          // 将 token 存入 localStorage\r\n          // localStorage.setItem('authToken', token);\r\n          console.log(\"535:\", response.data.response);\r\n          this.total = response.data.response.total;\r\n          this.handleSizeChange(1);\r\n        } else {\r\n          console.error(\"登录成功但未返回 token:\", response.data.response);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"登录失败:\", error);\r\n      }\r\n    },\r\n    getyiqidetails(token, start) {\r\n      const headers = {\r\n        clientid: \"5a298e93-158d-4e22-83cf-6ceb62e9b4f1\",\r\n        clientsecret: \"2c8ec39e-9887-482a-b28b-e64c496b601c\",\r\n      };\r\n      const body = {\r\n        method: \"equipment/getEquipments\",\r\n        params: {\r\n          token: token,\r\n          start: start ? start * 16 : 0,\r\n          num: 16,\r\n        },\r\n      };\r\n      axios\r\n        .post(jlURL, body, {})\r\n        .then((response) => {\r\n          console.log(response.data, 535);\r\n          this.devicedata = response.data.response;\r\n          this.loading = false;\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"Error:\", error);\r\n        });\r\n    },  \r\n    ...mapActions(\"equipment\", [\"fetchEquipmentTags\"]),\r\n    changetit(index) {\r\n      this.titactive = index;\r\n      this.isshowwhat = index;\r\n      if (index == 1) {\r\n        this.showdh = false;\r\n        this.showdh1 = true;\r\n        this.noAnimation = true;\r\n      } else {\r\n        this.showdh = true;\r\n        this.showdh1 = false;\r\n        this.noAnimation = false;\r\n      }\r\n    },\r\n    handleCascaderChange(value) {\r\n      console.log(\"选中的值:\", value[1]);\r\n      this.gettoken(value[1]);\r\n    },\r\n    hidedetailsss() {\r\n      this.isshowsss = false;\r\n    },\r\n    opendialog(payload) {\r\n      if (payload == 1) {\r\n        this.isshow = true;\r\n\r\n        this.data.forEach((item) => {\r\n          if (item.category == \"电子显微镜\") {\r\n            this.isshow = true;\r\n            this.tableDataItem = item.items;\r\n            console.log(this.tableDataItem);\r\n          }\r\n        });\r\n      }\r\n\r\n      // 在这里处理事件\r\n    },\r\n    toggleSubMenu(item) {\r\n      this.isshowsss = true;\r\n      console.log(item, \"选中的设备信息\");\r\n      this.selectedItem = {\r\n        id: item.id,\r\n        name: item.name,\r\n        imgurl: item.iconreal_url,\r\n        location: item.location + item.location2, //位置\r\n        status: !item.is_using ? \"当前使用\" : \"可使用\", //仪器状态  可领用\r\n        details: [\r\n          {\r\n            name: \"产品名称\",\r\n            value: item.name,\r\n          },\r\n          {\r\n            name: \"价格\",\r\n            value: item.price,\r\n          },\r\n          {\r\n            name: \"购入时间\",\r\n            value: `${new Date(item.purchased_date * 1000).getFullYear()}/${\r\n              new Date(item.purchased_date * 1000).getMonth() + 1\r\n            }/${new Date(item.purchased_date * 1000).getDate()}`,\r\n          },\r\n          {\r\n            name: \"制造国家\",\r\n            value: item.manu_at,\r\n          },\r\n          {\r\n            name: \"生产厂家\",\r\n            value: item.manufacturer,\r\n          },\r\n          {\r\n            name: \"负责人\",\r\n            value: item.contact,\r\n          },\r\n          {\r\n            name: \"联系电话\",\r\n            value: item.phone,\r\n          },\r\n        ],\r\n        maintenance_records: {\r\n          maintenance_content: \"校准和系统升级\", //维护内容\r\n          date: \"2024-01-10\", //维护时间\r\n          next_maintenance_date: \"2022-01-10\", //下次维护时间\r\n        },\r\n        management_name: \"王工\",\r\n        management_contact_info: \"15698567542\",\r\n      };\r\n    },\r\n    // // 设置内容\r\n    // setContent(content) {\r\n    //   this.isshowsss = true;\r\n    //   this.selectedItem = content;\r\n    //   console.log(this.selectedItem);\r\n\r\n    //   this.activeContent = content;\r\n    // },\r\n    showdetails(item) {\r\n      // item.items.forEach((item) => {\r\n      //   this.newArr.push({ name: item.name });\r\n      // });\r\n      // console.log(this.newArr);\r\n\r\n      this.isshow = true;\r\n      this.tableDataItem = item.items;\r\n    },\r\n    hidedetails() {\r\n      this.isshow = false;\r\n    },\r\n\r\n    oc(value) {\r\n      console.log(value, \"收到的值\");\r\n      this.showdh = value;\r\n    },\r\n    xuanzedialog(value) {\r\n      const optionMapping = {\r\n        选项1: 0,\r\n        选项2: 1,\r\n        选项3: 2,\r\n        选项4: 3,\r\n        选项5: 4,\r\n        选项6: 5,\r\n        选项7: 6,\r\n        选项8: 7,\r\n        选项9: 8,\r\n        选项10: 9,\r\n        选项11: 10,\r\n        选项12: 11,\r\n        选项13: 12,\r\n      };\r\n\r\n      const index = optionMapping[value];\r\n      if (index !== undefined) {\r\n        this.tableDataItem = this.data[index].items;\r\n      } else {\r\n        console.error(\"无效的选项: \", value);\r\n      }\r\n    },\r\n  },\r\n  // 生命周期 - 创建完成（可以访问当前this实例）\r\n  created() {},\r\n  // 生命周期 - 挂载完成（可以访问DOM元素）\r\n  mounted() {\r\n    this.gettoken(\"\");\r\n    this.showdh1 = true;\r\n    this.fetchEquipmentTags();\r\n    // setTimeout(() => {\r\n    //   this.showdh1 = false;\r\n    //   this.noAnimation = false;\r\n    // }, 1000); // 动画持续时间为1秒\r\n    console.log(1222);\r\n  },\r\n  beforeCreate() {}, // 生命周期 - 创建之前\r\n  beforeMount() {}, // 生命周期 - 挂载之前\r\n  beforeUpdate() {}, // 生命周期 - 更新之前\r\n  updated() {}, // 生命周期 - 更新之后\r\n  beforeUnmount() {\r\n    // 在组件销毁之前清除定时器\r\n    console.log(1111);\r\n  },\r\n\r\n  unmounted() {\r\n    console.log(2222);\r\n  }, // 生命周期 - 销毁之前\r\n  destroyed() {\r\n    console.log(1221);\r\n  }, // 生命周期 - 销毁完成\r\n  activated() {}, // 如果页面有keep-alive缓存功能，这个函数会触发\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.container {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-content: space-between;\r\n  align-items: stretch;\r\n  height: 1080px;\r\n\r\n  // text-align: center;\r\n  /* 定位 el-cascader 的 placeholder 样式 */\r\n\r\n  /deep/.sect.el-tooltip__trigger {\r\n    text-align: left;\r\n    width: 200px;\r\n    color: #fff;\r\n    // background-color: #00ffc0;\r\n  }\r\n\r\n  /deep/.el-input__wrapper {\r\n    box-shadow: none;\r\n    border: none;\r\n    background-color: #5a6972;\r\n    color: #fff;\r\n  }\r\n\r\n  /deep/.sect .el-input__inner {\r\n    color: #fff;\r\n  }\r\n\r\n  .sbdetails {\r\n    position: absolute;\r\n    top: 50%;\r\n    left: 50%;\r\n    transform: translate(-50%, -50%);\r\n    z-index: 9999;\r\n  }\r\n\r\n  .el-input {\r\n    margin-left: 5px;\r\n    width: 145px;\r\n    height: 34px;\r\n    color: #fff !important;\r\n\r\n    ::v-deep .el-input__wrapper {\r\n      background: url(\"../assets/image/inputss.png\");\r\n      background-size: 100% 100%;\r\n      background-repeat: no-repeat;\r\n      box-shadow: none !important;\r\n      color: #fff !important;\r\n    }\r\n\r\n    /deep/.el-input__inner {\r\n      color: #fff !important;\r\n    }\r\n\r\n    .el-input__inner::placeholder {\r\n      color: #fff;\r\n      /* 设置占位符颜色 */\r\n    }\r\n  }\r\n\r\n  .suosuo {\r\n    position: absolute;\r\n    top: 62px;\r\n    right: -32px;\r\n    cursor: pointer;\r\n  }\r\n\r\n  .box {\r\n    // margin-top: 6px;\r\n    padding-top: 5px;\r\n    margin-bottom: 0.225rem;\r\n    max-height: 745px;\r\n    width: 330px;\r\n    // height: 800px;\r\n\r\n    overflow-y: scroll;\r\n\r\n    /* 设置垂直滚动条 */\r\n    /* 设置滚动条的样式 */\r\n    &::-webkit-scrollbar {\r\n      width: 0.1px;\r\n      /* 设置滚动条的宽度 */\r\n    }\r\n\r\n    /* 设置滚动条轨道的样式 */\r\n    &::-webkit-scrollbar-track {\r\n      background-color: #f1f1f1;\r\n      /* 设置滚动条轨道的背景色 */\r\n    }\r\n\r\n    /* 设置滚动条滑块的样式 */\r\n    &::-webkit-scrollbar-thumb {\r\n      background-color: #334f6e;\r\n      /* 设置滚动条滑块的背景色 */\r\n    }\r\n\r\n    /* 鼠标悬停在滚动条上时的样式 */\r\n    &::-webkit-scrollbar-thumb:hover {\r\n      background-color: #555;\r\n      /* 设置鼠标悬停时滚动条滑块的背景色 */\r\n    }\r\n\r\n    .xiaobox {\r\n      margin-top: 20px;\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .siqiu {\r\n        width: 16px;\r\n        height: 16px;\r\n      }\r\n\r\n      .shuru {\r\n        display: flex;\r\n        align-items: center;\r\n        padding-left: 10px;\r\n        background: url(\"../assets/image/shebei12.png\");\r\n        background-size: 100% 100%;\r\n        background-repeat: no-repeat;\r\n        width: 261px;\r\n        height: 32px;\r\n        font-family: Source Han Sans SC;\r\n        font-weight: bold;\r\n        font-size: 17px;\r\n        color: #ffffff;\r\n      }\r\n    }\r\n\r\n    .xiaoboxs {\r\n      cursor: pointer;\r\n      margin-top: 14px;\r\n      display: flex;\r\n      margin-left: 5px;\r\n      align-items: center;\r\n\r\n      .nihaowo {\r\n        width: 78px;\r\n        font-family: Source Han Sans SC;\r\n        font-weight: bold;\r\n        font-size: 10px;\r\n        color: #ffffff;\r\n        display: flex;\r\n        white-space: nowrap;\r\n        /* 不换行 */\r\n        overflow: hidden;\r\n        /* 超出部分隐藏 */\r\n        text-overflow: ellipsis;\r\n        /* 超出部分显示省略号 */\r\n      }\r\n\r\n      .siqiu {\r\n        width: 16px;\r\n        height: 16px;\r\n        margin-left: 10px;\r\n        margin-right: 7px;\r\n      }\r\n\r\n      .shuru {\r\n        display: flex;\r\n        align-items: center;\r\n        padding-left: 10px;\r\n        background: url(\"../assets/image/shebei12.png\");\r\n        background-size: 100% 100%;\r\n        background-repeat: no-repeat;\r\n        width: 261px;\r\n        height: 32px;\r\n        font-family: Source Han Sans SC;\r\n        font-weight: bold;\r\n        font-size: 17px;\r\n        color: #ffffff;\r\n        white-space: nowrap;\r\n        /* 不换行 */\r\n        overflow: hidden;\r\n        /* 超出部分隐藏 */\r\n        text-overflow: ellipsis;\r\n        /* 超出部分显示省略号 */\r\n      }\r\n    }\r\n  }\r\n\r\n  .left-panel {\r\n    position: fixed;\r\n    z-index: 1;\r\n    top: 100px;\r\n    left: 22px;\r\n    width: 330px;\r\n    height: 937px;\r\n\r\n    background-size: 100% 100%;\r\n    transform: translate(-122%);\r\n    transition: transform 0.5s ease-in-out;\r\n  }\r\n\r\n  .left-panel-active {\r\n    transform: translate(0%);\r\n  }\r\n\r\n  .left-panel-active1 {\r\n    // transform: translate(0%);\r\n    animation: slideOut 1s ease-in-out forwards;\r\n  }\r\n\r\n  @keyframes slideOut {\r\n    100% {\r\n      transform: translateX(0%);\r\n    }\r\n\r\n    // 85% {\r\n    //   transform: translateX(-25%);\r\n    // }\r\n\r\n    // 65% {\r\n    //   transform: translateX(-15%);\r\n    // }\r\n\r\n    // 40% {\r\n    //   transform: translateX(-55%);\r\n    // }\r\n\r\n    // 30% {\r\n    //   transform: translateX(-40%);\r\n    // }\r\n\r\n    0% {\r\n      transform: translateX(-100%);\r\n    }\r\n  }\r\n\r\n  .rtitle {\r\n    margin-top: 16px;\r\n  }\r\n\r\n  @keyframes slideIn {\r\n    0% {\r\n      transform: translateX(100%);\r\n    }\r\n\r\n    // 30% {\r\n    //   transform: translateX(65%);\r\n    // }\r\n\r\n    // 40% {\r\n    //   transform: translateX(40%);\r\n    // }\r\n\r\n    // 65% {\r\n    //   transform: translateX(15%);\r\n    // }\r\n\r\n    // 85% {\r\n    //   transform: translateX(25%);\r\n    // }\r\n\r\n    100% {\r\n      transform: translateX(0%);\r\n    }\r\n  }\r\n\r\n  .right-panel {\r\n    position: fixed;\r\n    z-index: 1;\r\n    right: 83px;\r\n    width: 330px;\r\n    top: 100px;\r\n    height: 937px;\r\n\r\n    background-size: 100% 100%;\r\n    transform: translate(122%);\r\n    transition: transform 0.5s ease-in-out;\r\n\r\n    .jk {\r\n      margin-top: 12px;\r\n      width: 90%;\r\n      height: 200px;\r\n    }\r\n\r\n    .box {\r\n      margin-top: 6px;\r\n      margin-bottom: 18px;\r\n      // background: url(\"../assets/image/zuoshang1.png\");\r\n      background-size: 100% 100%;\r\n      background-repeat: no-repeat;\r\n\r\n      width: 94%;\r\n      // height: 428px;\r\n\r\n      .loudong {\r\n        margin-top: 21px;\r\n        margin-bottom: 25px;\r\n      }\r\n\r\n      .wenzi {\r\n        font-family: Microsoft YaHei;\r\n        font-weight: 400;\r\n        font-size: 10px;\r\n        color: #bdecf9;\r\n        text-align: left;\r\n        margin-left: 20px;\r\n        margin-right: 20px;\r\n\r\n        .h2biaoti {\r\n          margin-bottom: 15px;\r\n          font-family: Microsoft YaHei;\r\n          font-weight: 400;\r\n          font-size: 12px;\r\n          color: #00ffff;\r\n        }\r\n      }\r\n\r\n      .p {\r\n        text-indent: 2em;\r\n        margin-bottom: 1em;\r\n        letter-spacing: 0.05em;\r\n      }\r\n    }\r\n\r\n    .boxxx {\r\n      margin-top: 6px;\r\n      margin-bottom: 18px;\r\n      background: url(\"../assets/image/zuoshang1.png\");\r\n      background-size: 100% 100%;\r\n      background-repeat: no-repeat;\r\n\r\n      width: 333px;\r\n      height: 420px;\r\n      overflow-y: scroll;\r\n      /* 设置垂直滚动条 */\r\n      // overflow: hidden;\r\n      /* 设置滚动条的样式 */\r\n\r\n      .zengti {\r\n        margin: 10px 0;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        width: 315px;\r\n        height: 38px;\r\n        gap: 5px;\r\n\r\n        .left {\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: space-evenly;\r\n          width: 84px;\r\n          height: 27px;\r\n\r\n          .yuan {\r\n            width: 12px;\r\n            height: 12px;\r\n            border-radius: 50%;\r\n            background-color: #08f7f7;\r\n          }\r\n\r\n          .wenziss {\r\n            display: flex;\r\n            align-items: flex-start;\r\n            flex-direction: column;\r\n\r\n            .p1 {\r\n              font-family: Source Han Sans SC;\r\n              font-weight: 500;\r\n              font-size: 10px;\r\n              color: #ffffff;\r\n            }\r\n\r\n            .p2 {\r\n              font-family: Source Han Sans SC;\r\n              font-weight: 500;\r\n              font-size: 10px;\r\n              color: #55cff9;\r\n            }\r\n          }\r\n        }\r\n\r\n        .right {\r\n          background: url(\"../assets/image/rightbeij.png\");\r\n          background-size: 100% 100%;\r\n          background-repeat: no-repeat;\r\n\r\n          width: 217px;\r\n          height: 38px;\r\n          font-family: Source Han Sans SC;\r\n          font-weight: 500;\r\n          font-size: 11px;\r\n          color: #ffffff;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n        }\r\n      }\r\n    }\r\n\r\n    .boxxx::-webkit-scrollbar {\r\n      width: 1px;\r\n      /* 设置滚动条的宽度 */\r\n    }\r\n\r\n    /* 设置滚动条轨道的样式 */\r\n    .boxxx::-webkit-scrollbar-track {\r\n      background-color: #f1f1f1;\r\n      /* 设置滚动条轨道的背景色 */\r\n    }\r\n\r\n    /* 设置滚动条滑块的样式 */\r\n    .boxxx::-webkit-scrollbar-thumb {\r\n      background-color: #013363;\r\n      /* 设置滚动条滑块的背景色 */\r\n    }\r\n  }\r\n\r\n  .no-animation {\r\n    transition: none;\r\n  }\r\n\r\n  .right-panel-active {\r\n    transform: translate(0%);\r\n    // animation: slideIn 1s ease-in-out ;\r\n  }\r\n\r\n  .right-panel-active1 {\r\n    // transform: translate(0%);\r\n    animation: slideIn 1s ease-in-out forwards;\r\n  }\r\n\r\n  @keyframes slideIn {\r\n    0% {\r\n      transform: translateX(100%);\r\n    }\r\n\r\n    // 30% {\r\n    //   transform: translateX(65%);\r\n    // }\r\n\r\n    // 40% {\r\n    //   transform: translateX(40%);\r\n    // }\r\n\r\n    // 65% {\r\n    //   transform: translateX(15%);\r\n    // }\r\n\r\n    // 85% {\r\n    //   transform: translateX(25%);\r\n    // }\r\n\r\n    100% {\r\n      transform: translateX(0%);\r\n    }\r\n  }\r\n\r\n  .completed {\r\n    background: #7ad0ff;\r\n  }\r\n\r\n  .incomplete {\r\n    background: #ff6041;\r\n  }\r\n\r\n  .warning {\r\n    background: #00ffc0;\r\n  }\r\n\r\n  .completeds {\r\n    color: #7ad0ff;\r\n  }\r\n\r\n  .incompletes {\r\n    color: #ff6041;\r\n  }\r\n\r\n  .warnings {\r\n    color: #00ffc0;\r\n  }\r\n}\r\n\r\n.ql-center {\r\n  display: flex;\r\n  margin-top: 20px;\r\n  justify-content: space-around;\r\n  margin-top: 14px;\r\n\r\n  .ql-Box {\r\n    width: 75px;\r\n    height: 49px;\r\n    border: 1px solid #7ad0ff;\r\n    // opacity: 0.6;\r\n    border-radius: 2px;\r\n\r\n    .ql-box1 {\r\n      font-family: Alibaba PuHuiTi;\r\n      font-weight: bold;\r\n      font-size: 16px;\r\n      color: #7ad0ff;\r\n      margin-top: -10px;\r\n    }\r\n\r\n    .ql-box {\r\n      display: flex;\r\n      padding-left: 8px;\r\n      padding-right: 9px;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      width: 100%;\r\n      height: 34px;\r\n\r\n      .left_ql {\r\n        width: 49px;\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        color: #ffffff;\r\n\r\n        .yuan {\r\n          width: 7px;\r\n          height: 7px;\r\n          border-radius: 50%;\r\n        }\r\n\r\n        .pp {\r\n          color: #fff;\r\n          font-size: 12px;\r\n        }\r\n      }\r\n\r\n      img {\r\n        height: 12px;\r\n        width: 7px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.warn1 {\r\n  background: url(\"../assets/image/warnred.png\");\r\n}\r\n\r\n.warn2 {\r\n  background: url(\"../assets/image/warnyellow.png\");\r\n}\r\n\r\n.warn3 {\r\n  background: url(\"../assets/image/warngreen.png\");\r\n}\r\n\r\n.warning12 {\r\n  background-size: 100% 100%;\r\n  // width: 365px;\r\n  height: 47px;\r\n\r\n  .info {\r\n    margin-top: 5px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    text-align: left;\r\n\r\n    margin-left: 50px;\r\n\r\n    .info1 {\r\n      display: flex;\r\n      flex-direction: column;\r\n\r\n      p:nth-of-type(1) {\r\n        font-size: 13px;\r\n        font-family: Alibaba PuHuiTi;\r\n        font-weight: bold;\r\n      }\r\n\r\n      .red {\r\n        color: #ff0000;\r\n      }\r\n\r\n      .green {\r\n        color: #00ffcc;\r\n      }\r\n\r\n      .yellow {\r\n        color: #ffff00;\r\n      }\r\n\r\n      p:nth-of-type(2) {\r\n        font-size: 16px;\r\n        font-family: Alibaba PuHuiTi;\r\n        font-weight: 400;\r\n        color: #ffffff;\r\n      }\r\n    }\r\n\r\n    .info2 {\r\n      margin-right: 10px;\r\n      font-size: 15px;\r\n      font-family: Alibaba PuHuiTi;\r\n      font-weight: 400;\r\n      color: #cffff8;\r\n    }\r\n  }\r\n}\r\n\r\n.xxxx {\r\n  position: absolute;\r\n  top: 1%;\r\n  right: 1%;\r\n  width: 25px;\r\n  height: 25px;\r\n  z-index: 99999;\r\n}\r\n\r\n/* 菜单容器 */\r\n.menu-container {\r\n  display: flex;\r\n  width: 330px;\r\n  height: 736px;\r\n}\r\n\r\n/* 菜单样式 */\r\n.menu {\r\n  width: 100%;\r\n  // background-color: #fff;\r\n  // border: 1px solid #ddd;\r\n  border-radius: 4px;\r\n}\r\n\r\n/deep/.el-pagination {\r\n  --el-pagination-bg-color: none;\r\n  --el-pagination-button-color: #fff;\r\n  --el-pagination-font-size: 17px;\r\n  margin-left: -13px;\r\n}\r\n\r\n/deep/.el-pagination__total {\r\n  color: #fff;\r\n  font-size: 15px;\r\n}\r\n\r\n/deep/.el-pagination button:disabled {\r\n  background: none;\r\n  font-size: 17px;\r\n}\r\n\r\n/deep/.el-pagination button {\r\n  background: none;\r\n  font-size: 17px;\r\n}\r\n\r\n/deep/.el-icon {\r\n  font-size: 17px !important;\r\n}\r\n\r\n/* 菜单项样式 */\r\n.menu-group {\r\n  margin-top: 14px;\r\n}\r\n\r\n.menu-item {\r\n  cursor: pointer;\r\n  background: url(\"../assets/image/rightbeij.png\");\r\n  background-size: 100% 100%;\r\n  background-repeat: no-repeat;\r\n\r\n  width: 100%;\r\n  height: 32px;\r\n  font-family: Source Han Sans SC;\r\n  font-weight: 500;\r\n  font-size: 17px;\r\n  color: #ffffff;\r\n  display: flex;\r\n  align-items: center;\r\n  padding-left: 10px;\r\n  // justify-content: center;\r\n}\r\n\r\n.menu-item:hover {\r\n  // background-color: #f0f0f0;\r\n}\r\n\r\n.submenu {\r\n  // background-color: #f9f9f9;\r\n  padding-left: 20px;\r\n}\r\n\r\n.submenu-item {\r\n  padding: 3px;\r\n  padding-left: 12px;\r\n  margin: 8px;\r\n  font-size: 16px;\r\n  color: #ffffff;\r\n  cursor: pointer;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background-color: #2c343f;\r\n}\r\n\r\n.submenu-item:hover {\r\n  background-color: #163561;\r\n}\r\n\r\n.qiuqiu {\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  .siqiu {\r\n    width: 16px;\r\n    height: 16px;\r\n    margin-left: 10px;\r\n    margin-right: 7px;\r\n  }\r\n}\r\n\r\n.listtype {\r\n  background: url(\"../assets/image/xuanzhotiemmoon.png\");\r\n  background-repeat: no-repeat;\r\n  background-size: 100% 100%;\r\n  width: 80px;\r\n  height: 26px;\r\n  text-align: center;\r\n  line-height: 26px;\r\n\r\n  font-size: 13px;\r\n  font-family: Alibaba PuHuiTi;\r\n  font-weight: 400;\r\n  color: #fff;\r\n}\r\n\r\n.listtypes {\r\n  background: url(\"../assets/image/xuanzhotiemmoon.png\");\r\n  background-repeat: no-repeat;\r\n  background-size: 100% 100%;\r\n  width: 100px;\r\n  height: 32px;\r\n  text-align: center;\r\n  line-height: 32px;\r\n  cursor: pointer;\r\n  font-size: 13px;\r\n  font-family: Alibaba PuHuiTi;\r\n  font-weight: 400;\r\n  color: #fff;\r\n}\r\n\r\n.ellipsis {\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  max-width: 190px;\r\n  /* 你可以根据需要调整宽度 */\r\n  font-size: 16px;\r\n}\r\n\r\n.botbtn {\r\n  position: fixed;\r\n  top: 978px;\r\n  // left: 228px;\r\n  left: 355px;\r\n  width: 200px;\r\n  height: 43px;\r\n  background: #022d56;\r\n  border-radius: 8px;\r\n  z-index: 20;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n\r\n  .btt {\r\n    color: #fff;\r\n    font-size: 17px;\r\n    line-height: 44px;\r\n    width: 88px;\r\n    text-align: center;\r\n    height: 43px;\r\n    cursor: pointer;\r\n  }\r\n\r\n  .btt1 {\r\n    color: rgb(8, 207, 241);\r\n    font-size: 17px;\r\n    line-height: 44px;\r\n    width: 88px;\r\n    text-align: center;\r\n    height: 43px;\r\n    cursor: pointer;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";OAwD8BA,UAAgC;OAsBxCC,UAAmC;;EAvEhDC,KAAK,EAAC;AAAQ;;;;EA0BdA,KAAK,EAAC;;;EAwBAA,KAAK,EAAC;AAAK;;EAMZA,KAAK,EAAC,gBAAgB;EAEtB,sBAAoB,EAAC,OAAO;EAC5B,4BAA0B,EAAC;;;EAGtBA,KAAK,EAAC;AAAM;;EAMRA,KAAK,EAAC;AAAQ;;;;;;;;;;uBA1EnCC,mBAAA,CA0HM,cAtHIC,KAAA,CAAAC,UAAU,I,cAHlBC,YAAA,CAIaC,wBAAA,CAHNH,KAAA,CAAAI,YAAY;;IAChBC,gBAAc,EAAEC,IAAA,CAAAC;uGAGnBC,mBAAA,CASM,OATNC,UASM,I,kBARJV,mBAAA,CAOMW,SAAA,QAAAC,WAAA,CANoBX,KAAA,CAAAY,WAAW,GAA3BC,IAAI,EAAEC,KAAK;yBADrBf,mBAAA,CAOM;MALHgB,GAAG,EAAED,KAAK;MACVhB,KAAK,EAAAkB,eAAA,CAAEhB,KAAA,CAAAiB,SAAS,IAAIH,KAAK;MACzBI,OAAK,EAAAC,MAAA,IAAEC,QAAA,CAAAC,SAAS,CAACP,KAAK;wBAEpBD,IAAI,gCAAAS,UAAA;oCAQHtB,KAAA,CAAAuB,SAAS,I,cALjBrB,YAAA,CAOSsB,gBAAA;;IANNC,GAAG,EAAEzB,KAAA,CAAAyB,GAAG;IACRC,YAAY,EAAE1B,KAAA,CAAA0B,YAAY;IAC3B5B,KAAK,EAAC,WAAW;IAChB6B,SAAS,EAAEC,MAAA,CAAAD,SAAS;IAEpBE,aAAW,EAAET,QAAA,CAAAU;uHAKR9B,KAAA,CAAA+B,MAAM,I,cAHd7B,YAAA,CAOU8B,iBAAA;;IANPC,KAAK,EAAEjC,KAAA,CAAAiC,KAAK;IACZC,cAAa,EAAEd,QAAA,CAAAe,YAAY;IAE3BN,aAAW,EAAET,QAAA,CAAAgB,WAAW;IACxBC,UAAU,EAAErC,KAAA,CAAAqC,UAAU;IACtBC,aAAa,EAAEtC,KAAA,CAAAuC;8IAEYvC,KAAA,CAAAC,UAAU,I,cAAxCF,mBAAA,CAyFM,OAzFNyC,UAyFM,GAxFJhC,mBAAA,CA2EM;IA1EJV,KAAK,EAAAkB,eAAA,EAAC,YAAY;2BACwBhB,KAAA,CAAAyC,MAAM;sBAA6BzC,KAAA,CAAA0C,WAAW;4BAAmC1C,KAAA,CAAA2C;;MAM3HC,YAAA,CAkESC,iBAAA;IAlEAC,YAAW,EAAE1B,QAAA,CAAA2B,UAAU;IAAEjD,KAAK,EAAC,SAAS;IAACkD,GAAG,EAAC;;sBACpD,MAMe,CANfJ,YAAA,CAMeK,sBAAA;MALbnD,KAAK,EAAC,MAAM;MACZoD,WAAW,EAAC,OAAO;MAClBC,OAAO,EAAE7C,IAAA,CAAA8C,aAAa;MACtB,iBAAe,EAAE,IAAI;MACrBC,QAAM,EAAEjC,QAAA,CAAAkC;sDAGXV,YAAA,CAIYW,mBAAA;MAHVzD,KAAK,EAAC,UAAU;kBACPE,KAAA,CAAAwD,KAAK;iEAALxD,KAAA,CAAAwD,KAAK,GAAArC,MAAA;MACd+B,WAAW,EAAC;uEAEd1C,mBAAA,CAA8D;MAAzDV,KAAK,EAAC,QAAQ;MAAC2D,GAAgC,EAAhC7D,UAAgC;MAAC8D,GAAG,EAAC;iCACzDlD,mBAAA,CAuCM,OAvCNmD,UAuCM,GAtCJC,mBAAA,4LAGU,E,+BACV7D,mBAAA,CAiCM,OAjCN8D,UAiCM,GA3BJD,mBAAA,YAAe,EACfpD,mBAAA,CAyBM,OAzBNsD,UAyBM,I,kBAxBJ/D,mBAAA,CAuBMW,SAAA,QAAAC,WAAA,CAtBoBX,KAAA,CAAAuC,UAAU,GAA1BwB,IAAI,EAAEjD,KAAK;2BADrBf,mBAAA,CAuBM;QArBHgB,GAAG,EAAED,KAAK;QACXhB,KAAK,EAAC;UAENU,mBAAA,CAUM,OAVNwD,UAUM,G,0BATJxD,mBAAA,CAIE;QAHAV,KAAK,EAAC,OAAO;QACb2D,GAAmC,EAAnC5D,UAAmC;QACnC6D,GAAG,EAAC;mCAENlD,mBAAA,CAEM;QAFDV,KAAK,EAAC,WAAW;QAAEoB,OAAK,EAAAC,MAAA,IAAEC,QAAA,CAAA6C,aAAa,CAACF,IAAI;0BAC5CA,IAAI,CAACG,IAAI,wBAAAC,UAAA,GAEdP,mBAAA,oEAAmE,C,GAGrEA,mBAAA,wXAKU,C;+DA5BH5D,KAAA,CAAAoE,OAAO,E,KAiCtBxB,YAAA,CASgByB,wBAAA;MARbC,YAAW,EAAElD,QAAA,CAAAmD,gBAAgB;MAC9B,qBAAmB,EAAC,MAAM;MACzBC,eAAc,EAAEpD,QAAA,CAAAqD,mBAAmB;MACnC,WAAS,EAAE,EAAE;MACb,aAAW,EAAE,CAAC;MACfC,MAAM,EAAC,yBAAyB;MAC/BC,KAAK,EAAE3E,KAAA,CAAA2E;2EAGVf,mBAAA,kBAAqB,C;;wDAGzBA,mBAAA,mBAAsB,EACtBA,mBAAA,UAAa,EACbA,mBAAA,UAAa,EAEbpD,mBAAA,CAOO;IANLV,KAAK,EAAAkB,eAAA,EAAC,aAAa;4BACwBhB,KAAA,CAAAyC,MAAM;sBAA6BzC,KAAA,CAAA0C,WAAW;6BAAoC1C,KAAA,CAAA2C", "ignoreList": []}]}