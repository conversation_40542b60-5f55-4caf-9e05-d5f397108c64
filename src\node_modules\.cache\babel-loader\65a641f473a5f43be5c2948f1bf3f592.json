{"remainingRequest": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\babel-loader\\lib\\index.js!E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\src\\views\\dayi\\zichan.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\src\\views\\dayi\\zichan.vue", "mtime": 1751448864722}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\babel.config.js", "mtime": 1726672621424}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["Electricity", "biao1s", "biao1ss", "Titles", "Electricity1", "Electricity2", "Electricity3", "Electricity4", "Electricity5", "Electricity6", "Electricity7", "Electricity8", "huanxing", "zhuzhuangtu", "zhuzhuangtu1", "axios", "baseURL", "process", "env", "VUE_APP_BASE_API", "api", "create", "headers", "clientid", "clients<PERSON>ret", "components", "data", "loading", "loading1", "loading2", "loading3", "loading4", "loading5", "sbnum", "chartDatazz", "title", "xAxisdata1", "xAxisdata2", "yAxisdata", "chartDatazz1", "chartData", "value", "legend", "chartData1", "xAxisdata", "yAxisdata1", "yAxisdata2", "chartData2", "name", "chartData3", "isshow", "options", "label", "selectvalue2", "options2", "selectvalue3", "options3", "selectvalue1", "options1", "options4", "selectvalue4", "optionData", "itemStyle", "color", "opacity", "optionData1", "computed", "equipmentRank", "$store", "getters", "yiqiStatus", "userDistribution", "testStatistics", "topUsers", "mounted", "dispatch", "length", "setInterval", "getdata2", "methods", "getdata1", "response", "post", "console", "log", "names", "map", "item", "reverse", "times", "time", "error", "controlCount", "getdata3", "outer", "inner", "incharge", "getdata4", "project", "lab", "test", "getdata5", "anniu"], "sources": ["E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\src\\views\\dayi\\zichan.vue"], "sourcesContent": ["<template>\r\n  <keep-alive>\r\n    <div class=\"contents\" v-if=\"isshow\" v-loading=\"false\" element-loading-text=\"Loading...\"\r\n      :element-loading-spinner=\"svg\" element-loading-background=\"rgba(0, 0, 0, 1)\">\r\n      <div class=\"toubu\">\r\n        <div style=\"margin-left: 20px; display: flex; align-items: center\" v-if=\"false\">\r\n          <div style=\"display: flex; width: 100%; align-items: center\">\r\n            <span class=\"sp\">当前位置：</span>\r\n            <el-select class=\"el-select\" v-model=\"selectvalue1\" placeholder=\"selectvalue1\"\r\n              style=\"width: 64px; height: 35.1px\" @change=\"handleChange\">\r\n              <el-option v-for=\"item in options1\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n            </el-select>\r\n            <el-select class=\"el-select\" v-model=\"selectvalue2\" placeholder=\"selectvalue2\"\r\n              style=\"width: 64px; height: 35.1px\" @change=\"handleChange\">\r\n              <el-option v-for=\"item in options2\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n            </el-select>\r\n            <el-select class=\"el-select\" v-model=\"selectvalue3\" placeholder=\"selectvalue3\"\r\n              style=\"width: 78px; height: 35.1px\" @change=\"handleChange\">\r\n              <el-option v-for=\"item in options3\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n            </el-select>\r\n          </div>\r\n          <img v-if=\"isshow\" class=\"img1sss\" @click=\"anniu()\" src=\"../../assets/image/table-x.png\" alt=\"\" />\r\n        </div>\r\n\r\n        <div class=\"all\">\r\n          <div class=\"all1\">\r\n            <!-- <Titles class=\"ltitle1\" tit=\"仪器实时状态\">\r\n              <div class=\"dayi\">\r\n                <span>共</span>\r\n                <span>{{ sbnum }}</span>\r\n                <span>台仪器安装客户端</span>\r\n              </div>\r\n              <Electricity1 v-if=\"yiqiStatus\" class=\"zhuzhuangtu\" :chartData=\"yiqiStatus\"></Electricity1>         \r\n            </Titles> -->\r\n            <Titles class=\"ltitle11\" tit=\"人员分布统计\">\r\n\r\n              <!-- <zhuzhuangtu class=\"zhuzhuangtu\" :chartData=\"chartData1\"></zhuzhuangtu> -->\r\n              <huanxing style=\"margin-top: 150px;\" v-if=\"userDistribution\" :chartData=\"userDistribution\"></huanxing>\r\n\r\n            </Titles>\r\n          </div>\r\n          <div class=\"line1\"></div>\r\n          <div class=\"all2\">\r\n            <!-- <Titles class=\"ltitle1\" tit=\"仪器设备使用情况\">\r\n            <div class=\"shinei\">\r\n              <Electricity8></Electricity8>\r\n            </div>\r\n          </Titles>\r\n          <Titles class=\"ltitle\" tit=\"办公设备使用情况\">\r\n            <div class=\"shinei\">\r\n              <Electricity8></Electricity8>\r\n            </div>\r\n          </Titles> -->\r\n            <div>\r\n              <Titles class=\"ltitle1\" tit=\"课题测试统计\">\r\n                <div class=\"shinei\">\r\n                  <Electricity3 v-if=\"testStatistics\" :chartData=\"testStatistics\"></Electricity3>\r\n                </div>\r\n              </Titles>\r\n            </div>\r\n          </div>\r\n          <div class=\"all3\">\r\n\r\n            <Titles class=\"ltitle1\" tit=\"仪器使用排行\">\r\n              <div class=\"shinei\">\r\n                <!-- <Electricity6></Electricity6> -->\r\n                <zhuzhuangtu v-if=\"equipmentRank\" class=\"zhuzhuangtu1\" :chartData=\"equipmentRank\"></zhuzhuangtu>\r\n              </div>\r\n            </Titles>\r\n            <Titles class=\"ltitle1\" tit=\"课题组使用统计\">\r\n              <div class=\"shinei\">\r\n                <!-- <huanxing :chartData=\"chartData\"></huanxing> -->\r\n                <zhuzhuangtu1 v-if=\"topUsers\" class=\"zhuzhuangtu1\" :chartData=\"topUsers\"></zhuzhuangtu1>\r\n              </div>\r\n            </Titles>\r\n            <!-- <div class=\"shuantitle\">\r\n            <div style=\"width: 50%\">\r\n              <div class=\"title\">实时负载率</div>\r\n              <div class=\"nenghao\">实时负载率:</div>\r\n              <p class=\"nhp\">30%</p>\r\n            </div>\r\n            <div style=\"width: 50%\">\r\n              <div class=\"title\">实时总功率</div>\r\n              <div class=\"nenghao\">实时总功率:</div>\r\n              <p class=\"nhp\">200Kw</p>\r\n            </div>\r\n          </div>\r\n      -->\r\n\r\n\r\n\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </keep-alive>\r\n</template>\r\n\r\n<script>\r\nimport Electricity from \"@/components/echarts/dianbiao/biao1.vue\";\r\nimport biao1s from \"@/components/echarts/dianbiao/biao1s.vue\";\r\nimport biao1ss from \"@/components/echarts/dianbiao/biao1ss.vue\";\r\nimport Titles from \"@/components/common/Titles.vue\";\r\nimport Electricity1 from \"@/components/dayi/Electricity1.vue\";\r\nimport Electricity2 from \"@/components/echarts/dianbiao/Electricity2.vue\";\r\nimport Electricity3 from \"@/components/dayi//zhexiantu.vue\";\r\nimport Electricity4 from \"@/components/echarts/dianbiao/Electricity4.vue\";\r\nimport Electricity5 from \"@/components/echarts/dianbiao/Electricity5.vue\";\r\nimport Electricity6 from \"@/components/dayi/Electricity6.vue\";\r\nimport Electricity7 from \"@/components/echarts/dianbiao/Electricity7.vue\";\r\nimport Electricity8 from \"@/components/dayi/Electricity8.vue\";\r\nimport huanxing from \"@/components/dayi/xiaobingtu.vue\";\r\nimport zhuzhuangtu from \"@/components/dayi/zhuzhuangtu.vue\";\r\nimport zhuzhuangtu1 from \"@/components/dayi/zhuzhuangtu1.vue\";\r\nimport axios from \"axios\";\r\n// 使用环境变量设置基础 API 地址\r\nconst baseURL = process.env.VUE_APP_BASE_API || '/lims/api';\r\n\r\nconst api = axios.create({\r\n  baseURL\r\n});\r\nconst headers = {\r\n  clientid: '5a298e93-158d-4e22-83cf-6ceb62e9b4f1',\r\n  clientsecret: '2c8ec39e-9887-482a-b28b-e64c496b601c'\r\n};\r\nexport default {\r\n  components: {\r\n    Titles,\r\n    Electricity1,\r\n    Electricity,\r\n    Electricity2,\r\n    Electricity3,\r\n    Electricity4,\r\n    Electricity5,\r\n    Electricity6,\r\n    Electricity7,\r\n    Electricity8,\r\n    huanxing,\r\n    biao1s,\r\n    biao1ss,\r\n    zhuzhuangtu,\r\n    zhuzhuangtu1\r\n  },\r\n  data() {\r\n    return {\r\n      loading: true,\r\n      loading1: true,\r\n      loading2: true,\r\n      loading3: true,\r\n      loading4: true,\r\n      loading5: true,\r\n      //svg: 'el-icon-loading' ,// 或者自定义 SVG 图标\r\n      sbnum: 723,\r\n      chartDatazz: {\r\n        title: [\"已领用\", \"未领用\"],\r\n        xAxisdata1: [80, 80, 97, 53, 95, 70, 88],\r\n        xAxisdata2: [100, 100, 100, 100, 100, 100, 100],\r\n        yAxisdata: [\"一月\", \"二月\", \"三月\", \"四月\", \"五月\", \"六月\", \"七月\"],\r\n      },\r\n      chartDatazz1: {\r\n        title: [\"已领用\", \"未领用\"],\r\n        xAxisdata1: [80, 80, 97, 53, 95, 70, 88],\r\n        xAxisdata2: [100, 100, 100, 100, 100, 100, 100],\r\n        yAxisdata: [\"一月\", \"二月\", \"三月\", \"四月\", \"五月\", \"六月\", \"七月\"],\r\n      },\r\n      chartData: {\r\n        value: [1321, 18582, 651],\r\n        legend: [\r\n          \"校外人员\",\r\n          \"校内人员\",\r\n          \"管理员\",\r\n        ],\r\n      },\r\n      chartData1: {\r\n        title: [\"已领用\", \"未领用\"],\r\n        xAxisdata: [\"20日\", \"21日\", \"22日\", \"23日\", \"25日\", \"24日\",],\r\n        yAxisdata1: [4, 7, 5, 9, 6, 5],\r\n        yAxisdata2: [4, 7, 5, 9, 6, 5],\r\n      },\r\n      chartData2: [\r\n        {\r\n          name: \"正在使用\",\r\n          value: 271,\r\n        },\r\n        {\r\n          name: \"待机中\",\r\n          value: 452,\r\n        }],\r\n      chartData3: [\r\n        {\r\n          name: \"正在使用\",\r\n          value: 271,\r\n        },\r\n        {\r\n          name: \"待机中\",\r\n          value: 452,\r\n\r\n        },\r\n        {\r\n          name: \"故障\",\r\n          value: 21,\r\n\r\n        }],\r\n      isshow: true,\r\n      options: [\r\n        {\r\n          value: \"总览\",\r\n          label: \"总览\",\r\n        },\r\n        {\r\n          value: \"能耗分析\",\r\n          label: \"能耗分析\",\r\n        },\r\n        {\r\n          value: \"能流分析\",\r\n          label: \"能流分析\",\r\n        },\r\n        {\r\n          value: \"设备状态\",\r\n          label: \"设备状态\",\r\n        },\r\n        {\r\n          value: \"一键抄表\",\r\n          label: \"一键抄表\",\r\n        },\r\n        {\r\n          value: \"费用管理\",\r\n          label: \"费用管理\",\r\n        },\r\n        {\r\n          value: \"碳排放管理\",\r\n          label: \"碳排放管理\",\r\n        },\r\n      ],\r\n      selectvalue2: \"B3\",\r\n      options2: [\r\n        {\r\n          value: \"B3\",\r\n          label: \"B3\",\r\n        },\r\n      ],\r\n      selectvalue3: \"B3\",\r\n      options3: [\r\n        {\r\n          value: \"B3\",\r\n          label: \"B3\",\r\n        },\r\n      ],\r\n      selectvalue1: \"B3\",\r\n      options1: [\r\n        {\r\n          value: \"B3\",\r\n          label: \"B3\",\r\n        },\r\n      ],\r\n      selectvalue3: \"B1栋\",\r\n      options4: [\r\n        {\r\n          value: \"B1栋\",\r\n          label: \"B1栋\",\r\n        },\r\n        {\r\n          value: \"B2栋\",\r\n          label: \"B2栋\",\r\n        },\r\n        {\r\n          value: \"B3栋\",\r\n          label: \"B3栋\",\r\n        },\r\n        {\r\n          value: \"B4栋\",\r\n          label: \"B4栋\",\r\n        },\r\n        {\r\n          value: \"W1栋\",\r\n          label: \"W1栋\",\r\n        },\r\n        {\r\n          value: \"W2栋\",\r\n          label: \"W2栋\",\r\n        },\r\n      ],\r\n      selectvalue4: \"B1栋\",\r\n      optionData: [\r\n        {\r\n          name: \"一级告警\",\r\n          value: 16,\r\n          itemStyle: { color: \"#EB6877\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"二级告警\",\r\n          value: 27,\r\n          itemStyle: { color: \"#F8B551\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"三级告警\",\r\n          value: 17,\r\n          itemStyle: { color: \"#B954E8\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"四级告警\",\r\n          value: 40,\r\n          itemStyle: { color: \"#0284F0\", opacity: 0.9 },\r\n        },\r\n      ],\r\n      optionData1: [\r\n        {\r\n          name: \"一级告警\",\r\n          value: 16,\r\n          itemStyle: { color: \"#EB6877\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"二级告警\",\r\n          value: 27,\r\n          itemStyle: { color: \"#F8B551\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"三级告警\",\r\n          value: 17,\r\n          itemStyle: { color: \"#B954E8\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"四级告警\",\r\n          value: 40,\r\n          itemStyle: { color: \"#0284F0\", opacity: 0.9 },\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  computed: {\r\n    loading() {\r\n      return this.loading1 || this.loading2 || this.loading3 || this.loading4 || this.loading5;\r\n    },\r\n    // 使用 Vuex 的 getters 获取持久化的数据\r\n    equipmentRank() {\r\n      return this.$store.getters[\"equipment/equipmentRank\"];\r\n    },\r\n    yiqiStatus() {\r\n      return this.$store.getters[\"equipment/yiqiStatus\"];\r\n    },\r\n    userDistribution() {\r\n      return this.$store.getters[\"equipment/userDistribution\"];\r\n    },\r\n    testStatistics() {\r\n      return this.$store.getters[\"equipment/testStatistics\"];\r\n    },\r\n    topUsers() {\r\n      return this.$store.getters[\"equipment/topUsers\"];\r\n    },\r\n  },\r\n  mounted() {\r\n\r\n    this.$store.dispatch('equipment/fetchEquipmentRank');\r\n    this.$store.dispatch('equipment/getdata2');\r\n    this.$store.dispatch('equipment/getdata3');\r\n    this.$store.dispatch('equipment/getdata4');\r\n    this.$store.dispatch('equipment/getdata5');\r\n\r\n    if (!this.equipmentRank.length) {\r\n      this.$store.dispatch('equipment/fetchEquipmentRank'); // 如果没有缓存，获取数据\r\n    }\r\n    if (!this.yiqiStatus.length) {\r\n      this.$store.dispatch('equipment/getdata2'); // 如果没有缓存，获取数据\r\n    }\r\n    if (!this.userDistribution.length) {\r\n      this.$store.dispatch('equipment/getdata3'); // 如果没有缓存，获取数据\r\n    }\r\n    if (!this.testStatistics.length) {\r\n      this.$store.dispatch('equipment/getdata4'); // 如果没有缓存，获取数据\r\n    }\r\n    if (!this.topUsers.length) {\r\n      this.$store.dispatch('equipment/getdata5'); // 如果没有缓存，获取数据\r\n    }\r\n    setInterval(() => {\r\n      this.$store.dispatch('equipment/fetchEquipmentRank');\r\n      this.$store.dispatch('equipment/getdata2');\r\n      this.$store.dispatch('equipment/getdata3');\r\n      this.$store.dispatch('equipment/getdata4');\r\n      this.$store.dispatch('equipment/getdata5');\r\n    }, 36000000);\r\n    // this.getdata1()\r\n    this.getdata2()\r\n    // this.getdata3()\r\n    // this.getdata4()\r\n    // this.getdata5()\r\n    // setInterval(() => {\r\n    //   this.getdata1()\r\n    //   this.getdata2()\r\n    //   this.getdata3()\r\n    //   this.getdata4()\r\n    //   this.getdata5()\r\n    // }, 10000);\r\n\r\n  },\r\n\r\n  methods: {\r\n\r\n    async getdata1() {  //仪器使用排行\r\n      try {\r\n        const response = await api.post('', {\r\n\r\n          \"method\": \"equipment/time_rank\",\r\n          \"params\": {\r\n            \"num\": 10,\r\n            \"start\": 1704038400,\r\n            \"end\": 1735660800\r\n          }\r\n\r\n        }, { headers });\r\n        // 检查是否成功拿到 token\r\n        if (response.data) {\r\n          console.log('仪器使用排行:', response.data);\r\n          let data = response.data.response\r\n          // 提取 names 和 times\r\n          const names = data.map(item => item.name).reverse();\r\n          const times = data.map(item => item.time).reverse();\r\n          this.chartDatazz1.yAxisdata = names\r\n          this.chartDatazz1.xAxisdata1 = times\r\n          this.loading1 = false\r\n        }\r\n      } catch (error) {\r\n        console.error('登录失败:', error);\r\n      }\r\n    },\r\n\r\n    async getdata2() {  //仪器使用情况\r\n      try {\r\n        const response = await api.post('', {\r\n          \"method\": \"equipment/getSummaryInfo\",\r\n          \"params\": {}\r\n        }, { headers });\r\n        // 检查是否成功拿到 token\r\n        if (response.data) {\r\n          this.sbnum = response.data.response.controlCount,\r\n            console.log('仪器使用情况:', response.data.response);\r\n\r\n\r\n        }\r\n\r\n      } catch (error) {\r\n        console.error('登录失败:', error);\r\n      }\r\n    },\r\n    async getdata3() {  //人员分布情况\r\n      try {\r\n        const response = await api.post('', {\r\n          \"method\": \"summarize/userStatus\",\r\n          \"params\": {}\r\n        }, { headers });\r\n        // 检查是否成功拿到 token\r\n        if (response.data) {\r\n          console.log('人员分布情况:', response.data);\r\n          this.chartData = {\r\n            value: [response.data.response.outer, response.data.response.inner, response.data.response.incharge],\r\n            legend: [\r\n              \"校外人员\",\r\n              \"校内人员\",\r\n              \"管理员\",\r\n            ],\r\n          }\r\n          this.loading3 = false\r\n        }\r\n      } catch (error) {\r\n        console.error('登录失败:', error);\r\n      }\r\n    },\r\n    async getdata4() {  //课题测试情况\r\n\r\n      try {\r\n        const response = await api.post('', {\r\n          \"method\": \"summarize/labStatus\",\r\n          \"params\": {}\r\n        }, { headers });\r\n        // 检查是否成功拿到 token\r\n        if (response) {\r\n          console.log('课题测试情况:', response.data.response);\r\n          this.chartData3 = [\r\n            {\r\n              name: \"总课题数\",\r\n              value: response.data.response.project,\r\n            },\r\n            {\r\n              name: \"课题数\",\r\n              value: response.data.response.lab,\r\n\r\n            },\r\n            {\r\n              name: \"测试数\",\r\n              value: response.data.response.test,\r\n\r\n            }]\r\n          this.loading4 = false\r\n        }\r\n      } catch (error) {\r\n        console.error('登录失败:', error);\r\n      }\r\n    },\r\n    async getdata5() {  //用户排行\r\n      try {\r\n        const response = await api.post('', {\r\n          \"method\": \"eq_reserv/getTopUsers\",\r\n          \"params\": {\r\n            \"num\": 9,\r\n            \"year\": 2024\r\n          }\r\n        }, { headers });\r\n        // 检查是否成功拿到 token\r\n        if (response.data) {\r\n          console.log('用户排行:', response.data.response);\r\n          let data = response.data.response\r\n          // 提取 names 和 times\r\n          const names = data.map(item => item.name).reverse();\r\n          const times = data.map(item => item.time).reverse();\r\n          this.chartDatazz.yAxisdata = names\r\n          this.chartDatazz.xAxisdata1 = times\r\n        }\r\n        this.loading5 = false\r\n      } catch (error) {\r\n        console.error('登录失败:', error);\r\n      }\r\n    },\r\n    anniu() {\r\n      this.isshow = false;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.zhuzhuangtu {\r\n  margin-top: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.zhuzhuangtu1 {\r\n  margin-top: -26px;\r\n\r\n}\r\n\r\n.all {\r\n  display: flex;\r\n  flex-direction: row;\r\n  margin-top: 5px;\r\n\r\n  .zong {\r\n    display: flex;\r\n    flex-direction: row;\r\n    margin-top: 10px;\r\n\r\n    .echart1,\r\n    .echart2 {\r\n      flex: 1;\r\n\r\n      .center {\r\n        margin-top: -24px;\r\n        font-family: \"Source Han Sans SC\", sans-serif;\r\n        font-weight: 400;\r\n        font-size: 17px;\r\n        color: #00ffb6;\r\n        text-align: center;\r\n        margin-bottom: 10px;\r\n      }\r\n\r\n      .btn {\r\n        width: 133px;\r\n        height: 31px;\r\n        border: 1px solid #2d6cb0;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        font-family: \"Source Han Sans SC\", sans-serif;\r\n        font-weight: bold;\r\n        font-size: 15px;\r\n        color: #ffffff;\r\n        border-radius: 30px;\r\n        margin-left: 7%;\r\n      }\r\n    }\r\n  }\r\n\r\n  .ltitle1 {\r\n    margin-top: 10px;\r\n  }\r\n\r\n  .ltitle11 {\r\n    margin-top: 30px;\r\n    position: relative;\r\n  }\r\n\r\n  .dayi {\r\n    position: absolute;\r\n    top: 42px;\r\n    left: 68px;\r\n    z-index: 20;\r\n    font-size: 22px;\r\n    color: #fff;\r\n    text-align: center;\r\n\r\n    span:nth-child(2) {\r\n      font-size: 32px;\r\n    }\r\n  }\r\n\r\n  .line1 {\r\n    width: 2px;\r\n    height: 823px;\r\n    opacity: 0.64;\r\n    background-color: #204964;\r\n  }\r\n\r\n  .all1 {\r\n    flex: 462;\r\n\r\n    .nenghao {\r\n      width: 227px;\r\n      height: 173px;\r\n      background: url(\"../../assets/image/nenghao.png\");\r\n      background-size: 100% 100%;\r\n      margin-left: 120px;\r\n      margin-top: 21px;\r\n      text-align: center;\r\n      font-family: Alibaba PuHuiTi;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #ffffff;\r\n      line-height: 213px;\r\n    }\r\n\r\n    .nhp {\r\n      text-align: center;\r\n      font-family: Alibaba PuHuiTi;\r\n      font-weight: 500;\r\n      font-size: 30px;\r\n      color: #2cc1ff;\r\n      margin-top: 8px;\r\n    }\r\n\r\n    .nh {\r\n      margin-left: 24px;\r\n      margin-top: 5px;\r\n      width: 423px;\r\n      height: 93px;\r\n      border: 1px solid #364d5a;\r\n      background-size: 100% 100%;\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      padding-left: 42px;\r\n\r\n      .nhimg {\r\n        width: 96.6px;\r\n        height: 70px;\r\n      }\r\n\r\n      .nhtit {\r\n        width: 148px;\r\n        margin-left: 10px;\r\n        margin-top: 10px;\r\n\r\n        .p11 {\r\n          font-family: PangMenZhengDao;\r\n          font-weight: 400;\r\n          font-size: 30px;\r\n          color: #7acfff;\r\n        }\r\n\r\n        .p12 {\r\n          font-family: PangMenZhengDao;\r\n          font-weight: 400;\r\n          font-size: 30px;\r\n          color: #ffa170;\r\n        }\r\n\r\n        .p2 {\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 400;\r\n          font-size: 17px;\r\n          color: #ffffff;\r\n        }\r\n      }\r\n\r\n      .nhtit1 {\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: center;\r\n        margin-left: 35px;\r\n\r\n        .nhimg1 {\r\n          width: 16px;\r\n          height: 20px;\r\n        }\r\n\r\n        .pp1 {\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 400;\r\n          font-size: 17px;\r\n          color: #0df29b;\r\n        }\r\n\r\n        .pp2 {\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 400;\r\n          font-size: 17px;\r\n          color: #ffa170;\r\n        }\r\n      }\r\n\r\n      .nht {\r\n        margin-top: 10px;\r\n        display: flex;\r\n        flex-direction: column;\r\n\r\n        .pp {\r\n          margin-left: 35px;\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 400;\r\n          font-size: 17px;\r\n\r\n          color: #cccccc;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .all2 {\r\n    margin-left: 38px;\r\n    flex: 667;\r\n    display: flex;\r\n    flex-direction: column;\r\n\r\n    .shinei {\r\n      .itemshei {\r\n        display: flex;\r\n        justify-content: space-around;\r\n\r\n        .nenghaos {\r\n          width: 227px;\r\n          height: 173px;\r\n          background: url(\"../../assets/image/nenghao.png\");\r\n          background-size: 100% 100%;\r\n          text-align: center;\r\n          margin-left: 10px;\r\n          margin-top: 23px;\r\n          text-align: center;\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 400;\r\n          font-size: 14px;\r\n          color: #ffffff;\r\n          line-height: 144px;\r\n        }\r\n\r\n        .nhps {\r\n          text-align: center;\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 500;\r\n          font-size: 21px;\r\n          color: #2cc1ff;\r\n          margin-top: 8px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .all3 {\r\n    flex: 668;\r\n    margin-left: 45px;\r\n  }\r\n}\r\n\r\n.shinei {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.shuantitle {\r\n  width: 100%;\r\n  display: flex;\r\n  margin-top: 10px;\r\n\r\n  .title {\r\n    width: 95%;\r\n    background: url(\"../../assets/image/title.png\");\r\n    background-size: 100% 100%;\r\n\r\n    height: 25px;\r\n    font-family: Source Han Sans SC;\r\n    font-weight: 400;\r\n    font-size: 25px;\r\n    color: #ffffff;\r\n    text-shadow: 2px 2px 3px rgba(0, 0, 0, 0.47);\r\n    font-style: italic;\r\n    text-align: left;\r\n    line-height: 4px;\r\n    padding-left: 33px;\r\n  }\r\n}\r\n\r\n.nenghao {\r\n  width: 167px;\r\n  height: 113px;\r\n  background: url(\"../../assets/image/nenghao.png\");\r\n  background-size: 100% 100%;\r\n  text-align: center;\r\n  margin-left: 83px;\r\n  margin-top: 63px;\r\n  text-align: center;\r\n  font-family: Alibaba PuHuiTi;\r\n  font-weight: 400;\r\n  font-size: 14px;\r\n  color: #ffffff;\r\n  line-height: 144px;\r\n}\r\n\r\n.nhp {\r\n  text-align: center;\r\n  font-family: Alibaba PuHuiTi;\r\n  font-weight: 500;\r\n  font-size: 21px;\r\n  color: #2cc1ff;\r\n  margin-top: 8px;\r\n}\r\n\r\n.contents {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  background: url(\"../../assets/image/zichanbeijin.png\");\r\n  background-size: 100% 100%;\r\n  width: 1863px;\r\n  height: 868px;\r\n  z-index: 99999;\r\n  padding-left: 34px;\r\n  padding-right: 22px;\r\n  padding-top: 21px;\r\n}\r\n\r\n.toubu {\r\n  width: 100%;\r\n\r\n  position: relative;\r\n}\r\n\r\n.el-select {\r\n  margin-top: -1px;\r\n  margin-left: 10px;\r\n  background: #00203d;\r\n  border-radius: 3px;\r\n  border: 1px solid #3e89db;\r\n\r\n  /deep/.el-select__wrapper {\r\n    background: #00203d !important;\r\n    box-shadow: none;\r\n  }\r\n\r\n  /deep/.el-select__wrapper .is-hovering:not {\r\n    box-shadow: none;\r\n  }\r\n\r\n  /deep/.el-select__wrapper:hover {\r\n    box-shadow: none;\r\n  }\r\n\r\n  /deep/.el-select__placeholder.is-transparent {\r\n    color: #2cc1ff;\r\n  }\r\n\r\n  /deep/.el-select__placeholder {\r\n    color: #2cc1ff;\r\n  }\r\n\r\n  /deep/.el-select-dropdown__item.is-hovering {\r\n    background-color: #2cc1ff !important;\r\n  }\r\n}\r\n\r\n.sp {\r\n  margin-top: -5px;\r\n  margin-left: 12px;\r\n  font-family: Alibaba PuHuiTi;\r\n  font-weight: bold;\r\n  font-size: 21px;\r\n  color: #2cc1ff;\r\n}\r\n\r\n.img1sss {\r\n  cursor: pointer;\r\n  width: 15px;\r\n  height: 15px;\r\n}\r\n</style>"], "mappings": ";;AAmGA,OAAOA,WAAU,MAAO,yCAAyC;AACjE,OAAOC,MAAK,MAAO,0CAA0C;AAC7D,OAAOC,OAAM,MAAO,2CAA2C;AAC/D,OAAOC,MAAK,MAAO,gCAAgC;AACnD,OAAOC,YAAW,MAAO,oCAAoC;AAC7D,OAAOC,YAAW,MAAO,gDAAgD;AACzE,OAAOC,YAAW,MAAO,kCAAkC;AAC3D,OAAOC,YAAW,MAAO,gDAAgD;AACzE,OAAOC,YAAW,MAAO,gDAAgD;AACzE,OAAOC,YAAW,MAAO,oCAAoC;AAC7D,OAAOC,YAAW,MAAO,gDAAgD;AACzE,OAAOC,YAAW,MAAO,oCAAoC;AAC7D,OAAOC,QAAO,MAAO,kCAAkC;AACvD,OAAOC,WAAU,MAAO,mCAAmC;AAC3D,OAAOC,YAAW,MAAO,oCAAoC;AAC7D,OAAOC,KAAI,MAAO,OAAO;AACzB;AACA,MAAMC,OAAM,GAAIC,OAAO,CAACC,GAAG,CAACC,gBAAe,IAAK,WAAW;AAE3D,MAAMC,GAAE,GAAIL,KAAK,CAACM,MAAM,CAAC;EACvBL;AACF,CAAC,CAAC;AACF,MAAMM,OAAM,GAAI;EACdC,QAAQ,EAAE,sCAAsC;EAChDC,YAAY,EAAE;AAChB,CAAC;AACD,eAAe;EACbC,UAAU,EAAE;IACVtB,MAAM;IACNC,YAAY;IACZJ,WAAW;IACXK,YAAY;IACZC,YAAY;IACZC,YAAY;IACZC,YAAY;IACZC,YAAY;IACZC,YAAY;IACZC,YAAY;IACZC,QAAQ;IACRX,MAAM;IACNC,OAAO;IACPW,WAAW;IACXC;EACF,CAAC;EACDY,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACd;MACAC,KAAK,EAAE,GAAG;MACVC,WAAW,EAAE;QACXC,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;QACrBC,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;QACxCC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;QAC/CC,SAAS,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;MACtD,CAAC;MACDC,YAAY,EAAE;QACZJ,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;QACrBC,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;QACxCC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;QAC/CC,SAAS,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;MACtD,CAAC;MACDE,SAAS,EAAE;QACTC,KAAK,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC;QACzBC,MAAM,EAAE,CACN,MAAM,EACN,MAAM,EACN,KAAK;MAET,CAAC;MACDC,UAAU,EAAE;QACVR,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;QACrBS,SAAS,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAE;QACtDC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC9BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;MAC/B,CAAC;MACDC,UAAU,EAAE,CACV;QACEC,IAAI,EAAE,MAAM;QACZP,KAAK,EAAE;MACT,CAAC,EACD;QACEO,IAAI,EAAE,KAAK;QACXP,KAAK,EAAE;MACT,CAAC,CAAC;MACJQ,UAAU,EAAE,CACV;QACED,IAAI,EAAE,MAAM;QACZP,KAAK,EAAE;MACT,CAAC,EACD;QACEO,IAAI,EAAE,KAAK;QACXP,KAAK,EAAE;MAET,CAAC,EACD;QACEO,IAAI,EAAE,IAAI;QACVP,KAAK,EAAE;MAET,CAAC,CAAC;MACJS,MAAM,EAAE,IAAI;MACZC,OAAO,EAAE,CACP;QACEV,KAAK,EAAE,IAAI;QACXW,KAAK,EAAE;MACT,CAAC,EACD;QACEX,KAAK,EAAE,MAAM;QACbW,KAAK,EAAE;MACT,CAAC,EACD;QACEX,KAAK,EAAE,MAAM;QACbW,KAAK,EAAE;MACT,CAAC,EACD;QACEX,KAAK,EAAE,MAAM;QACbW,KAAK,EAAE;MACT,CAAC,EACD;QACEX,KAAK,EAAE,MAAM;QACbW,KAAK,EAAE;MACT,CAAC,EACD;QACEX,KAAK,EAAE,MAAM;QACbW,KAAK,EAAE;MACT,CAAC,EACD;QACEX,KAAK,EAAE,OAAO;QACdW,KAAK,EAAE;MACT,CAAC,CACF;MACDC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE,CACR;QACEb,KAAK,EAAE,IAAI;QACXW,KAAK,EAAE;MACT,CAAC,CACF;MACDG,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE,CACR;QACEf,KAAK,EAAE,IAAI;QACXW,KAAK,EAAE;MACT,CAAC,CACF;MACDK,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE,CACR;QACEjB,KAAK,EAAE,IAAI;QACXW,KAAK,EAAE;MACT,CAAC,CACF;MACDG,YAAY,EAAE,KAAK;MACnBI,QAAQ,EAAE,CACR;QACElB,KAAK,EAAE,KAAK;QACZW,KAAK,EAAE;MACT,CAAC,EACD;QACEX,KAAK,EAAE,KAAK;QACZW,KAAK,EAAE;MACT,CAAC,EACD;QACEX,KAAK,EAAE,KAAK;QACZW,KAAK,EAAE;MACT,CAAC,EACD;QACEX,KAAK,EAAE,KAAK;QACZW,KAAK,EAAE;MACT,CAAC,EACD;QACEX,KAAK,EAAE,KAAK;QACZW,KAAK,EAAE;MACT,CAAC,EACD;QACEX,KAAK,EAAE,KAAK;QACZW,KAAK,EAAE;MACT,CAAC,CACF;MACDQ,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE,CACV;QACEb,IAAI,EAAE,MAAM;QACZP,KAAK,EAAE,EAAE;QACTqB,SAAS,EAAE;UAAEC,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAI;MAC9C,CAAC,EACD;QACEhB,IAAI,EAAE,MAAM;QACZP,KAAK,EAAE,EAAE;QACTqB,SAAS,EAAE;UAAEC,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAI;MAC9C,CAAC,EACD;QACEhB,IAAI,EAAE,MAAM;QACZP,KAAK,EAAE,EAAE;QACTqB,SAAS,EAAE;UAAEC,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAI;MAC9C,CAAC,EACD;QACEhB,IAAI,EAAE,MAAM;QACZP,KAAK,EAAE,EAAE;QACTqB,SAAS,EAAE;UAAEC,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAI;MAC9C,CAAC,CACF;MACDC,WAAW,EAAE,CACX;QACEjB,IAAI,EAAE,MAAM;QACZP,KAAK,EAAE,EAAE;QACTqB,SAAS,EAAE;UAAEC,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAI;MAC9C,CAAC,EACD;QACEhB,IAAI,EAAE,MAAM;QACZP,KAAK,EAAE,EAAE;QACTqB,SAAS,EAAE;UAAEC,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAI;MAC9C,CAAC,EACD;QACEhB,IAAI,EAAE,MAAM;QACZP,KAAK,EAAE,EAAE;QACTqB,SAAS,EAAE;UAAEC,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAI;MAC9C,CAAC,EACD;QACEhB,IAAI,EAAE,MAAM;QACZP,KAAK,EAAE,EAAE;QACTqB,SAAS,EAAE;UAAEC,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAI;MAC9C,CAAC;IAEL,CAAC;EACH,CAAC;EACDE,QAAQ,EAAE;IACRvC,OAAOA,CAAA,EAAG;MACR,OAAO,IAAI,CAACC,QAAO,IAAK,IAAI,CAACC,QAAO,IAAK,IAAI,CAACC,QAAO,IAAK,IAAI,CAACC,QAAO,IAAK,IAAI,CAACC,QAAQ;IAC1F,CAAC;IACD;IACAmC,aAAaA,CAAA,EAAG;MACd,OAAO,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,yBAAyB,CAAC;IACvD,CAAC;IACDC,UAAUA,CAAA,EAAG;MACX,OAAO,IAAI,CAACF,MAAM,CAACC,OAAO,CAAC,sBAAsB,CAAC;IACpD,CAAC;IACDE,gBAAgBA,CAAA,EAAG;MACjB,OAAO,IAAI,CAACH,MAAM,CAACC,OAAO,CAAC,4BAA4B,CAAC;IAC1D,CAAC;IACDG,cAAcA,CAAA,EAAG;MACf,OAAO,IAAI,CAACJ,MAAM,CAACC,OAAO,CAAC,0BAA0B,CAAC;IACxD,CAAC;IACDI,QAAQA,CAAA,EAAG;MACT,OAAO,IAAI,CAACL,MAAM,CAACC,OAAO,CAAC,oBAAoB,CAAC;IAClD;EACF,CAAC;EACDK,OAAOA,CAAA,EAAG;IAER,IAAI,CAACN,MAAM,CAACO,QAAQ,CAAC,8BAA8B,CAAC;IACpD,IAAI,CAACP,MAAM,CAACO,QAAQ,CAAC,oBAAoB,CAAC;IAC1C,IAAI,CAACP,MAAM,CAACO,QAAQ,CAAC,oBAAoB,CAAC;IAC1C,IAAI,CAACP,MAAM,CAACO,QAAQ,CAAC,oBAAoB,CAAC;IAC1C,IAAI,CAACP,MAAM,CAACO,QAAQ,CAAC,oBAAoB,CAAC;IAE1C,IAAI,CAAC,IAAI,CAACR,aAAa,CAACS,MAAM,EAAE;MAC9B,IAAI,CAACR,MAAM,CAACO,QAAQ,CAAC,8BAA8B,CAAC,EAAE;IACxD;IACA,IAAI,CAAC,IAAI,CAACL,UAAU,CAACM,MAAM,EAAE;MAC3B,IAAI,CAACR,MAAM,CAACO,QAAQ,CAAC,oBAAoB,CAAC,EAAE;IAC9C;IACA,IAAI,CAAC,IAAI,CAACJ,gBAAgB,CAACK,MAAM,EAAE;MACjC,IAAI,CAACR,MAAM,CAACO,QAAQ,CAAC,oBAAoB,CAAC,EAAE;IAC9C;IACA,IAAI,CAAC,IAAI,CAACH,cAAc,CAACI,MAAM,EAAE;MAC/B,IAAI,CAACR,MAAM,CAACO,QAAQ,CAAC,oBAAoB,CAAC,EAAE;IAC9C;IACA,IAAI,CAAC,IAAI,CAACF,QAAQ,CAACG,MAAM,EAAE;MACzB,IAAI,CAACR,MAAM,CAACO,QAAQ,CAAC,oBAAoB,CAAC,EAAE;IAC9C;IACAE,WAAW,CAAC,MAAM;MAChB,IAAI,CAACT,MAAM,CAACO,QAAQ,CAAC,8BAA8B,CAAC;MACpD,IAAI,CAACP,MAAM,CAACO,QAAQ,CAAC,oBAAoB,CAAC;MAC1C,IAAI,CAACP,MAAM,CAACO,QAAQ,CAAC,oBAAoB,CAAC;MAC1C,IAAI,CAACP,MAAM,CAACO,QAAQ,CAAC,oBAAoB,CAAC;MAC1C,IAAI,CAACP,MAAM,CAACO,QAAQ,CAAC,oBAAoB,CAAC;IAC5C,CAAC,EAAE,QAAQ,CAAC;IACZ;IACA,IAAI,CAACG,QAAQ,CAAC;IACd;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAEF,CAAC;EAEDC,OAAO,EAAE;IAEP,MAAMC,QAAQA,CAAA,EAAG;MAAG;MAClB,IAAI;QACF,MAAMC,QAAO,GAAI,MAAM7D,GAAG,CAAC8D,IAAI,CAAC,EAAE,EAAE;UAElC,QAAQ,EAAE,qBAAqB;UAC/B,QAAQ,EAAE;YACR,KAAK,EAAE,EAAE;YACT,OAAO,EAAE,UAAU;YACnB,KAAK,EAAE;UACT;QAEF,CAAC,EAAE;UAAE5D;QAAQ,CAAC,CAAC;QACf;QACA,IAAI2D,QAAQ,CAACvD,IAAI,EAAE;UACjByD,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEH,QAAQ,CAACvD,IAAI,CAAC;UACrC,IAAIA,IAAG,GAAIuD,QAAQ,CAACvD,IAAI,CAACuD,QAAO;UAChC;UACA,MAAMI,KAAI,GAAI3D,IAAI,CAAC4D,GAAG,CAACC,IAAG,IAAKA,IAAI,CAACvC,IAAI,CAAC,CAACwC,OAAO,CAAC,CAAC;UACnD,MAAMC,KAAI,GAAI/D,IAAI,CAAC4D,GAAG,CAACC,IAAG,IAAKA,IAAI,CAACG,IAAI,CAAC,CAACF,OAAO,CAAC,CAAC;UACnD,IAAI,CAACjD,YAAY,CAACD,SAAQ,GAAI+C,KAAI;UAClC,IAAI,CAAC9C,YAAY,CAACH,UAAS,GAAIqD,KAAI;UACnC,IAAI,CAAC7D,QAAO,GAAI,KAAI;QACtB;MACF,EAAE,OAAO+D,KAAK,EAAE;QACdR,OAAO,CAACQ,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC/B;IACF,CAAC;IAED,MAAMb,QAAQA,CAAA,EAAG;MAAG;MAClB,IAAI;QACF,MAAMG,QAAO,GAAI,MAAM7D,GAAG,CAAC8D,IAAI,CAAC,EAAE,EAAE;UAClC,QAAQ,EAAE,0BAA0B;UACpC,QAAQ,EAAE,CAAC;QACb,CAAC,EAAE;UAAE5D;QAAQ,CAAC,CAAC;QACf;QACA,IAAI2D,QAAQ,CAACvD,IAAI,EAAE;UACjB,IAAI,CAACO,KAAI,GAAIgD,QAAQ,CAACvD,IAAI,CAACuD,QAAQ,CAACW,YAAY,EAC9CT,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEH,QAAQ,CAACvD,IAAI,CAACuD,QAAQ,CAAC;QAGlD;MAEF,EAAE,OAAOU,KAAK,EAAE;QACdR,OAAO,CAACQ,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC/B;IACF,CAAC;IACD,MAAME,QAAQA,CAAA,EAAG;MAAG;MAClB,IAAI;QACF,MAAMZ,QAAO,GAAI,MAAM7D,GAAG,CAAC8D,IAAI,CAAC,EAAE,EAAE;UAClC,QAAQ,EAAE,sBAAsB;UAChC,QAAQ,EAAE,CAAC;QACb,CAAC,EAAE;UAAE5D;QAAQ,CAAC,CAAC;QACf;QACA,IAAI2D,QAAQ,CAACvD,IAAI,EAAE;UACjByD,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEH,QAAQ,CAACvD,IAAI,CAAC;UACrC,IAAI,CAACc,SAAQ,GAAI;YACfC,KAAK,EAAE,CAACwC,QAAQ,CAACvD,IAAI,CAACuD,QAAQ,CAACa,KAAK,EAAEb,QAAQ,CAACvD,IAAI,CAACuD,QAAQ,CAACc,KAAK,EAAEd,QAAQ,CAACvD,IAAI,CAACuD,QAAQ,CAACe,QAAQ,CAAC;YACpGtD,MAAM,EAAE,CACN,MAAM,EACN,MAAM,EACN,KAAK;UAET;UACA,IAAI,CAACZ,QAAO,GAAI,KAAI;QACtB;MACF,EAAE,OAAO6D,KAAK,EAAE;QACdR,OAAO,CAACQ,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC/B;IACF,CAAC;IACD,MAAMM,QAAQA,CAAA,EAAG;MAAG;;MAElB,IAAI;QACF,MAAMhB,QAAO,GAAI,MAAM7D,GAAG,CAAC8D,IAAI,CAAC,EAAE,EAAE;UAClC,QAAQ,EAAE,qBAAqB;UAC/B,QAAQ,EAAE,CAAC;QACb,CAAC,EAAE;UAAE5D;QAAQ,CAAC,CAAC;QACf;QACA,IAAI2D,QAAQ,EAAE;UACZE,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEH,QAAQ,CAACvD,IAAI,CAACuD,QAAQ,CAAC;UAC9C,IAAI,CAAChC,UAAS,GAAI,CAChB;YACED,IAAI,EAAE,MAAM;YACZP,KAAK,EAAEwC,QAAQ,CAACvD,IAAI,CAACuD,QAAQ,CAACiB;UAChC,CAAC,EACD;YACElD,IAAI,EAAE,KAAK;YACXP,KAAK,EAAEwC,QAAQ,CAACvD,IAAI,CAACuD,QAAQ,CAACkB;UAEhC,CAAC,EACD;YACEnD,IAAI,EAAE,KAAK;YACXP,KAAK,EAAEwC,QAAQ,CAACvD,IAAI,CAACuD,QAAQ,CAACmB;UAEhC,CAAC;UACH,IAAI,CAACrE,QAAO,GAAI,KAAI;QACtB;MACF,EAAE,OAAO4D,KAAK,EAAE;QACdR,OAAO,CAACQ,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC/B;IACF,CAAC;IACD,MAAMU,QAAQA,CAAA,EAAG;MAAG;MAClB,IAAI;QACF,MAAMpB,QAAO,GAAI,MAAM7D,GAAG,CAAC8D,IAAI,CAAC,EAAE,EAAE;UAClC,QAAQ,EAAE,uBAAuB;UACjC,QAAQ,EAAE;YACR,KAAK,EAAE,CAAC;YACR,MAAM,EAAE;UACV;QACF,CAAC,EAAE;UAAE5D;QAAQ,CAAC,CAAC;QACf;QACA,IAAI2D,QAAQ,CAACvD,IAAI,EAAE;UACjByD,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEH,QAAQ,CAACvD,IAAI,CAACuD,QAAQ,CAAC;UAC5C,IAAIvD,IAAG,GAAIuD,QAAQ,CAACvD,IAAI,CAACuD,QAAO;UAChC;UACA,MAAMI,KAAI,GAAI3D,IAAI,CAAC4D,GAAG,CAACC,IAAG,IAAKA,IAAI,CAACvC,IAAI,CAAC,CAACwC,OAAO,CAAC,CAAC;UACnD,MAAMC,KAAI,GAAI/D,IAAI,CAAC4D,GAAG,CAACC,IAAG,IAAKA,IAAI,CAACG,IAAI,CAAC,CAACF,OAAO,CAAC,CAAC;UACnD,IAAI,CAACtD,WAAW,CAACI,SAAQ,GAAI+C,KAAI;UACjC,IAAI,CAACnD,WAAW,CAACE,UAAS,GAAIqD,KAAI;QACpC;QACA,IAAI,CAACzD,QAAO,GAAI,KAAI;MACtB,EAAE,OAAO2D,KAAK,EAAE;QACdR,OAAO,CAACQ,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC/B;IACF,CAAC;IACDW,KAAKA,CAAA,EAAG;MACN,IAAI,CAACpD,MAAK,GAAI,KAAK;IACrB;EACF;AACF,CAAC", "ignoreList": []}]}