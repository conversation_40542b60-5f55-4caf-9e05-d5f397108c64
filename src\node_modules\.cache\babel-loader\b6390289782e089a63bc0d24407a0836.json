{"remainingRequest": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\babel-loader\\lib\\index.js!E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\src\\api\\device.js", "dependencies": [{"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\src\\api\\device.js", "mtime": 1751446815529}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\babel.config.js", "mtime": 1726672621424}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["http", "getDeviceData", "deviceId", "dmId", "console", "log", "today", "Date", "formatDate", "date", "pad", "num", "String", "padStart", "getFullYear", "getMonth", "getDate", "getHours", "getMinutes", "getSeconds", "startOfDay", "setHours", "endOfDay", "get", "itemDataId", "undefined", "from", "to", "getDevicedetails", "getDeviceWarningList", "params", "pageNum", "pageSize", "buildingId", "severitys", "hasFixed", "deviceTypes", "deviceType", "buildingEnergyDataList", "query", "queryString", "Object", "keys", "map", "key", "encodeURIComponent", "join", "post"], "sources": ["E:/svn/SH-20240918-0186-SFTianjinUniversityH5/src/src/api/device.js"], "sourcesContent": ["import http from \"@/utils/request\";\r\n\r\n// 获取设备曲线图\r\nexport function getDeviceData(deviceId, dmId) {\r\n  console.log(deviceId, dmId, 'getDeviceData')\r\n  const today = new Date();\r\n\r\n  // Helper function to format date as \"YYYY-MM-DD HH:mm:ss\"\r\n  const formatDate = (date) => {\r\n    const pad = (num) => String(num).padStart(2, \"0\");\r\n    return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(\r\n      date.getDate()\r\n    )} ${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(\r\n      date.getSeconds()\r\n    )}`;\r\n  };\r\n\r\n  const startOfDay = new Date(today.setHours(0, 0, 0, 0));\r\n  const endOfDay = new Date(today.setHours(23, 59, 59, 999));\r\n\r\n  return http.get(\"/device/api/deviceItemDataList\", {\r\n    deviceId,\r\n    itemDataId: dmId !== undefined ? dmId : `${deviceId}1`, // 修复条件逻辑\r\n    from: formatDate(startOfDay),\r\n    to: formatDate(endOfDay),\r\n  });\r\n}\r\n\r\n// export function getDeviceData(deviceId, dmId) {\r\n//   return http.get(\"/device/api/deviceItemDataList\", {\r\n//     deviceId,\r\n//     itemDataId: dmId !== undefined ? dmId : `${deviceId}1`, // 修复条件逻辑\r\n//     // limit: 200,\r\n//     from: \"2025-01-22 00:00:00\",\r\n//     to: \"2025-01-22 23:59:59\",\r\n//   });\r\n// }\r\n\r\n//获取设备详细信息\r\nexport function getDevicedetails(deviceId) {\r\n  return http.get(\"/device/api/resourceDevice\", {\r\n    deviceId,\r\n  });\r\n}\r\n\r\n// 获取设备警告列表\r\nexport function getDeviceWarningList(params) {\r\n  return http.get(\"/device/api/deviceWarningList\", {\r\n    pageNum: params.pageNum || 1,\r\n    pageSize: params.pageSize || 10,\r\n    buildingId: params.buildingId || 1,\r\n    severitys: params.severitys || \"一级,二级,三级\",\r\n    hasFixed: params.hasFixed,\r\n    deviceTypes: params.deviceTypes,\r\n    deviceType: params.deviceType\r\n  });\r\n}\r\n\r\n// 建筑总用能数据列表\r\n// @ApiImplicitParam(name=\"buildingId\", value=\"建筑ID\", dataType=\"int\", required=true),\r\n// @ApiImplicitParam(name=\"from\", value=\"时间起始\", dataType=\"string\", required=true),\r\n// @ApiImplicitParam(name=\"to\", value=\"时间终点\", dataType=\"string\", required=true),\r\n// @ApiImplicitParam(name=\"deviceType\", value=\"能耗类型\", dataType=\"string\", required=true),\r\n// @ApiImplicitParam(name=\"displayType\", value=\"显示类型\", dataType=\"string\", required=true),\r\nexport function buildingEnergyDataList(query) {\r\n  // 构建查询字符串\r\n  const queryString = Object.keys(query)\r\n    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(query[key])}`)\r\n    .join('&');\r\n  \r\n  return http.post(`/energy/api/buildingEnergyDataList?${queryString}`);\r\n}"], "mappings": ";;AAAA,OAAOA,IAAI,MAAM,iBAAiB;;AAElC;AACA,OAAO,SAASC,aAAaA,CAACC,QAAQ,EAAEC,IAAI,EAAE;EAC5CC,OAAO,CAACC,GAAG,CAACH,QAAQ,EAAEC,IAAI,EAAE,eAAe,CAAC;EAC5C,MAAMG,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;;EAExB;EACA,MAAMC,UAAU,GAAIC,IAAI,IAAK;IAC3B,MAAMC,GAAG,GAAIC,GAAG,IAAKC,MAAM,CAACD,GAAG,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACjD,OAAO,GAAGJ,IAAI,CAACK,WAAW,CAAC,CAAC,IAAIJ,GAAG,CAACD,IAAI,CAACM,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,IAAIL,GAAG,CAC7DD,IAAI,CAACO,OAAO,CAAC,CACf,CAAC,IAAIN,GAAG,CAACD,IAAI,CAACQ,QAAQ,CAAC,CAAC,CAAC,IAAIP,GAAG,CAACD,IAAI,CAACS,UAAU,CAAC,CAAC,CAAC,IAAIR,GAAG,CACxDD,IAAI,CAACU,UAAU,CAAC,CAClB,CAAC,EAAE;EACL,CAAC;EAED,MAAMC,UAAU,GAAG,IAAIb,IAAI,CAACD,KAAK,CAACe,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EACvD,MAAMC,QAAQ,GAAG,IAAIf,IAAI,CAACD,KAAK,CAACe,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;EAE1D,OAAOrB,IAAI,CAACuB,GAAG,CAAC,gCAAgC,EAAE;IAChDrB,QAAQ;IACRsB,UAAU,EAAErB,IAAI,KAAKsB,SAAS,GAAGtB,IAAI,GAAG,GAAGD,QAAQ,GAAG;IAAE;IACxDwB,IAAI,EAAElB,UAAU,CAACY,UAAU,CAAC;IAC5BO,EAAE,EAAEnB,UAAU,CAACc,QAAQ;EACzB,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,OAAO,SAASM,gBAAgBA,CAAC1B,QAAQ,EAAE;EACzC,OAAOF,IAAI,CAACuB,GAAG,CAAC,4BAA4B,EAAE;IAC5CrB;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAAS2B,oBAAoBA,CAACC,MAAM,EAAE;EAC3C,OAAO9B,IAAI,CAACuB,GAAG,CAAC,+BAA+B,EAAE;IAC/CQ,OAAO,EAAED,MAAM,CAACC,OAAO,IAAI,CAAC;IAC5BC,QAAQ,EAAEF,MAAM,CAACE,QAAQ,IAAI,EAAE;IAC/BC,UAAU,EAAEH,MAAM,CAACG,UAAU,IAAI,CAAC;IAClCC,SAAS,EAAEJ,MAAM,CAACI,SAAS,IAAI,UAAU;IACzCC,QAAQ,EAAEL,MAAM,CAACK,QAAQ;IACzBC,WAAW,EAAEN,MAAM,CAACM,WAAW;IAC/BC,UAAU,EAAEP,MAAM,CAACO;EACrB,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,sBAAsBA,CAACC,KAAK,EAAE;EAC5C;EACA,MAAMC,WAAW,GAAGC,MAAM,CAACC,IAAI,CAACH,KAAK,CAAC,CACnCI,GAAG,CAACC,GAAG,IAAI,GAAGC,kBAAkB,CAACD,GAAG,CAAC,IAAIC,kBAAkB,CAACN,KAAK,CAACK,GAAG,CAAC,CAAC,EAAE,CAAC,CAC1EE,IAAI,CAAC,GAAG,CAAC;EAEZ,OAAO9C,IAAI,CAAC+C,IAAI,CAAC,sCAAsCP,WAAW,EAAE,CAAC;AACvE", "ignoreList": []}]}