{"remainingRequest": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\babel-loader\\lib\\index.js!E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\components\\echarts\\SystemDete.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\components\\echarts\\SystemDete.vue", "mtime": 1751449256775}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\babel.config.js", "mtime": 1726672621424}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["echarts", "mapGetters", "name", "props", "equipmentStatus", "type", "Array", "default", "computed", "yiqiStatus", "data", "chart", "watch", "handler", "newVal", "updateChart", "deep", "mounted", "initChart", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "methods", "init", "$refs", "echart", "filteredData", "length", "filter", "item", "value", "colors", "option", "legend", "top", "right", "map", "it", "textStyle", "color", "fontSize", "fontFamily", "itemWidth", "itemHeight", "tooltip", "trigger", "formatter", "series", "radius", "center", "roseType", "label", "show", "normal", "position", "labelLine", "length2", "i", "itemStyle", "borderColor", "borderWidth", "setOption"], "sources": ["E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\components\\echarts\\SystemDete.vue"], "sourcesContent": ["<template>\n  <div class=\"echart\" ref=\"echart\"></div>\n</template>\n\n<script>\nimport * as echarts from \"echarts\";\nimport { mapGetters } from 'vuex';\n\nexport default {\n  name: \"IoTequip\",\n  props: {\n    equipmentStatus: {\n      type: Array,\n      default: () => []\n    }\n  },\n  computed: {\n    ...mapGetters({\n      yiqiStatus: 'equipment/yiqiStatus'\n    })\n  },\n  data() {\n    return {\n      chart: null\n    };\n  },\n  watch: {\n    yiqiStatus: {\n      handler(newVal) {\n        this.updateChart();\n      },\n      deep: true\n    }\n  },\n  mounted() {\n    this.initChart();\n    this.updateChart();\n  },\n  beforeDestroy() {\n    if (this.chart) {\n      this.chart.dispose();\n      this.chart = null;\n    }\n  },\n  methods: {\n    initChart() {\n      this.chart = echarts.init(this.$refs.echart);\n    },\n    updateChart() {\n      if (!this.chart) return;\n\n      // 过滤掉故障状态，只保留正在使用和待机中\n      const filteredData = this.yiqiStatus && this.yiqiStatus.length ?\n        this.yiqiStatus.filter(item => item.name !== \"故障\") : [];\n\n      const data = filteredData.length ? filteredData : [\n        { name: \"正在使用\", value: 0 },\n        { name: \"待机中\", value: 0 },\n      ];\n\n      const colors = [\n        \"37, 171, 200\",\n        \"214, 128, 120\",\n        \"252, 182, 53\",\n        \"47, 255, 242\",\n        \"42, 191, 191\"\n      ];\n\n      const option = {\n        legend: {\n          top: \"10\",\n          right: \"18%\",\n          data: data.map((it) => it.name),\n          textStyle: {\n            color: \"#fff\",\n            fontSize: 16,\n            fontFamily: \"Alibaba PuHuiTi\",\n          },\n          itemWidth: 13,\n          itemHeight: 13,\n        },\n        tooltip: {\n          trigger: \"item\",\n          formatter: \"{a} <br/>{b} : {c} ({d}%)\",\n          textStyle: {\n            fontSize: 15,\n          },\n        },\n        series: [\n          {\n            name: \"仪器状态\",\n            type: \"pie\",\n            radius: [\"30%\", \"80%\"],\n            center: [\"50%\", \"60%\"],\n            roseType: \"radius\",\n            label: {\n              show: true,\n              normal: {\n                position: \"outside\",\n                fontSize: 18,\n                formatter: \"{d}%\",\n                color: \"#fff\",\n              },\n            },\n            labelLine: {\n              length: 2,\n              length2: 7,\n            },\n            data: data.map((it, i) => {\n              return {\n                value: it.value,\n                name: it.name,\n                itemStyle: {\n                  color: `rgba(${colors[i]},0.7)`,\n                  borderColor: `rgba(${colors[i]},1)`,\n                  borderWidth: 1,\n                },\n              };\n            }),\n          },\n        ],\n      };\n\n      this.chart.setOption(option);\n    },\n  },\n};\n</script>\n\n<style lang=\"less\" scoped>\n.echart {\n  width: 100%;\n  height: 100%;\n}\n\n@media (max-height: 1080px) {\n  .echart {\n    width: 100%;\n    height: 100% !important;\n  }\n}\n</style>"], "mappings": ";;;AAKA,OAAO,KAAKA,OAAM,MAAO,SAAS;AAClC,SAASC,UAAS,QAAS,MAAM;AAEjC,eAAe;EACbC,IAAI,EAAE,UAAU;EAChBC,KAAK,EAAE;IACLC,eAAe,EAAE;MACfC,IAAI,EAAEC,KAAK;MACXC,OAAO,EAAEA,CAAA,KAAM;IACjB;EACF,CAAC;EACDC,QAAQ,EAAE;IACR,GAAGP,UAAU,CAAC;MACZQ,UAAU,EAAE;IACd,CAAC;EACH,CAAC;EACDC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,KAAK,EAAE;IACT,CAAC;EACH,CAAC;EACDC,KAAK,EAAE;IACLH,UAAU,EAAE;MACVI,OAAOA,CAACC,MAAM,EAAE;QACd,IAAI,CAACC,WAAW,CAAC,CAAC;MACpB,CAAC;MACDC,IAAI,EAAE;IACR;EACF,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,SAAS,CAAC,CAAC;IAChB,IAAI,CAACH,WAAW,CAAC,CAAC;EACpB,CAAC;EACDI,aAAaA,CAAA,EAAG;IACd,IAAI,IAAI,CAACR,KAAK,EAAE;MACd,IAAI,CAACA,KAAK,CAACS,OAAO,CAAC,CAAC;MACpB,IAAI,CAACT,KAAI,GAAI,IAAI;IACnB;EACF,CAAC;EACDU,OAAO,EAAE;IACPH,SAASA,CAAA,EAAG;MACV,IAAI,CAACP,KAAI,GAAIX,OAAO,CAACsB,IAAI,CAAC,IAAI,CAACC,KAAK,CAACC,MAAM,CAAC;IAC9C,CAAC;IACDT,WAAWA,CAAA,EAAG;MACZ,IAAI,CAAC,IAAI,CAACJ,KAAK,EAAE;;MAEjB;MACA,MAAMc,YAAW,GAAI,IAAI,CAAChB,UAAS,IAAK,IAAI,CAACA,UAAU,CAACiB,MAAK,GAC3D,IAAI,CAACjB,UAAU,CAACkB,MAAM,CAACC,IAAG,IAAKA,IAAI,CAAC1B,IAAG,KAAM,IAAI,IAAI,EAAE;MAEzD,MAAMQ,IAAG,GAAIe,YAAY,CAACC,MAAK,GAAID,YAAW,GAAI,CAChD;QAAEvB,IAAI,EAAE,MAAM;QAAE2B,KAAK,EAAE;MAAE,CAAC,EAC1B;QAAE3B,IAAI,EAAE,KAAK;QAAE2B,KAAK,EAAE;MAAE,CAAC,CAC1B;MAED,MAAMC,MAAK,GAAI,CACb,cAAc,EACd,eAAe,EACf,cAAc,EACd,cAAc,EACd,cAAa,CACd;MAED,MAAMC,MAAK,GAAI;QACbC,MAAM,EAAE;UACNC,GAAG,EAAE,IAAI;UACTC,KAAK,EAAE,KAAK;UACZxB,IAAI,EAAEA,IAAI,CAACyB,GAAG,CAAEC,EAAE,IAAKA,EAAE,CAAClC,IAAI,CAAC;UAC/BmC,SAAS,EAAE;YACTC,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE;UACd,CAAC;UACDC,SAAS,EAAE,EAAE;UACbC,UAAU,EAAE;QACd,CAAC;QACDC,OAAO,EAAE;UACPC,OAAO,EAAE,MAAM;UACfC,SAAS,EAAE,2BAA2B;UACtCR,SAAS,EAAE;YACTE,QAAQ,EAAE;UACZ;QACF,CAAC;QACDO,MAAM,EAAE,CACN;UACE5C,IAAI,EAAE,MAAM;UACZG,IAAI,EAAE,KAAK;UACX0C,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;UACtBC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;UACtBC,QAAQ,EAAE,QAAQ;UAClBC,KAAK,EAAE;YACLC,IAAI,EAAE,IAAI;YACVC,MAAM,EAAE;cACNC,QAAQ,EAAE,SAAS;cACnBd,QAAQ,EAAE,EAAE;cACZM,SAAS,EAAE,MAAM;cACjBP,KAAK,EAAE;YACT;UACF,CAAC;UACDgB,SAAS,EAAE;YACT5B,MAAM,EAAE,CAAC;YACT6B,OAAO,EAAE;UACX,CAAC;UACD7C,IAAI,EAAEA,IAAI,CAACyB,GAAG,CAAC,CAACC,EAAE,EAAEoB,CAAC,KAAK;YACxB,OAAO;cACL3B,KAAK,EAAEO,EAAE,CAACP,KAAK;cACf3B,IAAI,EAAEkC,EAAE,CAAClC,IAAI;cACbuD,SAAS,EAAE;gBACTnB,KAAK,EAAE,QAAQR,MAAM,CAAC0B,CAAC,CAAC,OAAO;gBAC/BE,WAAW,EAAE,QAAQ5B,MAAM,CAAC0B,CAAC,CAAC,KAAK;gBACnCG,WAAW,EAAE;cACf;YACF,CAAC;UACH,CAAC;QACH,CAAC;MAEL,CAAC;MAED,IAAI,CAAChD,KAAK,CAACiD,SAAS,CAAC7B,MAAM,CAAC;IAC9B;EACF;AACF,CAAC", "ignoreList": []}]}