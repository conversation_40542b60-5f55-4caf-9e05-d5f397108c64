{"remainingRequest": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\babel-loader\\lib\\index.js!E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\views\\floor.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\views\\floor.vue", "mtime": 1751448014712}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\babel.config.js", "mtime": 1726672621424}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["mapActions", "mapGetters", "component0", "huanxing", "zhexian", "zhexian1", "SystemDete", "echarts1", "tedai", "biaoGe", "shuang<PERSON>ng", "axios", "baseURL", "process", "env", "VUE_APP_BASE_API", "api", "create", "components", "props", "data", "loading", "currentPage1", "isshowwhat", "isshowsss", "titactive", "total", "changeTitle", "activeSubmenu", "activeContent", "newArr", "isshow", "xxxx", "cgqlist", "listtable", "options", "value", "label", "children", "devicedata", "input", "activeTab", "listst", "name", "showdh", "showdh1", "noAnimation", "selectedItem", "imgurl", "location", "status", "details", "maintenance_records", "maintenance_content", "date", "next_maintenance_date", "management_name", "management_contact_info", "tableDataItem", "jlURL", "Title", "tableTitle", "key", "ids", "nhlist", "title", "unit", "warnlist1", "type", "time", "isButton2Active", "status1", "status2", "selectedIndex", "componentTag", "token", "computed", "watch", "methods", "handleSizeChange", "val", "console", "log", "getyiqidetails", "handleCurrentChange", "gettoken", "id", "response", "post", "method", "params", "criteria", "cat", "error", "start", "headers", "clientid", "clients<PERSON>ret", "body", "num", "then", "catch", "changetit", "index", "handleCascaderChange", "hidedetailsss", "opendialog", "payload", "for<PERSON>ach", "item", "category", "items", "toggleSubMenu", "iconreal_url", "location2", "is_using", "price", "Date", "purchased_date", "getFullYear", "getMonth", "getDate", "manu_at", "manufacturer", "contact", "phone", "showdetails", "hidedetails", "oc", "xuanzedialog", "optionMapping", "选项1", "选项2", "选项3", "选项4", "选项5", "选项6", "选项7", "选项8", "选项9", "选项10", "选项11", "选项12", "选项13", "undefined", "created", "mounted", "fetchEquipmentTags", "beforeCreate", "beforeMount", "beforeUpdate", "updated", "beforeUnmount", "unmounted", "destroyed", "activated"], "sources": ["E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\views\\floor.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <component\r\n      :is=\"componentTag\"\r\n      @fatherMethoddd=\"fatherMethoddd\"\r\n      v-if=\"isshowwhat\"\r\n    ></component>\r\n    <div class=\"botbtn\">\r\n      <div\r\n        v-for=\"(item, index) in changeTitle\"\r\n        :key=\"index\"\r\n        :class=\"titactive == index ? 'btt1' : 'btt'\"\r\n        @click=\"changetit(index)\"\r\n      >\r\n        {{ item }}\r\n      </div>\r\n    </div>\r\n    <tedai\r\n      :ids=\"ids\"\r\n      :selectedItem=\"selectedItem\"\r\n      class=\"sbdetails\"\r\n      :zengtiimg=\"zengtiimg\"\r\n      v-if=\"isshowsss\"\r\n      @hidedetails=\"hidedetailsss\"\r\n    ></tedai>\r\n    <biaoGe\r\n      :Title=\"Title\"\r\n      @xuanze-dialog=\"xuanzedialog\"\r\n      v-if=\"isshow\"\r\n      @hidedetails=\"hidedetails\"\r\n      :tableTitle=\"tableTitle\"\r\n      :tableDataItem=\"devicedata\"\r\n    ></biaoGe>\r\n    <div class=\"container\" v-if=\"!isshowwhat\">\r\n      <div\r\n        class=\"left-panel\"\r\n        :class=\"{\r\n          'left-panel-active': showdh,\r\n          'no-animation': noAnimation,\r\n          'left-panel-active1': showdh1,\r\n        }\"\r\n      >\r\n        <Title2 @open-dialog=\"opendialog\" class=\"ltitle1\" tit=\"大仪管理\">\r\n          <el-cascader\r\n            class=\"sect\"\r\n            placeholder=\"请选择类别\"\r\n            :options=\"equipmentTags\"\r\n            :show-all-levels=\"true\"\r\n            @change=\"handleCascaderChange\"\r\n          ></el-cascader>\r\n\r\n          <el-input\r\n            class=\"el-input\"\r\n            v-model=\"input\"\r\n            placeholder=\"请输入关键字\"\r\n          ></el-input>\r\n          <img class=\"suosuo\" src=\"../assets/image/suosuo.png\" alt=\"\" />\r\n          <div class=\"box\">\r\n            <!-- <div class=\"xiaobox\">\r\n              <img class=\"siqiu\" src=\"../assets/image/shixinqiu.png\" alt=\"\" />\r\n              <div class=\"shuru\">全部设备</div>\r\n            </div> -->\r\n            <div\r\n              class=\"menu-container\"\r\n              v-loading=\"loading\"\r\n              element-loading-text=\"拼命加载中\"\r\n              element-loading-background=\"rgba(0, 0, 0, 0)\"\r\n            >\r\n              <!-- 动态生成菜单 -->\r\n              <div class=\"menu\">\r\n                <div\r\n                  v-for=\"(menu, index) in devicedata\"\r\n                  :key=\"index\"\r\n                  class=\"menu-group\"\r\n                >\r\n                  <div class=\"qiuqiu\">\r\n                    <img\r\n                      class=\"siqiu\"\r\n                      src=\"../assets/image/shixinqiu.png\"\r\n                      alt=\"\"\r\n                    />\r\n                    <div class=\"menu-item\" @click=\"toggleSubMenu(menu)\">\r\n                      {{ menu.name }}\r\n                    </div>\r\n                    <!-- <div class=\"listtypes\" @click=\"showdetails(menu)\">详情</div> -->\r\n                  </div>\r\n\r\n                  <!-- <div v-show=\"activeSubmenu === menu.id\" class=\"submenu\">\r\n                    <div v-for=\"(item, subIndex) in menu.items\" :key=\"subIndex\" class=\"submenu-item\"\r\n                      @click=\"setContent(item)\">\r\n                      <p class=\"ellipsis\" :title=\"item.name\">{{ item.name }}</p>\r\n                    </div>\r\n                  </div> -->\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <el-pagination\r\n            @size-change=\"handleSizeChange\"\r\n            hide-on-single-page=\"true\"\r\n            @current-change=\"handleCurrentChange\"\r\n            :page-size=\"16\"\r\n            :pager-count=\"4\"\r\n            layout=\"prev, pager, next,total\"\r\n            :total=\"total\"\r\n          >\r\n          </el-pagination>\r\n          <!-- shebei12.png -->\r\n        </Title2>\r\n      </div>\r\n      <!-- shebeibgc.png -->\r\n      <!-- 右侧内容 -->\r\n      <!-- 右侧内容 -->\r\n\r\n      <div\r\n        class=\"right-panel\"\r\n        :class=\"{\r\n          'right-panel-active': showdh,\r\n          'no-animation': noAnimation,\r\n          'right-panel-active1': showdh1,\r\n        }\"\r\n      ></div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）\r\n// 例如：import 《组件名称》 from '《组件路径》';\r\nimport { mapActions, mapGetters } from \"vuex\";\r\nimport component0 from \"@/views/dayi/zichan.vue\";\r\nimport huanxing from \"@/components/echarts/huanxing.vue\";\r\nimport zhexian from \"@/components/echarts/zhexian.vue\";\r\nimport zhexian1 from \"@/components/echarts/zhexian1.vue\";\r\nimport SystemDete from \"@/components/echarts/SystemDete.vue\";\r\nimport echarts1 from \"@/components/echarts/bingjifang/echarts3.vue\";\r\nimport tedai from \"@/components/common/cl_details.vue\";\r\nimport biaoGe from \"@/components/common/biaoGes.vue\";\r\nimport shuangxiang from \"@/components/echarts/shuangxiang.vue\";\r\nimport axios from \"axios\";\r\n// import details from \"@/components/common/details.vue\";\r\n// 使用环境变量设置基础 API 地址\r\nconst baseURL = process.env.VUE_APP_BASE_API || \"/lims/api\";\r\n\r\nconst api = axios.create({\r\n  baseURL,\r\n});\r\nexport default {\r\n  // import引入的组件需要注入到对象中才能使用\r\n  components: {\r\n    tedai,\r\n    huanxing,\r\n    zhexian,\r\n    zhexian1,\r\n    SystemDete,\r\n    echarts1,\r\n    shuangxiang,\r\n    biaoGe,\r\n    component0,\r\n  },\r\n  props: [\"tabledata\", \"zengtiimg\"],\r\n\r\n  data() {\r\n    // 这里存放数据\r\n    return {\r\n      loading: true,\r\n      currentPage1: 5,\r\n      isshowwhat: false,\r\n      isshowsss: false,\r\n      titactive: 0,\r\n      total: null,\r\n      changeTitle: [\"数据列表\", \"数据统计\"],\r\n      activeSubmenu: null, // 当前激活的子菜单\r\n      activeContent: null, // 当前显示的内容\r\n      newArr: [],\r\n      isshow: false,\r\n      xxxx: false,\r\n      cgqlist: [],\r\n      listtable: [],\r\n      options: [\r\n        {\r\n          value: \"zhinan\",\r\n          label: \"指南\",\r\n          children: [\r\n            {\r\n              value: \"shejiyuanze\",\r\n              label: \"设计原则\",\r\n            },\r\n            {\r\n              value: \"daohang\",\r\n              label: \"导航\",\r\n            },\r\n          ],\r\n        },\r\n      ],\r\n      devicedata: [],\r\n      input: \"\",\r\n      activeTab: \"today\",\r\n      listst: [\r\n        {\r\n          name: \"广东质检中诚认证有限公司到中广...\",\r\n        },\r\n        { name: \"材料科学、化学工程及医药研发成...\" },\r\n        { name: \"酒精代谢能力检测：您的酒量，基...\" },\r\n        { name: \"植酸检测方法及作用\" },\r\n        { name: \"兔拉检测：行业中芯片造假技术...\" },\r\n        { name: \"酒精代谢能力检测：您的酒量，基...\" },\r\n      ],\r\n      showdh: true,\r\n      showdh1: false,\r\n      noAnimation: false,\r\n      selectedItem: {\r\n        name: \"离子溅射仪\", //产品名称\r\n        imgurl: \"https://3d.dddtask.cn/enginner-xufeng/image/tianjin/lizi.png\",\r\n        location: \"北洋园校区54楼, E105\", //位置\r\n        status: \"已领用\", //仪器状态  可领用\r\n        details: [\r\n          {\r\n            name: \"产品名称\",\r\n            value: \"离子溅射仪\",\r\n          },\r\n          {\r\n            name: \"原值\",\r\n            value: \"--\",\r\n          },\r\n          {\r\n            name: \"购入时间\",\r\n            value: \"2020/09/02\",\r\n          },\r\n          {\r\n            name: \"品牌\",\r\n            value: \"--\",\r\n          },\r\n          {\r\n            name: \"生产厂家\",\r\n            value: \"--\",\r\n          },\r\n          {\r\n            name: \"供应商名称\",\r\n            value: \"--\",\r\n          },\r\n          {\r\n            name: \"供应商联系信息\",\r\n            value: \"--\",\r\n          },\r\n        ],\r\n        maintenance_records: {\r\n          maintenance_content: \"校准和系统升级\", //维护内容\r\n          date: \"2024-01-10\", //维护时间\r\n          next_maintenance_date: \"2022-01-10\", //下次维护时间\r\n        },\r\n        management_name: \"王工\",\r\n        management_contact_info: \"15698567542\",\r\n      },\r\n      tableDataItem: [],\r\n      jlURL,\r\n      Title: \"资产管理\",\r\n      tableTitle: [\r\n        { key: \"楼层\" },\r\n        { key: \"设备编号\" },\r\n        { key: \"设备名称\" },\r\n        { key: \"房间号\" },\r\n        { key: \"模型\" },\r\n        { key: \"设备状态\" },\r\n        { key: \"状态说明\" },\r\n      ],\r\n      ids: null,\r\n      nhlist: [\r\n        {\r\n          title: \"供气压力\",\r\n          status: \"0.3Mpa\",\r\n          unit: \"℃\",\r\n        },\r\n\r\n        {\r\n          title: \"供气流量\",\r\n          status: \"6M3/min\",\r\n          unit: \"㎡\",\r\n        },\r\n        {\r\n          title: \"露点温度\",\r\n          status: \"6℃\",\r\n          unit: \"℃\",\r\n        },\r\n        {\r\n          title: \"含氧量\",\r\n          status: \"6PPM\",\r\n          unit: \"㎡\",\r\n        },\r\n      ],\r\n      warnlist1: [\r\n        {\r\n          type: 1,\r\n          name: \"检测到烟雾，可能有着火灾的发生...\",\r\n          value: \"\",\r\n          time: \"09:13:59  2023-06-07\",\r\n        },\r\n        {\r\n          type: 2,\r\n          name: \"检测到烟雾，可能预示着火灾的发生..\",\r\n          value: \"\",\r\n          time: \"10:13:18  2023-06-12\",\r\n        },\r\n        {\r\n          type: 3,\r\n          name: \"实验室内检测到漏水，可能来自冷凝水..\",\r\n          value: \"\",\r\n          time: \"10:13:18  2023-06-12\",\r\n        },\r\n      ],\r\n      isButton2Active: false,\r\n      status: \"巡检中\",\r\n      status1: \"已完成\",\r\n      status2: \"待巡检\",\r\n      selectedIndex: 0,\r\n      componentTag: \"component0\",\r\n      token: \"\",\r\n    };\r\n  },\r\n  // 计算属性类似于data概念\r\n  computed: {\r\n    ...mapGetters(\"equipment\", [\"equipmentTags\"]),\r\n  },\r\n  // 监控data中的数据变化\r\n  watch: {},\r\n  // 方法集合\r\n  methods: {\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.getyiqidetails(this.token, val - 1);\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`);\r\n      this.getyiqidetails(this.token, val - 1);\r\n    },\r\n    async gettoken(id) {\r\n      try {\r\n        const response = await api.post(\"\", {\r\n          method: \"equipment/searchEquipments\",\r\n          params: {\r\n            criteria: {\r\n              cat: id,\r\n              location:'58楼ACE区',\r\n              // \"group\": 1,\r\n              // \"searchtext\": \"搜索内容\"\r\n            },\r\n          },\r\n        });\r\n\r\n        // 检查是否成功拿到 token\r\n        if (response.data && response.data.response.token) {\r\n          const token = response.data.response.token;\r\n          this.token = token;\r\n          // 将 token 存入 localStorage\r\n          // localStorage.setItem('authToken', token);\r\n          console.log(\"535:\", response.data.response);\r\n          this.total = response.data.response.total;\r\n          this.handleSizeChange(1);\r\n        } else {\r\n          console.error(\"登录成功但未返回 token:\", response.data.response);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"登录失败:\", error);\r\n      }\r\n    },\r\n    getyiqidetails(token, start) {\r\n      const headers = {\r\n        clientid: \"5a298e93-158d-4e22-83cf-6ceb62e9b4f1\",\r\n        clientsecret: \"2c8ec39e-9887-482a-b28b-e64c496b601c\",\r\n      };\r\n      const body = {\r\n        method: \"equipment/getEquipments\",\r\n        params: {\r\n          token: token,\r\n          start: start ? start * 16 : 0,\r\n          num: 16,\r\n        },\r\n      };\r\n      axios\r\n        .post(jlURL, body, {})\r\n        .then((response) => {\r\n          console.log(response.data, 535);\r\n          this.devicedata = response.data.response;\r\n          this.loading = false;\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"Error:\", error);\r\n        });\r\n    },  \r\n    ...mapActions(\"equipment\", [\"fetchEquipmentTags\"]),\r\n    changetit(index) {\r\n      this.titactive = index;\r\n      this.isshowwhat = index;\r\n      if (index == 1) {\r\n        this.showdh = false;\r\n        this.showdh1 = true;\r\n        this.noAnimation = true;\r\n      } else {\r\n        this.showdh = true;\r\n        this.showdh1 = false;\r\n        this.noAnimation = false;\r\n      }\r\n    },\r\n    handleCascaderChange(value) {\r\n      console.log(\"选中的值:\", value[1]);\r\n      this.gettoken(value[1]);\r\n    },\r\n    hidedetailsss() {\r\n      this.isshowsss = false;\r\n    },\r\n    opendialog(payload) {\r\n      if (payload == 1) {\r\n        this.isshow = true;\r\n\r\n        this.data.forEach((item) => {\r\n          if (item.category == \"电子显微镜\") {\r\n            this.isshow = true;\r\n            this.tableDataItem = item.items;\r\n            console.log(this.tableDataItem);\r\n          }\r\n        });\r\n      }\r\n\r\n      // 在这里处理事件\r\n    },\r\n    toggleSubMenu(item) {\r\n      this.isshowsss = true;\r\n      console.log(item, \"选中的设备信息\");\r\n      this.selectedItem = {\r\n        id: item.id,\r\n        name: item.name,\r\n        imgurl: item.iconreal_url,\r\n        location: item.location + item.location2, //位置\r\n        status: !item.is_using ? \"当前使用\" : \"可使用\", //仪器状态  可领用\r\n        details: [\r\n          {\r\n            name: \"产品名称\",\r\n            value: item.name,\r\n          },\r\n          {\r\n            name: \"价格\",\r\n            value: item.price,\r\n          },\r\n          {\r\n            name: \"购入时间\",\r\n            value: `${new Date(item.purchased_date * 1000).getFullYear()}/${\r\n              new Date(item.purchased_date * 1000).getMonth() + 1\r\n            }/${new Date(item.purchased_date * 1000).getDate()}`,\r\n          },\r\n          {\r\n            name: \"制造国家\",\r\n            value: item.manu_at,\r\n          },\r\n          {\r\n            name: \"生产厂家\",\r\n            value: item.manufacturer,\r\n          },\r\n          {\r\n            name: \"负责人\",\r\n            value: item.contact,\r\n          },\r\n          {\r\n            name: \"联系电话\",\r\n            value: item.phone,\r\n          },\r\n        ],\r\n        maintenance_records: {\r\n          maintenance_content: \"校准和系统升级\", //维护内容\r\n          date: \"2024-01-10\", //维护时间\r\n          next_maintenance_date: \"2022-01-10\", //下次维护时间\r\n        },\r\n        management_name: \"王工\",\r\n        management_contact_info: \"15698567542\",\r\n      };\r\n    },\r\n    // // 设置内容\r\n    // setContent(content) {\r\n    //   this.isshowsss = true;\r\n    //   this.selectedItem = content;\r\n    //   console.log(this.selectedItem);\r\n\r\n    //   this.activeContent = content;\r\n    // },\r\n    showdetails(item) {\r\n      // item.items.forEach((item) => {\r\n      //   this.newArr.push({ name: item.name });\r\n      // });\r\n      // console.log(this.newArr);\r\n\r\n      this.isshow = true;\r\n      this.tableDataItem = item.items;\r\n    },\r\n    hidedetails() {\r\n      this.isshow = false;\r\n    },\r\n\r\n    oc(value) {\r\n      console.log(value, \"收到的值\");\r\n      this.showdh = value;\r\n    },\r\n    xuanzedialog(value) {\r\n      const optionMapping = {\r\n        选项1: 0,\r\n        选项2: 1,\r\n        选项3: 2,\r\n        选项4: 3,\r\n        选项5: 4,\r\n        选项6: 5,\r\n        选项7: 6,\r\n        选项8: 7,\r\n        选项9: 8,\r\n        选项10: 9,\r\n        选项11: 10,\r\n        选项12: 11,\r\n        选项13: 12,\r\n      };\r\n\r\n      const index = optionMapping[value];\r\n      if (index !== undefined) {\r\n        this.tableDataItem = this.data[index].items;\r\n      } else {\r\n        console.error(\"无效的选项: \", value);\r\n      }\r\n    },\r\n  },\r\n  // 生命周期 - 创建完成（可以访问当前this实例）\r\n  created() {},\r\n  // 生命周期 - 挂载完成（可以访问DOM元素）\r\n  mounted() {\r\n    this.gettoken(\"\");\r\n    this.showdh1 = true;\r\n    this.fetchEquipmentTags();\r\n    // setTimeout(() => {\r\n    //   this.showdh1 = false;\r\n    //   this.noAnimation = false;\r\n    // }, 1000); // 动画持续时间为1秒\r\n    console.log(1222);\r\n  },\r\n  beforeCreate() {}, // 生命周期 - 创建之前\r\n  beforeMount() {}, // 生命周期 - 挂载之前\r\n  beforeUpdate() {}, // 生命周期 - 更新之前\r\n  updated() {}, // 生命周期 - 更新之后\r\n  beforeUnmount() {\r\n    // 在组件销毁之前清除定时器\r\n    console.log(1111);\r\n  },\r\n\r\n  unmounted() {\r\n    console.log(2222);\r\n  }, // 生命周期 - 销毁之前\r\n  destroyed() {\r\n    console.log(1221);\r\n  }, // 生命周期 - 销毁完成\r\n  activated() {}, // 如果页面有keep-alive缓存功能，这个函数会触发\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.container {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-content: space-between;\r\n  align-items: stretch;\r\n  height: 1080px;\r\n\r\n  // text-align: center;\r\n  /* 定位 el-cascader 的 placeholder 样式 */\r\n\r\n  /deep/.sect.el-tooltip__trigger {\r\n    text-align: left;\r\n    width: 200px;\r\n    color: #fff;\r\n    // background-color: #00ffc0;\r\n  }\r\n\r\n  /deep/.el-input__wrapper {\r\n    box-shadow: none;\r\n    border: none;\r\n    background-color: #5a6972;\r\n    color: #fff;\r\n  }\r\n\r\n  /deep/.sect .el-input__inner {\r\n    color: #fff;\r\n  }\r\n\r\n  .sbdetails {\r\n    position: absolute;\r\n    top: 50%;\r\n    left: 50%;\r\n    transform: translate(-50%, -50%);\r\n    z-index: 9999;\r\n  }\r\n\r\n  .el-input {\r\n    margin-left: 5px;\r\n    width: 145px;\r\n    height: 34px;\r\n    color: #fff !important;\r\n\r\n    ::v-deep .el-input__wrapper {\r\n      background: url(\"../assets/image/inputss.png\");\r\n      background-size: 100% 100%;\r\n      background-repeat: no-repeat;\r\n      box-shadow: none !important;\r\n      color: #fff !important;\r\n    }\r\n\r\n    /deep/.el-input__inner {\r\n      color: #fff !important;\r\n    }\r\n\r\n    .el-input__inner::placeholder {\r\n      color: #fff;\r\n      /* 设置占位符颜色 */\r\n    }\r\n  }\r\n\r\n  .suosuo {\r\n    position: absolute;\r\n    top: 62px;\r\n    right: -32px;\r\n    cursor: pointer;\r\n  }\r\n\r\n  .box {\r\n    // margin-top: 6px;\r\n    padding-top: 5px;\r\n    margin-bottom: 0.225rem;\r\n    max-height: 745px;\r\n    width: 330px;\r\n    // height: 800px;\r\n\r\n    overflow-y: scroll;\r\n\r\n    /* 设置垂直滚动条 */\r\n    /* 设置滚动条的样式 */\r\n    &::-webkit-scrollbar {\r\n      width: 0.1px;\r\n      /* 设置滚动条的宽度 */\r\n    }\r\n\r\n    /* 设置滚动条轨道的样式 */\r\n    &::-webkit-scrollbar-track {\r\n      background-color: #f1f1f1;\r\n      /* 设置滚动条轨道的背景色 */\r\n    }\r\n\r\n    /* 设置滚动条滑块的样式 */\r\n    &::-webkit-scrollbar-thumb {\r\n      background-color: #334f6e;\r\n      /* 设置滚动条滑块的背景色 */\r\n    }\r\n\r\n    /* 鼠标悬停在滚动条上时的样式 */\r\n    &::-webkit-scrollbar-thumb:hover {\r\n      background-color: #555;\r\n      /* 设置鼠标悬停时滚动条滑块的背景色 */\r\n    }\r\n\r\n    .xiaobox {\r\n      margin-top: 20px;\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .siqiu {\r\n        width: 16px;\r\n        height: 16px;\r\n      }\r\n\r\n      .shuru {\r\n        display: flex;\r\n        align-items: center;\r\n        padding-left: 10px;\r\n        background: url(\"../assets/image/shebei12.png\");\r\n        background-size: 100% 100%;\r\n        background-repeat: no-repeat;\r\n        width: 261px;\r\n        height: 32px;\r\n        font-family: Source Han Sans SC;\r\n        font-weight: bold;\r\n        font-size: 17px;\r\n        color: #ffffff;\r\n      }\r\n    }\r\n\r\n    .xiaoboxs {\r\n      cursor: pointer;\r\n      margin-top: 14px;\r\n      display: flex;\r\n      margin-left: 5px;\r\n      align-items: center;\r\n\r\n      .nihaowo {\r\n        width: 78px;\r\n        font-family: Source Han Sans SC;\r\n        font-weight: bold;\r\n        font-size: 10px;\r\n        color: #ffffff;\r\n        display: flex;\r\n        white-space: nowrap;\r\n        /* 不换行 */\r\n        overflow: hidden;\r\n        /* 超出部分隐藏 */\r\n        text-overflow: ellipsis;\r\n        /* 超出部分显示省略号 */\r\n      }\r\n\r\n      .siqiu {\r\n        width: 16px;\r\n        height: 16px;\r\n        margin-left: 10px;\r\n        margin-right: 7px;\r\n      }\r\n\r\n      .shuru {\r\n        display: flex;\r\n        align-items: center;\r\n        padding-left: 10px;\r\n        background: url(\"../assets/image/shebei12.png\");\r\n        background-size: 100% 100%;\r\n        background-repeat: no-repeat;\r\n        width: 261px;\r\n        height: 32px;\r\n        font-family: Source Han Sans SC;\r\n        font-weight: bold;\r\n        font-size: 17px;\r\n        color: #ffffff;\r\n        white-space: nowrap;\r\n        /* 不换行 */\r\n        overflow: hidden;\r\n        /* 超出部分隐藏 */\r\n        text-overflow: ellipsis;\r\n        /* 超出部分显示省略号 */\r\n      }\r\n    }\r\n  }\r\n\r\n  .left-panel {\r\n    position: fixed;\r\n    z-index: 1;\r\n    top: 100px;\r\n    left: 22px;\r\n    width: 330px;\r\n    height: 937px;\r\n\r\n    background-size: 100% 100%;\r\n    transform: translate(-122%);\r\n    transition: transform 0.5s ease-in-out;\r\n  }\r\n\r\n  .left-panel-active {\r\n    transform: translate(0%);\r\n  }\r\n\r\n  .left-panel-active1 {\r\n    // transform: translate(0%);\r\n    animation: slideOut 1s ease-in-out forwards;\r\n  }\r\n\r\n  @keyframes slideOut {\r\n    100% {\r\n      transform: translateX(0%);\r\n    }\r\n\r\n    // 85% {\r\n    //   transform: translateX(-25%);\r\n    // }\r\n\r\n    // 65% {\r\n    //   transform: translateX(-15%);\r\n    // }\r\n\r\n    // 40% {\r\n    //   transform: translateX(-55%);\r\n    // }\r\n\r\n    // 30% {\r\n    //   transform: translateX(-40%);\r\n    // }\r\n\r\n    0% {\r\n      transform: translateX(-100%);\r\n    }\r\n  }\r\n\r\n  .rtitle {\r\n    margin-top: 16px;\r\n  }\r\n\r\n  @keyframes slideIn {\r\n    0% {\r\n      transform: translateX(100%);\r\n    }\r\n\r\n    // 30% {\r\n    //   transform: translateX(65%);\r\n    // }\r\n\r\n    // 40% {\r\n    //   transform: translateX(40%);\r\n    // }\r\n\r\n    // 65% {\r\n    //   transform: translateX(15%);\r\n    // }\r\n\r\n    // 85% {\r\n    //   transform: translateX(25%);\r\n    // }\r\n\r\n    100% {\r\n      transform: translateX(0%);\r\n    }\r\n  }\r\n\r\n  .right-panel {\r\n    position: fixed;\r\n    z-index: 1;\r\n    right: 83px;\r\n    width: 330px;\r\n    top: 100px;\r\n    height: 937px;\r\n\r\n    background-size: 100% 100%;\r\n    transform: translate(122%);\r\n    transition: transform 0.5s ease-in-out;\r\n\r\n    .jk {\r\n      margin-top: 12px;\r\n      width: 90%;\r\n      height: 200px;\r\n    }\r\n\r\n    .box {\r\n      margin-top: 6px;\r\n      margin-bottom: 18px;\r\n      // background: url(\"../assets/image/zuoshang1.png\");\r\n      background-size: 100% 100%;\r\n      background-repeat: no-repeat;\r\n\r\n      width: 94%;\r\n      // height: 428px;\r\n\r\n      .loudong {\r\n        margin-top: 21px;\r\n        margin-bottom: 25px;\r\n      }\r\n\r\n      .wenzi {\r\n        font-family: Microsoft YaHei;\r\n        font-weight: 400;\r\n        font-size: 10px;\r\n        color: #bdecf9;\r\n        text-align: left;\r\n        margin-left: 20px;\r\n        margin-right: 20px;\r\n\r\n        .h2biaoti {\r\n          margin-bottom: 15px;\r\n          font-family: Microsoft YaHei;\r\n          font-weight: 400;\r\n          font-size: 12px;\r\n          color: #00ffff;\r\n        }\r\n      }\r\n\r\n      .p {\r\n        text-indent: 2em;\r\n        margin-bottom: 1em;\r\n        letter-spacing: 0.05em;\r\n      }\r\n    }\r\n\r\n    .boxxx {\r\n      margin-top: 6px;\r\n      margin-bottom: 18px;\r\n      background: url(\"../assets/image/zuoshang1.png\");\r\n      background-size: 100% 100%;\r\n      background-repeat: no-repeat;\r\n\r\n      width: 333px;\r\n      height: 420px;\r\n      overflow-y: scroll;\r\n      /* 设置垂直滚动条 */\r\n      // overflow: hidden;\r\n      /* 设置滚动条的样式 */\r\n\r\n      .zengti {\r\n        margin: 10px 0;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        width: 315px;\r\n        height: 38px;\r\n        gap: 5px;\r\n\r\n        .left {\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: space-evenly;\r\n          width: 84px;\r\n          height: 27px;\r\n\r\n          .yuan {\r\n            width: 12px;\r\n            height: 12px;\r\n            border-radius: 50%;\r\n            background-color: #08f7f7;\r\n          }\r\n\r\n          .wenziss {\r\n            display: flex;\r\n            align-items: flex-start;\r\n            flex-direction: column;\r\n\r\n            .p1 {\r\n              font-family: Source Han Sans SC;\r\n              font-weight: 500;\r\n              font-size: 10px;\r\n              color: #ffffff;\r\n            }\r\n\r\n            .p2 {\r\n              font-family: Source Han Sans SC;\r\n              font-weight: 500;\r\n              font-size: 10px;\r\n              color: #55cff9;\r\n            }\r\n          }\r\n        }\r\n\r\n        .right {\r\n          background: url(\"../assets/image/rightbeij.png\");\r\n          background-size: 100% 100%;\r\n          background-repeat: no-repeat;\r\n\r\n          width: 217px;\r\n          height: 38px;\r\n          font-family: Source Han Sans SC;\r\n          font-weight: 500;\r\n          font-size: 11px;\r\n          color: #ffffff;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n        }\r\n      }\r\n    }\r\n\r\n    .boxxx::-webkit-scrollbar {\r\n      width: 1px;\r\n      /* 设置滚动条的宽度 */\r\n    }\r\n\r\n    /* 设置滚动条轨道的样式 */\r\n    .boxxx::-webkit-scrollbar-track {\r\n      background-color: #f1f1f1;\r\n      /* 设置滚动条轨道的背景色 */\r\n    }\r\n\r\n    /* 设置滚动条滑块的样式 */\r\n    .boxxx::-webkit-scrollbar-thumb {\r\n      background-color: #013363;\r\n      /* 设置滚动条滑块的背景色 */\r\n    }\r\n  }\r\n\r\n  .no-animation {\r\n    transition: none;\r\n  }\r\n\r\n  .right-panel-active {\r\n    transform: translate(0%);\r\n    // animation: slideIn 1s ease-in-out ;\r\n  }\r\n\r\n  .right-panel-active1 {\r\n    // transform: translate(0%);\r\n    animation: slideIn 1s ease-in-out forwards;\r\n  }\r\n\r\n  @keyframes slideIn {\r\n    0% {\r\n      transform: translateX(100%);\r\n    }\r\n\r\n    // 30% {\r\n    //   transform: translateX(65%);\r\n    // }\r\n\r\n    // 40% {\r\n    //   transform: translateX(40%);\r\n    // }\r\n\r\n    // 65% {\r\n    //   transform: translateX(15%);\r\n    // }\r\n\r\n    // 85% {\r\n    //   transform: translateX(25%);\r\n    // }\r\n\r\n    100% {\r\n      transform: translateX(0%);\r\n    }\r\n  }\r\n\r\n  .completed {\r\n    background: #7ad0ff;\r\n  }\r\n\r\n  .incomplete {\r\n    background: #ff6041;\r\n  }\r\n\r\n  .warning {\r\n    background: #00ffc0;\r\n  }\r\n\r\n  .completeds {\r\n    color: #7ad0ff;\r\n  }\r\n\r\n  .incompletes {\r\n    color: #ff6041;\r\n  }\r\n\r\n  .warnings {\r\n    color: #00ffc0;\r\n  }\r\n}\r\n\r\n.ql-center {\r\n  display: flex;\r\n  margin-top: 20px;\r\n  justify-content: space-around;\r\n  margin-top: 14px;\r\n\r\n  .ql-Box {\r\n    width: 75px;\r\n    height: 49px;\r\n    border: 1px solid #7ad0ff;\r\n    // opacity: 0.6;\r\n    border-radius: 2px;\r\n\r\n    .ql-box1 {\r\n      font-family: Alibaba PuHuiTi;\r\n      font-weight: bold;\r\n      font-size: 16px;\r\n      color: #7ad0ff;\r\n      margin-top: -10px;\r\n    }\r\n\r\n    .ql-box {\r\n      display: flex;\r\n      padding-left: 8px;\r\n      padding-right: 9px;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      width: 100%;\r\n      height: 34px;\r\n\r\n      .left_ql {\r\n        width: 49px;\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        color: #ffffff;\r\n\r\n        .yuan {\r\n          width: 7px;\r\n          height: 7px;\r\n          border-radius: 50%;\r\n        }\r\n\r\n        .pp {\r\n          color: #fff;\r\n          font-size: 12px;\r\n        }\r\n      }\r\n\r\n      img {\r\n        height: 12px;\r\n        width: 7px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.warn1 {\r\n  background: url(\"../assets/image/warnred.png\");\r\n}\r\n\r\n.warn2 {\r\n  background: url(\"../assets/image/warnyellow.png\");\r\n}\r\n\r\n.warn3 {\r\n  background: url(\"../assets/image/warngreen.png\");\r\n}\r\n\r\n.warning12 {\r\n  background-size: 100% 100%;\r\n  // width: 365px;\r\n  height: 47px;\r\n\r\n  .info {\r\n    margin-top: 5px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    text-align: left;\r\n\r\n    margin-left: 50px;\r\n\r\n    .info1 {\r\n      display: flex;\r\n      flex-direction: column;\r\n\r\n      p:nth-of-type(1) {\r\n        font-size: 13px;\r\n        font-family: Alibaba PuHuiTi;\r\n        font-weight: bold;\r\n      }\r\n\r\n      .red {\r\n        color: #ff0000;\r\n      }\r\n\r\n      .green {\r\n        color: #00ffcc;\r\n      }\r\n\r\n      .yellow {\r\n        color: #ffff00;\r\n      }\r\n\r\n      p:nth-of-type(2) {\r\n        font-size: 16px;\r\n        font-family: Alibaba PuHuiTi;\r\n        font-weight: 400;\r\n        color: #ffffff;\r\n      }\r\n    }\r\n\r\n    .info2 {\r\n      margin-right: 10px;\r\n      font-size: 15px;\r\n      font-family: Alibaba PuHuiTi;\r\n      font-weight: 400;\r\n      color: #cffff8;\r\n    }\r\n  }\r\n}\r\n\r\n.xxxx {\r\n  position: absolute;\r\n  top: 1%;\r\n  right: 1%;\r\n  width: 25px;\r\n  height: 25px;\r\n  z-index: 99999;\r\n}\r\n\r\n/* 菜单容器 */\r\n.menu-container {\r\n  display: flex;\r\n  width: 330px;\r\n  height: 736px;\r\n}\r\n\r\n/* 菜单样式 */\r\n.menu {\r\n  width: 100%;\r\n  // background-color: #fff;\r\n  // border: 1px solid #ddd;\r\n  border-radius: 4px;\r\n}\r\n\r\n/deep/.el-pagination {\r\n  --el-pagination-bg-color: none;\r\n  --el-pagination-button-color: #fff;\r\n  --el-pagination-font-size: 17px;\r\n  margin-left: -13px;\r\n}\r\n\r\n/deep/.el-pagination__total {\r\n  color: #fff;\r\n  font-size: 15px;\r\n}\r\n\r\n/deep/.el-pagination button:disabled {\r\n  background: none;\r\n  font-size: 17px;\r\n}\r\n\r\n/deep/.el-pagination button {\r\n  background: none;\r\n  font-size: 17px;\r\n}\r\n\r\n/deep/.el-icon {\r\n  font-size: 17px !important;\r\n}\r\n\r\n/* 菜单项样式 */\r\n.menu-group {\r\n  margin-top: 14px;\r\n}\r\n\r\n.menu-item {\r\n  cursor: pointer;\r\n  background: url(\"../assets/image/rightbeij.png\");\r\n  background-size: 100% 100%;\r\n  background-repeat: no-repeat;\r\n\r\n  width: 100%;\r\n  height: 32px;\r\n  font-family: Source Han Sans SC;\r\n  font-weight: 500;\r\n  font-size: 17px;\r\n  color: #ffffff;\r\n  display: flex;\r\n  align-items: center;\r\n  padding-left: 10px;\r\n  // justify-content: center;\r\n}\r\n\r\n.menu-item:hover {\r\n  // background-color: #f0f0f0;\r\n}\r\n\r\n.submenu {\r\n  // background-color: #f9f9f9;\r\n  padding-left: 20px;\r\n}\r\n\r\n.submenu-item {\r\n  padding: 3px;\r\n  padding-left: 12px;\r\n  margin: 8px;\r\n  font-size: 16px;\r\n  color: #ffffff;\r\n  cursor: pointer;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background-color: #2c343f;\r\n}\r\n\r\n.submenu-item:hover {\r\n  background-color: #163561;\r\n}\r\n\r\n.qiuqiu {\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  .siqiu {\r\n    width: 16px;\r\n    height: 16px;\r\n    margin-left: 10px;\r\n    margin-right: 7px;\r\n  }\r\n}\r\n\r\n.listtype {\r\n  background: url(\"../assets/image/xuanzhotiemmoon.png\");\r\n  background-repeat: no-repeat;\r\n  background-size: 100% 100%;\r\n  width: 80px;\r\n  height: 26px;\r\n  text-align: center;\r\n  line-height: 26px;\r\n\r\n  font-size: 13px;\r\n  font-family: Alibaba PuHuiTi;\r\n  font-weight: 400;\r\n  color: #fff;\r\n}\r\n\r\n.listtypes {\r\n  background: url(\"../assets/image/xuanzhotiemmoon.png\");\r\n  background-repeat: no-repeat;\r\n  background-size: 100% 100%;\r\n  width: 100px;\r\n  height: 32px;\r\n  text-align: center;\r\n  line-height: 32px;\r\n  cursor: pointer;\r\n  font-size: 13px;\r\n  font-family: Alibaba PuHuiTi;\r\n  font-weight: 400;\r\n  color: #fff;\r\n}\r\n\r\n.ellipsis {\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  max-width: 190px;\r\n  /* 你可以根据需要调整宽度 */\r\n  font-size: 16px;\r\n}\r\n\r\n.botbtn {\r\n  position: fixed;\r\n  top: 978px;\r\n  // left: 228px;\r\n  left: 355px;\r\n  width: 200px;\r\n  height: 43px;\r\n  background: #022d56;\r\n  border-radius: 8px;\r\n  z-index: 20;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n\r\n  .btt {\r\n    color: #fff;\r\n    font-size: 17px;\r\n    line-height: 44px;\r\n    width: 88px;\r\n    text-align: center;\r\n    height: 43px;\r\n    cursor: pointer;\r\n  }\r\n\r\n  .btt1 {\r\n    color: rgb(8, 207, 241);\r\n    font-size: 17px;\r\n    line-height: 44px;\r\n    width: 88px;\r\n    text-align: center;\r\n    height: 43px;\r\n    cursor: pointer;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;AA+HA;AACA;AACA,SAASA,UAAU,EAAEC,UAAS,QAAS,MAAM;AAC7C,OAAOC,UAAS,MAAO,yBAAyB;AAChD,OAAOC,QAAO,MAAO,mCAAmC;AACxD,OAAOC,OAAM,MAAO,kCAAkC;AACtD,OAAOC,QAAO,MAAO,mCAAmC;AACxD,OAAOC,UAAS,MAAO,qCAAqC;AAC5D,OAAOC,QAAO,MAAO,8CAA8C;AACnE,OAAOC,KAAI,MAAO,oCAAoC;AACtD,OAAOC,MAAK,MAAO,iCAAiC;AACpD,OAAOC,WAAU,MAAO,sCAAsC;AAC9D,OAAOC,KAAI,MAAO,OAAO;AACzB;AACA;AACA,MAAMC,OAAM,GAAIC,OAAO,CAACC,GAAG,CAACC,gBAAe,IAAK,WAAW;AAE3D,MAAMC,GAAE,GAAIL,KAAK,CAACM,MAAM,CAAC;EACvBL;AACF,CAAC,CAAC;AACF,eAAe;EACb;EACAM,UAAU,EAAE;IACVV,KAAK;IACLL,QAAQ;IACRC,OAAO;IACPC,QAAQ;IACRC,UAAU;IACVC,QAAQ;IACRG,WAAW;IACXD,MAAM;IACNP;EACF,CAAC;EACDiB,KAAK,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC;EAEjCC,IAAIA,CAAA,EAAG;IACL;IACA,OAAO;MACLC,OAAO,EAAE,IAAI;MACbC,YAAY,EAAE,CAAC;MACfC,UAAU,EAAE,KAAK;MACjBC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE,CAAC;MACZC,KAAK,EAAE,IAAI;MACXC,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;MAC7BC,aAAa,EAAE,IAAI;MAAE;MACrBC,aAAa,EAAE,IAAI;MAAE;MACrBC,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE,KAAK;MACbC,IAAI,EAAE,KAAK;MACXC,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE,EAAE;MACbC,OAAO,EAAE,CACP;QACEC,KAAK,EAAE,QAAQ;QACfC,KAAK,EAAE,IAAI;QACXC,QAAQ,EAAE,CACR;UACEF,KAAK,EAAE,aAAa;UACpBC,KAAK,EAAE;QACT,CAAC,EACD;UACED,KAAK,EAAE,SAAS;UAChBC,KAAK,EAAE;QACT,CAAC;MAEL,CAAC,CACF;MACDE,UAAU,EAAE,EAAE;MACdC,KAAK,EAAE,EAAE;MACTC,SAAS,EAAE,OAAO;MAClBC,MAAM,EAAE,CACN;QACEC,IAAI,EAAE;MACR,CAAC,EACD;QAAEA,IAAI,EAAE;MAAqB,CAAC,EAC9B;QAAEA,IAAI,EAAE;MAAqB,CAAC,EAC9B;QAAEA,IAAI,EAAE;MAAY,CAAC,EACrB;QAAEA,IAAI,EAAE;MAAoB,CAAC,EAC7B;QAAEA,IAAI,EAAE;MAAqB,CAAC,CAC/B;MACDC,MAAM,EAAE,IAAI;MACZC,OAAO,EAAE,KAAK;MACdC,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE;QACZJ,IAAI,EAAE,OAAO;QAAE;QACfK,MAAM,EAAE,8DAA8D;QACtEC,QAAQ,EAAE,gBAAgB;QAAE;QAC5BC,MAAM,EAAE,KAAK;QAAE;QACfC,OAAO,EAAE,CACP;UACER,IAAI,EAAE,MAAM;UACZP,KAAK,EAAE;QACT,CAAC,EACD;UACEO,IAAI,EAAE,IAAI;UACVP,KAAK,EAAE;QACT,CAAC,EACD;UACEO,IAAI,EAAE,MAAM;UACZP,KAAK,EAAE;QACT,CAAC,EACD;UACEO,IAAI,EAAE,IAAI;UACVP,KAAK,EAAE;QACT,CAAC,EACD;UACEO,IAAI,EAAE,MAAM;UACZP,KAAK,EAAE;QACT,CAAC,EACD;UACEO,IAAI,EAAE,OAAO;UACbP,KAAK,EAAE;QACT,CAAC,EACD;UACEO,IAAI,EAAE,SAAS;UACfP,KAAK,EAAE;QACT,CAAC,CACF;QACDgB,mBAAmB,EAAE;UACnBC,mBAAmB,EAAE,SAAS;UAAE;UAChCC,IAAI,EAAE,YAAY;UAAE;UACpBC,qBAAqB,EAAE,YAAY,CAAE;QACvC,CAAC;QACDC,eAAe,EAAE,IAAI;QACrBC,uBAAuB,EAAE;MAC3B,CAAC;MACDC,aAAa,EAAE,EAAE;MACjBC,KAAK;MACLC,KAAK,EAAE,MAAM;MACbC,UAAU,EAAE,CACV;QAAEC,GAAG,EAAE;MAAK,CAAC,EACb;QAAEA,GAAG,EAAE;MAAO,CAAC,EACf;QAAEA,GAAG,EAAE;MAAO,CAAC,EACf;QAAEA,GAAG,EAAE;MAAM,CAAC,EACd;QAAEA,GAAG,EAAE;MAAK,CAAC,EACb;QAAEA,GAAG,EAAE;MAAO,CAAC,EACf;QAAEA,GAAG,EAAE;MAAO,CAAC,CAChB;MACDC,GAAG,EAAE,IAAI;MACTC,MAAM,EAAE,CACN;QACEC,KAAK,EAAE,MAAM;QACbf,MAAM,EAAE,QAAQ;QAChBgB,IAAI,EAAE;MACR,CAAC,EAED;QACED,KAAK,EAAE,MAAM;QACbf,MAAM,EAAE,SAAS;QACjBgB,IAAI,EAAE;MACR,CAAC,EACD;QACED,KAAK,EAAE,MAAM;QACbf,MAAM,EAAE,IAAI;QACZgB,IAAI,EAAE;MACR,CAAC,EACD;QACED,KAAK,EAAE,KAAK;QACZf,MAAM,EAAE,MAAM;QACdgB,IAAI,EAAE;MACR,CAAC,CACF;MACDC,SAAS,EAAE,CACT;QACEC,IAAI,EAAE,CAAC;QACPzB,IAAI,EAAE,oBAAoB;QAC1BP,KAAK,EAAE,EAAE;QACTiC,IAAI,EAAE;MACR,CAAC,EACD;QACED,IAAI,EAAE,CAAC;QACPzB,IAAI,EAAE,oBAAoB;QAC1BP,KAAK,EAAE,EAAE;QACTiC,IAAI,EAAE;MACR,CAAC,EACD;QACED,IAAI,EAAE,CAAC;QACPzB,IAAI,EAAE,qBAAqB;QAC3BP,KAAK,EAAE,EAAE;QACTiC,IAAI,EAAE;MACR,CAAC,CACF;MACDC,eAAe,EAAE,KAAK;MACtBpB,MAAM,EAAE,KAAK;MACbqB,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE,KAAK;MACdC,aAAa,EAAE,CAAC;MAChBC,YAAY,EAAE,YAAY;MAC1BC,KAAK,EAAE;IACT,CAAC;EACH,CAAC;EACD;EACAC,QAAQ,EAAE;IACR,GAAG3E,UAAU,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC;EAC9C,CAAC;EACD;EACA4E,KAAK,EAAE,CAAC,CAAC;EACT;EACAC,OAAO,EAAE;IACPC,gBAAgBA,CAACC,GAAG,EAAE;MACpBC,OAAO,CAACC,GAAG,CAAC,MAAMF,GAAG,IAAI,CAAC;MAC1B,IAAI,CAACG,cAAc,CAAC,IAAI,CAACR,KAAK,EAAEK,GAAE,GAAI,CAAC,CAAC;IAC1C,CAAC;IACDI,mBAAmBA,CAACJ,GAAG,EAAE;MACvBC,OAAO,CAACC,GAAG,CAAC,QAAQF,GAAG,EAAE,CAAC;MAC1B,IAAI,CAACG,cAAc,CAAC,IAAI,CAACR,KAAK,EAAEK,GAAE,GAAI,CAAC,CAAC;IAC1C,CAAC;IACD,MAAMK,QAAQA,CAACC,EAAE,EAAE;MACjB,IAAI;QACF,MAAMC,QAAO,GAAI,MAAMvE,GAAG,CAACwE,IAAI,CAAC,EAAE,EAAE;UAClCC,MAAM,EAAE,4BAA4B;UACpCC,MAAM,EAAE;YACNC,QAAQ,EAAE;cACRC,GAAG,EAAEN,EAAE;cACPrC,QAAQ,EAAC;cACT;cACA;YACF;UACF;QACF,CAAC,CAAC;;QAEF;QACA,IAAIsC,QAAQ,CAACnE,IAAG,IAAKmE,QAAQ,CAACnE,IAAI,CAACmE,QAAQ,CAACZ,KAAK,EAAE;UACjD,MAAMA,KAAI,GAAIY,QAAQ,CAACnE,IAAI,CAACmE,QAAQ,CAACZ,KAAK;UAC1C,IAAI,CAACA,KAAI,GAAIA,KAAK;UAClB;UACA;UACAM,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEK,QAAQ,CAACnE,IAAI,CAACmE,QAAQ,CAAC;UAC3C,IAAI,CAAC7D,KAAI,GAAI6D,QAAQ,CAACnE,IAAI,CAACmE,QAAQ,CAAC7D,KAAK;UACzC,IAAI,CAACqD,gBAAgB,CAAC,CAAC,CAAC;QAC1B,OAAO;UACLE,OAAO,CAACY,KAAK,CAAC,iBAAiB,EAAEN,QAAQ,CAACnE,IAAI,CAACmE,QAAQ,CAAC;QAC1D;MACF,EAAE,OAAOM,KAAK,EAAE;QACdZ,OAAO,CAACY,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC/B;IACF,CAAC;IACDV,cAAcA,CAACR,KAAK,EAAEmB,KAAK,EAAE;MAC3B,MAAMC,OAAM,GAAI;QACdC,QAAQ,EAAE,sCAAsC;QAChDC,YAAY,EAAE;MAChB,CAAC;MACD,MAAMC,IAAG,GAAI;QACXT,MAAM,EAAE,yBAAyB;QACjCC,MAAM,EAAE;UACNf,KAAK,EAAEA,KAAK;UACZmB,KAAK,EAAEA,KAAI,GAAIA,KAAI,GAAI,EAAC,GAAI,CAAC;UAC7BK,GAAG,EAAE;QACP;MACF,CAAC;MACDxF,KAAI,CACD6E,IAAI,CAAC7B,KAAK,EAAEuC,IAAI,EAAE,CAAC,CAAC,EACpBE,IAAI,CAAEb,QAAQ,IAAK;QAClBN,OAAO,CAACC,GAAG,CAACK,QAAQ,CAACnE,IAAI,EAAE,GAAG,CAAC;QAC/B,IAAI,CAACmB,UAAS,GAAIgD,QAAQ,CAACnE,IAAI,CAACmE,QAAQ;QACxC,IAAI,CAAClE,OAAM,GAAI,KAAK;MACtB,CAAC,EACAgF,KAAK,CAAER,KAAK,IAAK;QAChBZ,OAAO,CAACY,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;MAChC,CAAC,CAAC;IACN,CAAC;IACD,GAAG7F,UAAU,CAAC,WAAW,EAAE,CAAC,oBAAoB,CAAC,CAAC;IAClDsG,SAASA,CAACC,KAAK,EAAE;MACf,IAAI,CAAC9E,SAAQ,GAAI8E,KAAK;MACtB,IAAI,CAAChF,UAAS,GAAIgF,KAAK;MACvB,IAAIA,KAAI,IAAK,CAAC,EAAE;QACd,IAAI,CAAC3D,MAAK,GAAI,KAAK;QACnB,IAAI,CAACC,OAAM,GAAI,IAAI;QACnB,IAAI,CAACC,WAAU,GAAI,IAAI;MACzB,OAAO;QACL,IAAI,CAACF,MAAK,GAAI,IAAI;QAClB,IAAI,CAACC,OAAM,GAAI,KAAK;QACpB,IAAI,CAACC,WAAU,GAAI,KAAK;MAC1B;IACF,CAAC;IACD0D,oBAAoBA,CAACpE,KAAK,EAAE;MAC1B6C,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE9C,KAAK,CAAC,CAAC,CAAC,CAAC;MAC9B,IAAI,CAACiD,QAAQ,CAACjD,KAAK,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC;IACDqE,aAAaA,CAAA,EAAG;MACd,IAAI,CAACjF,SAAQ,GAAI,KAAK;IACxB,CAAC;IACDkF,UAAUA,CAACC,OAAO,EAAE;MAClB,IAAIA,OAAM,IAAK,CAAC,EAAE;QAChB,IAAI,CAAC5E,MAAK,GAAI,IAAI;QAElB,IAAI,CAACX,IAAI,CAACwF,OAAO,CAAEC,IAAI,IAAK;UAC1B,IAAIA,IAAI,CAACC,QAAO,IAAK,OAAO,EAAE;YAC5B,IAAI,CAAC/E,MAAK,GAAI,IAAI;YAClB,IAAI,CAAC2B,aAAY,GAAImD,IAAI,CAACE,KAAK;YAC/B9B,OAAO,CAACC,GAAG,CAAC,IAAI,CAACxB,aAAa,CAAC;UACjC;QACF,CAAC,CAAC;MACJ;;MAEA;IACF,CAAC;IACDsD,aAAaA,CAACH,IAAI,EAAE;MAClB,IAAI,CAACrF,SAAQ,GAAI,IAAI;MACrByD,OAAO,CAACC,GAAG,CAAC2B,IAAI,EAAE,SAAS,CAAC;MAC5B,IAAI,CAAC9D,YAAW,GAAI;QAClBuC,EAAE,EAAEuB,IAAI,CAACvB,EAAE;QACX3C,IAAI,EAAEkE,IAAI,CAAClE,IAAI;QACfK,MAAM,EAAE6D,IAAI,CAACI,YAAY;QACzBhE,QAAQ,EAAE4D,IAAI,CAAC5D,QAAO,GAAI4D,IAAI,CAACK,SAAS;QAAE;QAC1ChE,MAAM,EAAE,CAAC2D,IAAI,CAACM,QAAO,GAAI,MAAK,GAAI,KAAK;QAAE;QACzChE,OAAO,EAAE,CACP;UACER,IAAI,EAAE,MAAM;UACZP,KAAK,EAAEyE,IAAI,CAAClE;QACd,CAAC,EACD;UACEA,IAAI,EAAE,IAAI;UACVP,KAAK,EAAEyE,IAAI,CAACO;QACd,CAAC,EACD;UACEzE,IAAI,EAAE,MAAM;UACZP,KAAK,EAAE,GAAG,IAAIiF,IAAI,CAACR,IAAI,CAACS,cAAa,GAAI,IAAI,CAAC,CAACC,WAAW,CAAC,CAAC,IAC1D,IAAIF,IAAI,CAACR,IAAI,CAACS,cAAa,GAAI,IAAI,CAAC,CAACE,QAAQ,CAAC,IAAI,KAChD,IAAIH,IAAI,CAACR,IAAI,CAACS,cAAa,GAAI,IAAI,CAAC,CAACG,OAAO,CAAC,CAAC;QACpD,CAAC,EACD;UACE9E,IAAI,EAAE,MAAM;UACZP,KAAK,EAAEyE,IAAI,CAACa;QACd,CAAC,EACD;UACE/E,IAAI,EAAE,MAAM;UACZP,KAAK,EAAEyE,IAAI,CAACc;QACd,CAAC,EACD;UACEhF,IAAI,EAAE,KAAK;UACXP,KAAK,EAAEyE,IAAI,CAACe;QACd,CAAC,EACD;UACEjF,IAAI,EAAE,MAAM;UACZP,KAAK,EAAEyE,IAAI,CAACgB;QACd,CAAC,CACF;QACDzE,mBAAmB,EAAE;UACnBC,mBAAmB,EAAE,SAAS;UAAE;UAChCC,IAAI,EAAE,YAAY;UAAE;UACpBC,qBAAqB,EAAE,YAAY,CAAE;QACvC,CAAC;QACDC,eAAe,EAAE,IAAI;QACrBC,uBAAuB,EAAE;MAC3B,CAAC;IACH,CAAC;IACD;IACA;IACA;IACA;IACA;;IAEA;IACA;IACAqE,WAAWA,CAACjB,IAAI,EAAE;MAChB;MACA;MACA;MACA;;MAEA,IAAI,CAAC9E,MAAK,GAAI,IAAI;MAClB,IAAI,CAAC2B,aAAY,GAAImD,IAAI,CAACE,KAAK;IACjC,CAAC;IACDgB,WAAWA,CAAA,EAAG;MACZ,IAAI,CAAChG,MAAK,GAAI,KAAK;IACrB,CAAC;IAEDiG,EAAEA,CAAC5F,KAAK,EAAE;MACR6C,OAAO,CAACC,GAAG,CAAC9C,KAAK,EAAE,MAAM,CAAC;MAC1B,IAAI,CAACQ,MAAK,GAAIR,KAAK;IACrB,CAAC;IACD6F,YAAYA,CAAC7F,KAAK,EAAE;MAClB,MAAM8F,aAAY,GAAI;QACpBC,GAAG,EAAE,CAAC;QACNC,GAAG,EAAE,CAAC;QACNC,GAAG,EAAE,CAAC;QACNC,GAAG,EAAE,CAAC;QACNC,GAAG,EAAE,CAAC;QACNC,GAAG,EAAE,CAAC;QACNC,GAAG,EAAE,CAAC;QACNC,GAAG,EAAE,CAAC;QACNC,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE;MACR,CAAC;MAED,MAAMxC,KAAI,GAAI2B,aAAa,CAAC9F,KAAK,CAAC;MAClC,IAAImE,KAAI,KAAMyC,SAAS,EAAE;QACvB,IAAI,CAACtF,aAAY,GAAI,IAAI,CAACtC,IAAI,CAACmF,KAAK,CAAC,CAACQ,KAAK;MAC7C,OAAO;QACL9B,OAAO,CAACY,KAAK,CAAC,SAAS,EAAEzD,KAAK,CAAC;MACjC;IACF;EACF,CAAC;EACD;EACA6G,OAAOA,CAAA,EAAG,CAAC,CAAC;EACZ;EACAC,OAAOA,CAAA,EAAG;IACR,IAAI,CAAC7D,QAAQ,CAAC,EAAE,CAAC;IACjB,IAAI,CAACxC,OAAM,GAAI,IAAI;IACnB,IAAI,CAACsG,kBAAkB,CAAC,CAAC;IACzB;IACA;IACA;IACA;IACAlE,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC;EACnB,CAAC;EACDkE,YAAYA,CAAA,EAAG,CAAC,CAAC;EAAE;EACnBC,WAAWA,CAAA,EAAG,CAAC,CAAC;EAAE;EAClBC,YAAYA,CAAA,EAAG,CAAC,CAAC;EAAE;EACnBC,OAAOA,CAAA,EAAG,CAAC,CAAC;EAAE;EACdC,aAAaA,CAAA,EAAG;IACd;IACAvE,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC;EACnB,CAAC;EAEDuE,SAASA,CAAA,EAAG;IACVxE,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC;EACnB,CAAC;EAAE;EACHwE,SAASA,CAAA,EAAG;IACVzE,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC;EACnB,CAAC;EAAE;EACHyE,SAASA,CAAA,EAAG,CAAC,CAAC,CAAE;AAClB,CAAC", "ignoreList": []}]}