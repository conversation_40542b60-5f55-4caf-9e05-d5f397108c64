{"remainingRequest": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\views\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\views\\index.vue", "mtime": 1751448949501}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\views\\index.vue"], "names": [], "mappings": ";AAsRA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAExB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC;MACH,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC;MACH,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClI,CAAC;MACH,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC;MACH,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACxB,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC1B;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3E,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjE,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnE,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnE,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjE,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrE,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrE,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChE,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9D,CAAC;MACH,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC;QACD,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC9B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC9B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACrB,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC7B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAChC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACX,CAAC;;QAED;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACX,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACX,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACX,CAAC;MACH,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC;MACH,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACf,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAClB,CAAC;EACH,CAAC;EACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/C,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3D,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC;EACH,CAAC;EACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;EACH,CAAC;EACD,CAAC,EAAE,CAAC,CAAC,CAAC;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5D,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACrB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACzC,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAChE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClE,CAAC;IACH,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtD,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC;QACH,CAAC;MACH,CAAC;;MAED,CAAC,CAAC,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC;;QAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9C,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MAClB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB;IACF,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtD,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC;QACH,CAAC;MACH,CAAC;;MAED,CAAC,CAAC,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC;;QAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MAClB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B;IACF,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC;;MAEF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MAClB;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3C,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvE;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvE;IACF,CAAC;;IAED,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC,CAAC,MAAM,CAAC,CAAC;IACT,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEhC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC;;MAEF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACrB;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACtB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACxB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvC;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvC;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACxB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvC;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvC;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC;IACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9C,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC7B,CAAC,CAAC,EAAE;QACF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACjD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC;;QAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC;;QAEF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;UACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;YACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChD,EAAE,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9C;QACF,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;YACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB;QACF;MACF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC1B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzE,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClE,CAAC;QACD,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpE,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACxB,CAAC,CAAC,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACf,CAAC,CAAC;;QAEF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAChC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;YACtB,CAAC,CAAC,EAAE,CAAC,CAAC;YACN,CAAC,CAAC,EAAE,CAAC,CAAC;YACN,CAAC,CAAC,EAAE,CAAC,CAAC;YACN,CAAC,CAAC,EAAE,CAAC,CAAC;YACN,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACd,CAAC;;UAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;UAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;cACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACf,CAAC;UACH,CAAC,CAAC;;UAEF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5D,CAAC;;YAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;gBACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjC;YACF;UACF,CAAC,CAAC;;UAEF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;cACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC;UACH,CAAC;;UAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C;MACF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrC;IACF,CAAC;EACH,CAAC;EACD,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACtD,CAAC;EACD,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;IAER,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC9B,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC5D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5C;MACA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MAC5C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;MACzC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzD,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrF,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3D,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B,CAAC,EAAE;IACL,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACX,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;EACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;EAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;EACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChD,CAAC", "file": "E:/svn/***********-0186-SFTianjinUniversityH5/src/src/views/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div>\n    <component :is=\"componentTag\" :tabledata=\"tabledata\" :zengtiimg=\"zengtiimg\" @fatherMethoddd=\"fatherMethoddd\"\n      ref=\"child\"></component>\n    <div class=\"container\" v-if=\"isshow\">\n      <div class=\"left-panel\" :class=\"{\n        'left-panel-active': showdh,\n        'no-animation': noAnimation,\n        'left-panel-active1': showdh1,\n      }\">\n        <Title class=\"ltitle1\" tit=\"平台介绍\" :isshow=\"true\">\n          <div class=\"zonghe\">\n            <div class=\"boxsty\" v-for=\"item in tablelist\" :key=\"item\">\n              <div class=\"mianji\">\n                <img :src=\"item.img\" class=\"img\" alt=\"\" />\n                <div class=\"wenzi\">\n                  <div class=\"top\">{{ item.name }}</div>\n                  <div class=\"bottom\">{{ item.value }}</div>\n                </div>\n              </div>\n            </div>\n          </div>\n          <div class=\"gongneng\" v-for=\"item in wenzilist\" :key=\"item\">\n            <div style=\"display: flex; align-items: center\">\n              <div class=\"yuan\"></div>\n              <div class=\"name\">{{ item.name }}</div>\n            </div>\n            <div class=\"value\">{{ item.value }}</div>\n          </div>\n        </Title>\n        <Title class=\"ltitle1\" tit=\"报警统计\" :isshow=\"true\">\n          <div class=\"boxxx\">\n            <huanxing :warningData=\"warningStats\"></huanxing>\n          </div>\n        </Title>\n        <!-- <Title class=\"ltitle1\" tit=\"能耗统计\" :isshow=\"true\">\n          <div class=\"box\">\n            <div class=\"zongheqt\">\n              <div class=\"left1\">\n                <div class=\"mianji\" v-for=\"item in dianlist\" :key=\"item\">\n                  <img :src=\"item.img\" class=\"img\" alt=\"\" />\n                  <div class=\"wenzis\">\n                    <div class=\"top\">12346</div>\n                    <div class=\"bottom\">\n                      <div style=\"\n                          font-family: Alibaba PuHuiTi;\n                          font-weight: 400;\n                          font-size: 13px;\n                          color: #3ba1f4;\n                        \">\n                        本日\n                      </div>\n                      /Kwh\n                    </div>\n                  </div>\n                </div>\n              </div>\n              <biao1></biao1>\n            </div>\n          </div>\n        </Title> -->\n      </div>\n\n      <!-- 弹出层 -->\n      <div class=\"popup-overlay\" v-if=\"showPopup\" @click=\"closePopup\">\n        <div class=\"popup-content\" @click.stop>\n          <div class=\"popup-header\">\n            <div class=\"popup-title\">历史预约详情</div>\n            <div class=\"close-btn\" @click=\"closePopup\">×</div>\n          </div>\n          <div class=\"popup-body\" v-loading=\"isAllLoading\" element-loading-text=\"加载中...\"\n            element-loading-background=\"rgba( 28, 37, 56, 0.8)\">\n            <div class=\"popup-table\">\n              <div class=\"table-header\">\n                <div class=\"col-2\">仪器名</div>\n                <div class=\"col-2\">组织机构</div>\n                <div class=\"col-2\">仪器位置</div>\n                <div class=\"col-2\">预约时长</div>\n                <div class=\"col-1\">预约人</div>\n                <div class=\"col-1\">预约状态</div>\n              </div>\n              <template v-if=\"tableDatass && tableDatass.length > 0\">\n                <div class=\"table-row\" v-for=\"item in tableDatass\" :key=\"item\">\n                  <div class=\"col-2\" :title=\"item.name\">{{ item.name }}</div>\n                  <div class=\"col-2\" :title=\"item.equipment_group\">\n                    {{ item.equipment_group }}\n                  </div>\n                  <div class=\"col-2\" :title=\"item.equipment_location\">\n                    {{ item.equipment_location }}\n                  </div>\n                  <div class=\"col-2\">{{ item.duration1 }}</div>\n                  <div class=\"col-1\">{{ item.roomNumber }}</div>\n                  <div class=\"col-1\">{{ item.status }}</div>\n                </div>\n              </template>\n              <template v-else>\n                <div class=\"empty-message\">暂无预约记录</div>\n              </template>\n            </div>\n            <!-- 分页组件 -->\n            <div class=\"pagination-container\">\n              <el-pagination :current-page=\"currentPage\" :page-size=\"pageSize\" :page-sizes=\"pageSizes\" :total=\"total\"\n                @size-change=\"handleSizeChange\" @current-change=\"handleCurrentChange\"\n                layout=\"total, sizes, prev, pager, next, jumper\" background />\n            </div>\n          </div>\n        </div>\n      </div>\n      <!-- 右侧内容 -->\n\n      <div class=\"right-panel\" :class=\"{\n        'right-panel-active': showdh,\n        'no-animation': noAnimation,\n        'right-panel-active1': showdh1,\n      }\">\n        <Title1 class=\"rtitle\" tit=\"今日预约\">\n          <div class=\"boxswq\" @click=\"showLargeTable\">\n            <div class=\"titleimgs\">\n              <div class=\"bgu\">\n                <div>预约总数</div>\n                <div>{{ yytotal }}</div>\n              </div>\n              <div class=\"bgu1\">\n                <div>已完成</div>\n                <div>{{ yytotal }}</div>\n              </div>\n            </div>\n            <div class=\"titless\">\n              <div class=\"item1\">仪器名</div>\n              <div class=\"item1\">预约时长</div>\n              <div class=\"item\">预约人</div>\n              <div class=\"item1\">预约状态</div>\n            </div>\n            <div class=\"titlesscontents\" v-loading=\"isLoading\" element-loading-text=\"加载中...\"\n              element-loading-spinner=\"el-icon-loading\" element-loading-background=\"rgba(0, 0, 0, 0.8)\">\n              <div class=\"contents\" v-for=\"item in todayReservations\" :key=\"item\">\n                <div class=\"item1\" :title=\"item.name\">{{ item.name }}</div>\n                <div class=\"item1\">{{ item.duration }}</div>\n                <div class=\"item\">{{ item.roomNumber }}</div>\n                <div class=\"item1\">{{ item.status }}</div>\n              </div>\n              <div v-if=\"!todayReservations.length\" class=\"empty-message\">\n                暂无预约记录\n              </div>\n            </div>\n          </div>\n        </Title1>\n\n        <Title1 class=\"rtitle\" tit=\"仪器状态\">\n          <div class=\"huangxing\">\n            <SystemDete></SystemDete>\n            <!-- <SystemDete></SystemDete> -->\n          </div>\n        </Title1>\n        <Title1 class=\"rtitle\" tit=\"异常跟踪处理\" :isshow=\"true\">\n          <div class=\"boxxxs\" @click=\"openbj()\">\n            <div class=\"ql-center\">\n              <div class=\"ql-Box\">\n                <div class=\"ql-box\">\n                  <div class=\"left_ql\">\n                    <div class=\"yuan status\" style=\"color: #5c9dee\"></div>\n                    <div class=\"pp\">未修复</div>\n                  </div>\n                </div>\n                <div class=\"ql-box1\" style=\"color: #b93851\">\n                  {{ unfixedCount }}\n                </div>\n              </div>\n              <div class=\"ql-Box\">\n                <div class=\"ql-box\">\n                  <div class=\"left_ql\">\n                    <div class=\"yuan1 status\" style=\"color: #89f6c1\"></div>\n                    <div class=\"pp\">已修复</div>\n                  </div>\n                </div>\n                <div class=\"ql-box1 status\" style=\"color: #89f6c1\">\n                  {{ fixedCount }}\n                </div>\n              </div>\n            </div>\n            <div class=\"unfixed-warnings\">\n              <!-- 未修复警告列表 -->\n              <div v-for=\"(warning, index) in warningData.unfixed\" :key=\"'unfixed-' + index\" class=\"warning12\">\n                <div class=\"info\">\n                  <div>\n                    <div class=\"zongduan\">\n                      <div class=\"yuan\" style=\"background-color: #b93851\"></div>\n                      <div class=\"cjhulizhong\" style=\"color: #b93851\">\n                        未修复\n                      </div>\n                    </div>\n                    <p class=\"info2\">{{ warning.warningCategory }}</p>\n                  </div>\n\n                  <div class=\"info1\">\n                    <p class=\"time\">{{ formatDate(warning.createdAt) }}</p>\n                    <p class=\"location\">{{ warning.errMsg }}</p>\n                  </div>\n                  <p class=\"info2\">\n                    {{ warning.deviceName }}\n                  </p>\n                </div>\n              </div>\n            </div>\n            <!-- 已修复警告列表 -->\n            <!-- <div v-for=\"(warning, index) in warningData.fixed\" :key=\"'fixed-'+index\" class=\"warning12\">\n              <div class=\"info\">\n                <div class=\"zongduan\">\n                  <div class=\"yuan\" style=\"background-color: #64f8bb\"></div>\n                  <div class=\"cjhulizhong\" style=\"color: #64f8bb\">已处理</div>\n                </div>\n                <div class=\"info1\">\n                  <p class=\"time\">{{ formatDate(warning.createdAt) }}</p>\n                  <p class=\"location\">{{ warning.errMsg }}</p>\n                </div>\n                <p class=\"info2\" style=\"color: #64f8bb\" @click=\"openbj()\">{{ warning.deviceName }}</p>\n              </div>\n            </div> -->\n          </div>\n        </Title1>\n      </div>\n    </div>\n    <table2 @close=\"closetan\" v-if=\"opentable2\"></table2>\n    <!-- <table-2 class=\"table2\" @close=\"closetan\" v-if=\"opentable2\"></table-2> -->\n    <!-- <div\n      class=\"center_container\"\n      :class=\"{\n        'right-panel-active11': showdh,\n        'no-animation': noAnimation,\n        'right-panel-active12': showdh1,\n      }\"\n    >\n      <img\n        class=\"btn\"\n        src=\"../assets/image/shang.png\"\n        @click=\"scrollUp\"\n        alt=\"向上\"\n      />\n      <div class=\"content\" ref=\"content\">\n        <div\n          :class=\"activef == index ? 'itema' : 'item'\"\n          v-for=\"(item, index) in resItems\"\n          :key=\"index\"\n          @click=\"switchactivef(item, index)\"\n          @mouseover=\"hoveredRoom = item\"\n          @mouseleave=\"hoveredRoom = null\"\n        >\n          {{\n            index === 0\n              ? title + \"F-\" + \"整体\"\n              : title + \"F-\" + (index < 10 ? \"10\" + index : \"1\" + index)\n          }}\n\n          <div class=\"tooltip\" v-if=\"hoveredRoom === item\">{{ item.name }}</div>\n        </div>\n      </div>\n      <img\n        class=\"btn\"\n        src=\"../assets/image/xia.png\"\n        @click=\"scrollDown\"\n        alt=\"向下\"\n      />\n    </div>\n    <div\n      @click=\"returnhome()\"\n      class=\"return\"\n      :class=\"{\n        'right-panel-active11': showdh,\n        'no-animation': noAnimation,\n        'right-panel-active12': showdh1,\n      }\"\n    >\n      返回\n    </div> -->\n  </div>\n</template>\n\n<script>\n// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）\n// 例如：import 《组件名称》 from '《组件路径》';\nimport huanxing from \"@/components/echarts/huanxing.vue\";\nimport zhexian from \"@/components/echarts/zhexian.vue\";\nimport zhexian1 from \"@/components/echarts/zhexian1.vue\";\nimport SystemDete from \"@/components/echarts/SystemDete.vue\";\nimport echarts1 from \"@/components/echarts/bingjifang/echarts3.vue\";\nimport echarts2 from \"@/components/echarts/bingjifang/echarts5.vue\";\nimport table2 from \"@/components/common/table2.vue\";\nimport shuangxiang from \"@/components/echarts/shuangxiang.vue\";\nimport shebei from \"@/views/shebei.vue\";\nimport { resourceDeviceList } from \"@/api/admin.js\";\nimport biao1 from \"../components/echarts/biao1.vue\";\nimport biao1ss from \"../components/echarts/biao1ss.vue\";\nimport axios from \"axios\";\nimport {\n  getDeviceData,\n  getDevicedetails,\n  getDeviceWarningList,\n} from \"@/api/device.js\";\n\n// resourceDeviceList\nexport default {\n  // import引入的组件需要注入到对象中才能使用\n  components: {\n    table2,\n    huanxing,\n    zhexian,\n    zhexian1,\n    SystemDete,\n    echarts1,\n    echarts2,\n    shuangxiang,\n    shebei,\n    biao1ss,\n    biao1,\n  },\n  props: [\"title\", \"resItems\"],\n  data() {\n    // 这里存放数据\n    return {\n      jlURL,\n      dstime,\n      responseData: null, // 存储返回的数据\n      error: null, // 存储错误信息\n      todayReservations: [], // 今日预约数据\n      allReservations: [], // 所有预约数据\n      allTableData: [], // 存储所有数据\n      allTableData1: [], // 存储所有数据\n      currentPage: 1, // 当前页码\n      pageSize: 10, // 每页显示条数\n      total: 0, // 总数据条数\n      yytotal: 0,\n      pageSizes: [10, 20, 50, 100], // 每页显示条数选项\n      opentable2: false,\n      hoveredRoom: null,\n      scrollPosition: 0,\n      flag: true,\n      localtitle: \"\",\n      dianlist: [\n        {\n          name: \"总用地面积\",\n          value: \"57874.1㎡\",\n          img: require(\"../assets/image/ri.png\"),\n        },\n        {\n          name: \"总建筑面积\",\n          value: \"7802.54㎡\",\n          img: require(\"../assets/image/zhou.png\"),\n        },\n        {\n          name: \"地上建筑面积\",\n          value: \"14085㎡\",\n          img: require(\"../assets/image/yue.png\"),\n        },\n      ],\n      tablelist: [\n        {\n          name: \"总用地面积\",\n          value: \"4423.8㎡\",\n          img: require(\"../assets/image/mianji1.png\"),\n        },\n        {\n          name: \"总建筑面积\",\n          value: \"16845㎡\",\n          img: require(\"../assets/image/mianji2.png\"),\n        },\n        {\n          name: \"地上建筑面积\",\n          value: \"14085㎡\",\n          img: require(\"../assets/image/mianji3.png\"),\n        },\n        {\n          name: \"地下建筑面积\",\n          value: \"2760㎡\",\n          img: require(\"../assets/image/mianji4.png\"),\n        },\n      ],\n      wenzilist: [\n        {\n          name: \"平台概述\",\n          value: \"天津大学大型仪器平台是天津大学批准设立的校级公共技术服务平台，聚焦兼顾多学科需求的保障学校基础能力，促进跨平台和交叉新兴学科能力的建设,以'专管共用'的管理模式，为科学研究提供高质量的开放式测试服务，开展仪器设备创新性功能开发与技术研发。\",\n        },\n      ],\n\n      activef: 0,\n      isshow: true,\n      isactive: 0,\n      tabledata: [],\n      zengtiimg: \"\",\n      lrdata: [\n        {\n          title1: \"温度\",\n          title2: \"22℃\",\n          title3: \"2022-04-01 12:00:00\",\n        },\n      ],\n      deviceTypes: \"CQQ11\",\n      activeTab: \"today\",\n      botlist: [\n        { name: \"总览\", code: \"\" },\n        { name: \"设备列表\", code: \"\" },\n        {\n          name: \"环境温湿度\",\n          code: \"CGQ11\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/wensiduchuanganqi.png\",\n        },\n        {\n          name: \"防爆温湿度\",\n          code: \"CGQ10\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/fangbao.png\",\n        },\n        {\n          name: \"冰箱状态\",\n          code: \"LRY193\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/bingxiang.png\",\n        },\n        {\n          name: \"培养箱状态\",\n          code: \"CGQ13\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/bingxiang.png\",\n        },\n        {\n          name: \"乙炔气体\",\n          code: \"CGQ7\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/fangbao.png\",\n        },\n        {\n          name: \"环境CO2\",\n          code: \"CGQ9\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/huanjingco2.png\",\n        },\n        {\n          name: \"环境O2\",\n          code: \"CGQ3\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/huanjingco2.png\",\n        },\n        {\n          name: \"甲烷气体\",\n          code: \"CGQ8\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/jiawan.png\",\n        },\n        {\n          name: \"房间压差\",\n          code: \"CGQ2\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/kqyc.png\",\n        },\n      ],\n      listst: [\n        {\n          name: \"广东质检中诚认证有限公司到中广...\",\n        },\n        { name: \"材料科学、化学工程及医药研发成...\" },\n        { name: \"酒精代谢能力检测：您的酒量，基...\" },\n        { name: \"植酸检测方法及作用\" },\n        { name: \"兔拉检测：行业中芯片造假技术...\" },\n        { name: \"酒精代谢能力检测：您的酒量，基...\" },\n      ],\n      showdh: true,\n      showdh1: false,\n      noAnimation: false,\n      localTitle: this.title, // 初始化本地数据属性\n      nhlist: [\n        {\n          title: \"供气压力\",\n          status: \"0.3Mpa\",\n          unit: \"℃\",\n        },\n\n        {\n          title: \"供气流量\",\n          status: \"6M3/min\",\n          unit: \"㎡\",\n        },\n        {\n          title: \"露点温度\",\n          status: \"6℃\",\n          unit: \"℃\",\n        },\n        {\n          title: \"含氧量\",\n          status: \"6PPM\",\n          unit: \"㎡\",\n        },\n      ],\n      warnlist1: [\n        {\n          type: 1,\n          time: \"视频监控报警-3号楼-3F-101\",\n          value: \"\",\n          name: \"2024-06-16   12:34:09\",\n        },\n        {\n          type: 2,\n          time: \"视频监控报警-3号楼-3F-101\",\n          value: \"\",\n          name: \"2024-06-16   12:34:09\",\n        },\n        {\n          type: 3,\n          name: \"2024-06-16   12:34:09\",\n          value: \"\",\n          time: \"视频监控报警-3号楼-3F-101\",\n        },\n      ],\n      isButton2Active: false,\n      status: \"巡检中\",\n      status1: \"已完成\",\n      status2: \"待巡检\",\n      selectedIndex: 0,\n      componentTag: \"\",\n      dectid: \"\",\n      showPopup: false,\n      isLoading: false, // 今日预约加载状态\n      isAllLoading: false, // 全部预约加载状态\n      warningData: {\n        unfixed: [],\n        fixed: [],\n        unfixedtotal: 0,\n        fixedtotal: 0,\n      },\n      baseURL: \"https://tjdx.yuankong.org.cn\",\n      token: localStorage.getItem(\"token\") || \"\",\n      warningStats: [],\n    };\n  },\n  // 计算属性类似于data概念\n  computed: {\n    formattedTitle() {\n      return {\n        title: `${this.localTitle}F实验室介绍`,\n        img: require(`../assets/img/floor/1Fbig.png`),\n      };\n    },\n    formattedTitle1() {\n      return `${this.localTitle}F实验室总览`;\n    },\n    formattedTitle2() {\n      return `实验室${this.localTitle}F环境信息`;\n    },\n    formattedTitle3() {\n      return `实验室${this.localTitle}F设备信息`;\n    },\n    formattedTitle4() {\n      return `实验室${this.localTitle}F事件详情`;\n    },\n    formatted1Title() {\n      return {\n        title: `${this.localTitle}实验室介绍`,\n        img: require(`../assets/img/floor/${this.title}Fbig.png`),\n      };\n    },\n    formatted1Title1() {\n      return `${this.localTitle}实验室总览`;\n    },\n    formatted1Title2() {\n      return `${this.localTitle}环境信息`;\n    },\n    formatted1Title3() {\n      return `${this.localTitle}设备信息`;\n    },\n    formatted1Title4() {\n      return `${this.localTitle}事件详情`;\n    },\n    unfixedCount() {\n      return this.warningData.unfixedtotal;\n    },\n    fixedCount() {\n      return this.warningData.fixedtotal;\n    },\n  },\n  // 监控data中的数据变化\n  watch: {\n    title(newVal) {\n      this.localTitle = newVal;\n    },\n    resItems(newVal) {\n      console.log(newVal);\n\n      // this.resItems = newVal;\n    },\n  },\n  // 方法集合\n  methods: {\n    // 获取当前日期的00:00:01的时间戳\n    getStartOfDayTimestamp() {\n      const now = new Date();\n      now.setHours(0, 0, 1, 0); // 设置时间为当天的 00:00:01\n      return Math.floor(now.getTime() / 1000); // 转换为 Unix 时间戳（秒）\n    },\n\n    // 获取当前日期的23:59:59的时间戳\n    getEndOfDayTimestamp() {\n      const now = new Date();\n      now.setHours(23, 59, 59, 0);\n      return Math.floor(now.getTime() / 1000);\n    },\n\n    // 格式化预约数据\n    formatReservationData(item) {\n      return {\n        name: item.equipment_name,\n        date: new Date(item.start * 1000).toLocaleDateString(),\n        date1: new Date(item.start * 1000).toLocaleString(),\n        duration: `${new Date(item.start * 1000).getHours()}:00-${new Date(\n          item.end * 1000\n        ).getHours()}:00`,\n        duration1: `${new Date(\n          item.start * 1000\n        ).toLocaleDateString()} ${new Date(\n          item.start * 1000\n        ).getHours()}:00 - ${new Date(\n          item.end * 1000\n        ).toLocaleDateString()} ${new Date(item.end * 1000).getHours()}:00`,\n        roomNumber: item.user_name,\n        status: item.is_using === \"1\" ? \"已预约\" : \"已预约\",\n        equipment_group: item.equipment_group || \"未设置\", // 添加组织机构字段\n        equipment_location: item.equipment_location || \"未设置\", // 添加仪器位置字段\n      };\n    },\n\n    // 获取今日预约数据\n    async fetchTodayReservations() {\n      this.isLoading = true;\n      const headers = {\n        clientid: \"5a298e93-158d-4e22-83cf-6ceb62e9b4f1\",\n        clientsecret: \"2c8ec39e-9887-482a-b28b-e64c496b601c\",\n      };\n\n      const requestBody = {\n        method: \"gpui/eq_reserv/reservList\",\n        params: {\n          dtstart: this.getStartOfDayTimestamp(),\n          dtend: this.getEndOfDayTimestamp(),\n          params: {\n            location: \"58\",\n            limit: [0, 10000],\n          },\n        },\n      };\n\n      try {\n        const response = await axios.post(\n          jlURL,\n          requestBody,\n          { headers }\n        );\n\n        this.todayReservations = response.data.response.map(\n          this.formatReservationData\n        );\n        this.yytotal = this.todayReservations.length;\n      } catch (err) {\n        console.error(\"获取今日预约数据失败:\", err);\n        this.error = err;\n      } finally {\n        this.isLoading = false;\n      }\n    },\n\n    // 获取所有预约数据\n    async fetchAllReservations() {\n      this.isAllLoading = true;\n      const headers = {\n        clientid: \"5a298e93-158d-4e22-83cf-6ceb62e9b4f1\",\n        clientsecret: \"2c8ec39e-9887-482a-b28b-e64c496b601c\",\n      };\n\n      const requestBody = {\n        method: \"gpui/eq_reserv/reservList\",\n        params: {\n          dtstart: 1704844800,\n          dtend: this.getEndOfDayTimestamp(),\n          params: {\n            location: \"58\",\n            limit: [0, 10000],\n          },\n        },\n      };\n\n      try {\n        const response = await axios.post(\n          jlURL,\n          requestBody,\n          { headers }\n        );\n\n        // 对数据进行时间倒序排序\n        const sortedData = response.data.response.sort(\n          (a, b) => b.start - a.start\n        );\n        this.allReservations = sortedData.map(this.formatReservationData);\n        this.total = this.allReservations.length;\n        this.handleCurrentChange(1);\n      } catch (err) {\n        console.error(\"获取所有预约数据失败:\", err);\n        this.error = err;\n      } finally {\n        this.isAllLoading = false;\n      }\n    },\n\n    // 显示大表格\n    async showLargeTable() {\n      this.showPopup = true;\n      // 只在没有数据或数据过期的情况下重新获取\n      if (!this.allReservations.length) {\n        await this.fetchAllReservations();\n      } else {\n        // 如果已有数据，直接更新分页\n        this.handleCurrentChange(1);\n      }\n    },\n\n    closetan() {\n      this.opentable2 = false;\n    },\n    openbj() {\n      this.opentable2 = true;\n    },\n    handleOpenDialog() {\n      console.log(1111);\n      this.$emit(\"open-bj\");\n    },\n    scrollUp() {\n      const content = this.$refs.content;\n      content.scrollTop -= 38; // 每次向上滑动25px\n    },\n    scrollDown() {\n      const content = this.$refs.content;\n      content.scrollTop += 38; // 每次向下滑动25px\n    },\n    returnhome() {\n      this.$emit(\"returnhome\");\n    },\n    async switchactivef(item, index) {\n      this.dectid = item.id;\n      const res = await resourceDeviceList({\n        resourceId: item.id,\n        deviceTypes: this.deviceTypes,\n      });\n\n      this.tabledata = res.data;\n\n      if (index) {\n        this.flag = false;\n        this.localTitle = item.roomid;\n      } else {\n        this.localTitle = this.title;\n        this.flag = true;\n      }\n      console.log(item);\n      this.activef = index;\n      // this.$emit(\"childEvent\", title, index);\n    },\n    slideUp() {\n      const contentHeight = this.$refs.content.scrollHeight;\n      if (this.position > -contentHeight + this.containerHeight) {\n        this.position -= this.step;\n        this.$refs.content.style.transform = `translateY(${this.position}px)`;\n      }\n    },\n    slideDown() {\n      if (this.position < 0) {\n        this.position += this.step;\n        this.$refs.content.style.transform = `translateY(${this.position}px)`;\n      }\n    },\n\n    //  this.dectid = item.id;\n    //     const res = await resourceDeviceList({\n    //       resourceId: item.id,\n    //       deviceTypes: this.deviceTypes,\n    //     });\n    //     console.log(res.data, \"qilei\");\n    //     this.tabledata = res.data;\n\n    async switchTab1(item, index) {\n      console.log(item.img);\n      this.zengtiimg = item.img;\n\n      this.deviceTypes = item.code;\n      const res = await resourceDeviceList({\n        resourceId: this.dectid,\n        deviceTypes: this.deviceTypes,\n      });\n\n      this.tabledata = res.data;\n\n      // this.switchactivef(item, item.code);\n      this.isactive = index;\n      if (index) {\n        this.componentTag = \"shebei\";\n        this.isshow = false;\n        this.showdh = true;\n        this.showdh1 = false;\n      } else {\n        this.componentTag = \"\";\n        this.isshow = true;\n        this.showdh = false;\n        this.showdh1 = true;\n      }\n    },\n    switchTab(tab) {\n      this.activeTab = tab;\n    },\n    qeihuan(index) {\n      console.log(index, \"123123\");\n    },\n\n    qiehuanyans(index) {\n      console.log(index, \"123123\");\n      this.currentIndex = index;\n    },\n    getClassForStatus(status) {\n      if (status === \"告警总数\") {\n        return \"completed\";\n      } else if (status === \"处理完\") {\n        return \"incomplete\";\n      } else if (status === \"未处理\") {\n        return \"warning\";\n      } else {\n        return \"default\"; // 没有匹配的状态时可以添加一个默认类\n      }\n    },\n    getClassForStatuss(status) {\n      if (status === \"告警总数\") {\n        return \"completeds\";\n      } else if (status === \"处理完\") {\n        return \"incompletes\";\n      } else if (status === \"未处理\") {\n        return \"warnings\";\n      } else {\n        return \"default\"; // 没有匹配的状态时可以添加一个默认类\n      }\n    },\n    qiehuanyans(index) {\n      console.log(index, \"123123\");\n      this.currentIndex = index;\n    },\n    oc(value) {\n      console.log(value, \"floor收到的值\");\n      this.showdh = value;\n    },\n    getClassForStatus(status) {\n      if (status === \"巡检中\") {\n        return \"completed\";\n      } else if (status === \"待巡检\") {\n        return \"incomplete\";\n      } else if (status === \"已完成\") {\n        return \"warning\";\n      } else {\n        return \"default\"; // 没有匹配的状态时可以添加一个默认类\n      }\n    },\n    getClassForStatuss(status) {\n      if (status === \"巡检中\") {\n        return \"completeds\";\n      } else if (status === \"待巡检\") {\n        return \"incompletes\";\n      } else if (status === \"已完成\") {\n        return \"warnings\";\n      } else {\n        return \"default\"; // 没有匹配的状态时可以添加一个默认类\n      }\n    },\n    closePopup() {\n      this.showPopup = false;\n    },\n    // 处理页码改变\n    handleCurrentChange(page) {\n      this.currentPage = page;\n      const start = (page - 1) * this.pageSize;\n      const end = start + this.pageSize;\n      this.tableDatass = this.allReservations.slice(start, end);\n      console.log(this.tableDatass, \"tableDatass\");\n    },\n\n    // 处理每页显示条数改变\n    handleSizeChange(size) {\n      this.pageSize = size;\n      this.handleCurrentChange(1);\n    },\n    async getWarningList(hasFixed) {\n      try {\n        // 在关键请求前检查token是否需要刷新\n        if (this.$auth && this.$auth.checkAndRefreshToken) {\n          await this.$auth.checkAndRefreshToken();\n        }\n\n        const response = await getDeviceWarningList({\n          hasFixed: hasFixed,\n        });\n\n        if (response.code === 200) {\n          if (hasFixed === \"N\") {\n            this.warningData.unfixed = response.rows;\n            this.warningData.unfixedtotal = response.total;\n          } else {\n            this.warningData.fixed = response.rows;\n            this.warningData.fixedtotal = response.total;\n          }\n        } else {\n          console.error(\"获取警告数据失败:\", response.msg);\n          // 只有在明确的认证错误时才清除token并跳转\n          if (response.code === 401) {\n            localStorage.removeItem(\"token\");\n            this.$router.push(\"/\");\n          }\n        }\n      } catch (error) {\n        console.error(\"请求警告数据出错:\", error);\n        // 请求错误时不要立即清除token，让拦截器处理\n      }\n    },\n    async fetchAllWarningData() {\n      await Promise.all([this.getWarningList(\"N\"), this.getWarningList(\"Y\")]);\n    },\n    formatDate(timestamp) {\n      const date = new Date(timestamp);\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(\n        2,\n        \"0\"\n      )}-${String(date.getDate()).padStart(2, \"0\")} ${String(\n        date.getHours()\n      ).padStart(2, \"0\")}:${String(date.getMinutes()).padStart(2, \"0\")}`;\n    },\n    async fetchWarningStats() {\n      try {\n        const res = await getDeviceWarningList({\n          pageSize: 9999,\n          currentPage: 1,\n          hasFixed: \"N\",\n        });\n\n        if (res.code === 200 && res.rows) {\n          // 定义所有可能的报警类型及其阈值（简化后的名称）\n          const allWarningTypes = {\n            压力: 14,\n            氧气: 48,\n            温度: 67,\n            湿度: 67,\n            // '气体泄漏': 50\n          };\n\n          // 统计各类型报警数量\n          const stats = {};\n          // 初始化所有报警类型的计数为0\n          Object.keys(allWarningTypes).forEach((type) => {\n            stats[type] = {\n              total: 0,\n              unresolved: 0,\n            };\n          });\n\n          // 统计实际数据（使用包含匹配）\n          res.rows.forEach((item) => {\n            // 查找匹配的报警类型（只要包含关键字就匹配）\n            const matchedType = Object.keys(allWarningTypes).find(type =>\n              item.warningCategory && item.warningCategory.includes(type)\n            );\n\n            if (matchedType) {\n              stats[matchedType].total++;\n              if (item.status === \"N\") {\n                stats[matchedType].unresolved++;\n              }\n            }\n          });\n\n          // 转换为图表所需格式\n          this.warningStats = Object.entries(stats).map(\n            ([category, count]) => ({\n              label: `${category}(${count.total}/${allWarningTypes[category]})`,\n              value: count.total,\n            })\n          );\n\n          console.log(this.warningStats, \"报警统计数据\");\n        }\n      } catch (error) {\n        console.error(\"获取报警统计数据失败:\", error);\n      }\n    },\n  },\n  // 生命周期 - 创建完成（可以访问当前this实例）\n  created() {\n    this.fetchAllWarningData();\n    // 每5分钟刷新一次数据\n    setInterval(this.fetchAllWarningData, 5 * 60 * 1000);\n  },\n  // 生命周期 - 挂载完成（可以访问DOM元素）\n  mounted() {\n    this.fetchTodayReservations();\n    this.showdh1 = true;\n    setTimeout(() => {\n      this.showdh1 = false;\n      this.noAnimation = false;\n    }, 1000);\n\n    // 定时刷新今日预约数据\n    setInterval(() => {\n      this.fetchTodayReservations();\n    }, 1000 * this.dstime);\n    ue.interface.setSliderValue = (value) => {\n      console.log(value, \"ue点击拿到的值\");\n      if (!isNaN(Number(value.data))) {\n        // let did = value.data; // 如果是数字，则赋值\n        // const result = this.sblist.filter(item => item.id == did);\n        // this.deviceId = result[0].deviceId\n        // console.log(this.deviceId, 'ue点击拿到的id');\n      }\n      // this.deid = JSON.parse(value.data) - 43846\n      // console.log(this.deid);\n      // if (!isNaN(parseInt(value.data, 10))) {\n      //   var dtdata1 = JSON.parse(JSON.stringify(this.dtdata))\n      //   console.log(dtdata1);\n      //   this.showdet = false\n      //   // this.did = dtdata1.find(item => item.id == value.data)?.deviceid;\n      //   // console.log(this.did);\n      //   var didata = JSON.parse(dtdata1.find(item => item.id == value.data).channelCode);\n      //   let data1 = dtdata1.find(item => item.id == value.data)\n      //   // this.details = didata\n      //   this.bid = data1.bid\n      //   this.fid = data1.fid\n      //   // this.hlsurl\n      //   // this.bm = data1.note\n      //   console.log(data1, 1111111);\n      //   // this.getCameraData(did)\n      // }\n    };\n    this.fetchWarningStats();\n    // 每30秒更新一次数据\n    setInterval(() => {\n      this.fetchWarningStats();\n    }, 30000);\n  },\n  beforeCreate() { }, // 生命周期 - 创建之前\n  beforeMount() { }, // 生命周期 - 挂载之前\n  beforeUpdate() { }, // 生命周期 - 更新之前\n  updated() { }, // 生命周期 - 更新之后\n  beforeUnmount() {\n    // 在组件销毁之前清除定时器\n    console.log(1111);\n  },\n\n  unmounted() {\n    console.log(2222);\n  }, // 生命周期 - 销毁之前\n  destroyed() {\n    console.log(1221);\n  }, // 生命周期 - 销毁完成\n  activated() { }, // 如果页面有keep-alive缓存功能，这个函数会触发\n};\n</script>\n<style lang=\"less\" scoped>\n.table2 {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  z-index: 99999;\n}\n\n.return {\n  position: fixed;\n  right: 373px;\n  top: 100px;\n  height: 44px;\n  width: 46px;\n  // overflow: hidden;\n  transform: translate(720%);\n  transition: transform 0.5s ease-in-out;\n\n  z-index: 999;\n  cursor: pointer;\n  text-align: center;\n  line-height: 67px;\n  font-family: Source Han Sans SC;\n  font-weight: 400;\n  font-size: 11px;\n  color: #ffffff;\n  background: url(\"../assets/image/return.png\");\n  background-size: 100% 100%;\n}\n\n.center_container {\n  position: fixed;\n  right: 359px;\n  top: 352px;\n  height: 401px;\n  width: 70px;\n  // overflow: hidden;\n  transform: translate(470%);\n  transition: transform 0.5s ease-in-out;\n  z-index: 999;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  background: url(\"../assets/image/louceng.png\");\n  background-size: 100% 100%;\n\n  .content::-webkit-scrollbar {\n    width: 0px;\n    display: none;\n    /* 设置滚动条的宽度 */\n  }\n\n  /* 设置滚动条轨道的样式 */\n  .content::-webkit-scrollbar-track {\n    background-color: #f1f1f1;\n    /* 设置滚动条轨道的背景色 */\n  }\n\n  /* 设置滚动条滑块的样式 */\n  .content::-webkit-scrollbar-thumb {\n    background-color: #888;\n    /* 设置滚动条滑块的背景色 */\n  }\n\n  /* 鼠标悬停在滚动条上时的样式 */\n  .content::-webkit-scrollbar-thumb:hover {\n    background-color: #555;\n    /* 设置鼠标悬停时滚动条滑块的背景色 */\n  }\n\n  .content {\n    height: 330px;\n    /* 内容区的总高度，视实际内容而定 */\n    transition: transform 0.5s ease;\n    overflow-y: auto;\n    text-align: center;\n\n    /* 设置滚动条的样式 */\n\n    .item {\n      cursor: pointer;\n      width: 75px;\n      height: 25px;\n      font-family: YouSheBiaoTiHei;\n      font-weight: 400;\n      font-size: 14px;\n      color: #86a6b7;\n      line-height: 25px;\n      margin-top: 12px;\n    }\n\n    .itema {\n      background: url(\"../assets/image/lcactive.png\");\n      background-size: 100% 100%;\n      cursor: pointer;\n      width: 66px;\n      height: 25px;\n      font-family: YouSheBiaoTiHei;\n      font-weight: 400;\n      font-size: 14px;\n      color: #ffffff;\n      line-height: 25px;\n      margin-top: 12px;\n    }\n\n    .tooltip {\n      position: absolute;\n      left: 80%;\n      // top: 15px;\n      background-color: #1a3867;\n      border: 1px solid #7ba6eb;\n      color: #fff;\n      padding: 5px;\n      z-index: 1;\n      white-space: nowrap;\n      font-size: 12px;\n      visibility: hidden;\n\n      opacity: 0;\n      transition: opacity 0.5s, visibility 0.5s;\n      z-index: 999;\n      font-family: Source Han Sans SC;\n    }\n\n    .item:hover .tooltip {\n      visibility: visible;\n      /* 当鼠标悬停时显示 */\n      opacity: 1;\n    }\n\n    .itema:hover .tooltip {\n      visibility: visible;\n      /* 当鼠标悬停时显示 */\n      opacity: 1;\n    }\n  }\n}\n\n.btn {\n  margin-top: 13px;\n  width: 27px;\n  height: 14px;\n  cursor: pointer;\n}\n\n.echart2 {\n  height: 180px;\n}\n\n.bott {\n  position: fixed;\n  z-index: 1;\n  bottom: 4px;\n  // left: 6px;\n  width: 1920px;\n  height: 50px;\n  display: flex;\n  flex-direction: row;\n  cursor: pointer;\n  text-align: center;\n\n  .bottit {\n    width: 153px;\n    height: 45px;\n    background: url(\"../assets/image/bot_b.png\");\n    background-size: 100% 100%;\n    margin-left: 19.496px;\n    font-family: Source Han Sans SC;\n    font-weight: bold;\n    font-size: 17px;\n    color: #ffffff;\n    line-height: 43px;\n    cursor: pointer;\n  }\n\n  .bottit1 {\n    width: 153px;\n    height: 69px;\n    background: url(\"../assets/image/bot_a.png\");\n    background-size: 100% 100%;\n    margin-left: 19.496px;\n    font-family: Source Han Sans SC;\n    font-weight: bold;\n    font-size: 17px;\n    color: #ffffff;\n    line-height: 87px;\n    cursor: pointer;\n    margin-top: -23px;\n  }\n}\n\n.container {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: space-between;\n  align-items: stretch;\n  height: 1080px;\n  text-align: center;\n\n  .left-panel {\n    position: fixed;\n    z-index: 1;\n    top: 75px;\n    left: 22px;\n    width: 387px;\n    height: 937px;\n    background-size: 100% 100%;\n    transform: translate(-122%);\n    transition: transform 0.5s ease-in-out;\n\n    .box {\n      // margin-top: 6px;\n      margin-bottom: 18px;\n      // background: url(\"../assets/image/zuoshang1.png\");\n      background-size: 100% 100%;\n      background-repeat: no-repeat;\n\n      // width: 330px;\n      // height: 404px;\n\n      .loudong {\n        margin-top: 21px;\n        margin-bottom: 25px;\n        width: 296px;\n        height: 178px;\n      }\n\n      .wenzi {\n        font-family: Microsoft YaHei;\n        font-weight: 400;\n        font-size: 10px;\n        color: #bdecf9;\n        text-align: left;\n        margin-left: 20px;\n        margin-right: 20px;\n      }\n\n      .p {\n        text-indent: 2em;\n        margin-bottom: 1em;\n        letter-spacing: 0.05em;\n      }\n    }\n  }\n\n  .left-panel-active {\n    transform: translate(0%);\n  }\n\n  .left-panel-active1 {\n    // transform: translate(0%);\n    animation: slideOut 1s ease-in-out forwards;\n  }\n\n  @keyframes slideOut {\n    100% {\n      transform: translateX(0%);\n    }\n\n    // 85% {\n    //   transform: translateX(-25%);\n    // }\n\n    // 65% {\n    //   transform: translateX(-15%);\n    // }\n\n    // 40% {\n    //   transform: translateX(-55%);\n    // }\n\n    // 30% {\n    //   transform: translateX(-40%);\n    // }\n\n    0% {\n      transform: translateX(-100%);\n    }\n  }\n\n  .rtitle {\n    margin-top: 16px;\n  }\n\n  .ltitle1 {\n    margin-top: 16px;\n  }\n\n  .right-panel {\n    position: fixed;\n    z-index: 1;\n    right: 22px;\n    width: 387px;\n    top: 75px;\n    height: 937px;\n\n    background-size: 100% 100%;\n    transform: translate(122%);\n    transition: transform 0.5s ease-in-out;\n\n    .box {\n      // margin-top: 6px;\n      margin-bottom: 18px;\n      // background: url(\"../assets/image/zuoshang1.png\");\n      background-size: 100% 100%;\n      background-repeat: no-repeat;\n      font-family: Source Han Sans SC;\n      font-weight: 400;\n      font-size: 12px;\n      color: #ffffff;\n      width: 330px;\n      height: 224px;\n\n      .titlest {\n        display: flex;\n\n        // shiyansimg.png\n        .itm {\n          cursor: pointer;\n          margin: 16px 9px 0 10px;\n          background: url(\"../assets/image/shiyansimg.png\");\n          background-size: 100% 100%;\n          background-repeat: no-repeat;\n          width: 100px;\n          height: 32px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n        }\n\n        .itms {\n          background: url(\"../assets/image/xuanzexuanzhong.png\") !important;\n          background-size: 100% 100%;\n          background-repeat: no-repeat;\n          width: 100px;\n          height: 41px !important;\n          padding-bottom: 10px;\n        }\n      }\n\n      .contentss {\n        display: flex;\n        align-items: center;\n        flex-wrap: wrap;\n        justify-content: space-around;\n        align-items: center;\n\n        .itm {\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          width: 112px;\n          height: 70px;\n          background: url(\"../assets/image/wendupng.png\");\n          background-size: 100% 100%;\n          background-repeat: no-repeat;\n          font-family: DIN;\n          font-weight: bold;\n          font-size: 22px;\n          color: #ffffff;\n\n          .danwei {\n            font-family: DIN;\n            font-weight: bold;\n            font-size: 12px;\n            color: #ffffff;\n          }\n        }\n\n        .wendyu {\n          font-family: Source Han Sans SC;\n          font-weight: 400;\n          font-size: 13px;\n          color: #ffffff;\n          margin-top: -7px;\n        }\n      }\n\n      .loudong {\n        margin-top: 21px;\n        margin-bottom: 25px;\n        width: 296px;\n        height: 178px;\n      }\n    }\n\n    .boxxxs {\n      margin-left: -10px;\n      margin-top: 1px;\n      margin-bottom: 18px;\n      // background: url(\"../assets/image/zuoshang1.png\");\n      background-size: 100% 100%;\n      background-repeat: no-repeat;\n\n      width: 366px;\n      cursor: pointer;\n      // height: 254px;\n    }\n  }\n\n  .boxxx {\n    // margin-top: 6px;\n    margin-bottom: 18px;\n    // background: url(\"../assets/image/zuoshang1.png\");\n    background-size: 100% 100%;\n    background-repeat: no-repeat;\n\n    width: 350px;\n    height: 284px;\n  }\n\n  .no-animation {\n    transition: none;\n  }\n\n  .right-panel-active {\n    transform: translate(0%);\n    // animation: slideIn 1s ease-in-out ;\n  }\n\n  .right-panel-active1 {\n    // transform: translate(0%);\n    animation: slideIn 1s ease-in-out forwards;\n  }\n\n  .right-panel-active11 {\n    transform: translate(0%) !important;\n    // animation: slideIn 1s ease-in-out ;\n  }\n\n  .right-panel-active12 {\n    // transform: translate(0%);\n    animation: slideIn 1s ease-in-out forwards !important;\n  }\n\n  @keyframes slideIn {\n    0% {\n      transform: translateX(100%);\n    }\n\n    // 30% {\n    //   transform: translateX(65%);\n    // }\n\n    // 40% {\n    //   transform: translateX(40%);\n    // }\n\n    // 65% {\n    //   transform: translateX(15%);\n    // }\n\n    // 85% {\n    //   transform: translateX(25%);\n    // }\n\n    100% {\n      transform: translateX(0%);\n    }\n  }\n\n  .completed {\n    background: #7ad0ff;\n  }\n\n  .incomplete {\n    background: #ff6041;\n  }\n\n  .warning {\n    background: #00ffc0;\n  }\n\n  .completeds {\n    color: #7ad0ff;\n  }\n\n  .incompletes {\n    color: #ff6041;\n  }\n\n  .warnings {\n    color: #00ffc0;\n  }\n}\n\n.ql-center {\n  display: flex;\n  // margin-top: 20px;\n  justify-content: space-around;\n  margin-top: 4px;\n  margin-bottom: 4px;\n\n  .ql-Box {\n    width: 46%;\n    height: 49px;\n    border: 1px solid #7ad0ff;\n    // opacity: 0.6;\n    border-radius: 2px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n\n    .ql-box1 {\n      font-family: Alibaba PuHuiTi;\n      font-weight: bold;\n      font-size: 19px;\n      color: #7ad0ff;\n    }\n\n    .ql-box {\n      display: flex;\n      // padding-left: 23px;\n      padding-right: 9px;\n      flex-direction: row;\n      justify-content: space-between;\n      align-items: center;\n      // width: 100%;\n      height: 24px;\n\n      .left_ql {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        color: #ffffff;\n\n        .yuan {\n          width: 8px;\n          height: 8px;\n          border-radius: 50%;\n          background-color: #b93851;\n          margin-right: 5px;\n        }\n\n        .yuan1 {\n          width: 8px;\n          height: 8px;\n          border-radius: 50%;\n          background-color: #84edc3;\n          margin-right: 5px;\n        }\n\n        .pp {\n          margin-left: 5px;\n          color: #fff;\n          font-size: 18px;\n        }\n      }\n\n      img {\n        height: 12px;\n        width: 8px;\n      }\n    }\n  }\n}\n\n.warn1 {\n  // background: url(\"../assets/image/warnred.png\");\n}\n\n.warn2 {\n  // background: url(\"../assets/image/warnyellow.png\");\n}\n\n.warn3 {\n  // background: url(\"../assets/image/warngreen.png\");\n}\n\n.unfixed-warnings {\n  height: 180px;\n  overflow-y: auto;\n}\n\n.warning12 {\n  background-size: 100% 100%;\n  height: 47px;\n  margin-bottom: 8px;\n\n  .info {\n    display: flex;\n    justify-content: space-between;\n    align-items: flex-start;\n    text-align: left;\n    font-size: 13px;\n    padding: 8px 12px;\n    background: rgba(25, 37, 60, 0.1);\n    border-radius: 4px;\n\n    .zongduan {\n      display: flex;\n      align-items: center;\n      min-width: 80px;\n\n      .yuan {\n        width: 8px;\n        height: 8px;\n        border-radius: 50%;\n        margin-right: 6px;\n      }\n\n      .cjhulizhong {\n        font-family: Microsoft YaHei;\n        font-weight: bold;\n        font-size: 14px;\n      }\n    }\n\n    .info1 {\n      flex: 1;\n      // margin: 0 12px;\n\n      .time {\n        font-family: Microsoft YaHei;\n        font-size: 14px;\n        color: #ffffff;\n        margin-bottom: 4px;\n      }\n\n      .location {\n        font-family: Microsoft YaHei;\n        font-size: 14px;\n        color: #ffffff;\n        white-space: nowrap;\n        overflow: hidden;\n        text-overflow: ellipsis;\n      }\n    }\n\n    .info2 {\n      cursor: pointer;\n      font-size: 14px;\n      font-family: Microsoft YaHei;\n      font-weight: 400;\n      color: #b93851;\n      // white-space: nowrap;\n      margin-left: 10px;\n    }\n  }\n}\n\n.zonghe {\n  // margin-bottom: 10px;\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center;\n  justify-content: center;\n\n  .boxsty {\n    width: 50%;\n    margin-top: 12px;\n\n    .mianji {\n      display: flex;\n      align-items: center;\n\n      .img {\n        width: 50px;\n        height: 49px;\n      }\n\n      .wenzi {\n        text-align: left;\n        margin-left: 5px;\n\n        .top {\n          // margin-bottom: 9px;\n          font-family: Alibaba PuHuiTi;\n          font-weight: 500;\n          font-size: 20px;\n          color: #ffffff;\n        }\n\n        .bottom {\n          font-family: Source Han Sans SC;\n          font-weight: 500;\n          font-size: 21px;\n          color: #59ffc4;\n        }\n      }\n    }\n  }\n}\n\n.gongneng {\n  margin-top: 12px;\n\n  display: flex;\n  flex-direction: column;\n  // align-items: center;\n  // font-family: Source Han Sans SC;\n  font-family: Alibaba PuHuiTi;\n  // font-weight: bold;\n  font-size: 22px;\n  color: #59ffc4;\n  text-align: left;\n\n  .yuan {\n    margin-right: 7px;\n    width: 16px;\n    height: 16px;\n    border-radius: 50%;\n    background-color: #85fdca;\n  }\n\n  .value {\n    font-family: Alibaba PuHuiTi;\n    font-weight: 500;\n    font-size: 20px;\n    color: #fff;\n    width: 100%;\n    margin-right: 3px;\n    text-indent: 40px;\n  }\n\n  .name {\n    // width: 58px;\n    font-size: 22px;\n  }\n}\n\n.zongheqt {\n  .left1 {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    margin-right: 20px;\n    margin-top: 7px;\n\n    .mianji {\n      background: url(\"../assets/image/zengfangti.png\");\n      background-repeat: no-repeat;\n      background-size: 100% 100%;\n      width: 106px;\n      height: 58px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n    .img {\n      width: 50px;\n      height: 49px;\n    }\n\n    .wenzis {\n      .top {\n        font-family: Alibaba PuHuiTi;\n        font-weight: 400;\n        font-size: 18px;\n        color: #ffffff;\n      }\n\n      .bottom {\n        display: flex;\n        align-items: flex-end;\n        font-family: Alibaba PuHuiTi;\n        font-weight: 400;\n        font-size: 13px;\n        color: #fff;\n        margin-left: 7px;\n      }\n    }\n  }\n}\n\n.boxswq {\n  width: 365px;\n  height: 242px;\n}\n\n.huangxing {\n  width: 359px;\n  height: 238px;\n}\n\n.cjhulizhong {\n  font-family: Microsoft YaHei;\n  font-weight: bold;\n  font-size: 14px;\n  color: #64f8bb;\n  margin-left: 8px;\n}\n\n.yuan {\n  width: 10px;\n  height: 10px;\n  background-color: #518acd;\n  border-radius: 50%;\n}\n\n.zongduan {\n  display: flex;\n  align-items: center;\n  font-size: 13px;\n}\n\n.titleimgs {\n  display: flex;\n  align-items: center;\n  justify-content: space-around;\n  margin-right: 10px;\n\n  .bgu {\n    background-color: #95871cbf !important;\n\n    // background: url(\"../assets/image/titlessimg.png\");\n    background-repeat: no-repeat;\n    background-size: 100% 100%;\n    width: 171px;\n    height: 38px;\n    font-family: Alibaba PuHuiTi;\n    font-weight: 400;\n    font-size: 22px;\n    color: #ffffff;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 0 12px 0 14px;\n  }\n\n  .bgu1 {\n    background-color: rgb(28, 128, 149) !important;\n\n    // background: url(\"../assets/image/titlessimg.png\");\n    background-repeat: no-repeat;\n    background-size: 100% 100%;\n    width: 171px;\n    height: 38px;\n    font-family: Alibaba PuHuiTi;\n    font-weight: 400;\n    font-size: 22px;\n    color: #ffffff;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 0 12px 0 14px;\n  }\n}\n\n.titlesscontents {\n  overflow: auto;\n  height: 154px;\n}\n\n/* 设置滚动条的样式 */\n.titlesscontents::-webkit-scrollbar {\n  width: 5px;\n  /* 设置滚动条的宽度 */\n}\n\n/* 设置滚动条轨道的样式 */\n.titlesscontents::-webkit-scrollbar-track {\n  background-color: #454f5d;\n  /* 设置滚动条轨道的背景色 */\n}\n\n/* 设置滚动条的样式 */\n.unfixed-warnings::-webkit-scrollbar {\n  width: 5px;\n  /* 设置滚动条的宽度 */\n}\n\n/* 设置滚动条轨道的样式 */\n.unfixed-warnings::-webkit-scrollbar-track {\n  background-color: #454f5d;\n  /* 设置滚动条轨道的背景色 */\n}\n\n/* 设置滚动条滑块的样式 */\n.unfixed-warnings::-webkit-scrollbar-thumb {\n  background-color: #f1f1f1;\n  /* 设置滚动条滑块的背景色 */\n}\n\n.pagination-container {\n  margin-top: 20px;\n  display: flex;\n  justify-content: flex-end;\n  padding: 10px;\n}\n\n:deep(.el-pagination) {\n\n  .el-pagination__total,\n  .el-pagination__sizes,\n  .el-pagination__jump,\n  .btn-prev,\n  .btn-next,\n  .el-pager li {\n    background-color: transparent;\n    color: #fff;\n  }\n\n  .el-pagination__total,\n  .el-pagination__jump {\n    color: #fff;\n  }\n\n  .el-select .el-input .el-input__inner {\n    color: #fff;\n    background-color: transparent;\n  }\n\n  .el-pager li.active {\n    background-color: #409eff;\n    color: #fff;\n  }\n}\n\n.loading-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100%;\n  width: 100%;\n}\n\n:deep(.el-loading-spinner) {\n  .el-loading-text {\n    color: #fff;\n    margin: 3px 0;\n    font-size: 14px;\n  }\n\n  .circular {\n    .path {\n      stroke: #3ba1f4;\n    }\n  }\n}\n\n/* 设置滚动条滑块的样式 */\n.titlesscontents::-webkit-scrollbar-thumb {\n  background-color: #f1f1f1;\n  /* 设置滚动条滑块的背景色 */\n}\n\n.titless {\n  margin-right: 10px;\n  width: 96%;\n  background: rgba(25, 37, 60, 0.5);\n  height: 32px;\n  margin-top: 8px;\n  display: flex;\n  font-family: Alibaba PuHuiTi;\n  font-weight: 400;\n  font-size: 17px;\n  color: #40d7ff;\n  display: flex;\n  align-items: center;\n  justify-content: space-around;\n\n  .item {\n    width: 100%;\n    flex: 1.1;\n  }\n\n  .item1 {\n    width: 100%;\n    flex: 1.9;\n  }\n}\n\n.contents {\n  border-bottom: 1px solid #3b5471;\n  margin-right: 10px;\n  width: 96%;\n  background: rgba(45, 58, 79, 0.2);\n  height: 32px;\n  display: flex;\n  font-family: Alibaba PuHuiTi;\n  font-weight: 400;\n  font-size: 15px;\n  color: #fff;\n  display: flex;\n  align-items: center;\n\n  .item {\n    width: 100%;\n    flex: 1;\n  }\n\n  .item1 {\n    width: 100%;\n    flex: 1.9;\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    position: relative;\n    cursor: pointer;\n\n    &:hover::after {\n      content: attr(title);\n      position: absolute;\n      left: 0;\n      top: 100%;\n      background: rgba(0, 0, 0, 0.8);\n      color: #fff;\n      padding: 4px 8px;\n      border-radius: 4px;\n      z-index: 999;\n      white-space: normal;\n    }\n  }\n}\n\n.contents:nth-child(odd) {\n  background: rgba(46, 61, 83, 0.4);\n}\n\n.contents:nth-child(even) {\n  background: rgba(37, 50, 69, 0.2);\n}\n\n.popup-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.7);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 9999;\n}\n\n.popup-content {\n  background: rgba(25, 37, 60, 0.95);\n  border: 1px solid #3ba1f4;\n  border-radius: 8px;\n  width: 80%;\n  max-width: 1000px;\n  max-height: 80vh;\n  overflow-y: auto;\n  padding: 20px;\n}\n\n.popup-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid #3ba1f4;\n}\n\n.popup-title {\n  font-family: Alibaba PuHuiTi;\n  font-size: 24px;\n  color: #40d7ff;\n}\n\n.close-btn {\n  font-size: 28px;\n  color: #fff;\n  cursor: pointer;\n  padding: 0 10px;\n\n  &:hover {\n    color: #40d7ff;\n  }\n}\n\n.popup-table {\n  .table-header {\n    display: flex;\n    background: rgba(25, 37, 60, 0.8);\n    padding: 12px;\n    color: #40d7ff;\n    font-family: Alibaba PuHuiTi;\n    font-size: 20px;\n\n    .col-1 {\n      flex: 1;\n      padding: 0 2px;\n      text-align: center;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n\n    .col-2 {\n      flex: 2.3;\n      padding: 0 2px;\n      text-align: center;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n  }\n\n  .table-row {\n    display: flex;\n    padding: 12px;\n    font-size: 12px;\n    border-bottom: 1px solid rgba(59, 161, 244, 0.2);\n    color: #fff;\n    font-family: Alibaba PuHuiTi;\n\n    &:hover {\n      background: rgba(59, 161, 244, 0.1);\n    }\n\n    .col-1 {\n      flex: 1;\n      padding: 0 2px;\n      text-align: center;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n\n    .col-2 {\n      flex: 2.3;\n      padding: 0 2px;\n      text-align: center;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n  }\n}\n\n.pagination-container {\n  margin-top: 20px;\n  display: flex;\n  justify-content: flex-end;\n  padding: 10px;\n}\n\n:deep(.el-pagination) {\n\n  .el-pagination__total,\n  .el-pagination__sizes,\n  .el-pagination__jump {\n    color: #fff !important;\n  }\n\n  &.is-background {\n\n    .btn-prev,\n    .btn-next,\n    .el-pager li {\n      background-color: rgba(25, 37, 60, 0.8) !important;\n      color: #fff !important;\n      border: 1px solid #3ba1f4;\n      margin: 0 3px;\n\n      &:hover {\n        color: #409eff !important;\n        background-color: rgba(37, 50, 69, 0.4) !important;\n      }\n\n      &.is-active {\n        background-color: #409eff !important;\n        color: #fff !important;\n        border-color: #409eff;\n      }\n\n      &:disabled {\n        background-color: rgba(25, 37, 60, 0.4) !important;\n        color: #606266 !important;\n      }\n    }\n  }\n}\n\n.loading-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100%;\n  width: 100%;\n}\n\n:deep(.el-loading-spinner) {\n  .el-loading-text {\n    color: #fff;\n    margin: 3px 0;\n    font-size: 14px;\n  }\n\n  .circular {\n    .path {\n      stroke: #3ba1f4;\n    }\n  }\n}\n</style>\n"]}]}