{"remainingRequest": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\src\\views\\dayi\\zichan.vue?vue&type=template&id=79dbed4e&scoped=true", "dependencies": [{"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\src\\views\\dayi\\zichan.vue", "mtime": 1751448864722}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 499162500000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\src\\views\\dayi\\zichan.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MAC5E,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7E,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACjG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACjG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACjG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACnG,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtB,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;cAEnC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cAC/E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEvG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC;cACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChF,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjG,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACzF,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACR,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC7B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAChC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC7B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAChC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC;;;;UAIE,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "E:/svn/SH-20240918-0186-SFTianjinUniversityH5/src/src/views/dayi/zichan.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <keep-alive>\r\n    <div class=\"contents\" v-if=\"isshow\" v-loading=\"false\" element-loading-text=\"Loading...\"\r\n      :element-loading-spinner=\"svg\" element-loading-background=\"rgba(0, 0, 0, 1)\">\r\n      <div class=\"toubu\">\r\n        <div style=\"margin-left: 20px; display: flex; align-items: center\" v-if=\"false\">\r\n          <div style=\"display: flex; width: 100%; align-items: center\">\r\n            <span class=\"sp\">当前位置：</span>\r\n            <el-select class=\"el-select\" v-model=\"selectvalue1\" placeholder=\"selectvalue1\"\r\n              style=\"width: 64px; height: 35.1px\" @change=\"handleChange\">\r\n              <el-option v-for=\"item in options1\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n            </el-select>\r\n            <el-select class=\"el-select\" v-model=\"selectvalue2\" placeholder=\"selectvalue2\"\r\n              style=\"width: 64px; height: 35.1px\" @change=\"handleChange\">\r\n              <el-option v-for=\"item in options2\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n            </el-select>\r\n            <el-select class=\"el-select\" v-model=\"selectvalue3\" placeholder=\"selectvalue3\"\r\n              style=\"width: 78px; height: 35.1px\" @change=\"handleChange\">\r\n              <el-option v-for=\"item in options3\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n            </el-select>\r\n          </div>\r\n          <img v-if=\"isshow\" class=\"img1sss\" @click=\"anniu()\" src=\"../../assets/image/table-x.png\" alt=\"\" />\r\n        </div>\r\n\r\n        <div class=\"all\">\r\n          <div class=\"all1\">\r\n            <!-- <Titles class=\"ltitle1\" tit=\"仪器实时状态\">\r\n              <div class=\"dayi\">\r\n                <span>共</span>\r\n                <span>{{ sbnum }}</span>\r\n                <span>台仪器安装客户端</span>\r\n              </div>\r\n              <Electricity1 v-if=\"yiqiStatus\" class=\"zhuzhuangtu\" :chartData=\"yiqiStatus\"></Electricity1>         \r\n            </Titles> -->\r\n            <Titles class=\"ltitle11\" tit=\"人员分布统计\">\r\n\r\n              <!-- <zhuzhuangtu class=\"zhuzhuangtu\" :chartData=\"chartData1\"></zhuzhuangtu> -->\r\n              <huanxing style=\"margin-top: 150px;\" v-if=\"userDistribution\" :chartData=\"userDistribution\"></huanxing>\r\n\r\n            </Titles>\r\n          </div>\r\n          <div class=\"line1\"></div>\r\n          <div class=\"all2\">\r\n            <!-- <Titles class=\"ltitle1\" tit=\"仪器设备使用情况\">\r\n            <div class=\"shinei\">\r\n              <Electricity8></Electricity8>\r\n            </div>\r\n          </Titles>\r\n          <Titles class=\"ltitle\" tit=\"办公设备使用情况\">\r\n            <div class=\"shinei\">\r\n              <Electricity8></Electricity8>\r\n            </div>\r\n          </Titles> -->\r\n            <div>\r\n              <Titles class=\"ltitle1\" tit=\"课题测试统计\">\r\n                <div class=\"shinei\">\r\n                  <Electricity3 v-if=\"testStatistics\" :chartData=\"testStatistics\"></Electricity3>\r\n                </div>\r\n              </Titles>\r\n            </div>\r\n          </div>\r\n          <div class=\"all3\">\r\n\r\n            <Titles class=\"ltitle1\" tit=\"仪器使用排行\">\r\n              <div class=\"shinei\">\r\n                <!-- <Electricity6></Electricity6> -->\r\n                <zhuzhuangtu v-if=\"equipmentRank\" class=\"zhuzhuangtu1\" :chartData=\"equipmentRank\"></zhuzhuangtu>\r\n              </div>\r\n            </Titles>\r\n            <Titles class=\"ltitle1\" tit=\"课题组使用统计\">\r\n              <div class=\"shinei\">\r\n                <!-- <huanxing :chartData=\"chartData\"></huanxing> -->\r\n                <zhuzhuangtu1 v-if=\"topUsers\" class=\"zhuzhuangtu1\" :chartData=\"topUsers\"></zhuzhuangtu1>\r\n              </div>\r\n            </Titles>\r\n            <!-- <div class=\"shuantitle\">\r\n            <div style=\"width: 50%\">\r\n              <div class=\"title\">实时负载率</div>\r\n              <div class=\"nenghao\">实时负载率:</div>\r\n              <p class=\"nhp\">30%</p>\r\n            </div>\r\n            <div style=\"width: 50%\">\r\n              <div class=\"title\">实时总功率</div>\r\n              <div class=\"nenghao\">实时总功率:</div>\r\n              <p class=\"nhp\">200Kw</p>\r\n            </div>\r\n          </div>\r\n      -->\r\n\r\n\r\n\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </keep-alive>\r\n</template>\r\n\r\n<script>\r\nimport Electricity from \"@/components/echarts/dianbiao/biao1.vue\";\r\nimport biao1s from \"@/components/echarts/dianbiao/biao1s.vue\";\r\nimport biao1ss from \"@/components/echarts/dianbiao/biao1ss.vue\";\r\nimport Titles from \"@/components/common/Titles.vue\";\r\nimport Electricity1 from \"@/components/dayi/Electricity1.vue\";\r\nimport Electricity2 from \"@/components/echarts/dianbiao/Electricity2.vue\";\r\nimport Electricity3 from \"@/components/dayi//zhexiantu.vue\";\r\nimport Electricity4 from \"@/components/echarts/dianbiao/Electricity4.vue\";\r\nimport Electricity5 from \"@/components/echarts/dianbiao/Electricity5.vue\";\r\nimport Electricity6 from \"@/components/dayi/Electricity6.vue\";\r\nimport Electricity7 from \"@/components/echarts/dianbiao/Electricity7.vue\";\r\nimport Electricity8 from \"@/components/dayi/Electricity8.vue\";\r\nimport huanxing from \"@/components/dayi/xiaobingtu.vue\";\r\nimport zhuzhuangtu from \"@/components/dayi/zhuzhuangtu.vue\";\r\nimport zhuzhuangtu1 from \"@/components/dayi/zhuzhuangtu1.vue\";\r\nimport axios from \"axios\";\r\n// 使用环境变量设置基础 API 地址\r\nconst baseURL = process.env.VUE_APP_BASE_API || '/lims/api';\r\n\r\nconst api = axios.create({\r\n  baseURL\r\n});\r\nconst headers = {\r\n  clientid: '5a298e93-158d-4e22-83cf-6ceb62e9b4f1',\r\n  clientsecret: '2c8ec39e-9887-482a-b28b-e64c496b601c'\r\n};\r\nexport default {\r\n  components: {\r\n    Titles,\r\n    Electricity1,\r\n    Electricity,\r\n    Electricity2,\r\n    Electricity3,\r\n    Electricity4,\r\n    Electricity5,\r\n    Electricity6,\r\n    Electricity7,\r\n    Electricity8,\r\n    huanxing,\r\n    biao1s,\r\n    biao1ss,\r\n    zhuzhuangtu,\r\n    zhuzhuangtu1\r\n  },\r\n  data() {\r\n    return {\r\n      loading: true,\r\n      loading1: true,\r\n      loading2: true,\r\n      loading3: true,\r\n      loading4: true,\r\n      loading5: true,\r\n      //svg: 'el-icon-loading' ,// 或者自定义 SVG 图标\r\n      sbnum: 723,\r\n      chartDatazz: {\r\n        title: [\"已领用\", \"未领用\"],\r\n        xAxisdata1: [80, 80, 97, 53, 95, 70, 88],\r\n        xAxisdata2: [100, 100, 100, 100, 100, 100, 100],\r\n        yAxisdata: [\"一月\", \"二月\", \"三月\", \"四月\", \"五月\", \"六月\", \"七月\"],\r\n      },\r\n      chartDatazz1: {\r\n        title: [\"已领用\", \"未领用\"],\r\n        xAxisdata1: [80, 80, 97, 53, 95, 70, 88],\r\n        xAxisdata2: [100, 100, 100, 100, 100, 100, 100],\r\n        yAxisdata: [\"一月\", \"二月\", \"三月\", \"四月\", \"五月\", \"六月\", \"七月\"],\r\n      },\r\n      chartData: {\r\n        value: [1321, 18582, 651],\r\n        legend: [\r\n          \"校外人员\",\r\n          \"校内人员\",\r\n          \"管理员\",\r\n        ],\r\n      },\r\n      chartData1: {\r\n        title: [\"已领用\", \"未领用\"],\r\n        xAxisdata: [\"20日\", \"21日\", \"22日\", \"23日\", \"25日\", \"24日\",],\r\n        yAxisdata1: [4, 7, 5, 9, 6, 5],\r\n        yAxisdata2: [4, 7, 5, 9, 6, 5],\r\n      },\r\n      chartData2: [\r\n        {\r\n          name: \"正在使用\",\r\n          value: 271,\r\n        },\r\n        {\r\n          name: \"待机中\",\r\n          value: 452,\r\n        }],\r\n      chartData3: [\r\n        {\r\n          name: \"正在使用\",\r\n          value: 271,\r\n        },\r\n        {\r\n          name: \"待机中\",\r\n          value: 452,\r\n\r\n        },\r\n        {\r\n          name: \"故障\",\r\n          value: 21,\r\n\r\n        }],\r\n      isshow: true,\r\n      options: [\r\n        {\r\n          value: \"总览\",\r\n          label: \"总览\",\r\n        },\r\n        {\r\n          value: \"能耗分析\",\r\n          label: \"能耗分析\",\r\n        },\r\n        {\r\n          value: \"能流分析\",\r\n          label: \"能流分析\",\r\n        },\r\n        {\r\n          value: \"设备状态\",\r\n          label: \"设备状态\",\r\n        },\r\n        {\r\n          value: \"一键抄表\",\r\n          label: \"一键抄表\",\r\n        },\r\n        {\r\n          value: \"费用管理\",\r\n          label: \"费用管理\",\r\n        },\r\n        {\r\n          value: \"碳排放管理\",\r\n          label: \"碳排放管理\",\r\n        },\r\n      ],\r\n      selectvalue2: \"B3\",\r\n      options2: [\r\n        {\r\n          value: \"B3\",\r\n          label: \"B3\",\r\n        },\r\n      ],\r\n      selectvalue3: \"B3\",\r\n      options3: [\r\n        {\r\n          value: \"B3\",\r\n          label: \"B3\",\r\n        },\r\n      ],\r\n      selectvalue1: \"B3\",\r\n      options1: [\r\n        {\r\n          value: \"B3\",\r\n          label: \"B3\",\r\n        },\r\n      ],\r\n      selectvalue3: \"B1栋\",\r\n      options4: [\r\n        {\r\n          value: \"B1栋\",\r\n          label: \"B1栋\",\r\n        },\r\n        {\r\n          value: \"B2栋\",\r\n          label: \"B2栋\",\r\n        },\r\n        {\r\n          value: \"B3栋\",\r\n          label: \"B3栋\",\r\n        },\r\n        {\r\n          value: \"B4栋\",\r\n          label: \"B4栋\",\r\n        },\r\n        {\r\n          value: \"W1栋\",\r\n          label: \"W1栋\",\r\n        },\r\n        {\r\n          value: \"W2栋\",\r\n          label: \"W2栋\",\r\n        },\r\n      ],\r\n      selectvalue4: \"B1栋\",\r\n      optionData: [\r\n        {\r\n          name: \"一级告警\",\r\n          value: 16,\r\n          itemStyle: { color: \"#EB6877\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"二级告警\",\r\n          value: 27,\r\n          itemStyle: { color: \"#F8B551\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"三级告警\",\r\n          value: 17,\r\n          itemStyle: { color: \"#B954E8\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"四级告警\",\r\n          value: 40,\r\n          itemStyle: { color: \"#0284F0\", opacity: 0.9 },\r\n        },\r\n      ],\r\n      optionData1: [\r\n        {\r\n          name: \"一级告警\",\r\n          value: 16,\r\n          itemStyle: { color: \"#EB6877\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"二级告警\",\r\n          value: 27,\r\n          itemStyle: { color: \"#F8B551\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"三级告警\",\r\n          value: 17,\r\n          itemStyle: { color: \"#B954E8\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"四级告警\",\r\n          value: 40,\r\n          itemStyle: { color: \"#0284F0\", opacity: 0.9 },\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  computed: {\r\n    loading() {\r\n      return this.loading1 || this.loading2 || this.loading3 || this.loading4 || this.loading5;\r\n    },\r\n    // 使用 Vuex 的 getters 获取持久化的数据\r\n    equipmentRank() {\r\n      return this.$store.getters[\"equipment/equipmentRank\"];\r\n    },\r\n    yiqiStatus() {\r\n      return this.$store.getters[\"equipment/yiqiStatus\"];\r\n    },\r\n    userDistribution() {\r\n      return this.$store.getters[\"equipment/userDistribution\"];\r\n    },\r\n    testStatistics() {\r\n      return this.$store.getters[\"equipment/testStatistics\"];\r\n    },\r\n    topUsers() {\r\n      return this.$store.getters[\"equipment/topUsers\"];\r\n    },\r\n  },\r\n  mounted() {\r\n\r\n    this.$store.dispatch('equipment/fetchEquipmentRank');\r\n    this.$store.dispatch('equipment/getdata2');\r\n    this.$store.dispatch('equipment/getdata3');\r\n    this.$store.dispatch('equipment/getdata4');\r\n    this.$store.dispatch('equipment/getdata5');\r\n\r\n    if (!this.equipmentRank.length) {\r\n      this.$store.dispatch('equipment/fetchEquipmentRank'); // 如果没有缓存，获取数据\r\n    }\r\n    if (!this.yiqiStatus.length) {\r\n      this.$store.dispatch('equipment/getdata2'); // 如果没有缓存，获取数据\r\n    }\r\n    if (!this.userDistribution.length) {\r\n      this.$store.dispatch('equipment/getdata3'); // 如果没有缓存，获取数据\r\n    }\r\n    if (!this.testStatistics.length) {\r\n      this.$store.dispatch('equipment/getdata4'); // 如果没有缓存，获取数据\r\n    }\r\n    if (!this.topUsers.length) {\r\n      this.$store.dispatch('equipment/getdata5'); // 如果没有缓存，获取数据\r\n    }\r\n    setInterval(() => {\r\n      this.$store.dispatch('equipment/fetchEquipmentRank');\r\n      this.$store.dispatch('equipment/getdata2');\r\n      this.$store.dispatch('equipment/getdata3');\r\n      this.$store.dispatch('equipment/getdata4');\r\n      this.$store.dispatch('equipment/getdata5');\r\n    }, 36000000);\r\n    // this.getdata1()\r\n    this.getdata2()\r\n    // this.getdata3()\r\n    // this.getdata4()\r\n    // this.getdata5()\r\n    // setInterval(() => {\r\n    //   this.getdata1()\r\n    //   this.getdata2()\r\n    //   this.getdata3()\r\n    //   this.getdata4()\r\n    //   this.getdata5()\r\n    // }, 10000);\r\n\r\n  },\r\n\r\n  methods: {\r\n\r\n    async getdata1() {  //仪器使用排行\r\n      try {\r\n        const response = await api.post('', {\r\n\r\n          \"method\": \"equipment/time_rank\",\r\n          \"params\": {\r\n            \"num\": 10,\r\n            \"start\": 1704038400,\r\n            \"end\": 1735660800\r\n          }\r\n\r\n        }, { headers });\r\n        // 检查是否成功拿到 token\r\n        if (response.data) {\r\n          console.log('仪器使用排行:', response.data);\r\n          let data = response.data.response\r\n          // 提取 names 和 times\r\n          const names = data.map(item => item.name).reverse();\r\n          const times = data.map(item => item.time).reverse();\r\n          this.chartDatazz1.yAxisdata = names\r\n          this.chartDatazz1.xAxisdata1 = times\r\n          this.loading1 = false\r\n        }\r\n      } catch (error) {\r\n        console.error('登录失败:', error);\r\n      }\r\n    },\r\n\r\n    async getdata2() {  //仪器使用情况\r\n      try {\r\n        const response = await api.post('', {\r\n          \"method\": \"equipment/getSummaryInfo\",\r\n          \"params\": {}\r\n        }, { headers });\r\n        // 检查是否成功拿到 token\r\n        if (response.data) {\r\n          this.sbnum = response.data.response.controlCount,\r\n            console.log('仪器使用情况:', response.data.response);\r\n\r\n\r\n        }\r\n\r\n      } catch (error) {\r\n        console.error('登录失败:', error);\r\n      }\r\n    },\r\n    async getdata3() {  //人员分布情况\r\n      try {\r\n        const response = await api.post('', {\r\n          \"method\": \"summarize/userStatus\",\r\n          \"params\": {}\r\n        }, { headers });\r\n        // 检查是否成功拿到 token\r\n        if (response.data) {\r\n          console.log('人员分布情况:', response.data);\r\n          this.chartData = {\r\n            value: [response.data.response.outer, response.data.response.inner, response.data.response.incharge],\r\n            legend: [\r\n              \"校外人员\",\r\n              \"校内人员\",\r\n              \"管理员\",\r\n            ],\r\n          }\r\n          this.loading3 = false\r\n        }\r\n      } catch (error) {\r\n        console.error('登录失败:', error);\r\n      }\r\n    },\r\n    async getdata4() {  //课题测试情况\r\n\r\n      try {\r\n        const response = await api.post('', {\r\n          \"method\": \"summarize/labStatus\",\r\n          \"params\": {}\r\n        }, { headers });\r\n        // 检查是否成功拿到 token\r\n        if (response) {\r\n          console.log('课题测试情况:', response.data.response);\r\n          this.chartData3 = [\r\n            {\r\n              name: \"总课题数\",\r\n              value: response.data.response.project,\r\n            },\r\n            {\r\n              name: \"课题数\",\r\n              value: response.data.response.lab,\r\n\r\n            },\r\n            {\r\n              name: \"测试数\",\r\n              value: response.data.response.test,\r\n\r\n            }]\r\n          this.loading4 = false\r\n        }\r\n      } catch (error) {\r\n        console.error('登录失败:', error);\r\n      }\r\n    },\r\n    async getdata5() {  //用户排行\r\n      try {\r\n        const response = await api.post('', {\r\n          \"method\": \"eq_reserv/getTopUsers\",\r\n          \"params\": {\r\n            \"num\": 9,\r\n            \"year\": 2024\r\n          }\r\n        }, { headers });\r\n        // 检查是否成功拿到 token\r\n        if (response.data) {\r\n          console.log('用户排行:', response.data.response);\r\n          let data = response.data.response\r\n          // 提取 names 和 times\r\n          const names = data.map(item => item.name).reverse();\r\n          const times = data.map(item => item.time).reverse();\r\n          this.chartDatazz.yAxisdata = names\r\n          this.chartDatazz.xAxisdata1 = times\r\n        }\r\n        this.loading5 = false\r\n      } catch (error) {\r\n        console.error('登录失败:', error);\r\n      }\r\n    },\r\n    anniu() {\r\n      this.isshow = false;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.zhuzhuangtu {\r\n  margin-top: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.zhuzhuangtu1 {\r\n  margin-top: -26px;\r\n\r\n}\r\n\r\n.all {\r\n  display: flex;\r\n  flex-direction: row;\r\n  margin-top: 5px;\r\n\r\n  .zong {\r\n    display: flex;\r\n    flex-direction: row;\r\n    margin-top: 10px;\r\n\r\n    .echart1,\r\n    .echart2 {\r\n      flex: 1;\r\n\r\n      .center {\r\n        margin-top: -24px;\r\n        font-family: \"Source Han Sans SC\", sans-serif;\r\n        font-weight: 400;\r\n        font-size: 17px;\r\n        color: #00ffb6;\r\n        text-align: center;\r\n        margin-bottom: 10px;\r\n      }\r\n\r\n      .btn {\r\n        width: 133px;\r\n        height: 31px;\r\n        border: 1px solid #2d6cb0;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        font-family: \"Source Han Sans SC\", sans-serif;\r\n        font-weight: bold;\r\n        font-size: 15px;\r\n        color: #ffffff;\r\n        border-radius: 30px;\r\n        margin-left: 7%;\r\n      }\r\n    }\r\n  }\r\n\r\n  .ltitle1 {\r\n    margin-top: 10px;\r\n  }\r\n\r\n  .ltitle11 {\r\n    margin-top: 30px;\r\n    position: relative;\r\n  }\r\n\r\n  .dayi {\r\n    position: absolute;\r\n    top: 42px;\r\n    left: 68px;\r\n    z-index: 20;\r\n    font-size: 22px;\r\n    color: #fff;\r\n    text-align: center;\r\n\r\n    span:nth-child(2) {\r\n      font-size: 32px;\r\n    }\r\n  }\r\n\r\n  .line1 {\r\n    width: 2px;\r\n    height: 823px;\r\n    opacity: 0.64;\r\n    background-color: #204964;\r\n  }\r\n\r\n  .all1 {\r\n    flex: 462;\r\n\r\n    .nenghao {\r\n      width: 227px;\r\n      height: 173px;\r\n      background: url(\"../../assets/image/nenghao.png\");\r\n      background-size: 100% 100%;\r\n      margin-left: 120px;\r\n      margin-top: 21px;\r\n      text-align: center;\r\n      font-family: Alibaba PuHuiTi;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #ffffff;\r\n      line-height: 213px;\r\n    }\r\n\r\n    .nhp {\r\n      text-align: center;\r\n      font-family: Alibaba PuHuiTi;\r\n      font-weight: 500;\r\n      font-size: 30px;\r\n      color: #2cc1ff;\r\n      margin-top: 8px;\r\n    }\r\n\r\n    .nh {\r\n      margin-left: 24px;\r\n      margin-top: 5px;\r\n      width: 423px;\r\n      height: 93px;\r\n      border: 1px solid #364d5a;\r\n      background-size: 100% 100%;\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      padding-left: 42px;\r\n\r\n      .nhimg {\r\n        width: 96.6px;\r\n        height: 70px;\r\n      }\r\n\r\n      .nhtit {\r\n        width: 148px;\r\n        margin-left: 10px;\r\n        margin-top: 10px;\r\n\r\n        .p11 {\r\n          font-family: PangMenZhengDao;\r\n          font-weight: 400;\r\n          font-size: 30px;\r\n          color: #7acfff;\r\n        }\r\n\r\n        .p12 {\r\n          font-family: PangMenZhengDao;\r\n          font-weight: 400;\r\n          font-size: 30px;\r\n          color: #ffa170;\r\n        }\r\n\r\n        .p2 {\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 400;\r\n          font-size: 17px;\r\n          color: #ffffff;\r\n        }\r\n      }\r\n\r\n      .nhtit1 {\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: center;\r\n        margin-left: 35px;\r\n\r\n        .nhimg1 {\r\n          width: 16px;\r\n          height: 20px;\r\n        }\r\n\r\n        .pp1 {\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 400;\r\n          font-size: 17px;\r\n          color: #0df29b;\r\n        }\r\n\r\n        .pp2 {\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 400;\r\n          font-size: 17px;\r\n          color: #ffa170;\r\n        }\r\n      }\r\n\r\n      .nht {\r\n        margin-top: 10px;\r\n        display: flex;\r\n        flex-direction: column;\r\n\r\n        .pp {\r\n          margin-left: 35px;\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 400;\r\n          font-size: 17px;\r\n\r\n          color: #cccccc;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .all2 {\r\n    margin-left: 38px;\r\n    flex: 667;\r\n    display: flex;\r\n    flex-direction: column;\r\n\r\n    .shinei {\r\n      .itemshei {\r\n        display: flex;\r\n        justify-content: space-around;\r\n\r\n        .nenghaos {\r\n          width: 227px;\r\n          height: 173px;\r\n          background: url(\"../../assets/image/nenghao.png\");\r\n          background-size: 100% 100%;\r\n          text-align: center;\r\n          margin-left: 10px;\r\n          margin-top: 23px;\r\n          text-align: center;\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 400;\r\n          font-size: 14px;\r\n          color: #ffffff;\r\n          line-height: 144px;\r\n        }\r\n\r\n        .nhps {\r\n          text-align: center;\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 500;\r\n          font-size: 21px;\r\n          color: #2cc1ff;\r\n          margin-top: 8px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .all3 {\r\n    flex: 668;\r\n    margin-left: 45px;\r\n  }\r\n}\r\n\r\n.shinei {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.shuantitle {\r\n  width: 100%;\r\n  display: flex;\r\n  margin-top: 10px;\r\n\r\n  .title {\r\n    width: 95%;\r\n    background: url(\"../../assets/image/title.png\");\r\n    background-size: 100% 100%;\r\n\r\n    height: 25px;\r\n    font-family: Source Han Sans SC;\r\n    font-weight: 400;\r\n    font-size: 25px;\r\n    color: #ffffff;\r\n    text-shadow: 2px 2px 3px rgba(0, 0, 0, 0.47);\r\n    font-style: italic;\r\n    text-align: left;\r\n    line-height: 4px;\r\n    padding-left: 33px;\r\n  }\r\n}\r\n\r\n.nenghao {\r\n  width: 167px;\r\n  height: 113px;\r\n  background: url(\"../../assets/image/nenghao.png\");\r\n  background-size: 100% 100%;\r\n  text-align: center;\r\n  margin-left: 83px;\r\n  margin-top: 63px;\r\n  text-align: center;\r\n  font-family: Alibaba PuHuiTi;\r\n  font-weight: 400;\r\n  font-size: 14px;\r\n  color: #ffffff;\r\n  line-height: 144px;\r\n}\r\n\r\n.nhp {\r\n  text-align: center;\r\n  font-family: Alibaba PuHuiTi;\r\n  font-weight: 500;\r\n  font-size: 21px;\r\n  color: #2cc1ff;\r\n  margin-top: 8px;\r\n}\r\n\r\n.contents {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  background: url(\"../../assets/image/zichanbeijin.png\");\r\n  background-size: 100% 100%;\r\n  width: 1863px;\r\n  height: 868px;\r\n  z-index: 99999;\r\n  padding-left: 34px;\r\n  padding-right: 22px;\r\n  padding-top: 21px;\r\n}\r\n\r\n.toubu {\r\n  width: 100%;\r\n\r\n  position: relative;\r\n}\r\n\r\n.el-select {\r\n  margin-top: -1px;\r\n  margin-left: 10px;\r\n  background: #00203d;\r\n  border-radius: 3px;\r\n  border: 1px solid #3e89db;\r\n\r\n  /deep/.el-select__wrapper {\r\n    background: #00203d !important;\r\n    box-shadow: none;\r\n  }\r\n\r\n  /deep/.el-select__wrapper .is-hovering:not {\r\n    box-shadow: none;\r\n  }\r\n\r\n  /deep/.el-select__wrapper:hover {\r\n    box-shadow: none;\r\n  }\r\n\r\n  /deep/.el-select__placeholder.is-transparent {\r\n    color: #2cc1ff;\r\n  }\r\n\r\n  /deep/.el-select__placeholder {\r\n    color: #2cc1ff;\r\n  }\r\n\r\n  /deep/.el-select-dropdown__item.is-hovering {\r\n    background-color: #2cc1ff !important;\r\n  }\r\n}\r\n\r\n.sp {\r\n  margin-top: -5px;\r\n  margin-left: 12px;\r\n  font-family: Alibaba PuHuiTi;\r\n  font-weight: bold;\r\n  font-size: 21px;\r\n  color: #2cc1ff;\r\n}\r\n\r\n.img1sss {\r\n  cursor: pointer;\r\n  width: 15px;\r\n  height: 15px;\r\n}\r\n</style>"]}]}