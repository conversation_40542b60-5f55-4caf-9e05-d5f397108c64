{"remainingRequest": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\views\\floor.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\views\\floor.vue", "mtime": 1751448014712}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQovLyDov5nph4zlj6/ku6Xlr7zlhaXlhbbku5bmlofku7bvvIjmr5TlpoLvvJrnu4Tku7bvvIzlt6Xlhbdqc++8jOesrOS4ieaWueaPkuS7tmpz77yManNvbuaWh+S7tu+8jOWbvueJh+aWh+S7tuetieetie+8iQ0KLy8g5L6L5aaC77yaaW1wb3J0IOOAiue7hOS7tuWQjeensOOAiyBmcm9tICfjgIrnu4Tku7bot6/lvoTjgIsnOw0KaW1wb3J0IHsgbWFwQWN0aW9ucywgbWFwR2V0dGVycyB9IGZyb20gInZ1ZXgiOw0KaW1wb3J0IGNvbXBvbmVudDAgZnJvbSAiQC92aWV3cy9kYXlpL3ppY2hhbi52dWUiOw0KaW1wb3J0IGh1YW54aW5nIGZyb20gIkAvY29tcG9uZW50cy9lY2hhcnRzL2h1YW54aW5nLnZ1ZSI7DQppbXBvcnQgemhleGlhbiBmcm9tICJAL2NvbXBvbmVudHMvZWNoYXJ0cy96aGV4aWFuLnZ1ZSI7DQppbXBvcnQgemhleGlhbjEgZnJvbSAiQC9jb21wb25lbnRzL2VjaGFydHMvemhleGlhbjEudnVlIjsNCmltcG9ydCBTeXN0ZW1EZXRlIGZyb20gIkAvY29tcG9uZW50cy9lY2hhcnRzL1N5c3RlbURldGUudnVlIjsNCmltcG9ydCBlY2hhcnRzMSBmcm9tICJAL2NvbXBvbmVudHMvZWNoYXJ0cy9iaW5namlmYW5nL2VjaGFydHMzLnZ1ZSI7DQppbXBvcnQgdGVkYWkgZnJvbSAiQC9jb21wb25lbnRzL2NvbW1vbi9jbF9kZXRhaWxzLnZ1ZSI7DQppbXBvcnQgYmlhb0dlIGZyb20gIkAvY29tcG9uZW50cy9jb21tb24vYmlhb0dlcy52dWUiOw0KaW1wb3J0IHNodWFuZ3hpYW5nIGZyb20gIkAvY29tcG9uZW50cy9lY2hhcnRzL3NodWFuZ3hpYW5nLnZ1ZSI7DQppbXBvcnQgYXhpb3MgZnJvbSAiYXhpb3MiOw0KLy8gaW1wb3J0IGRldGFpbHMgZnJvbSAiQC9jb21wb25lbnRzL2NvbW1vbi9kZXRhaWxzLnZ1ZSI7DQovLyDkvb/nlKjnjq/looPlj5jph4/orr7nva7ln7rnoYAgQVBJIOWcsOWdgA0KY29uc3QgYmFzZVVSTCA9IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgfHwgIi9saW1zL2FwaSI7DQoNCmNvbnN0IGFwaSA9IGF4aW9zLmNyZWF0ZSh7DQogIGJhc2VVUkwsDQp9KTsNCmV4cG9ydCBkZWZhdWx0IHsNCiAgLy8gaW1wb3J05byV5YWl55qE57uE5Lu26ZyA6KaB5rOo5YWl5Yiw5a+56LGh5Lit5omN6IO95L2/55SoDQogIGNvbXBvbmVudHM6IHsNCiAgICB0ZWRhaSwNCiAgICBodWFueGluZywNCiAgICB6aGV4aWFuLA0KICAgIHpoZXhpYW4xLA0KICAgIFN5c3RlbURldGUsDQogICAgZWNoYXJ0czEsDQogICAgc2h1YW5neGlhbmcsDQogICAgYmlhb0dlLA0KICAgIGNvbXBvbmVudDAsDQogIH0sDQogIHByb3BzOiBbInRhYmxlZGF0YSIsICJ6ZW5ndGlpbWciXSwNCg0KICBkYXRhKCkgew0KICAgIC8vIOi/memHjOWtmOaUvuaVsOaNrg0KICAgIHJldHVybiB7DQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgY3VycmVudFBhZ2UxOiA1LA0KICAgICAgaXNzaG93d2hhdDogZmFsc2UsDQogICAgICBpc3Nob3dzc3M6IGZhbHNlLA0KICAgICAgdGl0YWN0aXZlOiAwLA0KICAgICAgdG90YWw6IG51bGwsDQogICAgICBjaGFuZ2VUaXRsZTogWyLmlbDmja7liJfooagiLCAi5pWw5o2u57uf6K6hIl0sDQogICAgICBhY3RpdmVTdWJtZW51OiBudWxsLCAvLyDlvZPliY3mv4DmtLvnmoTlrZDoj5zljZUNCiAgICAgIGFjdGl2ZUNvbnRlbnQ6IG51bGwsIC8vIOW9k+WJjeaYvuekuueahOWGheWuuQ0KICAgICAgbmV3QXJyOiBbXSwNCiAgICAgIGlzc2hvdzogZmFsc2UsDQogICAgICB4eHh4OiBmYWxzZSwNCiAgICAgIGNncWxpc3Q6IFtdLA0KICAgICAgbGlzdHRhYmxlOiBbXSwNCiAgICAgIG9wdGlvbnM6IFsNCiAgICAgICAgew0KICAgICAgICAgIHZhbHVlOiAiemhpbmFuIiwNCiAgICAgICAgICBsYWJlbDogIuaMh+WNlyIsDQogICAgICAgICAgY2hpbGRyZW46IFsNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgdmFsdWU6ICJzaGVqaXl1YW56ZSIsDQogICAgICAgICAgICAgIGxhYmVsOiAi6K6+6K6h5Y6f5YiZIiwNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIHZhbHVlOiAiZGFvaGFuZyIsDQogICAgICAgICAgICAgIGxhYmVsOiAi5a+86IiqIiwNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgXSwNCiAgICAgICAgfSwNCiAgICAgIF0sDQogICAgICBkZXZpY2VkYXRhOiBbXSwNCiAgICAgIGlucHV0OiAiIiwNCiAgICAgIGFjdGl2ZVRhYjogInRvZGF5IiwNCiAgICAgIGxpc3RzdDogWw0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogIuW5v+S4nOi0qOajgOS4reivmuiupOivgeaciemZkOWFrOWPuOWIsOS4reW5vy4uLiIsDQogICAgICAgIH0sDQogICAgICAgIHsgbmFtZTogIuadkOaWmeenkeWtpuOAgeWMluWtpuW3peeoi+WPiuWMu+iNr+eglOWPkeaIkC4uLiIgfSwNCiAgICAgICAgeyBuYW1lOiAi6YWS57K+5Luj6LCi6IO95Yqb5qOA5rWL77ya5oKo55qE6YWS6YeP77yM5Z+6Li4uIiB9LA0KICAgICAgICB7IG5hbWU6ICLmpI3phbjmo4DmtYvmlrnms5Xlj4rkvZznlKgiIH0sDQogICAgICAgIHsgbmFtZTogIuWFlOaLieajgOa1i++8muihjOS4muS4reiKr+eJh+mAoOWBh+aKgOacry4uLiIgfSwNCiAgICAgICAgeyBuYW1lOiAi6YWS57K+5Luj6LCi6IO95Yqb5qOA5rWL77ya5oKo55qE6YWS6YeP77yM5Z+6Li4uIiB9LA0KICAgICAgXSwNCiAgICAgIHNob3dkaDogdHJ1ZSwNCiAgICAgIHNob3dkaDE6IGZhbHNlLA0KICAgICAgbm9BbmltYXRpb246IGZhbHNlLA0KICAgICAgc2VsZWN0ZWRJdGVtOiB7DQogICAgICAgIG5hbWU6ICLnprvlrZDmuoXlsITku6oiLCAvL+S6p+WTgeWQjeensA0KICAgICAgICBpbWd1cmw6ICJodHRwczovLzNkLmRkZHRhc2suY24vZW5naW5uZXIteHVmZW5nL2ltYWdlL3RpYW5qaW4vbGl6aS5wbmciLA0KICAgICAgICBsb2NhdGlvbjogIuWMl+a0i+WbreagoeWMujU05qW8LCBFMTA1IiwgLy/kvY3nva4NCiAgICAgICAgc3RhdHVzOiAi5bey6aKG55SoIiwgLy/ku6rlmajnirbmgIEgIOWPr+mihueUqA0KICAgICAgICBkZXRhaWxzOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgbmFtZTogIuS6p+WTgeWQjeensCIsDQogICAgICAgICAgICB2YWx1ZTogIuemu+WtkOa6heWwhOS7qiIsDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBuYW1lOiAi5Y6f5YC8IiwNCiAgICAgICAgICAgIHZhbHVlOiAiLS0iLA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgbmFtZTogIui0reWFpeaXtumXtCIsDQogICAgICAgICAgICB2YWx1ZTogIjIwMjAvMDkvMDIiLA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgbmFtZTogIuWTgeeJjCIsDQogICAgICAgICAgICB2YWx1ZTogIi0tIiwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIG5hbWU6ICLnlJ/kuqfljoLlrrYiLA0KICAgICAgICAgICAgdmFsdWU6ICItLSIsDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBuYW1lOiAi5L6b5bqU5ZWG5ZCN56ewIiwNCiAgICAgICAgICAgIHZhbHVlOiAiLS0iLA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgbmFtZTogIuS+m+W6lOWVhuiBlOezu+S/oeaBryIsDQogICAgICAgICAgICB2YWx1ZTogIi0tIiwNCiAgICAgICAgICB9LA0KICAgICAgICBdLA0KICAgICAgICBtYWludGVuYW5jZV9yZWNvcmRzOiB7DQogICAgICAgICAgbWFpbnRlbmFuY2VfY29udGVudDogIuagoeWHhuWSjOezu+e7n+WNh+e6pyIsIC8v57u05oqk5YaF5a65DQogICAgICAgICAgZGF0ZTogIjIwMjQtMDEtMTAiLCAvL+e7tOaKpOaXtumXtA0KICAgICAgICAgIG5leHRfbWFpbnRlbmFuY2VfZGF0ZTogIjIwMjItMDEtMTAiLCAvL+S4i+asoee7tOaKpOaXtumXtA0KICAgICAgICB9LA0KICAgICAgICBtYW5hZ2VtZW50X25hbWU6ICLnjovlt6UiLA0KICAgICAgICBtYW5hZ2VtZW50X2NvbnRhY3RfaW5mbzogIjE1Njk4NTY3NTQyIiwNCiAgICAgIH0sDQogICAgICB0YWJsZURhdGFJdGVtOiBbXSwNCiAgICAgIGpsVVJMLA0KICAgICAgVGl0bGU6ICLotYTkuqfnrqHnkIYiLA0KICAgICAgdGFibGVUaXRsZTogWw0KICAgICAgICB7IGtleTogIualvOWxgiIgfSwNCiAgICAgICAgeyBrZXk6ICLorr7lpIfnvJblj7ciIH0sDQogICAgICAgIHsga2V5OiAi6K6+5aSH5ZCN56ewIiB9LA0KICAgICAgICB7IGtleTogIuaIv+mXtOWPtyIgfSwNCiAgICAgICAgeyBrZXk6ICLmqKHlnosiIH0sDQogICAgICAgIHsga2V5OiAi6K6+5aSH54q25oCBIiB9LA0KICAgICAgICB7IGtleTogIueKtuaAgeivtOaYjiIgfSwNCiAgICAgIF0sDQogICAgICBpZHM6IG51bGwsDQogICAgICBuaGxpc3Q6IFsNCiAgICAgICAgew0KICAgICAgICAgIHRpdGxlOiAi5L6b5rCU5Y6L5YqbIiwNCiAgICAgICAgICBzdGF0dXM6ICIwLjNNcGEiLA0KICAgICAgICAgIHVuaXQ6ICLihIMiLA0KICAgICAgICB9LA0KDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogIuS+m+awlOa1gemHjyIsDQogICAgICAgICAgc3RhdHVzOiAiNk0zL21pbiIsDQogICAgICAgICAgdW5pdDogIuOOoSIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogIumcsueCuea4qeW6piIsDQogICAgICAgICAgc3RhdHVzOiAiNuKEgyIsDQogICAgICAgICAgdW5pdDogIuKEgyIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogIuWQq+awp+mHjyIsDQogICAgICAgICAgc3RhdHVzOiAiNlBQTSIsDQogICAgICAgICAgdW5pdDogIuOOoSIsDQogICAgICAgIH0sDQogICAgICBdLA0KICAgICAgd2Fybmxpc3QxOiBbDQogICAgICAgIHsNCiAgICAgICAgICB0eXBlOiAxLA0KICAgICAgICAgIG5hbWU6ICLmo4DmtYvliLDng5/pm77vvIzlj6/og73mnInnnYDngavngb7nmoTlj5HnlJ8uLi4iLA0KICAgICAgICAgIHZhbHVlOiAiIiwNCiAgICAgICAgICB0aW1lOiAiMDk6MTM6NTkgIDIwMjMtMDYtMDciLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdHlwZTogMiwNCiAgICAgICAgICBuYW1lOiAi5qOA5rWL5Yiw54Of6Zu+77yM5Y+v6IO96aKE56S6552A54Gr54G+55qE5Y+R55SfLi4iLA0KICAgICAgICAgIHZhbHVlOiAiIiwNCiAgICAgICAgICB0aW1lOiAiMTA6MTM6MTggIDIwMjMtMDYtMTIiLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdHlwZTogMywNCiAgICAgICAgICBuYW1lOiAi5a6e6aqM5a6k5YaF5qOA5rWL5Yiw5ryP5rC077yM5Y+v6IO95p2l6Ieq5Ya35Yed5rC0Li4iLA0KICAgICAgICAgIHZhbHVlOiAiIiwNCiAgICAgICAgICB0aW1lOiAiMTA6MTM6MTggIDIwMjMtMDYtMTIiLA0KICAgICAgICB9LA0KICAgICAgXSwNCiAgICAgIGlzQnV0dG9uMkFjdGl2ZTogZmFsc2UsDQogICAgICBzdGF0dXM6ICLlt6Hmo4DkuK0iLA0KICAgICAgc3RhdHVzMTogIuW3suWujOaIkCIsDQogICAgICBzdGF0dXMyOiAi5b6F5beh5qOAIiwNCiAgICAgIHNlbGVjdGVkSW5kZXg6IDAsDQogICAgICBjb21wb25lbnRUYWc6ICJjb21wb25lbnQwIiwNCiAgICAgIHRva2VuOiAiIiwNCiAgICB9Ow0KICB9LA0KICAvLyDorqHnrpflsZ7mgKfnsbvkvLzkuo5kYXRh5qaC5b+1DQogIGNvbXB1dGVkOiB7DQogICAgLi4ubWFwR2V0dGVycygiZXF1aXBtZW50IiwgWyJlcXVpcG1lbnRUYWdzIl0pLA0KICB9LA0KICAvLyDnm5HmjqdkYXRh5Lit55qE5pWw5o2u5Y+Y5YyWDQogIHdhdGNoOiB7fSwNCiAgLy8g5pa55rOV6ZuG5ZCIDQogIG1ldGhvZHM6IHsNCiAgICBoYW5kbGVTaXplQ2hhbmdlKHZhbCkgew0KICAgICAgY29uc29sZS5sb2coYOavj+mhtSAke3ZhbH0g5p2hYCk7DQogICAgICB0aGlzLmdldHlpcWlkZXRhaWxzKHRoaXMudG9rZW4sIHZhbCAtIDEpOw0KICAgIH0sDQogICAgaGFuZGxlQ3VycmVudENoYW5nZSh2YWwpIHsNCiAgICAgIGNvbnNvbGUubG9nKGDlvZPliY3pobU6ICR7dmFsfWApOw0KICAgICAgdGhpcy5nZXR5aXFpZGV0YWlscyh0aGlzLnRva2VuLCB2YWwgLSAxKTsNCiAgICB9LA0KICAgIGFzeW5jIGdldHRva2VuKGlkKSB7DQogICAgICB0cnkgew0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaS5wb3N0KCIiLCB7DQogICAgICAgICAgbWV0aG9kOiAiZXF1aXBtZW50L3NlYXJjaEVxdWlwbWVudHMiLA0KICAgICAgICAgIHBhcmFtczogew0KICAgICAgICAgICAgY3JpdGVyaWE6IHsNCiAgICAgICAgICAgICAgY2F0OiBpZCwNCiAgICAgICAgICAgICAgbG9jYXRpb246JzU45qW8QUNF5Yy6JywNCiAgICAgICAgICAgICAgLy8gImdyb3VwIjogMSwNCiAgICAgICAgICAgICAgLy8gInNlYXJjaHRleHQiOiAi5pCc57Si5YaF5a65Ig0KICAgICAgICAgICAgfSwNCiAgICAgICAgICB9LA0KICAgICAgICB9KTsNCg0KICAgICAgICAvLyDmo4Dmn6XmmK/lkKbmiJDlip/mi7/liLAgdG9rZW4NCiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEgJiYgcmVzcG9uc2UuZGF0YS5yZXNwb25zZS50b2tlbikgew0KICAgICAgICAgIGNvbnN0IHRva2VuID0gcmVzcG9uc2UuZGF0YS5yZXNwb25zZS50b2tlbjsNCiAgICAgICAgICB0aGlzLnRva2VuID0gdG9rZW47DQogICAgICAgICAgLy8g5bCGIHRva2VuIOWtmOWFpSBsb2NhbFN0b3JhZ2UNCiAgICAgICAgICAvLyBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnYXV0aFRva2VuJywgdG9rZW4pOw0KICAgICAgICAgIGNvbnNvbGUubG9nKCI1MzU6IiwgcmVzcG9uc2UuZGF0YS5yZXNwb25zZSk7DQogICAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLmRhdGEucmVzcG9uc2UudG90YWw7DQogICAgICAgICAgdGhpcy5oYW5kbGVTaXplQ2hhbmdlKDEpOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIueZu+W9leaIkOWKn+S9huacqui/lOWbniB0b2tlbjoiLCByZXNwb25zZS5kYXRhLnJlc3BvbnNlKTsNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcigi55m75b2V5aSx6LSlOiIsIGVycm9yKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGdldHlpcWlkZXRhaWxzKHRva2VuLCBzdGFydCkgew0KICAgICAgY29uc3QgaGVhZGVycyA9IHsNCiAgICAgICAgY2xpZW50aWQ6ICI1YTI5OGU5My0xNThkLTRlMjItODNjZi02Y2ViNjJlOWI0ZjEiLA0KICAgICAgICBjbGllbnRzZWNyZXQ6ICIyYzhlYzM5ZS05ODg3LTQ4MmEtYjI4Yi1lNjRjNDk2YjYwMWMiLA0KICAgICAgfTsNCiAgICAgIGNvbnN0IGJvZHkgPSB7DQogICAgICAgIG1ldGhvZDogImVxdWlwbWVudC9nZXRFcXVpcG1lbnRzIiwNCiAgICAgICAgcGFyYW1zOiB7DQogICAgICAgICAgdG9rZW46IHRva2VuLA0KICAgICAgICAgIHN0YXJ0OiBzdGFydCA/IHN0YXJ0ICogMTYgOiAwLA0KICAgICAgICAgIG51bTogMTYsDQogICAgICAgIH0sDQogICAgICB9Ow0KICAgICAgYXhpb3MNCiAgICAgICAgLnBvc3QoamxVUkwsIGJvZHksIHt9KQ0KICAgICAgICAudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICBjb25zb2xlLmxvZyhyZXNwb25zZS5kYXRhLCA1MzUpOw0KICAgICAgICAgIHRoaXMuZGV2aWNlZGF0YSA9IHJlc3BvbnNlLmRhdGEucmVzcG9uc2U7DQogICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoZXJyb3IpID0+IHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCJFcnJvcjoiLCBlcnJvcik7DQogICAgICAgIH0pOw0KICAgIH0sICANCiAgICAuLi5tYXBBY3Rpb25zKCJlcXVpcG1lbnQiLCBbImZldGNoRXF1aXBtZW50VGFncyJdKSwNCiAgICBjaGFuZ2V0aXQoaW5kZXgpIHsNCiAgICAgIHRoaXMudGl0YWN0aXZlID0gaW5kZXg7DQogICAgICB0aGlzLmlzc2hvd3doYXQgPSBpbmRleDsNCiAgICAgIGlmIChpbmRleCA9PSAxKSB7DQogICAgICAgIHRoaXMuc2hvd2RoID0gZmFsc2U7DQogICAgICAgIHRoaXMuc2hvd2RoMSA9IHRydWU7DQogICAgICAgIHRoaXMubm9BbmltYXRpb24gPSB0cnVlOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5zaG93ZGggPSB0cnVlOw0KICAgICAgICB0aGlzLnNob3dkaDEgPSBmYWxzZTsNCiAgICAgICAgdGhpcy5ub0FuaW1hdGlvbiA9IGZhbHNlOw0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlQ2FzY2FkZXJDaGFuZ2UodmFsdWUpIHsNCiAgICAgIGNvbnNvbGUubG9nKCLpgInkuK3nmoTlgLw6IiwgdmFsdWVbMV0pOw0KICAgICAgdGhpcy5nZXR0b2tlbih2YWx1ZVsxXSk7DQogICAgfSwNCiAgICBoaWRlZGV0YWlsc3NzKCkgew0KICAgICAgdGhpcy5pc3Nob3dzc3MgPSBmYWxzZTsNCiAgICB9LA0KICAgIG9wZW5kaWFsb2cocGF5bG9hZCkgew0KICAgICAgaWYgKHBheWxvYWQgPT0gMSkgew0KICAgICAgICB0aGlzLmlzc2hvdyA9IHRydWU7DQoNCiAgICAgICAgdGhpcy5kYXRhLmZvckVhY2goKGl0ZW0pID0+IHsNCiAgICAgICAgICBpZiAoaXRlbS5jYXRlZ29yeSA9PSAi55S15a2Q5pi+5b6u6ZWcIikgew0KICAgICAgICAgICAgdGhpcy5pc3Nob3cgPSB0cnVlOw0KICAgICAgICAgICAgdGhpcy50YWJsZURhdGFJdGVtID0gaXRlbS5pdGVtczsNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKHRoaXMudGFibGVEYXRhSXRlbSk7DQogICAgICAgICAgfQ0KICAgICAgICB9KTsNCiAgICAgIH0NCg0KICAgICAgLy8g5Zyo6L+Z6YeM5aSE55CG5LqL5Lu2DQogICAgfSwNCiAgICB0b2dnbGVTdWJNZW51KGl0ZW0pIHsNCiAgICAgIHRoaXMuaXNzaG93c3NzID0gdHJ1ZTsNCiAgICAgIGNvbnNvbGUubG9nKGl0ZW0sICLpgInkuK3nmoTorr7lpIfkv6Hmga8iKTsNCiAgICAgIHRoaXMuc2VsZWN0ZWRJdGVtID0gew0KICAgICAgICBpZDogaXRlbS5pZCwNCiAgICAgICAgbmFtZTogaXRlbS5uYW1lLA0KICAgICAgICBpbWd1cmw6IGl0ZW0uaWNvbnJlYWxfdXJsLA0KICAgICAgICBsb2NhdGlvbjogaXRlbS5sb2NhdGlvbiArIGl0ZW0ubG9jYXRpb24yLCAvL+S9jee9rg0KICAgICAgICBzdGF0dXM6ICFpdGVtLmlzX3VzaW5nID8gIuW9k+WJjeS9v+eUqCIgOiAi5Y+v5L2/55SoIiwgLy/ku6rlmajnirbmgIEgIOWPr+mihueUqA0KICAgICAgICBkZXRhaWxzOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgbmFtZTogIuS6p+WTgeWQjeensCIsDQogICAgICAgICAgICB2YWx1ZTogaXRlbS5uYW1lLA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgbmFtZTogIuS7t+agvCIsDQogICAgICAgICAgICB2YWx1ZTogaXRlbS5wcmljZSwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIG5hbWU6ICLotK3lhaXml7bpl7QiLA0KICAgICAgICAgICAgdmFsdWU6IGAke25ldyBEYXRlKGl0ZW0ucHVyY2hhc2VkX2RhdGUgKiAxMDAwKS5nZXRGdWxsWWVhcigpfS8kew0KICAgICAgICAgICAgICBuZXcgRGF0ZShpdGVtLnB1cmNoYXNlZF9kYXRlICogMTAwMCkuZ2V0TW9udGgoKSArIDENCiAgICAgICAgICAgIH0vJHtuZXcgRGF0ZShpdGVtLnB1cmNoYXNlZF9kYXRlICogMTAwMCkuZ2V0RGF0ZSgpfWAsDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBuYW1lOiAi5Yi26YCg5Zu95a62IiwNCiAgICAgICAgICAgIHZhbHVlOiBpdGVtLm1hbnVfYXQsDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBuYW1lOiAi55Sf5Lqn5Y6C5a62IiwNCiAgICAgICAgICAgIHZhbHVlOiBpdGVtLm1hbnVmYWN0dXJlciwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIG5hbWU6ICLotJ/otKPkuroiLA0KICAgICAgICAgICAgdmFsdWU6IGl0ZW0uY29udGFjdCwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIG5hbWU6ICLogZTns7vnlLXor50iLA0KICAgICAgICAgICAgdmFsdWU6IGl0ZW0ucGhvbmUsDQogICAgICAgICAgfSwNCiAgICAgICAgXSwNCiAgICAgICAgbWFpbnRlbmFuY2VfcmVjb3Jkczogew0KICAgICAgICAgIG1haW50ZW5hbmNlX2NvbnRlbnQ6ICLmoKHlh4blkozns7vnu5/ljYfnuqciLCAvL+e7tOaKpOWGheWuuQ0KICAgICAgICAgIGRhdGU6ICIyMDI0LTAxLTEwIiwgLy/nu7TmiqTml7bpl7QNCiAgICAgICAgICBuZXh0X21haW50ZW5hbmNlX2RhdGU6ICIyMDIyLTAxLTEwIiwgLy/kuIvmrKHnu7TmiqTml7bpl7QNCiAgICAgICAgfSwNCiAgICAgICAgbWFuYWdlbWVudF9uYW1lOiAi546L5belIiwNCiAgICAgICAgbWFuYWdlbWVudF9jb250YWN0X2luZm86ICIxNTY5ODU2NzU0MiIsDQogICAgICB9Ow0KICAgIH0sDQogICAgLy8gLy8g6K6+572u5YaF5a65DQogICAgLy8gc2V0Q29udGVudChjb250ZW50KSB7DQogICAgLy8gICB0aGlzLmlzc2hvd3NzcyA9IHRydWU7DQogICAgLy8gICB0aGlzLnNlbGVjdGVkSXRlbSA9IGNvbnRlbnQ7DQogICAgLy8gICBjb25zb2xlLmxvZyh0aGlzLnNlbGVjdGVkSXRlbSk7DQoNCiAgICAvLyAgIHRoaXMuYWN0aXZlQ29udGVudCA9IGNvbnRlbnQ7DQogICAgLy8gfSwNCiAgICBzaG93ZGV0YWlscyhpdGVtKSB7DQogICAgICAvLyBpdGVtLml0ZW1zLmZvckVhY2goKGl0ZW0pID0+IHsNCiAgICAgIC8vICAgdGhpcy5uZXdBcnIucHVzaCh7IG5hbWU6IGl0ZW0ubmFtZSB9KTsNCiAgICAgIC8vIH0pOw0KICAgICAgLy8gY29uc29sZS5sb2codGhpcy5uZXdBcnIpOw0KDQogICAgICB0aGlzLmlzc2hvdyA9IHRydWU7DQogICAgICB0aGlzLnRhYmxlRGF0YUl0ZW0gPSBpdGVtLml0ZW1zOw0KICAgIH0sDQogICAgaGlkZWRldGFpbHMoKSB7DQogICAgICB0aGlzLmlzc2hvdyA9IGZhbHNlOw0KICAgIH0sDQoNCiAgICBvYyh2YWx1ZSkgew0KICAgICAgY29uc29sZS5sb2codmFsdWUsICLmlLbliLDnmoTlgLwiKTsNCiAgICAgIHRoaXMuc2hvd2RoID0gdmFsdWU7DQogICAgfSwNCiAgICB4dWFuemVkaWFsb2codmFsdWUpIHsNCiAgICAgIGNvbnN0IG9wdGlvbk1hcHBpbmcgPSB7DQogICAgICAgIOmAiemhuTE6IDAsDQogICAgICAgIOmAiemhuTI6IDEsDQogICAgICAgIOmAiemhuTM6IDIsDQogICAgICAgIOmAiemhuTQ6IDMsDQogICAgICAgIOmAiemhuTU6IDQsDQogICAgICAgIOmAiemhuTY6IDUsDQogICAgICAgIOmAiemhuTc6IDYsDQogICAgICAgIOmAiemhuTg6IDcsDQogICAgICAgIOmAiemhuTk6IDgsDQogICAgICAgIOmAiemhuTEwOiA5LA0KICAgICAgICDpgInpobkxMTogMTAsDQogICAgICAgIOmAiemhuTEyOiAxMSwNCiAgICAgICAg6YCJ6aG5MTM6IDEyLA0KICAgICAgfTsNCg0KICAgICAgY29uc3QgaW5kZXggPSBvcHRpb25NYXBwaW5nW3ZhbHVlXTsNCiAgICAgIGlmIChpbmRleCAhPT0gdW5kZWZpbmVkKSB7DQogICAgICAgIHRoaXMudGFibGVEYXRhSXRlbSA9IHRoaXMuZGF0YVtpbmRleF0uaXRlbXM7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBjb25zb2xlLmVycm9yKCLml6DmlYjnmoTpgInpobk6ICIsIHZhbHVlKTsNCiAgICAgIH0NCiAgICB9LA0KICB9LA0KICAvLyDnlJ/lkb3lkajmnJ8gLSDliJvlu7rlrozmiJDvvIjlj6/ku6Xorr/pl67lvZPliY10aGlz5a6e5L6L77yJDQogIGNyZWF0ZWQoKSB7fSwNCiAgLy8g55Sf5ZG95ZGo5pyfIC0g5oyC6L295a6M5oiQ77yI5Y+v5Lul6K6/6ZeuRE9N5YWD57Sg77yJDQogIG1vdW50ZWQoKSB7DQogICAgdGhpcy5nZXR0b2tlbigiIik7DQogICAgdGhpcy5zaG93ZGgxID0gdHJ1ZTsNCiAgICB0aGlzLmZldGNoRXF1aXBtZW50VGFncygpOw0KICAgIC8vIHNldFRpbWVvdXQoKCkgPT4gew0KICAgIC8vICAgdGhpcy5zaG93ZGgxID0gZmFsc2U7DQogICAgLy8gICB0aGlzLm5vQW5pbWF0aW9uID0gZmFsc2U7DQogICAgLy8gfSwgMTAwMCk7IC8vIOWKqOeUu+aMgee7reaXtumXtOS4ujHnp5INCiAgICBjb25zb2xlLmxvZygxMjIyKTsNCiAgfSwNCiAgYmVmb3JlQ3JlYXRlKCkge30sIC8vIOeUn+WRveWRqOacnyAtIOWIm+W7uuS5i+WJjQ0KICBiZWZvcmVNb3VudCgpIHt9LCAvLyDnlJ/lkb3lkajmnJ8gLSDmjILovb3kuYvliY0NCiAgYmVmb3JlVXBkYXRlKCkge30sIC8vIOeUn+WRveWRqOacnyAtIOabtOaWsOS5i+WJjQ0KICB1cGRhdGVkKCkge30sIC8vIOeUn+WRveWRqOacnyAtIOabtOaWsOS5i+WQjg0KICBiZWZvcmVVbm1vdW50KCkgew0KICAgIC8vIOWcqOe7hOS7tumUgOavgeS5i+WJjea4hemZpOWumuaXtuWZqA0KICAgIGNvbnNvbGUubG9nKDExMTEpOw0KICB9LA0KDQogIHVubW91bnRlZCgpIHsNCiAgICBjb25zb2xlLmxvZygyMjIyKTsNCiAgfSwgLy8g55Sf5ZG95ZGo5pyfIC0g6ZSA5q+B5LmL5YmNDQogIGRlc3Ryb3llZCgpIHsNCiAgICBjb25zb2xlLmxvZygxMjIxKTsNCiAgfSwgLy8g55Sf5ZG95ZGo5pyfIC0g6ZSA5q+B5a6M5oiQDQogIGFjdGl2YXRlZCgpIHt9LCAvLyDlpoLmnpzpobXpnaLmnIlrZWVwLWFsaXZl57yT5a2Y5Yqf6IO977yM6L+Z5Liq5Ye95pWw5Lya6Kem5Y+RDQp9Ow0K"}, {"version": 3, "sources": ["E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\views\\floor.vue"], "names": [], "mappings": ";AA+HA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;AACnB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE3D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACT,CAAC,CAAC;AACF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACR;cACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC;YACD;cACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACb,CAAC;UACH,CAAC;QACH,CAAC;MACH,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC;QACD,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC9B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC9B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACrB,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC7B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAChC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACP;YACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC;UACD;YACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACb,CAAC;UACD;YACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC;UACD;YACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACb,CAAC;UACD;YACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACb,CAAC;UACD;YACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACb,CAAC;UACD;YACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACb,CAAC;QACH,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACV,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QACb,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACf,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACf,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACd,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QACb,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACf,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACjB,CAAC;MACD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACX,CAAC;;QAED;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACX,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACX,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACX,CAAC;MACH,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC;MACH,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACX,CAAC;EACH,CAAC;EACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/C,CAAC;EACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACT,CAAC,EAAE,CAAC,CAAC,CAAC;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAC1C,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAC1C,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACR,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACb,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC;UACH,CAAC;QACH,CAAC,CAAC;;QAEF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACjD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D;MACF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC3B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtD,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;UAC7B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACT,CAAC;MACH,CAAC;MACD,CAAC,CAAC,CAAC,CAAC;QACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC;IACN,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACzB,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;QAElB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAC1B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC;QACF,CAAC,CAAC;MACJ;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QAClB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACP;YACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC;UACD;YACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC;UACD;YACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC7D,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;YACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtD,CAAC;UACD;YACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC;UACD;YACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC;UACD;YACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC;UACD;YACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC;QACH,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC,CAAC;IACH,CAAC;IACD,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACT,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACvB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEnC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC,EAAE,CAAC;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MAChC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAC1C,CAAC,EAAE,CAAC,CAAC;MACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACpB,CAAC,CAAC,CAAC,EAAE,CAAC;QACN,CAAC,CAAC,CAAC,EAAE,CAAC;QACN,CAAC,CAAC,CAAC,EAAE,CAAC;QACN,CAAC,CAAC,CAAC,EAAE,CAAC;QACN,CAAC,CAAC,CAAC,EAAE,CAAC;QACN,CAAC,CAAC,CAAC,EAAE,CAAC;QACN,CAAC,CAAC,CAAC,EAAE,CAAC;QACN,CAAC,CAAC,CAAC,EAAE,CAAC;QACN,CAAC,CAAC,CAAC,EAAE,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACV,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7C,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjC;IACF,CAAC;EACH,CAAC;EACD,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;IACpB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;EAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;EAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;EAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;EAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/C,CAAC", "file": "E:/svn/***********-0186-SFTianjinUniversityH5/src/src/views/floor.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div>\r\n    <component\r\n      :is=\"componentTag\"\r\n      @fatherMethoddd=\"fatherMethoddd\"\r\n      v-if=\"isshowwhat\"\r\n    ></component>\r\n    <div class=\"botbtn\">\r\n      <div\r\n        v-for=\"(item, index) in changeTitle\"\r\n        :key=\"index\"\r\n        :class=\"titactive == index ? 'btt1' : 'btt'\"\r\n        @click=\"changetit(index)\"\r\n      >\r\n        {{ item }}\r\n      </div>\r\n    </div>\r\n    <tedai\r\n      :ids=\"ids\"\r\n      :selectedItem=\"selectedItem\"\r\n      class=\"sbdetails\"\r\n      :zengtiimg=\"zengtiimg\"\r\n      v-if=\"isshowsss\"\r\n      @hidedetails=\"hidedetailsss\"\r\n    ></tedai>\r\n    <biaoGe\r\n      :Title=\"Title\"\r\n      @xuanze-dialog=\"xuanzedialog\"\r\n      v-if=\"isshow\"\r\n      @hidedetails=\"hidedetails\"\r\n      :tableTitle=\"tableTitle\"\r\n      :tableDataItem=\"devicedata\"\r\n    ></biaoGe>\r\n    <div class=\"container\" v-if=\"!isshowwhat\">\r\n      <div\r\n        class=\"left-panel\"\r\n        :class=\"{\r\n          'left-panel-active': showdh,\r\n          'no-animation': noAnimation,\r\n          'left-panel-active1': showdh1,\r\n        }\"\r\n      >\r\n        <Title2 @open-dialog=\"opendialog\" class=\"ltitle1\" tit=\"大仪管理\">\r\n          <el-cascader\r\n            class=\"sect\"\r\n            placeholder=\"请选择类别\"\r\n            :options=\"equipmentTags\"\r\n            :show-all-levels=\"true\"\r\n            @change=\"handleCascaderChange\"\r\n          ></el-cascader>\r\n\r\n          <el-input\r\n            class=\"el-input\"\r\n            v-model=\"input\"\r\n            placeholder=\"请输入关键字\"\r\n          ></el-input>\r\n          <img class=\"suosuo\" src=\"../assets/image/suosuo.png\" alt=\"\" />\r\n          <div class=\"box\">\r\n            <!-- <div class=\"xiaobox\">\r\n              <img class=\"siqiu\" src=\"../assets/image/shixinqiu.png\" alt=\"\" />\r\n              <div class=\"shuru\">全部设备</div>\r\n            </div> -->\r\n            <div\r\n              class=\"menu-container\"\r\n              v-loading=\"loading\"\r\n              element-loading-text=\"拼命加载中\"\r\n              element-loading-background=\"rgba(0, 0, 0, 0)\"\r\n            >\r\n              <!-- 动态生成菜单 -->\r\n              <div class=\"menu\">\r\n                <div\r\n                  v-for=\"(menu, index) in devicedata\"\r\n                  :key=\"index\"\r\n                  class=\"menu-group\"\r\n                >\r\n                  <div class=\"qiuqiu\">\r\n                    <img\r\n                      class=\"siqiu\"\r\n                      src=\"../assets/image/shixinqiu.png\"\r\n                      alt=\"\"\r\n                    />\r\n                    <div class=\"menu-item\" @click=\"toggleSubMenu(menu)\">\r\n                      {{ menu.name }}\r\n                    </div>\r\n                    <!-- <div class=\"listtypes\" @click=\"showdetails(menu)\">详情</div> -->\r\n                  </div>\r\n\r\n                  <!-- <div v-show=\"activeSubmenu === menu.id\" class=\"submenu\">\r\n                    <div v-for=\"(item, subIndex) in menu.items\" :key=\"subIndex\" class=\"submenu-item\"\r\n                      @click=\"setContent(item)\">\r\n                      <p class=\"ellipsis\" :title=\"item.name\">{{ item.name }}</p>\r\n                    </div>\r\n                  </div> -->\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <el-pagination\r\n            @size-change=\"handleSizeChange\"\r\n            hide-on-single-page=\"true\"\r\n            @current-change=\"handleCurrentChange\"\r\n            :page-size=\"16\"\r\n            :pager-count=\"4\"\r\n            layout=\"prev, pager, next,total\"\r\n            :total=\"total\"\r\n          >\r\n          </el-pagination>\r\n          <!-- shebei12.png -->\r\n        </Title2>\r\n      </div>\r\n      <!-- shebeibgc.png -->\r\n      <!-- 右侧内容 -->\r\n      <!-- 右侧内容 -->\r\n\r\n      <div\r\n        class=\"right-panel\"\r\n        :class=\"{\r\n          'right-panel-active': showdh,\r\n          'no-animation': noAnimation,\r\n          'right-panel-active1': showdh1,\r\n        }\"\r\n      ></div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）\r\n// 例如：import 《组件名称》 from '《组件路径》';\r\nimport { mapActions, mapGetters } from \"vuex\";\r\nimport component0 from \"@/views/dayi/zichan.vue\";\r\nimport huanxing from \"@/components/echarts/huanxing.vue\";\r\nimport zhexian from \"@/components/echarts/zhexian.vue\";\r\nimport zhexian1 from \"@/components/echarts/zhexian1.vue\";\r\nimport SystemDete from \"@/components/echarts/SystemDete.vue\";\r\nimport echarts1 from \"@/components/echarts/bingjifang/echarts3.vue\";\r\nimport tedai from \"@/components/common/cl_details.vue\";\r\nimport biaoGe from \"@/components/common/biaoGes.vue\";\r\nimport shuangxiang from \"@/components/echarts/shuangxiang.vue\";\r\nimport axios from \"axios\";\r\n// import details from \"@/components/common/details.vue\";\r\n// 使用环境变量设置基础 API 地址\r\nconst baseURL = process.env.VUE_APP_BASE_API || \"/lims/api\";\r\n\r\nconst api = axios.create({\r\n  baseURL,\r\n});\r\nexport default {\r\n  // import引入的组件需要注入到对象中才能使用\r\n  components: {\r\n    tedai,\r\n    huanxing,\r\n    zhexian,\r\n    zhexian1,\r\n    SystemDete,\r\n    echarts1,\r\n    shuangxiang,\r\n    biaoGe,\r\n    component0,\r\n  },\r\n  props: [\"tabledata\", \"zengtiimg\"],\r\n\r\n  data() {\r\n    // 这里存放数据\r\n    return {\r\n      loading: true,\r\n      currentPage1: 5,\r\n      isshowwhat: false,\r\n      isshowsss: false,\r\n      titactive: 0,\r\n      total: null,\r\n      changeTitle: [\"数据列表\", \"数据统计\"],\r\n      activeSubmenu: null, // 当前激活的子菜单\r\n      activeContent: null, // 当前显示的内容\r\n      newArr: [],\r\n      isshow: false,\r\n      xxxx: false,\r\n      cgqlist: [],\r\n      listtable: [],\r\n      options: [\r\n        {\r\n          value: \"zhinan\",\r\n          label: \"指南\",\r\n          children: [\r\n            {\r\n              value: \"shejiyuanze\",\r\n              label: \"设计原则\",\r\n            },\r\n            {\r\n              value: \"daohang\",\r\n              label: \"导航\",\r\n            },\r\n          ],\r\n        },\r\n      ],\r\n      devicedata: [],\r\n      input: \"\",\r\n      activeTab: \"today\",\r\n      listst: [\r\n        {\r\n          name: \"广东质检中诚认证有限公司到中广...\",\r\n        },\r\n        { name: \"材料科学、化学工程及医药研发成...\" },\r\n        { name: \"酒精代谢能力检测：您的酒量，基...\" },\r\n        { name: \"植酸检测方法及作用\" },\r\n        { name: \"兔拉检测：行业中芯片造假技术...\" },\r\n        { name: \"酒精代谢能力检测：您的酒量，基...\" },\r\n      ],\r\n      showdh: true,\r\n      showdh1: false,\r\n      noAnimation: false,\r\n      selectedItem: {\r\n        name: \"离子溅射仪\", //产品名称\r\n        imgurl: \"https://3d.dddtask.cn/enginner-xufeng/image/tianjin/lizi.png\",\r\n        location: \"北洋园校区54楼, E105\", //位置\r\n        status: \"已领用\", //仪器状态  可领用\r\n        details: [\r\n          {\r\n            name: \"产品名称\",\r\n            value: \"离子溅射仪\",\r\n          },\r\n          {\r\n            name: \"原值\",\r\n            value: \"--\",\r\n          },\r\n          {\r\n            name: \"购入时间\",\r\n            value: \"2020/09/02\",\r\n          },\r\n          {\r\n            name: \"品牌\",\r\n            value: \"--\",\r\n          },\r\n          {\r\n            name: \"生产厂家\",\r\n            value: \"--\",\r\n          },\r\n          {\r\n            name: \"供应商名称\",\r\n            value: \"--\",\r\n          },\r\n          {\r\n            name: \"供应商联系信息\",\r\n            value: \"--\",\r\n          },\r\n        ],\r\n        maintenance_records: {\r\n          maintenance_content: \"校准和系统升级\", //维护内容\r\n          date: \"2024-01-10\", //维护时间\r\n          next_maintenance_date: \"2022-01-10\", //下次维护时间\r\n        },\r\n        management_name: \"王工\",\r\n        management_contact_info: \"15698567542\",\r\n      },\r\n      tableDataItem: [],\r\n      jlURL,\r\n      Title: \"资产管理\",\r\n      tableTitle: [\r\n        { key: \"楼层\" },\r\n        { key: \"设备编号\" },\r\n        { key: \"设备名称\" },\r\n        { key: \"房间号\" },\r\n        { key: \"模型\" },\r\n        { key: \"设备状态\" },\r\n        { key: \"状态说明\" },\r\n      ],\r\n      ids: null,\r\n      nhlist: [\r\n        {\r\n          title: \"供气压力\",\r\n          status: \"0.3Mpa\",\r\n          unit: \"℃\",\r\n        },\r\n\r\n        {\r\n          title: \"供气流量\",\r\n          status: \"6M3/min\",\r\n          unit: \"㎡\",\r\n        },\r\n        {\r\n          title: \"露点温度\",\r\n          status: \"6℃\",\r\n          unit: \"℃\",\r\n        },\r\n        {\r\n          title: \"含氧量\",\r\n          status: \"6PPM\",\r\n          unit: \"㎡\",\r\n        },\r\n      ],\r\n      warnlist1: [\r\n        {\r\n          type: 1,\r\n          name: \"检测到烟雾，可能有着火灾的发生...\",\r\n          value: \"\",\r\n          time: \"09:13:59  2023-06-07\",\r\n        },\r\n        {\r\n          type: 2,\r\n          name: \"检测到烟雾，可能预示着火灾的发生..\",\r\n          value: \"\",\r\n          time: \"10:13:18  2023-06-12\",\r\n        },\r\n        {\r\n          type: 3,\r\n          name: \"实验室内检测到漏水，可能来自冷凝水..\",\r\n          value: \"\",\r\n          time: \"10:13:18  2023-06-12\",\r\n        },\r\n      ],\r\n      isButton2Active: false,\r\n      status: \"巡检中\",\r\n      status1: \"已完成\",\r\n      status2: \"待巡检\",\r\n      selectedIndex: 0,\r\n      componentTag: \"component0\",\r\n      token: \"\",\r\n    };\r\n  },\r\n  // 计算属性类似于data概念\r\n  computed: {\r\n    ...mapGetters(\"equipment\", [\"equipmentTags\"]),\r\n  },\r\n  // 监控data中的数据变化\r\n  watch: {},\r\n  // 方法集合\r\n  methods: {\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.getyiqidetails(this.token, val - 1);\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`);\r\n      this.getyiqidetails(this.token, val - 1);\r\n    },\r\n    async gettoken(id) {\r\n      try {\r\n        const response = await api.post(\"\", {\r\n          method: \"equipment/searchEquipments\",\r\n          params: {\r\n            criteria: {\r\n              cat: id,\r\n              location:'58楼ACE区',\r\n              // \"group\": 1,\r\n              // \"searchtext\": \"搜索内容\"\r\n            },\r\n          },\r\n        });\r\n\r\n        // 检查是否成功拿到 token\r\n        if (response.data && response.data.response.token) {\r\n          const token = response.data.response.token;\r\n          this.token = token;\r\n          // 将 token 存入 localStorage\r\n          // localStorage.setItem('authToken', token);\r\n          console.log(\"535:\", response.data.response);\r\n          this.total = response.data.response.total;\r\n          this.handleSizeChange(1);\r\n        } else {\r\n          console.error(\"登录成功但未返回 token:\", response.data.response);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"登录失败:\", error);\r\n      }\r\n    },\r\n    getyiqidetails(token, start) {\r\n      const headers = {\r\n        clientid: \"5a298e93-158d-4e22-83cf-6ceb62e9b4f1\",\r\n        clientsecret: \"2c8ec39e-9887-482a-b28b-e64c496b601c\",\r\n      };\r\n      const body = {\r\n        method: \"equipment/getEquipments\",\r\n        params: {\r\n          token: token,\r\n          start: start ? start * 16 : 0,\r\n          num: 16,\r\n        },\r\n      };\r\n      axios\r\n        .post(jlURL, body, {})\r\n        .then((response) => {\r\n          console.log(response.data, 535);\r\n          this.devicedata = response.data.response;\r\n          this.loading = false;\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"Error:\", error);\r\n        });\r\n    },  \r\n    ...mapActions(\"equipment\", [\"fetchEquipmentTags\"]),\r\n    changetit(index) {\r\n      this.titactive = index;\r\n      this.isshowwhat = index;\r\n      if (index == 1) {\r\n        this.showdh = false;\r\n        this.showdh1 = true;\r\n        this.noAnimation = true;\r\n      } else {\r\n        this.showdh = true;\r\n        this.showdh1 = false;\r\n        this.noAnimation = false;\r\n      }\r\n    },\r\n    handleCascaderChange(value) {\r\n      console.log(\"选中的值:\", value[1]);\r\n      this.gettoken(value[1]);\r\n    },\r\n    hidedetailsss() {\r\n      this.isshowsss = false;\r\n    },\r\n    opendialog(payload) {\r\n      if (payload == 1) {\r\n        this.isshow = true;\r\n\r\n        this.data.forEach((item) => {\r\n          if (item.category == \"电子显微镜\") {\r\n            this.isshow = true;\r\n            this.tableDataItem = item.items;\r\n            console.log(this.tableDataItem);\r\n          }\r\n        });\r\n      }\r\n\r\n      // 在这里处理事件\r\n    },\r\n    toggleSubMenu(item) {\r\n      this.isshowsss = true;\r\n      console.log(item, \"选中的设备信息\");\r\n      this.selectedItem = {\r\n        id: item.id,\r\n        name: item.name,\r\n        imgurl: item.iconreal_url,\r\n        location: item.location + item.location2, //位置\r\n        status: !item.is_using ? \"当前使用\" : \"可使用\", //仪器状态  可领用\r\n        details: [\r\n          {\r\n            name: \"产品名称\",\r\n            value: item.name,\r\n          },\r\n          {\r\n            name: \"价格\",\r\n            value: item.price,\r\n          },\r\n          {\r\n            name: \"购入时间\",\r\n            value: `${new Date(item.purchased_date * 1000).getFullYear()}/${\r\n              new Date(item.purchased_date * 1000).getMonth() + 1\r\n            }/${new Date(item.purchased_date * 1000).getDate()}`,\r\n          },\r\n          {\r\n            name: \"制造国家\",\r\n            value: item.manu_at,\r\n          },\r\n          {\r\n            name: \"生产厂家\",\r\n            value: item.manufacturer,\r\n          },\r\n          {\r\n            name: \"负责人\",\r\n            value: item.contact,\r\n          },\r\n          {\r\n            name: \"联系电话\",\r\n            value: item.phone,\r\n          },\r\n        ],\r\n        maintenance_records: {\r\n          maintenance_content: \"校准和系统升级\", //维护内容\r\n          date: \"2024-01-10\", //维护时间\r\n          next_maintenance_date: \"2022-01-10\", //下次维护时间\r\n        },\r\n        management_name: \"王工\",\r\n        management_contact_info: \"15698567542\",\r\n      };\r\n    },\r\n    // // 设置内容\r\n    // setContent(content) {\r\n    //   this.isshowsss = true;\r\n    //   this.selectedItem = content;\r\n    //   console.log(this.selectedItem);\r\n\r\n    //   this.activeContent = content;\r\n    // },\r\n    showdetails(item) {\r\n      // item.items.forEach((item) => {\r\n      //   this.newArr.push({ name: item.name });\r\n      // });\r\n      // console.log(this.newArr);\r\n\r\n      this.isshow = true;\r\n      this.tableDataItem = item.items;\r\n    },\r\n    hidedetails() {\r\n      this.isshow = false;\r\n    },\r\n\r\n    oc(value) {\r\n      console.log(value, \"收到的值\");\r\n      this.showdh = value;\r\n    },\r\n    xuanzedialog(value) {\r\n      const optionMapping = {\r\n        选项1: 0,\r\n        选项2: 1,\r\n        选项3: 2,\r\n        选项4: 3,\r\n        选项5: 4,\r\n        选项6: 5,\r\n        选项7: 6,\r\n        选项8: 7,\r\n        选项9: 8,\r\n        选项10: 9,\r\n        选项11: 10,\r\n        选项12: 11,\r\n        选项13: 12,\r\n      };\r\n\r\n      const index = optionMapping[value];\r\n      if (index !== undefined) {\r\n        this.tableDataItem = this.data[index].items;\r\n      } else {\r\n        console.error(\"无效的选项: \", value);\r\n      }\r\n    },\r\n  },\r\n  // 生命周期 - 创建完成（可以访问当前this实例）\r\n  created() {},\r\n  // 生命周期 - 挂载完成（可以访问DOM元素）\r\n  mounted() {\r\n    this.gettoken(\"\");\r\n    this.showdh1 = true;\r\n    this.fetchEquipmentTags();\r\n    // setTimeout(() => {\r\n    //   this.showdh1 = false;\r\n    //   this.noAnimation = false;\r\n    // }, 1000); // 动画持续时间为1秒\r\n    console.log(1222);\r\n  },\r\n  beforeCreate() {}, // 生命周期 - 创建之前\r\n  beforeMount() {}, // 生命周期 - 挂载之前\r\n  beforeUpdate() {}, // 生命周期 - 更新之前\r\n  updated() {}, // 生命周期 - 更新之后\r\n  beforeUnmount() {\r\n    // 在组件销毁之前清除定时器\r\n    console.log(1111);\r\n  },\r\n\r\n  unmounted() {\r\n    console.log(2222);\r\n  }, // 生命周期 - 销毁之前\r\n  destroyed() {\r\n    console.log(1221);\r\n  }, // 生命周期 - 销毁完成\r\n  activated() {}, // 如果页面有keep-alive缓存功能，这个函数会触发\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.container {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-content: space-between;\r\n  align-items: stretch;\r\n  height: 1080px;\r\n\r\n  // text-align: center;\r\n  /* 定位 el-cascader 的 placeholder 样式 */\r\n\r\n  /deep/.sect.el-tooltip__trigger {\r\n    text-align: left;\r\n    width: 200px;\r\n    color: #fff;\r\n    // background-color: #00ffc0;\r\n  }\r\n\r\n  /deep/.el-input__wrapper {\r\n    box-shadow: none;\r\n    border: none;\r\n    background-color: #5a6972;\r\n    color: #fff;\r\n  }\r\n\r\n  /deep/.sect .el-input__inner {\r\n    color: #fff;\r\n  }\r\n\r\n  .sbdetails {\r\n    position: absolute;\r\n    top: 50%;\r\n    left: 50%;\r\n    transform: translate(-50%, -50%);\r\n    z-index: 9999;\r\n  }\r\n\r\n  .el-input {\r\n    margin-left: 5px;\r\n    width: 145px;\r\n    height: 34px;\r\n    color: #fff !important;\r\n\r\n    ::v-deep .el-input__wrapper {\r\n      background: url(\"../assets/image/inputss.png\");\r\n      background-size: 100% 100%;\r\n      background-repeat: no-repeat;\r\n      box-shadow: none !important;\r\n      color: #fff !important;\r\n    }\r\n\r\n    /deep/.el-input__inner {\r\n      color: #fff !important;\r\n    }\r\n\r\n    .el-input__inner::placeholder {\r\n      color: #fff;\r\n      /* 设置占位符颜色 */\r\n    }\r\n  }\r\n\r\n  .suosuo {\r\n    position: absolute;\r\n    top: 62px;\r\n    right: -32px;\r\n    cursor: pointer;\r\n  }\r\n\r\n  .box {\r\n    // margin-top: 6px;\r\n    padding-top: 5px;\r\n    margin-bottom: 0.225rem;\r\n    max-height: 745px;\r\n    width: 330px;\r\n    // height: 800px;\r\n\r\n    overflow-y: scroll;\r\n\r\n    /* 设置垂直滚动条 */\r\n    /* 设置滚动条的样式 */\r\n    &::-webkit-scrollbar {\r\n      width: 0.1px;\r\n      /* 设置滚动条的宽度 */\r\n    }\r\n\r\n    /* 设置滚动条轨道的样式 */\r\n    &::-webkit-scrollbar-track {\r\n      background-color: #f1f1f1;\r\n      /* 设置滚动条轨道的背景色 */\r\n    }\r\n\r\n    /* 设置滚动条滑块的样式 */\r\n    &::-webkit-scrollbar-thumb {\r\n      background-color: #334f6e;\r\n      /* 设置滚动条滑块的背景色 */\r\n    }\r\n\r\n    /* 鼠标悬停在滚动条上时的样式 */\r\n    &::-webkit-scrollbar-thumb:hover {\r\n      background-color: #555;\r\n      /* 设置鼠标悬停时滚动条滑块的背景色 */\r\n    }\r\n\r\n    .xiaobox {\r\n      margin-top: 20px;\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .siqiu {\r\n        width: 16px;\r\n        height: 16px;\r\n      }\r\n\r\n      .shuru {\r\n        display: flex;\r\n        align-items: center;\r\n        padding-left: 10px;\r\n        background: url(\"../assets/image/shebei12.png\");\r\n        background-size: 100% 100%;\r\n        background-repeat: no-repeat;\r\n        width: 261px;\r\n        height: 32px;\r\n        font-family: Source Han Sans SC;\r\n        font-weight: bold;\r\n        font-size: 17px;\r\n        color: #ffffff;\r\n      }\r\n    }\r\n\r\n    .xiaoboxs {\r\n      cursor: pointer;\r\n      margin-top: 14px;\r\n      display: flex;\r\n      margin-left: 5px;\r\n      align-items: center;\r\n\r\n      .nihaowo {\r\n        width: 78px;\r\n        font-family: Source Han Sans SC;\r\n        font-weight: bold;\r\n        font-size: 10px;\r\n        color: #ffffff;\r\n        display: flex;\r\n        white-space: nowrap;\r\n        /* 不换行 */\r\n        overflow: hidden;\r\n        /* 超出部分隐藏 */\r\n        text-overflow: ellipsis;\r\n        /* 超出部分显示省略号 */\r\n      }\r\n\r\n      .siqiu {\r\n        width: 16px;\r\n        height: 16px;\r\n        margin-left: 10px;\r\n        margin-right: 7px;\r\n      }\r\n\r\n      .shuru {\r\n        display: flex;\r\n        align-items: center;\r\n        padding-left: 10px;\r\n        background: url(\"../assets/image/shebei12.png\");\r\n        background-size: 100% 100%;\r\n        background-repeat: no-repeat;\r\n        width: 261px;\r\n        height: 32px;\r\n        font-family: Source Han Sans SC;\r\n        font-weight: bold;\r\n        font-size: 17px;\r\n        color: #ffffff;\r\n        white-space: nowrap;\r\n        /* 不换行 */\r\n        overflow: hidden;\r\n        /* 超出部分隐藏 */\r\n        text-overflow: ellipsis;\r\n        /* 超出部分显示省略号 */\r\n      }\r\n    }\r\n  }\r\n\r\n  .left-panel {\r\n    position: fixed;\r\n    z-index: 1;\r\n    top: 100px;\r\n    left: 22px;\r\n    width: 330px;\r\n    height: 937px;\r\n\r\n    background-size: 100% 100%;\r\n    transform: translate(-122%);\r\n    transition: transform 0.5s ease-in-out;\r\n  }\r\n\r\n  .left-panel-active {\r\n    transform: translate(0%);\r\n  }\r\n\r\n  .left-panel-active1 {\r\n    // transform: translate(0%);\r\n    animation: slideOut 1s ease-in-out forwards;\r\n  }\r\n\r\n  @keyframes slideOut {\r\n    100% {\r\n      transform: translateX(0%);\r\n    }\r\n\r\n    // 85% {\r\n    //   transform: translateX(-25%);\r\n    // }\r\n\r\n    // 65% {\r\n    //   transform: translateX(-15%);\r\n    // }\r\n\r\n    // 40% {\r\n    //   transform: translateX(-55%);\r\n    // }\r\n\r\n    // 30% {\r\n    //   transform: translateX(-40%);\r\n    // }\r\n\r\n    0% {\r\n      transform: translateX(-100%);\r\n    }\r\n  }\r\n\r\n  .rtitle {\r\n    margin-top: 16px;\r\n  }\r\n\r\n  @keyframes slideIn {\r\n    0% {\r\n      transform: translateX(100%);\r\n    }\r\n\r\n    // 30% {\r\n    //   transform: translateX(65%);\r\n    // }\r\n\r\n    // 40% {\r\n    //   transform: translateX(40%);\r\n    // }\r\n\r\n    // 65% {\r\n    //   transform: translateX(15%);\r\n    // }\r\n\r\n    // 85% {\r\n    //   transform: translateX(25%);\r\n    // }\r\n\r\n    100% {\r\n      transform: translateX(0%);\r\n    }\r\n  }\r\n\r\n  .right-panel {\r\n    position: fixed;\r\n    z-index: 1;\r\n    right: 83px;\r\n    width: 330px;\r\n    top: 100px;\r\n    height: 937px;\r\n\r\n    background-size: 100% 100%;\r\n    transform: translate(122%);\r\n    transition: transform 0.5s ease-in-out;\r\n\r\n    .jk {\r\n      margin-top: 12px;\r\n      width: 90%;\r\n      height: 200px;\r\n    }\r\n\r\n    .box {\r\n      margin-top: 6px;\r\n      margin-bottom: 18px;\r\n      // background: url(\"../assets/image/zuoshang1.png\");\r\n      background-size: 100% 100%;\r\n      background-repeat: no-repeat;\r\n\r\n      width: 94%;\r\n      // height: 428px;\r\n\r\n      .loudong {\r\n        margin-top: 21px;\r\n        margin-bottom: 25px;\r\n      }\r\n\r\n      .wenzi {\r\n        font-family: Microsoft YaHei;\r\n        font-weight: 400;\r\n        font-size: 10px;\r\n        color: #bdecf9;\r\n        text-align: left;\r\n        margin-left: 20px;\r\n        margin-right: 20px;\r\n\r\n        .h2biaoti {\r\n          margin-bottom: 15px;\r\n          font-family: Microsoft YaHei;\r\n          font-weight: 400;\r\n          font-size: 12px;\r\n          color: #00ffff;\r\n        }\r\n      }\r\n\r\n      .p {\r\n        text-indent: 2em;\r\n        margin-bottom: 1em;\r\n        letter-spacing: 0.05em;\r\n      }\r\n    }\r\n\r\n    .boxxx {\r\n      margin-top: 6px;\r\n      margin-bottom: 18px;\r\n      background: url(\"../assets/image/zuoshang1.png\");\r\n      background-size: 100% 100%;\r\n      background-repeat: no-repeat;\r\n\r\n      width: 333px;\r\n      height: 420px;\r\n      overflow-y: scroll;\r\n      /* 设置垂直滚动条 */\r\n      // overflow: hidden;\r\n      /* 设置滚动条的样式 */\r\n\r\n      .zengti {\r\n        margin: 10px 0;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        width: 315px;\r\n        height: 38px;\r\n        gap: 5px;\r\n\r\n        .left {\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: space-evenly;\r\n          width: 84px;\r\n          height: 27px;\r\n\r\n          .yuan {\r\n            width: 12px;\r\n            height: 12px;\r\n            border-radius: 50%;\r\n            background-color: #08f7f7;\r\n          }\r\n\r\n          .wenziss {\r\n            display: flex;\r\n            align-items: flex-start;\r\n            flex-direction: column;\r\n\r\n            .p1 {\r\n              font-family: Source Han Sans SC;\r\n              font-weight: 500;\r\n              font-size: 10px;\r\n              color: #ffffff;\r\n            }\r\n\r\n            .p2 {\r\n              font-family: Source Han Sans SC;\r\n              font-weight: 500;\r\n              font-size: 10px;\r\n              color: #55cff9;\r\n            }\r\n          }\r\n        }\r\n\r\n        .right {\r\n          background: url(\"../assets/image/rightbeij.png\");\r\n          background-size: 100% 100%;\r\n          background-repeat: no-repeat;\r\n\r\n          width: 217px;\r\n          height: 38px;\r\n          font-family: Source Han Sans SC;\r\n          font-weight: 500;\r\n          font-size: 11px;\r\n          color: #ffffff;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n        }\r\n      }\r\n    }\r\n\r\n    .boxxx::-webkit-scrollbar {\r\n      width: 1px;\r\n      /* 设置滚动条的宽度 */\r\n    }\r\n\r\n    /* 设置滚动条轨道的样式 */\r\n    .boxxx::-webkit-scrollbar-track {\r\n      background-color: #f1f1f1;\r\n      /* 设置滚动条轨道的背景色 */\r\n    }\r\n\r\n    /* 设置滚动条滑块的样式 */\r\n    .boxxx::-webkit-scrollbar-thumb {\r\n      background-color: #013363;\r\n      /* 设置滚动条滑块的背景色 */\r\n    }\r\n  }\r\n\r\n  .no-animation {\r\n    transition: none;\r\n  }\r\n\r\n  .right-panel-active {\r\n    transform: translate(0%);\r\n    // animation: slideIn 1s ease-in-out ;\r\n  }\r\n\r\n  .right-panel-active1 {\r\n    // transform: translate(0%);\r\n    animation: slideIn 1s ease-in-out forwards;\r\n  }\r\n\r\n  @keyframes slideIn {\r\n    0% {\r\n      transform: translateX(100%);\r\n    }\r\n\r\n    // 30% {\r\n    //   transform: translateX(65%);\r\n    // }\r\n\r\n    // 40% {\r\n    //   transform: translateX(40%);\r\n    // }\r\n\r\n    // 65% {\r\n    //   transform: translateX(15%);\r\n    // }\r\n\r\n    // 85% {\r\n    //   transform: translateX(25%);\r\n    // }\r\n\r\n    100% {\r\n      transform: translateX(0%);\r\n    }\r\n  }\r\n\r\n  .completed {\r\n    background: #7ad0ff;\r\n  }\r\n\r\n  .incomplete {\r\n    background: #ff6041;\r\n  }\r\n\r\n  .warning {\r\n    background: #00ffc0;\r\n  }\r\n\r\n  .completeds {\r\n    color: #7ad0ff;\r\n  }\r\n\r\n  .incompletes {\r\n    color: #ff6041;\r\n  }\r\n\r\n  .warnings {\r\n    color: #00ffc0;\r\n  }\r\n}\r\n\r\n.ql-center {\r\n  display: flex;\r\n  margin-top: 20px;\r\n  justify-content: space-around;\r\n  margin-top: 14px;\r\n\r\n  .ql-Box {\r\n    width: 75px;\r\n    height: 49px;\r\n    border: 1px solid #7ad0ff;\r\n    // opacity: 0.6;\r\n    border-radius: 2px;\r\n\r\n    .ql-box1 {\r\n      font-family: Alibaba PuHuiTi;\r\n      font-weight: bold;\r\n      font-size: 16px;\r\n      color: #7ad0ff;\r\n      margin-top: -10px;\r\n    }\r\n\r\n    .ql-box {\r\n      display: flex;\r\n      padding-left: 8px;\r\n      padding-right: 9px;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      width: 100%;\r\n      height: 34px;\r\n\r\n      .left_ql {\r\n        width: 49px;\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        color: #ffffff;\r\n\r\n        .yuan {\r\n          width: 7px;\r\n          height: 7px;\r\n          border-radius: 50%;\r\n        }\r\n\r\n        .pp {\r\n          color: #fff;\r\n          font-size: 12px;\r\n        }\r\n      }\r\n\r\n      img {\r\n        height: 12px;\r\n        width: 7px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.warn1 {\r\n  background: url(\"../assets/image/warnred.png\");\r\n}\r\n\r\n.warn2 {\r\n  background: url(\"../assets/image/warnyellow.png\");\r\n}\r\n\r\n.warn3 {\r\n  background: url(\"../assets/image/warngreen.png\");\r\n}\r\n\r\n.warning12 {\r\n  background-size: 100% 100%;\r\n  // width: 365px;\r\n  height: 47px;\r\n\r\n  .info {\r\n    margin-top: 5px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    text-align: left;\r\n\r\n    margin-left: 50px;\r\n\r\n    .info1 {\r\n      display: flex;\r\n      flex-direction: column;\r\n\r\n      p:nth-of-type(1) {\r\n        font-size: 13px;\r\n        font-family: Alibaba PuHuiTi;\r\n        font-weight: bold;\r\n      }\r\n\r\n      .red {\r\n        color: #ff0000;\r\n      }\r\n\r\n      .green {\r\n        color: #00ffcc;\r\n      }\r\n\r\n      .yellow {\r\n        color: #ffff00;\r\n      }\r\n\r\n      p:nth-of-type(2) {\r\n        font-size: 16px;\r\n        font-family: Alibaba PuHuiTi;\r\n        font-weight: 400;\r\n        color: #ffffff;\r\n      }\r\n    }\r\n\r\n    .info2 {\r\n      margin-right: 10px;\r\n      font-size: 15px;\r\n      font-family: Alibaba PuHuiTi;\r\n      font-weight: 400;\r\n      color: #cffff8;\r\n    }\r\n  }\r\n}\r\n\r\n.xxxx {\r\n  position: absolute;\r\n  top: 1%;\r\n  right: 1%;\r\n  width: 25px;\r\n  height: 25px;\r\n  z-index: 99999;\r\n}\r\n\r\n/* 菜单容器 */\r\n.menu-container {\r\n  display: flex;\r\n  width: 330px;\r\n  height: 736px;\r\n}\r\n\r\n/* 菜单样式 */\r\n.menu {\r\n  width: 100%;\r\n  // background-color: #fff;\r\n  // border: 1px solid #ddd;\r\n  border-radius: 4px;\r\n}\r\n\r\n/deep/.el-pagination {\r\n  --el-pagination-bg-color: none;\r\n  --el-pagination-button-color: #fff;\r\n  --el-pagination-font-size: 17px;\r\n  margin-left: -13px;\r\n}\r\n\r\n/deep/.el-pagination__total {\r\n  color: #fff;\r\n  font-size: 15px;\r\n}\r\n\r\n/deep/.el-pagination button:disabled {\r\n  background: none;\r\n  font-size: 17px;\r\n}\r\n\r\n/deep/.el-pagination button {\r\n  background: none;\r\n  font-size: 17px;\r\n}\r\n\r\n/deep/.el-icon {\r\n  font-size: 17px !important;\r\n}\r\n\r\n/* 菜单项样式 */\r\n.menu-group {\r\n  margin-top: 14px;\r\n}\r\n\r\n.menu-item {\r\n  cursor: pointer;\r\n  background: url(\"../assets/image/rightbeij.png\");\r\n  background-size: 100% 100%;\r\n  background-repeat: no-repeat;\r\n\r\n  width: 100%;\r\n  height: 32px;\r\n  font-family: Source Han Sans SC;\r\n  font-weight: 500;\r\n  font-size: 17px;\r\n  color: #ffffff;\r\n  display: flex;\r\n  align-items: center;\r\n  padding-left: 10px;\r\n  // justify-content: center;\r\n}\r\n\r\n.menu-item:hover {\r\n  // background-color: #f0f0f0;\r\n}\r\n\r\n.submenu {\r\n  // background-color: #f9f9f9;\r\n  padding-left: 20px;\r\n}\r\n\r\n.submenu-item {\r\n  padding: 3px;\r\n  padding-left: 12px;\r\n  margin: 8px;\r\n  font-size: 16px;\r\n  color: #ffffff;\r\n  cursor: pointer;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background-color: #2c343f;\r\n}\r\n\r\n.submenu-item:hover {\r\n  background-color: #163561;\r\n}\r\n\r\n.qiuqiu {\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  .siqiu {\r\n    width: 16px;\r\n    height: 16px;\r\n    margin-left: 10px;\r\n    margin-right: 7px;\r\n  }\r\n}\r\n\r\n.listtype {\r\n  background: url(\"../assets/image/xuanzhotiemmoon.png\");\r\n  background-repeat: no-repeat;\r\n  background-size: 100% 100%;\r\n  width: 80px;\r\n  height: 26px;\r\n  text-align: center;\r\n  line-height: 26px;\r\n\r\n  font-size: 13px;\r\n  font-family: Alibaba PuHuiTi;\r\n  font-weight: 400;\r\n  color: #fff;\r\n}\r\n\r\n.listtypes {\r\n  background: url(\"../assets/image/xuanzhotiemmoon.png\");\r\n  background-repeat: no-repeat;\r\n  background-size: 100% 100%;\r\n  width: 100px;\r\n  height: 32px;\r\n  text-align: center;\r\n  line-height: 32px;\r\n  cursor: pointer;\r\n  font-size: 13px;\r\n  font-family: Alibaba PuHuiTi;\r\n  font-weight: 400;\r\n  color: #fff;\r\n}\r\n\r\n.ellipsis {\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  max-width: 190px;\r\n  /* 你可以根据需要调整宽度 */\r\n  font-size: 16px;\r\n}\r\n\r\n.botbtn {\r\n  position: fixed;\r\n  top: 978px;\r\n  // left: 228px;\r\n  left: 355px;\r\n  width: 200px;\r\n  height: 43px;\r\n  background: #022d56;\r\n  border-radius: 8px;\r\n  z-index: 20;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n\r\n  .btt {\r\n    color: #fff;\r\n    font-size: 17px;\r\n    line-height: 44px;\r\n    width: 88px;\r\n    text-align: center;\r\n    height: 43px;\r\n    cursor: pointer;\r\n  }\r\n\r\n  .btt1 {\r\n    color: rgb(8, 207, 241);\r\n    font-size: 17px;\r\n    line-height: 44px;\r\n    width: 88px;\r\n    text-align: center;\r\n    height: 43px;\r\n    cursor: pointer;\r\n  }\r\n}\r\n</style>\r\n"]}]}