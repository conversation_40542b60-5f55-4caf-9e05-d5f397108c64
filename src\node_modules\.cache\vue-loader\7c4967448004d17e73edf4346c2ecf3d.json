{"remainingRequest": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\views\\dayi\\zichan.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\views\\dayi\\zichan.vue", "mtime": 1751448864722}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\views\\dayi\\zichan.vue"], "names": [], "mappings": ";AAmGA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;AACnB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE3D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACR,CAAC,CAAC;AACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrD,CAAC;AACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;MACxC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACvD,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACvD,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC;MACH,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAChC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACV;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACZ,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACV;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACZ,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;QAEZ,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;QAEX,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC;MACH,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC;MACH,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC;MACH,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC;MACH,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;MACH,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACV;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC/C,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC/C,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC/C,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC/C,CAAC;MACH,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC/C,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC/C,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC/C,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC/C,CAAC;MACH,CAAC;IACH,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1F,CAAC;IACD,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvD,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1D,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxD,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClD,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;IAER,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE1C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrE;IACA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3D;IACA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3D;IACA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3D;IACA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3D;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;IACrB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEd,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;IAEP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;UAElC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB;;QAEF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACf,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACtB;MACF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACf,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;QAGlD;;MAEF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACf,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACN,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC;UACH;UACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACtB;MACF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEzB,CAAC,CAAC,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACf,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;YAChB;cACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvC,CAAC;YACD;cACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEnC,CAAC;YACD;cACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEpC,CAAC;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACtB;MACF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACb;QACF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACf,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACpC;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACtB,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC;EACH,CAAC;AACH,CAAC", "file": "E:/svn/***********-0186-SFTianjinUniversityH5/src/src/views/dayi/zichan.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <keep-alive>\r\n    <div class=\"contents\" v-if=\"isshow\" v-loading=\"false\" element-loading-text=\"Loading...\"\r\n      :element-loading-spinner=\"svg\" element-loading-background=\"rgba(0, 0, 0, 1)\">\r\n      <div class=\"toubu\">\r\n        <div style=\"margin-left: 20px; display: flex; align-items: center\" v-if=\"false\">\r\n          <div style=\"display: flex; width: 100%; align-items: center\">\r\n            <span class=\"sp\">当前位置：</span>\r\n            <el-select class=\"el-select\" v-model=\"selectvalue1\" placeholder=\"selectvalue1\"\r\n              style=\"width: 64px; height: 35.1px\" @change=\"handleChange\">\r\n              <el-option v-for=\"item in options1\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n            </el-select>\r\n            <el-select class=\"el-select\" v-model=\"selectvalue2\" placeholder=\"selectvalue2\"\r\n              style=\"width: 64px; height: 35.1px\" @change=\"handleChange\">\r\n              <el-option v-for=\"item in options2\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n            </el-select>\r\n            <el-select class=\"el-select\" v-model=\"selectvalue3\" placeholder=\"selectvalue3\"\r\n              style=\"width: 78px; height: 35.1px\" @change=\"handleChange\">\r\n              <el-option v-for=\"item in options3\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n            </el-select>\r\n          </div>\r\n          <img v-if=\"isshow\" class=\"img1sss\" @click=\"anniu()\" src=\"../../assets/image/table-x.png\" alt=\"\" />\r\n        </div>\r\n\r\n        <div class=\"all\">\r\n          <div class=\"all1\">\r\n            <!-- <Titles class=\"ltitle1\" tit=\"仪器实时状态\">\r\n              <div class=\"dayi\">\r\n                <span>共</span>\r\n                <span>{{ sbnum }}</span>\r\n                <span>台仪器安装客户端</span>\r\n              </div>\r\n              <Electricity1 v-if=\"yiqiStatus\" class=\"zhuzhuangtu\" :chartData=\"yiqiStatus\"></Electricity1>         \r\n            </Titles> -->\r\n            <Titles class=\"ltitle11\" tit=\"人员分布统计\">\r\n\r\n              <!-- <zhuzhuangtu class=\"zhuzhuangtu\" :chartData=\"chartData1\"></zhuzhuangtu> -->\r\n              <huanxing style=\"margin-top: 150px;\" v-if=\"userDistribution\" :chartData=\"userDistribution\"></huanxing>\r\n\r\n            </Titles>\r\n          </div>\r\n          <div class=\"line1\"></div>\r\n          <div class=\"all2\">\r\n            <!-- <Titles class=\"ltitle1\" tit=\"仪器设备使用情况\">\r\n            <div class=\"shinei\">\r\n              <Electricity8></Electricity8>\r\n            </div>\r\n          </Titles>\r\n          <Titles class=\"ltitle\" tit=\"办公设备使用情况\">\r\n            <div class=\"shinei\">\r\n              <Electricity8></Electricity8>\r\n            </div>\r\n          </Titles> -->\r\n            <div>\r\n              <Titles class=\"ltitle1\" tit=\"课题测试统计\">\r\n                <div class=\"shinei\">\r\n                  <Electricity3 v-if=\"testStatistics\" :chartData=\"testStatistics\"></Electricity3>\r\n                </div>\r\n              </Titles>\r\n            </div>\r\n          </div>\r\n          <div class=\"all3\">\r\n\r\n            <Titles class=\"ltitle1\" tit=\"仪器使用排行\">\r\n              <div class=\"shinei\">\r\n                <!-- <Electricity6></Electricity6> -->\r\n                <zhuzhuangtu v-if=\"equipmentRank\" class=\"zhuzhuangtu1\" :chartData=\"equipmentRank\"></zhuzhuangtu>\r\n              </div>\r\n            </Titles>\r\n            <Titles class=\"ltitle1\" tit=\"课题组使用统计\">\r\n              <div class=\"shinei\">\r\n                <!-- <huanxing :chartData=\"chartData\"></huanxing> -->\r\n                <zhuzhuangtu1 v-if=\"topUsers\" class=\"zhuzhuangtu1\" :chartData=\"topUsers\"></zhuzhuangtu1>\r\n              </div>\r\n            </Titles>\r\n            <!-- <div class=\"shuantitle\">\r\n            <div style=\"width: 50%\">\r\n              <div class=\"title\">实时负载率</div>\r\n              <div class=\"nenghao\">实时负载率:</div>\r\n              <p class=\"nhp\">30%</p>\r\n            </div>\r\n            <div style=\"width: 50%\">\r\n              <div class=\"title\">实时总功率</div>\r\n              <div class=\"nenghao\">实时总功率:</div>\r\n              <p class=\"nhp\">200Kw</p>\r\n            </div>\r\n          </div>\r\n      -->\r\n\r\n\r\n\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </keep-alive>\r\n</template>\r\n\r\n<script>\r\nimport Electricity from \"@/components/echarts/dianbiao/biao1.vue\";\r\nimport biao1s from \"@/components/echarts/dianbiao/biao1s.vue\";\r\nimport biao1ss from \"@/components/echarts/dianbiao/biao1ss.vue\";\r\nimport Titles from \"@/components/common/Titles.vue\";\r\nimport Electricity1 from \"@/components/dayi/Electricity1.vue\";\r\nimport Electricity2 from \"@/components/echarts/dianbiao/Electricity2.vue\";\r\nimport Electricity3 from \"@/components/dayi//zhexiantu.vue\";\r\nimport Electricity4 from \"@/components/echarts/dianbiao/Electricity4.vue\";\r\nimport Electricity5 from \"@/components/echarts/dianbiao/Electricity5.vue\";\r\nimport Electricity6 from \"@/components/dayi/Electricity6.vue\";\r\nimport Electricity7 from \"@/components/echarts/dianbiao/Electricity7.vue\";\r\nimport Electricity8 from \"@/components/dayi/Electricity8.vue\";\r\nimport huanxing from \"@/components/dayi/xiaobingtu.vue\";\r\nimport zhuzhuangtu from \"@/components/dayi/zhuzhuangtu.vue\";\r\nimport zhuzhuangtu1 from \"@/components/dayi/zhuzhuangtu1.vue\";\r\nimport axios from \"axios\";\r\n// 使用环境变量设置基础 API 地址\r\nconst baseURL = process.env.VUE_APP_BASE_API || '/lims/api';\r\n\r\nconst api = axios.create({\r\n  baseURL\r\n});\r\nconst headers = {\r\n  clientid: '5a298e93-158d-4e22-83cf-6ceb62e9b4f1',\r\n  clientsecret: '2c8ec39e-9887-482a-b28b-e64c496b601c'\r\n};\r\nexport default {\r\n  components: {\r\n    Titles,\r\n    Electricity1,\r\n    Electricity,\r\n    Electricity2,\r\n    Electricity3,\r\n    Electricity4,\r\n    Electricity5,\r\n    Electricity6,\r\n    Electricity7,\r\n    Electricity8,\r\n    huanxing,\r\n    biao1s,\r\n    biao1ss,\r\n    zhuzhuangtu,\r\n    zhuzhuangtu1\r\n  },\r\n  data() {\r\n    return {\r\n      loading: true,\r\n      loading1: true,\r\n      loading2: true,\r\n      loading3: true,\r\n      loading4: true,\r\n      loading5: true,\r\n      //svg: 'el-icon-loading' ,// 或者自定义 SVG 图标\r\n      sbnum: 723,\r\n      chartDatazz: {\r\n        title: [\"已领用\", \"未领用\"],\r\n        xAxisdata1: [80, 80, 97, 53, 95, 70, 88],\r\n        xAxisdata2: [100, 100, 100, 100, 100, 100, 100],\r\n        yAxisdata: [\"一月\", \"二月\", \"三月\", \"四月\", \"五月\", \"六月\", \"七月\"],\r\n      },\r\n      chartDatazz1: {\r\n        title: [\"已领用\", \"未领用\"],\r\n        xAxisdata1: [80, 80, 97, 53, 95, 70, 88],\r\n        xAxisdata2: [100, 100, 100, 100, 100, 100, 100],\r\n        yAxisdata: [\"一月\", \"二月\", \"三月\", \"四月\", \"五月\", \"六月\", \"七月\"],\r\n      },\r\n      chartData: {\r\n        value: [1321, 18582, 651],\r\n        legend: [\r\n          \"校外人员\",\r\n          \"校内人员\",\r\n          \"管理员\",\r\n        ],\r\n      },\r\n      chartData1: {\r\n        title: [\"已领用\", \"未领用\"],\r\n        xAxisdata: [\"20日\", \"21日\", \"22日\", \"23日\", \"25日\", \"24日\",],\r\n        yAxisdata1: [4, 7, 5, 9, 6, 5],\r\n        yAxisdata2: [4, 7, 5, 9, 6, 5],\r\n      },\r\n      chartData2: [\r\n        {\r\n          name: \"正在使用\",\r\n          value: 271,\r\n        },\r\n        {\r\n          name: \"待机中\",\r\n          value: 452,\r\n        }],\r\n      chartData3: [\r\n        {\r\n          name: \"正在使用\",\r\n          value: 271,\r\n        },\r\n        {\r\n          name: \"待机中\",\r\n          value: 452,\r\n\r\n        },\r\n        {\r\n          name: \"故障\",\r\n          value: 21,\r\n\r\n        }],\r\n      isshow: true,\r\n      options: [\r\n        {\r\n          value: \"总览\",\r\n          label: \"总览\",\r\n        },\r\n        {\r\n          value: \"能耗分析\",\r\n          label: \"能耗分析\",\r\n        },\r\n        {\r\n          value: \"能流分析\",\r\n          label: \"能流分析\",\r\n        },\r\n        {\r\n          value: \"设备状态\",\r\n          label: \"设备状态\",\r\n        },\r\n        {\r\n          value: \"一键抄表\",\r\n          label: \"一键抄表\",\r\n        },\r\n        {\r\n          value: \"费用管理\",\r\n          label: \"费用管理\",\r\n        },\r\n        {\r\n          value: \"碳排放管理\",\r\n          label: \"碳排放管理\",\r\n        },\r\n      ],\r\n      selectvalue2: \"B3\",\r\n      options2: [\r\n        {\r\n          value: \"B3\",\r\n          label: \"B3\",\r\n        },\r\n      ],\r\n      selectvalue3: \"B3\",\r\n      options3: [\r\n        {\r\n          value: \"B3\",\r\n          label: \"B3\",\r\n        },\r\n      ],\r\n      selectvalue1: \"B3\",\r\n      options1: [\r\n        {\r\n          value: \"B3\",\r\n          label: \"B3\",\r\n        },\r\n      ],\r\n      selectvalue3: \"B1栋\",\r\n      options4: [\r\n        {\r\n          value: \"B1栋\",\r\n          label: \"B1栋\",\r\n        },\r\n        {\r\n          value: \"B2栋\",\r\n          label: \"B2栋\",\r\n        },\r\n        {\r\n          value: \"B3栋\",\r\n          label: \"B3栋\",\r\n        },\r\n        {\r\n          value: \"B4栋\",\r\n          label: \"B4栋\",\r\n        },\r\n        {\r\n          value: \"W1栋\",\r\n          label: \"W1栋\",\r\n        },\r\n        {\r\n          value: \"W2栋\",\r\n          label: \"W2栋\",\r\n        },\r\n      ],\r\n      selectvalue4: \"B1栋\",\r\n      optionData: [\r\n        {\r\n          name: \"一级告警\",\r\n          value: 16,\r\n          itemStyle: { color: \"#EB6877\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"二级告警\",\r\n          value: 27,\r\n          itemStyle: { color: \"#F8B551\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"三级告警\",\r\n          value: 17,\r\n          itemStyle: { color: \"#B954E8\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"四级告警\",\r\n          value: 40,\r\n          itemStyle: { color: \"#0284F0\", opacity: 0.9 },\r\n        },\r\n      ],\r\n      optionData1: [\r\n        {\r\n          name: \"一级告警\",\r\n          value: 16,\r\n          itemStyle: { color: \"#EB6877\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"二级告警\",\r\n          value: 27,\r\n          itemStyle: { color: \"#F8B551\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"三级告警\",\r\n          value: 17,\r\n          itemStyle: { color: \"#B954E8\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"四级告警\",\r\n          value: 40,\r\n          itemStyle: { color: \"#0284F0\", opacity: 0.9 },\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  computed: {\r\n    loading() {\r\n      return this.loading1 || this.loading2 || this.loading3 || this.loading4 || this.loading5;\r\n    },\r\n    // 使用 Vuex 的 getters 获取持久化的数据\r\n    equipmentRank() {\r\n      return this.$store.getters[\"equipment/equipmentRank\"];\r\n    },\r\n    yiqiStatus() {\r\n      return this.$store.getters[\"equipment/yiqiStatus\"];\r\n    },\r\n    userDistribution() {\r\n      return this.$store.getters[\"equipment/userDistribution\"];\r\n    },\r\n    testStatistics() {\r\n      return this.$store.getters[\"equipment/testStatistics\"];\r\n    },\r\n    topUsers() {\r\n      return this.$store.getters[\"equipment/topUsers\"];\r\n    },\r\n  },\r\n  mounted() {\r\n\r\n    this.$store.dispatch('equipment/fetchEquipmentRank');\r\n    this.$store.dispatch('equipment/getdata2');\r\n    this.$store.dispatch('equipment/getdata3');\r\n    this.$store.dispatch('equipment/getdata4');\r\n    this.$store.dispatch('equipment/getdata5');\r\n\r\n    if (!this.equipmentRank.length) {\r\n      this.$store.dispatch('equipment/fetchEquipmentRank'); // 如果没有缓存，获取数据\r\n    }\r\n    if (!this.yiqiStatus.length) {\r\n      this.$store.dispatch('equipment/getdata2'); // 如果没有缓存，获取数据\r\n    }\r\n    if (!this.userDistribution.length) {\r\n      this.$store.dispatch('equipment/getdata3'); // 如果没有缓存，获取数据\r\n    }\r\n    if (!this.testStatistics.length) {\r\n      this.$store.dispatch('equipment/getdata4'); // 如果没有缓存，获取数据\r\n    }\r\n    if (!this.topUsers.length) {\r\n      this.$store.dispatch('equipment/getdata5'); // 如果没有缓存，获取数据\r\n    }\r\n    setInterval(() => {\r\n      this.$store.dispatch('equipment/fetchEquipmentRank');\r\n      this.$store.dispatch('equipment/getdata2');\r\n      this.$store.dispatch('equipment/getdata3');\r\n      this.$store.dispatch('equipment/getdata4');\r\n      this.$store.dispatch('equipment/getdata5');\r\n    }, 36000000);\r\n    // this.getdata1()\r\n    this.getdata2()\r\n    // this.getdata3()\r\n    // this.getdata4()\r\n    // this.getdata5()\r\n    // setInterval(() => {\r\n    //   this.getdata1()\r\n    //   this.getdata2()\r\n    //   this.getdata3()\r\n    //   this.getdata4()\r\n    //   this.getdata5()\r\n    // }, 10000);\r\n\r\n  },\r\n\r\n  methods: {\r\n\r\n    async getdata1() {  //仪器使用排行\r\n      try {\r\n        const response = await api.post('', {\r\n\r\n          \"method\": \"equipment/time_rank\",\r\n          \"params\": {\r\n            \"num\": 10,\r\n            \"start\": 1704038400,\r\n            \"end\": 1735660800\r\n          }\r\n\r\n        }, { headers });\r\n        // 检查是否成功拿到 token\r\n        if (response.data) {\r\n          console.log('仪器使用排行:', response.data);\r\n          let data = response.data.response\r\n          // 提取 names 和 times\r\n          const names = data.map(item => item.name).reverse();\r\n          const times = data.map(item => item.time).reverse();\r\n          this.chartDatazz1.yAxisdata = names\r\n          this.chartDatazz1.xAxisdata1 = times\r\n          this.loading1 = false\r\n        }\r\n      } catch (error) {\r\n        console.error('登录失败:', error);\r\n      }\r\n    },\r\n\r\n    async getdata2() {  //仪器使用情况\r\n      try {\r\n        const response = await api.post('', {\r\n          \"method\": \"equipment/getSummaryInfo\",\r\n          \"params\": {}\r\n        }, { headers });\r\n        // 检查是否成功拿到 token\r\n        if (response.data) {\r\n          this.sbnum = response.data.response.controlCount,\r\n            console.log('仪器使用情况:', response.data.response);\r\n\r\n\r\n        }\r\n\r\n      } catch (error) {\r\n        console.error('登录失败:', error);\r\n      }\r\n    },\r\n    async getdata3() {  //人员分布情况\r\n      try {\r\n        const response = await api.post('', {\r\n          \"method\": \"summarize/userStatus\",\r\n          \"params\": {}\r\n        }, { headers });\r\n        // 检查是否成功拿到 token\r\n        if (response.data) {\r\n          console.log('人员分布情况:', response.data);\r\n          this.chartData = {\r\n            value: [response.data.response.outer, response.data.response.inner, response.data.response.incharge],\r\n            legend: [\r\n              \"校外人员\",\r\n              \"校内人员\",\r\n              \"管理员\",\r\n            ],\r\n          }\r\n          this.loading3 = false\r\n        }\r\n      } catch (error) {\r\n        console.error('登录失败:', error);\r\n      }\r\n    },\r\n    async getdata4() {  //课题测试情况\r\n\r\n      try {\r\n        const response = await api.post('', {\r\n          \"method\": \"summarize/labStatus\",\r\n          \"params\": {}\r\n        }, { headers });\r\n        // 检查是否成功拿到 token\r\n        if (response) {\r\n          console.log('课题测试情况:', response.data.response);\r\n          this.chartData3 = [\r\n            {\r\n              name: \"总课题数\",\r\n              value: response.data.response.project,\r\n            },\r\n            {\r\n              name: \"课题数\",\r\n              value: response.data.response.lab,\r\n\r\n            },\r\n            {\r\n              name: \"测试数\",\r\n              value: response.data.response.test,\r\n\r\n            }]\r\n          this.loading4 = false\r\n        }\r\n      } catch (error) {\r\n        console.error('登录失败:', error);\r\n      }\r\n    },\r\n    async getdata5() {  //用户排行\r\n      try {\r\n        const response = await api.post('', {\r\n          \"method\": \"eq_reserv/getTopUsers\",\r\n          \"params\": {\r\n            \"num\": 9,\r\n            \"year\": 2024\r\n          }\r\n        }, { headers });\r\n        // 检查是否成功拿到 token\r\n        if (response.data) {\r\n          console.log('用户排行:', response.data.response);\r\n          let data = response.data.response\r\n          // 提取 names 和 times\r\n          const names = data.map(item => item.name).reverse();\r\n          const times = data.map(item => item.time).reverse();\r\n          this.chartDatazz.yAxisdata = names\r\n          this.chartDatazz.xAxisdata1 = times\r\n        }\r\n        this.loading5 = false\r\n      } catch (error) {\r\n        console.error('登录失败:', error);\r\n      }\r\n    },\r\n    anniu() {\r\n      this.isshow = false;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.zhuzhuangtu {\r\n  margin-top: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.zhuzhuangtu1 {\r\n  margin-top: -26px;\r\n\r\n}\r\n\r\n.all {\r\n  display: flex;\r\n  flex-direction: row;\r\n  margin-top: 5px;\r\n\r\n  .zong {\r\n    display: flex;\r\n    flex-direction: row;\r\n    margin-top: 10px;\r\n\r\n    .echart1,\r\n    .echart2 {\r\n      flex: 1;\r\n\r\n      .center {\r\n        margin-top: -24px;\r\n        font-family: \"Source Han Sans SC\", sans-serif;\r\n        font-weight: 400;\r\n        font-size: 17px;\r\n        color: #00ffb6;\r\n        text-align: center;\r\n        margin-bottom: 10px;\r\n      }\r\n\r\n      .btn {\r\n        width: 133px;\r\n        height: 31px;\r\n        border: 1px solid #2d6cb0;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        font-family: \"Source Han Sans SC\", sans-serif;\r\n        font-weight: bold;\r\n        font-size: 15px;\r\n        color: #ffffff;\r\n        border-radius: 30px;\r\n        margin-left: 7%;\r\n      }\r\n    }\r\n  }\r\n\r\n  .ltitle1 {\r\n    margin-top: 10px;\r\n  }\r\n\r\n  .ltitle11 {\r\n    margin-top: 30px;\r\n    position: relative;\r\n  }\r\n\r\n  .dayi {\r\n    position: absolute;\r\n    top: 42px;\r\n    left: 68px;\r\n    z-index: 20;\r\n    font-size: 22px;\r\n    color: #fff;\r\n    text-align: center;\r\n\r\n    span:nth-child(2) {\r\n      font-size: 32px;\r\n    }\r\n  }\r\n\r\n  .line1 {\r\n    width: 2px;\r\n    height: 823px;\r\n    opacity: 0.64;\r\n    background-color: #204964;\r\n  }\r\n\r\n  .all1 {\r\n    flex: 462;\r\n\r\n    .nenghao {\r\n      width: 227px;\r\n      height: 173px;\r\n      background: url(\"../../assets/image/nenghao.png\");\r\n      background-size: 100% 100%;\r\n      margin-left: 120px;\r\n      margin-top: 21px;\r\n      text-align: center;\r\n      font-family: Alibaba PuHuiTi;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #ffffff;\r\n      line-height: 213px;\r\n    }\r\n\r\n    .nhp {\r\n      text-align: center;\r\n      font-family: Alibaba PuHuiTi;\r\n      font-weight: 500;\r\n      font-size: 30px;\r\n      color: #2cc1ff;\r\n      margin-top: 8px;\r\n    }\r\n\r\n    .nh {\r\n      margin-left: 24px;\r\n      margin-top: 5px;\r\n      width: 423px;\r\n      height: 93px;\r\n      border: 1px solid #364d5a;\r\n      background-size: 100% 100%;\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      padding-left: 42px;\r\n\r\n      .nhimg {\r\n        width: 96.6px;\r\n        height: 70px;\r\n      }\r\n\r\n      .nhtit {\r\n        width: 148px;\r\n        margin-left: 10px;\r\n        margin-top: 10px;\r\n\r\n        .p11 {\r\n          font-family: PangMenZhengDao;\r\n          font-weight: 400;\r\n          font-size: 30px;\r\n          color: #7acfff;\r\n        }\r\n\r\n        .p12 {\r\n          font-family: PangMenZhengDao;\r\n          font-weight: 400;\r\n          font-size: 30px;\r\n          color: #ffa170;\r\n        }\r\n\r\n        .p2 {\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 400;\r\n          font-size: 17px;\r\n          color: #ffffff;\r\n        }\r\n      }\r\n\r\n      .nhtit1 {\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: center;\r\n        margin-left: 35px;\r\n\r\n        .nhimg1 {\r\n          width: 16px;\r\n          height: 20px;\r\n        }\r\n\r\n        .pp1 {\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 400;\r\n          font-size: 17px;\r\n          color: #0df29b;\r\n        }\r\n\r\n        .pp2 {\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 400;\r\n          font-size: 17px;\r\n          color: #ffa170;\r\n        }\r\n      }\r\n\r\n      .nht {\r\n        margin-top: 10px;\r\n        display: flex;\r\n        flex-direction: column;\r\n\r\n        .pp {\r\n          margin-left: 35px;\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 400;\r\n          font-size: 17px;\r\n\r\n          color: #cccccc;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .all2 {\r\n    margin-left: 38px;\r\n    flex: 667;\r\n    display: flex;\r\n    flex-direction: column;\r\n\r\n    .shinei {\r\n      .itemshei {\r\n        display: flex;\r\n        justify-content: space-around;\r\n\r\n        .nenghaos {\r\n          width: 227px;\r\n          height: 173px;\r\n          background: url(\"../../assets/image/nenghao.png\");\r\n          background-size: 100% 100%;\r\n          text-align: center;\r\n          margin-left: 10px;\r\n          margin-top: 23px;\r\n          text-align: center;\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 400;\r\n          font-size: 14px;\r\n          color: #ffffff;\r\n          line-height: 144px;\r\n        }\r\n\r\n        .nhps {\r\n          text-align: center;\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 500;\r\n          font-size: 21px;\r\n          color: #2cc1ff;\r\n          margin-top: 8px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .all3 {\r\n    flex: 668;\r\n    margin-left: 45px;\r\n  }\r\n}\r\n\r\n.shinei {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.shuantitle {\r\n  width: 100%;\r\n  display: flex;\r\n  margin-top: 10px;\r\n\r\n  .title {\r\n    width: 95%;\r\n    background: url(\"../../assets/image/title.png\");\r\n    background-size: 100% 100%;\r\n\r\n    height: 25px;\r\n    font-family: Source Han Sans SC;\r\n    font-weight: 400;\r\n    font-size: 25px;\r\n    color: #ffffff;\r\n    text-shadow: 2px 2px 3px rgba(0, 0, 0, 0.47);\r\n    font-style: italic;\r\n    text-align: left;\r\n    line-height: 4px;\r\n    padding-left: 33px;\r\n  }\r\n}\r\n\r\n.nenghao {\r\n  width: 167px;\r\n  height: 113px;\r\n  background: url(\"../../assets/image/nenghao.png\");\r\n  background-size: 100% 100%;\r\n  text-align: center;\r\n  margin-left: 83px;\r\n  margin-top: 63px;\r\n  text-align: center;\r\n  font-family: Alibaba PuHuiTi;\r\n  font-weight: 400;\r\n  font-size: 14px;\r\n  color: #ffffff;\r\n  line-height: 144px;\r\n}\r\n\r\n.nhp {\r\n  text-align: center;\r\n  font-family: Alibaba PuHuiTi;\r\n  font-weight: 500;\r\n  font-size: 21px;\r\n  color: #2cc1ff;\r\n  margin-top: 8px;\r\n}\r\n\r\n.contents {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  background: url(\"../../assets/image/zichanbeijin.png\");\r\n  background-size: 100% 100%;\r\n  width: 1863px;\r\n  height: 868px;\r\n  z-index: 99999;\r\n  padding-left: 34px;\r\n  padding-right: 22px;\r\n  padding-top: 21px;\r\n}\r\n\r\n.toubu {\r\n  width: 100%;\r\n\r\n  position: relative;\r\n}\r\n\r\n.el-select {\r\n  margin-top: -1px;\r\n  margin-left: 10px;\r\n  background: #00203d;\r\n  border-radius: 3px;\r\n  border: 1px solid #3e89db;\r\n\r\n  /deep/.el-select__wrapper {\r\n    background: #00203d !important;\r\n    box-shadow: none;\r\n  }\r\n\r\n  /deep/.el-select__wrapper .is-hovering:not {\r\n    box-shadow: none;\r\n  }\r\n\r\n  /deep/.el-select__wrapper:hover {\r\n    box-shadow: none;\r\n  }\r\n\r\n  /deep/.el-select__placeholder.is-transparent {\r\n    color: #2cc1ff;\r\n  }\r\n\r\n  /deep/.el-select__placeholder {\r\n    color: #2cc1ff;\r\n  }\r\n\r\n  /deep/.el-select-dropdown__item.is-hovering {\r\n    background-color: #2cc1ff !important;\r\n  }\r\n}\r\n\r\n.sp {\r\n  margin-top: -5px;\r\n  margin-left: 12px;\r\n  font-family: Alibaba PuHuiTi;\r\n  font-weight: bold;\r\n  font-size: 21px;\r\n  color: #2cc1ff;\r\n}\r\n\r\n.img1sss {\r\n  cursor: pointer;\r\n  width: 15px;\r\n  height: 15px;\r\n}\r\n</style>"]}]}