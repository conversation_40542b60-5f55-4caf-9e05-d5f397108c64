{"remainingRequest": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\views\\index.vue?vue&type=template&id=a83bd3b0&scoped=true", "dependencies": [{"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\views\\index.vue", "mtime": 1751448706819}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdj4KICAgIDxjb21wb25lbnQgOmlzPSJjb21wb25lbnRUYWciIDp0YWJsZWRhdGE9InRhYmxlZGF0YSIgOnplbmd0aWltZz0iemVuZ3RpaW1nIiBAZmF0aGVyTWV0aG9kZGQ9ImZhdGhlck1ldGhvZGRkIgogICAgICByZWY9ImNoaWxkIj48L2NvbXBvbmVudD4KICAgIDxkaXYgY2xhc3M9ImNvbnRhaW5lciIgdi1pZj0iaXNzaG93Ij4KICAgICAgPGRpdiBjbGFzcz0ibGVmdC1wYW5lbCIgOmNsYXNzPSJ7CiAgICAgICAgJ2xlZnQtcGFuZWwtYWN0aXZlJzogc2hvd2RoLAogICAgICAgICduby1hbmltYXRpb24nOiBub0FuaW1hdGlvbiwKICAgICAgICAnbGVmdC1wYW5lbC1hY3RpdmUxJzogc2hvd2RoMSwKICAgICAgfSI+CiAgICAgICAgPFRpdGxlIGNsYXNzPSJsdGl0bGUxIiB0aXQ9IuW5s+WPsOS7i+e7jSIgOmlzc2hvdz0idHJ1ZSI+CiAgICAgICAgICA8ZGl2IGNsYXNzPSJ6b25naGUiPgogICAgICAgICAgICA8ZGl2IGNsYXNzPSJib3hzdHkiIHYtZm9yPSJpdGVtIGluIHRhYmxlbGlzdCIgOmtleT0iaXRlbSI+CiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ibWlhbmppIj4KICAgICAgICAgICAgICAgIDxpbWcgOnNyYz0iaXRlbS5pbWciIGNsYXNzPSJpbWciIGFsdD0iIiAvPgogICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0id2VuemkiPgogICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJ0b3AiPnt7IGl0ZW0ubmFtZSB9fTwvZGl2PgogICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJib3R0b20iPnt7IGl0ZW0udmFsdWUgfX08L2Rpdj4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgIDwvZGl2PgogICAgICAgICAgPGRpdiBjbGFzcz0iZ29uZ25lbmciIHYtZm9yPSJpdGVtIGluIHdlbnppbGlzdCIgOmtleT0iaXRlbSI+CiAgICAgICAgICAgIDxkaXYgc3R5bGU9ImRpc3BsYXk6IGZsZXg7IGFsaWduLWl0ZW1zOiBjZW50ZXIiPgogICAgICAgICAgICAgIDxkaXYgY2xhc3M9Inl1YW4iPjwvZGl2PgogICAgICAgICAgICAgIDxkaXYgY2xhc3M9Im5hbWUiPnt7IGl0ZW0ubmFtZSB9fTwvZGl2PgogICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgPGRpdiBjbGFzcz0idmFsdWUiPnt7IGl0ZW0udmFsdWUgfX08L2Rpdj4KICAgICAgICAgIDwvZGl2PgogICAgICAgIDwvVGl0bGU+CiAgICAgICAgPFRpdGxlIGNsYXNzPSJsdGl0bGUxIiB0aXQ9IuaKpeitpue7n+iuoSIgOmlzc2hvdz0idHJ1ZSI+CiAgICAgICAgICA8ZGl2IGNsYXNzPSJib3h4eCI+CiAgICAgICAgICAgIDxodWFueGluZyA6d2FybmluZ0RhdGE9Indhcm5pbmdTdGF0cyI+PC9odWFueGluZz4KICAgICAgICAgIDwvZGl2PgogICAgICAgIDwvVGl0bGU+CiAgICAgICAgPCEtLSA8VGl0bGUgY2xhc3M9Imx0aXRsZTEiIHRpdD0i6IO96ICX57uf6K6hIiA6aXNzaG93PSJ0cnVlIj4KICAgICAgICAgIDxkaXYgY2xhc3M9ImJveCI+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9InpvbmdoZXF0Ij4KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJsZWZ0MSI+CiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJtaWFuamkiIHYtZm9yPSJpdGVtIGluIGRpYW5saXN0IiA6a2V5PSJpdGVtIj4KICAgICAgICAgICAgICAgICAgPGltZyA6c3JjPSJpdGVtLmltZyIgY2xhc3M9ImltZyIgYWx0PSIiIC8+CiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9IndlbnppcyI+CiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0idG9wIj4xMjM0NjwvZGl2PgogICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImJvdHRvbSI+CiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPSIKICAgICAgICAgICAgICAgICAgICAgICAgICBmb250LWZhbWlseTogQWxpYmFiYSBQdUh1aVRpOwogICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA0MDA7CiAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udC1zaXplOiAxM3B4OwogICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAjM2JhMWY0OwogICAgICAgICAgICAgICAgICAgICAgICAiPgogICAgICAgICAgICAgICAgICAgICAgICDmnKzml6UKICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgICAgICAgL0t3aAogICAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgIDxiaWFvMT48L2JpYW8xPgogICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgIDwvZGl2PgogICAgICAgIDwvVGl0bGU+IC0tPgogICAgICA8L2Rpdj4KCiAgICAgIDwhLS0g5by55Ye65bGCIC0tPgogICAgICA8ZGl2IGNsYXNzPSJwb3B1cC1vdmVybGF5IiB2LWlmPSJzaG93UG9wdXAiIEBjbGljaz0iY2xvc2VQb3B1cCI+CiAgICAgICAgPGRpdiBjbGFzcz0icG9wdXAtY29udGVudCIgQGNsaWNrLnN0b3A+CiAgICAgICAgICA8ZGl2IGNsYXNzPSJwb3B1cC1oZWFkZXIiPgogICAgICAgICAgICA8ZGl2IGNsYXNzPSJwb3B1cC10aXRsZSI+5Y6G5Y+y6aKE57qm6K+m5oOFPC9kaXY+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9ImNsb3NlLWJ0biIgQGNsaWNrPSJjbG9zZVBvcHVwIj7DlzwvZGl2PgogICAgICAgICAgPC9kaXY+CiAgICAgICAgICA8ZGl2IGNsYXNzPSJwb3B1cC1ib2R5IiB2LWxvYWRpbmc9ImlzQWxsTG9hZGluZyIgZWxlbWVudC1sb2FkaW5nLXRleHQ9IuWKoOi9veS4rS4uLiIKICAgICAgICAgICAgZWxlbWVudC1sb2FkaW5nLWJhY2tncm91bmQ9InJnYmEoIDI4LCAzNywgNTYsIDAuOCkiPgogICAgICAgICAgICA8ZGl2IGNsYXNzPSJwb3B1cC10YWJsZSI+CiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0idGFibGUtaGVhZGVyIj4KICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImNvbC0yIj7ku6rlmajlkI08L2Rpdj4KICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImNvbC0yIj7nu4Tnu4fmnLrmnoQ8L2Rpdj4KICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImNvbC0yIj7ku6rlmajkvY3nva48L2Rpdj4KICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImNvbC0yIj7pooTnuqbml7bplb88L2Rpdj4KICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImNvbC0xIj7pooTnuqbkuro8L2Rpdj4KICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImNvbC0xIj7pooTnuqbnirbmgIE8L2Rpdj4KICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICA8dGVtcGxhdGUgdi1pZj0idGFibGVEYXRhc3MgJiYgdGFibGVEYXRhc3MubGVuZ3RoID4gMCI+CiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJ0YWJsZS1yb3ciIHYtZm9yPSJpdGVtIGluIHRhYmxlRGF0YXNzIiA6a2V5PSJpdGVtIj4KICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iY29sLTIiIDp0aXRsZT0iaXRlbS5uYW1lIj57eyBpdGVtLm5hbWUgfX08L2Rpdj4KICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iY29sLTIiIDp0aXRsZT0iaXRlbS5lcXVpcG1lbnRfZ3JvdXAiPgogICAgICAgICAgICAgICAgICAgIHt7IGl0ZW0uZXF1aXBtZW50X2dyb3VwIH19CiAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJjb2wtMiIgOnRpdGxlPSJpdGVtLmVxdWlwbWVudF9sb2NhdGlvbiI+CiAgICAgICAgICAgICAgICAgICAge3sgaXRlbS5lcXVpcG1lbnRfbG9jYXRpb24gfX0KICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImNvbC0yIj57eyBpdGVtLmR1cmF0aW9uMSB9fTwvZGl2PgogICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJjb2wtMSI+e3sgaXRlbS5yb29tTnVtYmVyIH19PC9kaXY+CiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImNvbC0xIj57eyBpdGVtLnN0YXR1cyB9fTwvZGl2PgogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgPC90ZW1wbGF0ZT4KICAgICAgICAgICAgICA8dGVtcGxhdGUgdi1lbHNlPgogICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iZW1wdHktbWVzc2FnZSI+5pqC5peg6aKE57qm6K6w5b2VPC9kaXY+CiAgICAgICAgICAgICAgPC90ZW1wbGF0ZT4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgIDwhLS0g5YiG6aG157uE5Lu2IC0tPgogICAgICAgICAgICA8ZGl2IGNsYXNzPSJwYWdpbmF0aW9uLWNvbnRhaW5lciI+CiAgICAgICAgICAgICAgPGVsLXBhZ2luYXRpb24gOmN1cnJlbnQtcGFnZT0iY3VycmVudFBhZ2UiIDpwYWdlLXNpemU9InBhZ2VTaXplIiA6cGFnZS1zaXplcz0icGFnZVNpemVzIiA6dG90YWw9InRvdGFsIgogICAgICAgICAgICAgICAgQHNpemUtY2hhbmdlPSJoYW5kbGVTaXplQ2hhbmdlIiBAY3VycmVudC1jaGFuZ2U9ImhhbmRsZUN1cnJlbnRDaGFuZ2UiCiAgICAgICAgICAgICAgICBsYXlvdXQ9InRvdGFsLCBzaXplcywgcHJldiwgcGFnZXIsIG5leHQsIGp1bXBlciIgYmFja2dyb3VuZCAvPgogICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgIDwvZGl2PgogICAgICAgIDwvZGl2PgogICAgICA8L2Rpdj4KICAgICAgPCEtLSDlj7PkvqflhoXlrrkgLS0+CgogICAgICA8ZGl2IGNsYXNzPSJyaWdodC1wYW5lbCIgOmNsYXNzPSJ7CiAgICAgICAgJ3JpZ2h0LXBhbmVsLWFjdGl2ZSc6IHNob3dkaCwKICAgICAgICAnbm8tYW5pbWF0aW9uJzogbm9BbmltYXRpb24sCiAgICAgICAgJ3JpZ2h0LXBhbmVsLWFjdGl2ZTEnOiBzaG93ZGgxLAogICAgICB9Ij4KICAgICAgICA8VGl0bGUxIGNsYXNzPSJydGl0bGUiIHRpdD0i5LuK5pel6aKE57qmIj4KICAgICAgICAgIDxkaXYgY2xhc3M9ImJveHN3cSIgQGNsaWNrPSJzaG93TGFyZ2VUYWJsZSI+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9InRpdGxlaW1ncyI+CiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iYmd1Ij4KICAgICAgICAgICAgICAgIDxkaXY+6aKE57qm5Luq5Zmo5pWwPC9kaXY+CiAgICAgICAgICAgICAgICA8ZGl2Pnt7IHl5dG90YWwgfX08L2Rpdj4KICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJiZ3UxIj4KICAgICAgICAgICAgICAgIDxkaXY+6aKE57qm5oC75pWwPC9kaXY+CiAgICAgICAgICAgICAgICA8ZGl2Pnt7IHRvZGF5UmVzZXJ2YXRpb25zLmxlbmd0aCB9fTwvZGl2PgogICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgPGRpdiBjbGFzcz0idGl0bGVzcyI+CiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iaXRlbTEiPuS7quWZqOWQjTwvZGl2PgogICAgICAgICAgICAgIDxkaXYgY2xhc3M9Iml0ZW0xIj7pooTnuqbml7bplb88L2Rpdj4KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJpdGVtIj7pooTnuqbkuro8L2Rpdj4KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJpdGVtMSI+6aKE57qm54q25oCBPC9kaXY+CiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8ZGl2IGNsYXNzPSJ0aXRsZXNzY29udGVudHMiIHYtbG9hZGluZz0iaXNMb2FkaW5nIiBlbGVtZW50LWxvYWRpbmctdGV4dD0i5Yqg6L295LitLi4uIgogICAgICAgICAgICAgIGVsZW1lbnQtbG9hZGluZy1zcGlubmVyPSJlbC1pY29uLWxvYWRpbmciIGVsZW1lbnQtbG9hZGluZy1iYWNrZ3JvdW5kPSJyZ2JhKDAsIDAsIDAsIDAuOCkiPgogICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImNvbnRlbnRzIiB2LWZvcj0iaXRlbSBpbiB0b2RheVJlc2VydmF0aW9ucyIgOmtleT0iaXRlbSI+CiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJpdGVtMSIgOnRpdGxlPSJpdGVtLm5hbWUiPnt7IGl0ZW0ubmFtZSB9fTwvZGl2PgogICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iaXRlbTEiPnt7IGl0ZW0uZHVyYXRpb24gfX08L2Rpdj4KICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9Iml0ZW0iPnt7IGl0ZW0ucm9vbU51bWJlciB9fTwvZGl2PgogICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iaXRlbTEiPnt7IGl0ZW0uc3RhdHVzIH19PC9kaXY+CiAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgPGRpdiB2LWlmPSIhdG9kYXlSZXNlcnZhdGlvbnMubGVuZ3RoIiBjbGFzcz0iZW1wdHktbWVzc2FnZSI+CiAgICAgICAgICAgICAgICDmmoLml6DpooTnuqborrDlvZUKICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICA8L2Rpdj4KICAgICAgICA8L1RpdGxlMT4KCiAgICAgICAgPFRpdGxlMSBjbGFzcz0icnRpdGxlIiB0aXQ9IuS7quWZqOeKtuaAgSI+CiAgICAgICAgICA8ZGl2IGNsYXNzPSJodWFuZ3hpbmciPgogICAgICAgICAgICA8U3lzdGVtRGV0ZT48L1N5c3RlbURldGU+CiAgICAgICAgICAgIDwhLS0gPFN5c3RlbURldGU+PC9TeXN0ZW1EZXRlPiAtLT4KICAgICAgICAgIDwvZGl2PgogICAgICAgIDwvVGl0bGUxPgogICAgICAgIDxUaXRsZTEgY2xhc3M9InJ0aXRsZSIgdGl0PSLlvILluLjot5/ouKrlpITnkIYiIDppc3Nob3c9InRydWUiPgogICAgICAgICAgPGRpdiBjbGFzcz0iYm94eHhzIiBAY2xpY2s9Im9wZW5iaigpIj4KICAgICAgICAgICAgPGRpdiBjbGFzcz0icWwtY2VudGVyIj4KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJxbC1Cb3giPgogICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0icWwtYm94Ij4KICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ibGVmdF9xbCI+CiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ieXVhbiBzdGF0dXMiIHN0eWxlPSJjb2xvcjogIzVjOWRlZSI+PC9kaXY+CiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0icHAiPuacquS/ruWkjTwvZGl2PgogICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0icWwtYm94MSIgc3R5bGU9ImNvbG9yOiAjYjkzODUxIj4KICAgICAgICAgICAgICAgICAge3sgdW5maXhlZENvdW50IH19CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJxbC1Cb3giPgogICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0icWwtYm94Ij4KICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ibGVmdF9xbCI+CiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ieXVhbjEgc3RhdHVzIiBzdHlsZT0iY29sb3I6ICM4OWY2YzEiPjwvZGl2PgogICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9InBwIj7lt7Lkv67lpI08L2Rpdj4KICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9InFsLWJveDEgc3RhdHVzIiBzdHlsZT0iY29sb3I6ICM4OWY2YzEiPgogICAgICAgICAgICAgICAgICB7eyBmaXhlZENvdW50IH19CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9InVuZml4ZWQtd2FybmluZ3MiPgogICAgICAgICAgICAgIDwhLS0g5pyq5L+u5aSN6K2m5ZGK5YiX6KGoIC0tPgogICAgICAgICAgICAgIDxkaXYgdi1mb3I9Iih3YXJuaW5nLCBpbmRleCkgaW4gd2FybmluZ0RhdGEudW5maXhlZCIgOmtleT0iJ3VuZml4ZWQtJyArIGluZGV4IiBjbGFzcz0id2FybmluZzEyIj4KICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImluZm8iPgogICAgICAgICAgICAgICAgICA8ZGl2PgogICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9InpvbmdkdWFuIj4KICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9Inl1YW4iIHN0eWxlPSJiYWNrZ3JvdW5kLWNvbG9yOiAjYjkzODUxIj48L2Rpdj4KICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImNqaHVsaXpob25nIiBzdHlsZT0iY29sb3I6ICNiOTM4NTEiPgogICAgICAgICAgICAgICAgICAgICAgICDmnKrkv67lpI0KICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzPSJpbmZvMiI+e3sgd2FybmluZy53YXJuaW5nQ2F0ZWdvcnkgfX08L3A+CiAgICAgICAgICAgICAgICAgIDwvZGl2PgoKICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iaW5mbzEiPgogICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzPSJ0aW1lIj57eyBmb3JtYXREYXRlKHdhcm5pbmcuY3JlYXRlZEF0KSB9fTwvcD4KICAgICAgICAgICAgICAgICAgICA8cCBjbGFzcz0ibG9jYXRpb24iPnt7IHdhcm5pbmcuZXJyTXNnIH19PC9wPgogICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgICAgPHAgY2xhc3M9ImluZm8yIj4KICAgICAgICAgICAgICAgICAgICB7eyB3YXJuaW5nLmRldmljZU5hbWUgfX0KICAgICAgICAgICAgICAgICAgPC9wPgogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8IS0tIOW3suS/ruWkjeitpuWRiuWIl+ihqCAtLT4KICAgICAgICAgICAgPCEtLSA8ZGl2IHYtZm9yPSIod2FybmluZywgaW5kZXgpIGluIHdhcm5pbmdEYXRhLmZpeGVkIiA6a2V5PSInZml4ZWQtJytpbmRleCIgY2xhc3M9Indhcm5pbmcxMiI+CiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iaW5mbyI+CiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJ6b25nZHVhbiI+CiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9Inl1YW4iIHN0eWxlPSJiYWNrZ3JvdW5kLWNvbG9yOiAjNjRmOGJiIj48L2Rpdj4KICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iY2podWxpemhvbmciIHN0eWxlPSJjb2xvcjogIzY0ZjhiYiI+5bey5aSE55CGPC9kaXY+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImluZm8xIj4KICAgICAgICAgICAgICAgICAgPHAgY2xhc3M9InRpbWUiPnt7IGZvcm1hdERhdGUod2FybmluZy5jcmVhdGVkQXQpIH19PC9wPgogICAgICAgICAgICAgICAgICA8cCBjbGFzcz0ibG9jYXRpb24iPnt7IHdhcm5pbmcuZXJyTXNnIH19PC9wPgogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICA8cCBjbGFzcz0iaW5mbzIiIHN0eWxlPSJjb2xvcjogIzY0ZjhiYiIgQGNsaWNrPSJvcGVuYmooKSI+e3sgd2FybmluZy5kZXZpY2VOYW1lIH19PC9wPgogICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8L2Rpdj4gLS0+CiAgICAgICAgICA8L2Rpdj4KICAgICAgICA8L1RpdGxlMT4KICAgICAgPC9kaXY+CiAgICA8L2Rpdj4KICAgIDx0YWJsZTIgQGNsb3NlPSJjbG9zZXRhbiIgdi1pZj0ib3BlbnRhYmxlMiI+PC90YWJsZTI+CiAgICA8IS0tIDx0YWJsZS0yIGNsYXNzPSJ0YWJsZTIiIEBjbG9zZT0iY2xvc2V0YW4iIHYtaWY9Im9wZW50YWJsZTIiPjwvdGFibGUtMj4gLS0+CiAgICA8IS0tIDxkaXYKICAgICAgY2xhc3M9ImNlbnRlcl9jb250YWluZXIiCiAgICAgIDpjbGFzcz0iewogICAgICAgICdyaWdodC1wYW5lbC1hY3RpdmUxMSc6IHNob3dkaCwKICAgICAgICAnbm8tYW5pbWF0aW9uJzogbm9BbmltYXRpb24sCiAgICAgICAgJ3JpZ2h0LXBhbmVsLWFjdGl2ZTEyJzogc2hvd2RoMSwKICAgICAgfSIKICAgID4KICAgICAgPGltZwogICAgICAgIGNsYXNzPSJidG4iCiAgICAgICAgc3JjPSIuLi9hc3NldHMvaW1hZ2Uvc2hhbmcucG5nIgogICAgICAgIEBjbGljaz0ic2Nyb2xsVXAiCiAgICAgICAgYWx0PSLlkJHkuIoiCiAgICAgIC8+CiAgICAgIDxkaXYgY2xhc3M9ImNvbnRlbnQiIHJlZj0iY29udGVudCI+CiAgICAgICAgPGRpdgogICAgICAgICAgOmNsYXNzPSJhY3RpdmVmID09IGluZGV4ID8gJ2l0ZW1hJyA6ICdpdGVtJyIKICAgICAgICAgIHYtZm9yPSIoaXRlbSwgaW5kZXgpIGluIHJlc0l0ZW1zIgogICAgICAgICAgOmtleT0iaW5kZXgiCiAgICAgICAgICBAY2xpY2s9InN3aXRjaGFjdGl2ZWYoaXRlbSwgaW5kZXgpIgogICAgICAgICAgQG1vdXNlb3Zlcj0iaG92ZXJlZFJvb20gPSBpdGVtIgogICAgICAgICAgQG1vdXNlbGVhdmU9ImhvdmVyZWRSb29tID0gbnVsbCIKICAgICAgICA+CiAgICAgICAgICB7ewogICAgICAgICAgICBpbmRleCA9PT0gMAogICAgICAgICAgICAgID8gdGl0bGUgKyAiRi0iICsgIuaVtOS9kyIKICAgICAgICAgICAgICA6IHRpdGxlICsgIkYtIiArIChpbmRleCA8IDEwID8gIjEwIiArIGluZGV4IDogIjEiICsgaW5kZXgpCiAgICAgICAgICB9fQoKICAgICAgICAgIDxkaXYgY2xhc3M9InRvb2x0aXAiIHYtaWY9ImhvdmVyZWRSb29tID09PSBpdGVtIj57eyBpdGVtLm5hbWUgfX08L2Rpdj4KICAgICAgICA8L2Rpdj4KICAgICAgPC9kaXY+CiAgICAgIDxpbWcKICAgICAgICBjbGFzcz0iYnRuIgogICAgICAgIHNyYz0iLi4vYXNzZXRzL2ltYWdlL3hpYS5wbmciCiAgICAgICAgQGNsaWNrPSJzY3JvbGxEb3duIgogICAgICAgIGFsdD0i5ZCR5LiLIgogICAgICAvPgogICAgPC9kaXY+CiAgICA8ZGl2CiAgICAgIEBjbGljaz0icmV0dXJuaG9tZSgpIgogICAgICBjbGFzcz0icmV0dXJuIgogICAgICA6Y2xhc3M9InsKICAgICAgICAncmlnaHQtcGFuZWwtYWN0aXZlMTEnOiBzaG93ZGgsCiAgICAgICAgJ25vLWFuaW1hdGlvbic6IG5vQW5pbWF0aW9uLAogICAgICAgICdyaWdodC1wYW5lbC1hY3RpdmUxMic6IHNob3dkaDEsCiAgICAgIH0iCiAgICA+CiAgICAgIOi/lOWbngogICAgPC9kaXY+IC0tPgogIDwvZGl2Pgo="}, {"version": 3, "sources": ["E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\views\\index.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACzC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACrC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3C,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC7C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1C,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClD,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACtD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;kBACzC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACjB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC3B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACjB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;0BACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;0BAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;0BAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;0BACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAChB,CAAC;wBACD,CAAC;sBACH,CAAC,CAAC,CAAC,CAAC,CAAC;sBACL,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC,CAAC,CAAC;kBACP,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnD,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC9B,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;gBACpD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC5D,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC1D,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC9C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;kBAC3B,CAAC,CAAC,CAAC,CAAC,CAAC;kBACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACjD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;kBAC9B,CAAC,CAAC,CAAC,CAAC,CAAC;kBACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC5C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC7C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3C,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACjE,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;MAEZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACzB,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1C,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC5B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC7E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cACzF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC1D,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3C,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzD,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAER,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACjB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACzC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACnB,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACjB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACjB,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC9F,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACf,CAAC,CAAC,CAAC,CAAC;oBACF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACzD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC7C,CAAC,CAAC;sBACJ,CAAC,CAAC,CAAC,CAAC,CAAC;oBACP,CAAC,CAAC,CAAC,CAAC,CAAC;oBACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;kBACnD,CAAC,CAAC,CAAC,CAAC,CAAC;;kBAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC7C,CAAC,CAAC,CAAC,CAAC,CAAC;kBACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;kBACzB,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC7F,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACzD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC1D,CAAC,CAAC,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;kBACtD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7C,CAAC,CAAC,CAAC,CAAC,CAAC;gBACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cACvF,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC9E,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjC,CAAC;IACH;MACE,CAAC,CAAC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACT,CAAC;MACD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACjC;UACE,CAAC;YACC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;cACR,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;cACpB,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7D,CAAC;;UAED,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACT,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjC,CAAC;IACH;MACE,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "E:/svn/***********-0186-SFTianjinUniversityH5/src/src/views/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div>\n    <component :is=\"componentTag\" :tabledata=\"tabledata\" :zengtiimg=\"zengtiimg\" @fatherMethoddd=\"fatherMethoddd\"\n      ref=\"child\"></component>\n    <div class=\"container\" v-if=\"isshow\">\n      <div class=\"left-panel\" :class=\"{\n        'left-panel-active': showdh,\n        'no-animation': noAnimation,\n        'left-panel-active1': showdh1,\n      }\">\n        <Title class=\"ltitle1\" tit=\"平台介绍\" :isshow=\"true\">\n          <div class=\"zonghe\">\n            <div class=\"boxsty\" v-for=\"item in tablelist\" :key=\"item\">\n              <div class=\"mianji\">\n                <img :src=\"item.img\" class=\"img\" alt=\"\" />\n                <div class=\"wenzi\">\n                  <div class=\"top\">{{ item.name }}</div>\n                  <div class=\"bottom\">{{ item.value }}</div>\n                </div>\n              </div>\n            </div>\n          </div>\n          <div class=\"gongneng\" v-for=\"item in wenzilist\" :key=\"item\">\n            <div style=\"display: flex; align-items: center\">\n              <div class=\"yuan\"></div>\n              <div class=\"name\">{{ item.name }}</div>\n            </div>\n            <div class=\"value\">{{ item.value }}</div>\n          </div>\n        </Title>\n        <Title class=\"ltitle1\" tit=\"报警统计\" :isshow=\"true\">\n          <div class=\"boxxx\">\n            <huanxing :warningData=\"warningStats\"></huanxing>\n          </div>\n        </Title>\n        <!-- <Title class=\"ltitle1\" tit=\"能耗统计\" :isshow=\"true\">\n          <div class=\"box\">\n            <div class=\"zongheqt\">\n              <div class=\"left1\">\n                <div class=\"mianji\" v-for=\"item in dianlist\" :key=\"item\">\n                  <img :src=\"item.img\" class=\"img\" alt=\"\" />\n                  <div class=\"wenzis\">\n                    <div class=\"top\">12346</div>\n                    <div class=\"bottom\">\n                      <div style=\"\n                          font-family: Alibaba PuHuiTi;\n                          font-weight: 400;\n                          font-size: 13px;\n                          color: #3ba1f4;\n                        \">\n                        本日\n                      </div>\n                      /Kwh\n                    </div>\n                  </div>\n                </div>\n              </div>\n              <biao1></biao1>\n            </div>\n          </div>\n        </Title> -->\n      </div>\n\n      <!-- 弹出层 -->\n      <div class=\"popup-overlay\" v-if=\"showPopup\" @click=\"closePopup\">\n        <div class=\"popup-content\" @click.stop>\n          <div class=\"popup-header\">\n            <div class=\"popup-title\">历史预约详情</div>\n            <div class=\"close-btn\" @click=\"closePopup\">×</div>\n          </div>\n          <div class=\"popup-body\" v-loading=\"isAllLoading\" element-loading-text=\"加载中...\"\n            element-loading-background=\"rgba( 28, 37, 56, 0.8)\">\n            <div class=\"popup-table\">\n              <div class=\"table-header\">\n                <div class=\"col-2\">仪器名</div>\n                <div class=\"col-2\">组织机构</div>\n                <div class=\"col-2\">仪器位置</div>\n                <div class=\"col-2\">预约时长</div>\n                <div class=\"col-1\">预约人</div>\n                <div class=\"col-1\">预约状态</div>\n              </div>\n              <template v-if=\"tableDatass && tableDatass.length > 0\">\n                <div class=\"table-row\" v-for=\"item in tableDatass\" :key=\"item\">\n                  <div class=\"col-2\" :title=\"item.name\">{{ item.name }}</div>\n                  <div class=\"col-2\" :title=\"item.equipment_group\">\n                    {{ item.equipment_group }}\n                  </div>\n                  <div class=\"col-2\" :title=\"item.equipment_location\">\n                    {{ item.equipment_location }}\n                  </div>\n                  <div class=\"col-2\">{{ item.duration1 }}</div>\n                  <div class=\"col-1\">{{ item.roomNumber }}</div>\n                  <div class=\"col-1\">{{ item.status }}</div>\n                </div>\n              </template>\n              <template v-else>\n                <div class=\"empty-message\">暂无预约记录</div>\n              </template>\n            </div>\n            <!-- 分页组件 -->\n            <div class=\"pagination-container\">\n              <el-pagination :current-page=\"currentPage\" :page-size=\"pageSize\" :page-sizes=\"pageSizes\" :total=\"total\"\n                @size-change=\"handleSizeChange\" @current-change=\"handleCurrentChange\"\n                layout=\"total, sizes, prev, pager, next, jumper\" background />\n            </div>\n          </div>\n        </div>\n      </div>\n      <!-- 右侧内容 -->\n\n      <div class=\"right-panel\" :class=\"{\n        'right-panel-active': showdh,\n        'no-animation': noAnimation,\n        'right-panel-active1': showdh1,\n      }\">\n        <Title1 class=\"rtitle\" tit=\"今日预约\">\n          <div class=\"boxswq\" @click=\"showLargeTable\">\n            <div class=\"titleimgs\">\n              <div class=\"bgu\">\n                <div>预约仪器数</div>\n                <div>{{ yytotal }}</div>\n              </div>\n              <div class=\"bgu1\">\n                <div>预约总数</div>\n                <div>{{ todayReservations.length }}</div>\n              </div>\n            </div>\n            <div class=\"titless\">\n              <div class=\"item1\">仪器名</div>\n              <div class=\"item1\">预约时长</div>\n              <div class=\"item\">预约人</div>\n              <div class=\"item1\">预约状态</div>\n            </div>\n            <div class=\"titlesscontents\" v-loading=\"isLoading\" element-loading-text=\"加载中...\"\n              element-loading-spinner=\"el-icon-loading\" element-loading-background=\"rgba(0, 0, 0, 0.8)\">\n              <div class=\"contents\" v-for=\"item in todayReservations\" :key=\"item\">\n                <div class=\"item1\" :title=\"item.name\">{{ item.name }}</div>\n                <div class=\"item1\">{{ item.duration }}</div>\n                <div class=\"item\">{{ item.roomNumber }}</div>\n                <div class=\"item1\">{{ item.status }}</div>\n              </div>\n              <div v-if=\"!todayReservations.length\" class=\"empty-message\">\n                暂无预约记录\n              </div>\n            </div>\n          </div>\n        </Title1>\n\n        <Title1 class=\"rtitle\" tit=\"仪器状态\">\n          <div class=\"huangxing\">\n            <SystemDete></SystemDete>\n            <!-- <SystemDete></SystemDete> -->\n          </div>\n        </Title1>\n        <Title1 class=\"rtitle\" tit=\"异常跟踪处理\" :isshow=\"true\">\n          <div class=\"boxxxs\" @click=\"openbj()\">\n            <div class=\"ql-center\">\n              <div class=\"ql-Box\">\n                <div class=\"ql-box\">\n                  <div class=\"left_ql\">\n                    <div class=\"yuan status\" style=\"color: #5c9dee\"></div>\n                    <div class=\"pp\">未修复</div>\n                  </div>\n                </div>\n                <div class=\"ql-box1\" style=\"color: #b93851\">\n                  {{ unfixedCount }}\n                </div>\n              </div>\n              <div class=\"ql-Box\">\n                <div class=\"ql-box\">\n                  <div class=\"left_ql\">\n                    <div class=\"yuan1 status\" style=\"color: #89f6c1\"></div>\n                    <div class=\"pp\">已修复</div>\n                  </div>\n                </div>\n                <div class=\"ql-box1 status\" style=\"color: #89f6c1\">\n                  {{ fixedCount }}\n                </div>\n              </div>\n            </div>\n            <div class=\"unfixed-warnings\">\n              <!-- 未修复警告列表 -->\n              <div v-for=\"(warning, index) in warningData.unfixed\" :key=\"'unfixed-' + index\" class=\"warning12\">\n                <div class=\"info\">\n                  <div>\n                    <div class=\"zongduan\">\n                      <div class=\"yuan\" style=\"background-color: #b93851\"></div>\n                      <div class=\"cjhulizhong\" style=\"color: #b93851\">\n                        未修复\n                      </div>\n                    </div>\n                    <p class=\"info2\">{{ warning.warningCategory }}</p>\n                  </div>\n\n                  <div class=\"info1\">\n                    <p class=\"time\">{{ formatDate(warning.createdAt) }}</p>\n                    <p class=\"location\">{{ warning.errMsg }}</p>\n                  </div>\n                  <p class=\"info2\">\n                    {{ warning.deviceName }}\n                  </p>\n                </div>\n              </div>\n            </div>\n            <!-- 已修复警告列表 -->\n            <!-- <div v-for=\"(warning, index) in warningData.fixed\" :key=\"'fixed-'+index\" class=\"warning12\">\n              <div class=\"info\">\n                <div class=\"zongduan\">\n                  <div class=\"yuan\" style=\"background-color: #64f8bb\"></div>\n                  <div class=\"cjhulizhong\" style=\"color: #64f8bb\">已处理</div>\n                </div>\n                <div class=\"info1\">\n                  <p class=\"time\">{{ formatDate(warning.createdAt) }}</p>\n                  <p class=\"location\">{{ warning.errMsg }}</p>\n                </div>\n                <p class=\"info2\" style=\"color: #64f8bb\" @click=\"openbj()\">{{ warning.deviceName }}</p>\n              </div>\n            </div> -->\n          </div>\n        </Title1>\n      </div>\n    </div>\n    <table2 @close=\"closetan\" v-if=\"opentable2\"></table2>\n    <!-- <table-2 class=\"table2\" @close=\"closetan\" v-if=\"opentable2\"></table-2> -->\n    <!-- <div\n      class=\"center_container\"\n      :class=\"{\n        'right-panel-active11': showdh,\n        'no-animation': noAnimation,\n        'right-panel-active12': showdh1,\n      }\"\n    >\n      <img\n        class=\"btn\"\n        src=\"../assets/image/shang.png\"\n        @click=\"scrollUp\"\n        alt=\"向上\"\n      />\n      <div class=\"content\" ref=\"content\">\n        <div\n          :class=\"activef == index ? 'itema' : 'item'\"\n          v-for=\"(item, index) in resItems\"\n          :key=\"index\"\n          @click=\"switchactivef(item, index)\"\n          @mouseover=\"hoveredRoom = item\"\n          @mouseleave=\"hoveredRoom = null\"\n        >\n          {{\n            index === 0\n              ? title + \"F-\" + \"整体\"\n              : title + \"F-\" + (index < 10 ? \"10\" + index : \"1\" + index)\n          }}\n\n          <div class=\"tooltip\" v-if=\"hoveredRoom === item\">{{ item.name }}</div>\n        </div>\n      </div>\n      <img\n        class=\"btn\"\n        src=\"../assets/image/xia.png\"\n        @click=\"scrollDown\"\n        alt=\"向下\"\n      />\n    </div>\n    <div\n      @click=\"returnhome()\"\n      class=\"return\"\n      :class=\"{\n        'right-panel-active11': showdh,\n        'no-animation': noAnimation,\n        'right-panel-active12': showdh1,\n      }\"\n    >\n      返回\n    </div> -->\n  </div>\n</template>\n\n<script>\n// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）\n// 例如：import 《组件名称》 from '《组件路径》';\nimport huanxing from \"@/components/echarts/huanxing.vue\";\nimport zhexian from \"@/components/echarts/zhexian.vue\";\nimport zhexian1 from \"@/components/echarts/zhexian1.vue\";\nimport SystemDete from \"@/components/echarts/SystemDete.vue\";\nimport echarts1 from \"@/components/echarts/bingjifang/echarts3.vue\";\nimport echarts2 from \"@/components/echarts/bingjifang/echarts5.vue\";\nimport table2 from \"@/components/common/table2.vue\";\nimport shuangxiang from \"@/components/echarts/shuangxiang.vue\";\nimport shebei from \"@/views/shebei.vue\";\nimport { resourceDeviceList } from \"@/api/admin.js\";\nimport biao1 from \"../components/echarts/biao1.vue\";\nimport biao1ss from \"../components/echarts/biao1ss.vue\";\nimport axios from \"axios\";\nimport {\n  getDeviceData,\n  getDevicedetails,\n  getDeviceWarningList,\n} from \"@/api/device.js\";\n\n// resourceDeviceList\nexport default {\n  // import引入的组件需要注入到对象中才能使用\n  components: {\n    table2,\n    huanxing,\n    zhexian,\n    zhexian1,\n    SystemDete,\n    echarts1,\n    echarts2,\n    shuangxiang,\n    shebei,\n    biao1ss,\n    biao1,\n  },\n  props: [\"title\", \"resItems\"],\n  data() {\n    // 这里存放数据\n    return {\n      jlURL,\n      dstime,\n      responseData: null, // 存储返回的数据\n      error: null, // 存储错误信息\n      todayReservations: [], // 今日预约数据\n      allReservations: [], // 所有预约数据\n      allTableData: [], // 存储所有数据\n      allTableData1: [], // 存储所有数据\n      currentPage: 1, // 当前页码\n      pageSize: 10, // 每页显示条数\n      total: 0, // 总数据条数\n      yytotal: 0,\n      pageSizes: [10, 20, 50, 100], // 每页显示条数选项\n      opentable2: false,\n      hoveredRoom: null,\n      scrollPosition: 0,\n      flag: true,\n      localtitle: \"\",\n      dianlist: [\n        {\n          name: \"总用地面积\",\n          value: \"57874.1㎡\",\n          img: require(\"../assets/image/ri.png\"),\n        },\n        {\n          name: \"总建筑面积\",\n          value: \"7802.54㎡\",\n          img: require(\"../assets/image/zhou.png\"),\n        },\n        {\n          name: \"地上建筑面积\",\n          value: \"14085㎡\",\n          img: require(\"../assets/image/yue.png\"),\n        },\n      ],\n      tablelist: [\n        {\n          name: \"总用地面积\",\n          value: \"4423.8㎡\",\n          img: require(\"../assets/image/mianji1.png\"),\n        },\n        {\n          name: \"总建筑面积\",\n          value: \"16845㎡\",\n          img: require(\"../assets/image/mianji2.png\"),\n        },\n        {\n          name: \"地上建筑面积\",\n          value: \"14085㎡\",\n          img: require(\"../assets/image/mianji3.png\"),\n        },\n        {\n          name: \"地下建筑面积\",\n          value: \"2760㎡\",\n          img: require(\"../assets/image/mianji4.png\"),\n        },\n      ],\n      wenzilist: [\n        {\n          name: \"平台概述\",\n          value: \"天津大学大型仪器平台是天津大学批准设立的校级公共技术服务平台，聚焦兼顾多学科需求的保障学校基础能力，促进跨平台和交叉新兴学科能力的建设,以'专管共用'的管理模式，为科学研究提供高质量的开放式测试服务，开展仪器设备创新性功能开发与技术研发。\",\n        },\n      ],\n\n      activef: 0,\n      isshow: true,\n      isactive: 0,\n      tabledata: [],\n      zengtiimg: \"\",\n      lrdata: [\n        {\n          title1: \"温度\",\n          title2: \"22℃\",\n          title3: \"2022-04-01 12:00:00\",\n        },\n      ],\n      deviceTypes: \"CQQ11\",\n      activeTab: \"today\",\n      botlist: [\n        { name: \"总览\", code: \"\" },\n        { name: \"设备列表\", code: \"\" },\n        {\n          name: \"环境温湿度\",\n          code: \"CGQ11\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/wensiduchuanganqi.png\",\n        },\n        {\n          name: \"防爆温湿度\",\n          code: \"CGQ10\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/fangbao.png\",\n        },\n        {\n          name: \"冰箱状态\",\n          code: \"LRY193\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/bingxiang.png\",\n        },\n        {\n          name: \"培养箱状态\",\n          code: \"CGQ13\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/bingxiang.png\",\n        },\n        {\n          name: \"乙炔气体\",\n          code: \"CGQ7\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/fangbao.png\",\n        },\n        {\n          name: \"环境CO2\",\n          code: \"CGQ9\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/huanjingco2.png\",\n        },\n        {\n          name: \"环境O2\",\n          code: \"CGQ3\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/huanjingco2.png\",\n        },\n        {\n          name: \"甲烷气体\",\n          code: \"CGQ8\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/jiawan.png\",\n        },\n        {\n          name: \"房间压差\",\n          code: \"CGQ2\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/kqyc.png\",\n        },\n      ],\n      listst: [\n        {\n          name: \"广东质检中诚认证有限公司到中广...\",\n        },\n        { name: \"材料科学、化学工程及医药研发成...\" },\n        { name: \"酒精代谢能力检测：您的酒量，基...\" },\n        { name: \"植酸检测方法及作用\" },\n        { name: \"兔拉检测：行业中芯片造假技术...\" },\n        { name: \"酒精代谢能力检测：您的酒量，基...\" },\n      ],\n      showdh: true,\n      showdh1: false,\n      noAnimation: false,\n      localTitle: this.title, // 初始化本地数据属性\n      nhlist: [\n        {\n          title: \"供气压力\",\n          status: \"0.3Mpa\",\n          unit: \"℃\",\n        },\n\n        {\n          title: \"供气流量\",\n          status: \"6M3/min\",\n          unit: \"㎡\",\n        },\n        {\n          title: \"露点温度\",\n          status: \"6℃\",\n          unit: \"℃\",\n        },\n        {\n          title: \"含氧量\",\n          status: \"6PPM\",\n          unit: \"㎡\",\n        },\n      ],\n      warnlist1: [\n        {\n          type: 1,\n          time: \"视频监控报警-3号楼-3F-101\",\n          value: \"\",\n          name: \"2024-06-16   12:34:09\",\n        },\n        {\n          type: 2,\n          time: \"视频监控报警-3号楼-3F-101\",\n          value: \"\",\n          name: \"2024-06-16   12:34:09\",\n        },\n        {\n          type: 3,\n          name: \"2024-06-16   12:34:09\",\n          value: \"\",\n          time: \"视频监控报警-3号楼-3F-101\",\n        },\n      ],\n      isButton2Active: false,\n      status: \"巡检中\",\n      status1: \"已完成\",\n      status2: \"待巡检\",\n      selectedIndex: 0,\n      componentTag: \"\",\n      dectid: \"\",\n      showPopup: false,\n      isLoading: false, // 今日预约加载状态\n      isAllLoading: false, // 全部预约加载状态\n      warningData: {\n        unfixed: [],\n        fixed: [],\n        unfixedtotal: 0,\n        fixedtotal: 0,\n      },\n      baseURL: \"https://tjdx.yuankong.org.cn\",\n      token: localStorage.getItem(\"token\") || \"\",\n      warningStats: [],\n    };\n  },\n  // 计算属性类似于data概念\n  computed: {\n    formattedTitle() {\n      return {\n        title: `${this.localTitle}F实验室介绍`,\n        img: require(`../assets/img/floor/1Fbig.png`),\n      };\n    },\n    formattedTitle1() {\n      return `${this.localTitle}F实验室总览`;\n    },\n    formattedTitle2() {\n      return `实验室${this.localTitle}F环境信息`;\n    },\n    formattedTitle3() {\n      return `实验室${this.localTitle}F设备信息`;\n    },\n    formattedTitle4() {\n      return `实验室${this.localTitle}F事件详情`;\n    },\n    formatted1Title() {\n      return {\n        title: `${this.localTitle}实验室介绍`,\n        img: require(`../assets/img/floor/${this.title}Fbig.png`),\n      };\n    },\n    formatted1Title1() {\n      return `${this.localTitle}实验室总览`;\n    },\n    formatted1Title2() {\n      return `${this.localTitle}环境信息`;\n    },\n    formatted1Title3() {\n      return `${this.localTitle}设备信息`;\n    },\n    formatted1Title4() {\n      return `${this.localTitle}事件详情`;\n    },\n    unfixedCount() {\n      return this.warningData.unfixedtotal;\n    },\n    fixedCount() {\n      return this.warningData.fixedtotal;\n    },\n  },\n  // 监控data中的数据变化\n  watch: {\n    title(newVal) {\n      this.localTitle = newVal;\n    },\n    resItems(newVal) {\n      console.log(newVal);\n\n      // this.resItems = newVal;\n    },\n  },\n  // 方法集合\n  methods: {\n    // 获取当前日期的00:00:01的时间戳\n    getStartOfDayTimestamp() {\n      const now = new Date();\n      now.setHours(0, 0, 1, 0); // 设置时间为当天的 00:00:01\n      return Math.floor(now.getTime() / 1000); // 转换为 Unix 时间戳（秒）\n    },\n\n    // 获取当前日期的23:59:59的时间戳\n    getEndOfDayTimestamp() {\n      const now = new Date();\n      now.setHours(23, 59, 59, 0);\n      return Math.floor(now.getTime() / 1000);\n    },\n\n    // 格式化预约数据\n    formatReservationData(item) {\n      return {\n        name: item.equipment_name,\n        date: new Date(item.start * 1000).toLocaleDateString(),\n        date1: new Date(item.start * 1000).toLocaleString(),\n        duration: `${new Date(item.start * 1000).getHours()}:00-${new Date(\n          item.end * 1000\n        ).getHours()}:00`,\n        duration1: `${new Date(\n          item.start * 1000\n        ).toLocaleDateString()} ${new Date(\n          item.start * 1000\n        ).getHours()}:00 - ${new Date(\n          item.end * 1000\n        ).toLocaleDateString()} ${new Date(item.end * 1000).getHours()}:00`,\n        roomNumber: item.user_name,\n        status: item.is_using === \"1\" ? \"已预约\" : \"已预约\",\n        equipment_group: item.equipment_group || \"未设置\", // 添加组织机构字段\n        equipment_location: item.equipment_location || \"未设置\", // 添加仪器位置字段\n      };\n    },\n\n    // 获取今日预约数据\n    async fetchTodayReservations() {\n      this.isLoading = true;\n      const headers = {\n        clientid: \"5a298e93-158d-4e22-83cf-6ceb62e9b4f1\",\n        clientsecret: \"2c8ec39e-9887-482a-b28b-e64c496b601c\",\n      };\n\n      const requestBody = {\n        method: \"gpui/eq_reserv/reservList\",\n        params: {\n          dtstart: this.getStartOfDayTimestamp(),\n          dtend: this.getEndOfDayTimestamp(),\n          params: {\n            location: \"58\",\n            limit: [0, 10000],\n          },\n        },\n      };\n\n      try {\n        const response = await axios.post(\n          jlURL,\n          requestBody,\n          { headers }\n        );\n\n        this.todayReservations = response.data.response.map(\n          this.formatReservationData\n        );\n        // 使用去重后的仪器个数\n        this.$nextTick(() => {\n          this.yytotal = this.uniqueEquipmentCount;\n        });\n      } catch (err) {\n        console.error(\"获取今日预约数据失败:\", err);\n        this.error = err;\n      } finally {\n        this.isLoading = false;\n      }\n    },\n\n    // 获取所有预约数据\n    async fetchAllReservations() {\n      this.isAllLoading = true;\n      const headers = {\n        clientid: \"5a298e93-158d-4e22-83cf-6ceb62e9b4f1\",\n        clientsecret: \"2c8ec39e-9887-482a-b28b-e64c496b601c\",\n      };\n\n      const requestBody = {\n        method: \"gpui/eq_reserv/reservList\",\n        params: {\n          dtstart: 1704844800,\n          dtend: this.getEndOfDayTimestamp(),\n          params: {\n            location: \"58\",\n            limit: [0, 10000],\n          },\n        },\n      };\n\n      try {\n        const response = await axios.post(\n          jlURL,\n          requestBody,\n          { headers }\n        );\n\n        // 对数据进行时间倒序排序\n        const sortedData = response.data.response.sort(\n          (a, b) => b.start - a.start\n        );\n        this.allReservations = sortedData.map(this.formatReservationData);\n        this.total = this.allReservations.length;\n        this.handleCurrentChange(1);\n      } catch (err) {\n        console.error(\"获取所有预约数据失败:\", err);\n        this.error = err;\n      } finally {\n        this.isAllLoading = false;\n      }\n    },\n\n    // 显示大表格\n    async showLargeTable() {\n      this.showPopup = true;\n      // 只在没有数据或数据过期的情况下重新获取\n      if (!this.allReservations.length) {\n        await this.fetchAllReservations();\n      } else {\n        // 如果已有数据，直接更新分页\n        this.handleCurrentChange(1);\n      }\n    },\n\n    closetan() {\n      this.opentable2 = false;\n    },\n    openbj() {\n      this.opentable2 = true;\n    },\n    handleOpenDialog() {\n      console.log(1111);\n      this.$emit(\"open-bj\");\n    },\n    scrollUp() {\n      const content = this.$refs.content;\n      content.scrollTop -= 38; // 每次向上滑动25px\n    },\n    scrollDown() {\n      const content = this.$refs.content;\n      content.scrollTop += 38; // 每次向下滑动25px\n    },\n    returnhome() {\n      this.$emit(\"returnhome\");\n    },\n    async switchactivef(item, index) {\n      this.dectid = item.id;\n      const res = await resourceDeviceList({\n        resourceId: item.id,\n        deviceTypes: this.deviceTypes,\n      });\n\n      this.tabledata = res.data;\n\n      if (index) {\n        this.flag = false;\n        this.localTitle = item.roomid;\n      } else {\n        this.localTitle = this.title;\n        this.flag = true;\n      }\n      console.log(item);\n      this.activef = index;\n      // this.$emit(\"childEvent\", title, index);\n    },\n    slideUp() {\n      const contentHeight = this.$refs.content.scrollHeight;\n      if (this.position > -contentHeight + this.containerHeight) {\n        this.position -= this.step;\n        this.$refs.content.style.transform = `translateY(${this.position}px)`;\n      }\n    },\n    slideDown() {\n      if (this.position < 0) {\n        this.position += this.step;\n        this.$refs.content.style.transform = `translateY(${this.position}px)`;\n      }\n    },\n\n    //  this.dectid = item.id;\n    //     const res = await resourceDeviceList({\n    //       resourceId: item.id,\n    //       deviceTypes: this.deviceTypes,\n    //     });\n    //     console.log(res.data, \"qilei\");\n    //     this.tabledata = res.data;\n\n    async switchTab1(item, index) {\n      console.log(item.img);\n      this.zengtiimg = item.img;\n\n      this.deviceTypes = item.code;\n      const res = await resourceDeviceList({\n        resourceId: this.dectid,\n        deviceTypes: this.deviceTypes,\n      });\n\n      this.tabledata = res.data;\n\n      // this.switchactivef(item, item.code);\n      this.isactive = index;\n      if (index) {\n        this.componentTag = \"shebei\";\n        this.isshow = false;\n        this.showdh = true;\n        this.showdh1 = false;\n      } else {\n        this.componentTag = \"\";\n        this.isshow = true;\n        this.showdh = false;\n        this.showdh1 = true;\n      }\n    },\n    switchTab(tab) {\n      this.activeTab = tab;\n    },\n    qeihuan(index) {\n      console.log(index, \"123123\");\n    },\n\n    qiehuanyans(index) {\n      console.log(index, \"123123\");\n      this.currentIndex = index;\n    },\n    getClassForStatus(status) {\n      if (status === \"告警总数\") {\n        return \"completed\";\n      } else if (status === \"处理完\") {\n        return \"incomplete\";\n      } else if (status === \"未处理\") {\n        return \"warning\";\n      } else {\n        return \"default\"; // 没有匹配的状态时可以添加一个默认类\n      }\n    },\n    getClassForStatuss(status) {\n      if (status === \"告警总数\") {\n        return \"completeds\";\n      } else if (status === \"处理完\") {\n        return \"incompletes\";\n      } else if (status === \"未处理\") {\n        return \"warnings\";\n      } else {\n        return \"default\"; // 没有匹配的状态时可以添加一个默认类\n      }\n    },\n    qiehuanyans(index) {\n      console.log(index, \"123123\");\n      this.currentIndex = index;\n    },\n    oc(value) {\n      console.log(value, \"floor收到的值\");\n      this.showdh = value;\n    },\n    getClassForStatus(status) {\n      if (status === \"巡检中\") {\n        return \"completed\";\n      } else if (status === \"待巡检\") {\n        return \"incomplete\";\n      } else if (status === \"已完成\") {\n        return \"warning\";\n      } else {\n        return \"default\"; // 没有匹配的状态时可以添加一个默认类\n      }\n    },\n    getClassForStatuss(status) {\n      if (status === \"巡检中\") {\n        return \"completeds\";\n      } else if (status === \"待巡检\") {\n        return \"incompletes\";\n      } else if (status === \"已完成\") {\n        return \"warnings\";\n      } else {\n        return \"default\"; // 没有匹配的状态时可以添加一个默认类\n      }\n    },\n    closePopup() {\n      this.showPopup = false;\n    },\n    // 处理页码改变\n    handleCurrentChange(page) {\n      this.currentPage = page;\n      const start = (page - 1) * this.pageSize;\n      const end = start + this.pageSize;\n      this.tableDatass = this.allReservations.slice(start, end);\n      console.log(this.tableDatass, \"tableDatass\");\n    },\n\n    // 处理每页显示条数改变\n    handleSizeChange(size) {\n      this.pageSize = size;\n      this.handleCurrentChange(1);\n    },\n    async getWarningList(hasFixed) {\n      try {\n        // 在关键请求前检查token是否需要刷新\n        if (this.$auth && this.$auth.checkAndRefreshToken) {\n          await this.$auth.checkAndRefreshToken();\n        }\n\n        const response = await getDeviceWarningList({\n          hasFixed: hasFixed,\n        });\n\n        if (response.code === 200) {\n          if (hasFixed === \"N\") {\n            this.warningData.unfixed = response.rows;\n            this.warningData.unfixedtotal = response.total;\n          } else {\n            this.warningData.fixed = response.rows;\n            this.warningData.fixedtotal = response.total;\n          }\n        } else {\n          console.error(\"获取警告数据失败:\", response.msg);\n          // 只有在明确的认证错误时才清除token并跳转\n          if (response.code === 401) {\n            localStorage.removeItem(\"token\");\n            this.$router.push(\"/\");\n          }\n        }\n      } catch (error) {\n        console.error(\"请求警告数据出错:\", error);\n        // 请求错误时不要立即清除token，让拦截器处理\n      }\n    },\n    async fetchAllWarningData() {\n      await Promise.all([this.getWarningList(\"N\"), this.getWarningList(\"Y\")]);\n    },\n    formatDate(timestamp) {\n      const date = new Date(timestamp);\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(\n        2,\n        \"0\"\n      )}-${String(date.getDate()).padStart(2, \"0\")} ${String(\n        date.getHours()\n      ).padStart(2, \"0\")}:${String(date.getMinutes()).padStart(2, \"0\")}`;\n    },\n    async fetchWarningStats() {\n      try {\n        const res = await getDeviceWarningList({\n          pageSize: 9999,\n          currentPage: 1,\n          hasFixed: \"N\",\n        });\n\n        if (res.code === 200 && res.rows) {\n          // 定义所有可能的报警类型及其阈值（简化后的名称）\n          const allWarningTypes = {\n            压力: 14,\n            氧气: 48,\n            温度: 67,\n            湿度: 67,\n            // '气体泄漏': 50\n          };\n\n          // 统计各类型报警数量\n          const stats = {};\n          // 初始化所有报警类型的计数为0\n          Object.keys(allWarningTypes).forEach((type) => {\n            stats[type] = {\n              total: 0,\n              unresolved: 0,\n            };\n          });\n\n          // 统计实际数据（使用包含匹配）\n          res.rows.forEach((item) => {\n            // 查找匹配的报警类型（只要包含关键字就匹配）\n            const matchedType = Object.keys(allWarningTypes).find(type =>\n              item.warningCategory && item.warningCategory.includes(type)\n            );\n\n            if (matchedType) {\n              stats[matchedType].total++;\n              if (item.status === \"N\") {\n                stats[matchedType].unresolved++;\n              }\n            }\n          });\n\n          // 转换为图表所需格式\n          this.warningStats = Object.entries(stats).map(\n            ([category, count]) => ({\n              label: `${category}(${count.total}/${allWarningTypes[category]})`,\n              value: count.total,\n            })\n          );\n\n          console.log(this.warningStats, \"报警统计数据\");\n        }\n      } catch (error) {\n        console.error(\"获取报警统计数据失败:\", error);\n      }\n    },\n  },\n  // 生命周期 - 创建完成（可以访问当前this实例）\n  created() {\n    this.fetchAllWarningData();\n    // 每5分钟刷新一次数据\n    setInterval(this.fetchAllWarningData, 5 * 60 * 1000);\n  },\n  // 生命周期 - 挂载完成（可以访问DOM元素）\n  mounted() {\n    this.fetchTodayReservations();\n    this.showdh1 = true;\n    setTimeout(() => {\n      this.showdh1 = false;\n      this.noAnimation = false;\n    }, 1000);\n\n    // 定时刷新今日预约数据\n    setInterval(() => {\n      this.fetchTodayReservations();\n    }, 1000 * this.dstime);\n    ue.interface.setSliderValue = (value) => {\n      console.log(value, \"ue点击拿到的值\");\n      if (!isNaN(Number(value.data))) {\n        // let did = value.data; // 如果是数字，则赋值\n        // const result = this.sblist.filter(item => item.id == did);\n        // this.deviceId = result[0].deviceId\n        // console.log(this.deviceId, 'ue点击拿到的id');\n      }\n      // this.deid = JSON.parse(value.data) - 43846\n      // console.log(this.deid);\n      // if (!isNaN(parseInt(value.data, 10))) {\n      //   var dtdata1 = JSON.parse(JSON.stringify(this.dtdata))\n      //   console.log(dtdata1);\n      //   this.showdet = false\n      //   // this.did = dtdata1.find(item => item.id == value.data)?.deviceid;\n      //   // console.log(this.did);\n      //   var didata = JSON.parse(dtdata1.find(item => item.id == value.data).channelCode);\n      //   let data1 = dtdata1.find(item => item.id == value.data)\n      //   // this.details = didata\n      //   this.bid = data1.bid\n      //   this.fid = data1.fid\n      //   // this.hlsurl\n      //   // this.bm = data1.note\n      //   console.log(data1, 1111111);\n      //   // this.getCameraData(did)\n      // }\n    };\n    this.fetchWarningStats();\n    // 每30秒更新一次数据\n    setInterval(() => {\n      this.fetchWarningStats();\n    }, 30000);\n  },\n  beforeCreate() { }, // 生命周期 - 创建之前\n  beforeMount() { }, // 生命周期 - 挂载之前\n  beforeUpdate() { }, // 生命周期 - 更新之前\n  updated() { }, // 生命周期 - 更新之后\n  beforeUnmount() {\n    // 在组件销毁之前清除定时器\n    console.log(1111);\n  },\n\n  unmounted() {\n    console.log(2222);\n  }, // 生命周期 - 销毁之前\n  destroyed() {\n    console.log(1221);\n  }, // 生命周期 - 销毁完成\n  activated() { }, // 如果页面有keep-alive缓存功能，这个函数会触发\n};\n</script>\n<style lang=\"less\" scoped>\n.table2 {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  z-index: 99999;\n}\n\n.return {\n  position: fixed;\n  right: 373px;\n  top: 100px;\n  height: 44px;\n  width: 46px;\n  // overflow: hidden;\n  transform: translate(720%);\n  transition: transform 0.5s ease-in-out;\n\n  z-index: 999;\n  cursor: pointer;\n  text-align: center;\n  line-height: 67px;\n  font-family: Source Han Sans SC;\n  font-weight: 400;\n  font-size: 11px;\n  color: #ffffff;\n  background: url(\"../assets/image/return.png\");\n  background-size: 100% 100%;\n}\n\n.center_container {\n  position: fixed;\n  right: 359px;\n  top: 352px;\n  height: 401px;\n  width: 70px;\n  // overflow: hidden;\n  transform: translate(470%);\n  transition: transform 0.5s ease-in-out;\n  z-index: 999;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  background: url(\"../assets/image/louceng.png\");\n  background-size: 100% 100%;\n\n  .content::-webkit-scrollbar {\n    width: 0px;\n    display: none;\n    /* 设置滚动条的宽度 */\n  }\n\n  /* 设置滚动条轨道的样式 */\n  .content::-webkit-scrollbar-track {\n    background-color: #f1f1f1;\n    /* 设置滚动条轨道的背景色 */\n  }\n\n  /* 设置滚动条滑块的样式 */\n  .content::-webkit-scrollbar-thumb {\n    background-color: #888;\n    /* 设置滚动条滑块的背景色 */\n  }\n\n  /* 鼠标悬停在滚动条上时的样式 */\n  .content::-webkit-scrollbar-thumb:hover {\n    background-color: #555;\n    /* 设置鼠标悬停时滚动条滑块的背景色 */\n  }\n\n  .content {\n    height: 330px;\n    /* 内容区的总高度，视实际内容而定 */\n    transition: transform 0.5s ease;\n    overflow-y: auto;\n    text-align: center;\n\n    /* 设置滚动条的样式 */\n\n    .item {\n      cursor: pointer;\n      width: 75px;\n      height: 25px;\n      font-family: YouSheBiaoTiHei;\n      font-weight: 400;\n      font-size: 14px;\n      color: #86a6b7;\n      line-height: 25px;\n      margin-top: 12px;\n    }\n\n    .itema {\n      background: url(\"../assets/image/lcactive.png\");\n      background-size: 100% 100%;\n      cursor: pointer;\n      width: 66px;\n      height: 25px;\n      font-family: YouSheBiaoTiHei;\n      font-weight: 400;\n      font-size: 14px;\n      color: #ffffff;\n      line-height: 25px;\n      margin-top: 12px;\n    }\n\n    .tooltip {\n      position: absolute;\n      left: 80%;\n      // top: 15px;\n      background-color: #1a3867;\n      border: 1px solid #7ba6eb;\n      color: #fff;\n      padding: 5px;\n      z-index: 1;\n      white-space: nowrap;\n      font-size: 12px;\n      visibility: hidden;\n\n      opacity: 0;\n      transition: opacity 0.5s, visibility 0.5s;\n      z-index: 999;\n      font-family: Source Han Sans SC;\n    }\n\n    .item:hover .tooltip {\n      visibility: visible;\n      /* 当鼠标悬停时显示 */\n      opacity: 1;\n    }\n\n    .itema:hover .tooltip {\n      visibility: visible;\n      /* 当鼠标悬停时显示 */\n      opacity: 1;\n    }\n  }\n}\n\n.btn {\n  margin-top: 13px;\n  width: 27px;\n  height: 14px;\n  cursor: pointer;\n}\n\n.echart2 {\n  height: 180px;\n}\n\n.bott {\n  position: fixed;\n  z-index: 1;\n  bottom: 4px;\n  // left: 6px;\n  width: 1920px;\n  height: 50px;\n  display: flex;\n  flex-direction: row;\n  cursor: pointer;\n  text-align: center;\n\n  .bottit {\n    width: 153px;\n    height: 45px;\n    background: url(\"../assets/image/bot_b.png\");\n    background-size: 100% 100%;\n    margin-left: 19.496px;\n    font-family: Source Han Sans SC;\n    font-weight: bold;\n    font-size: 17px;\n    color: #ffffff;\n    line-height: 43px;\n    cursor: pointer;\n  }\n\n  .bottit1 {\n    width: 153px;\n    height: 69px;\n    background: url(\"../assets/image/bot_a.png\");\n    background-size: 100% 100%;\n    margin-left: 19.496px;\n    font-family: Source Han Sans SC;\n    font-weight: bold;\n    font-size: 17px;\n    color: #ffffff;\n    line-height: 87px;\n    cursor: pointer;\n    margin-top: -23px;\n  }\n}\n\n.container {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: space-between;\n  align-items: stretch;\n  height: 1080px;\n  text-align: center;\n\n  .left-panel {\n    position: fixed;\n    z-index: 1;\n    top: 75px;\n    left: 22px;\n    width: 387px;\n    height: 937px;\n    background-size: 100% 100%;\n    transform: translate(-122%);\n    transition: transform 0.5s ease-in-out;\n\n    .box {\n      // margin-top: 6px;\n      margin-bottom: 18px;\n      // background: url(\"../assets/image/zuoshang1.png\");\n      background-size: 100% 100%;\n      background-repeat: no-repeat;\n\n      // width: 330px;\n      // height: 404px;\n\n      .loudong {\n        margin-top: 21px;\n        margin-bottom: 25px;\n        width: 296px;\n        height: 178px;\n      }\n\n      .wenzi {\n        font-family: Microsoft YaHei;\n        font-weight: 400;\n        font-size: 10px;\n        color: #bdecf9;\n        text-align: left;\n        margin-left: 20px;\n        margin-right: 20px;\n      }\n\n      .p {\n        text-indent: 2em;\n        margin-bottom: 1em;\n        letter-spacing: 0.05em;\n      }\n    }\n  }\n\n  .left-panel-active {\n    transform: translate(0%);\n  }\n\n  .left-panel-active1 {\n    // transform: translate(0%);\n    animation: slideOut 1s ease-in-out forwards;\n  }\n\n  @keyframes slideOut {\n    100% {\n      transform: translateX(0%);\n    }\n\n    // 85% {\n    //   transform: translateX(-25%);\n    // }\n\n    // 65% {\n    //   transform: translateX(-15%);\n    // }\n\n    // 40% {\n    //   transform: translateX(-55%);\n    // }\n\n    // 30% {\n    //   transform: translateX(-40%);\n    // }\n\n    0% {\n      transform: translateX(-100%);\n    }\n  }\n\n  .rtitle {\n    margin-top: 16px;\n  }\n\n  .ltitle1 {\n    margin-top: 16px;\n  }\n\n  .right-panel {\n    position: fixed;\n    z-index: 1;\n    right: 22px;\n    width: 387px;\n    top: 75px;\n    height: 937px;\n\n    background-size: 100% 100%;\n    transform: translate(122%);\n    transition: transform 0.5s ease-in-out;\n\n    .box {\n      // margin-top: 6px;\n      margin-bottom: 18px;\n      // background: url(\"../assets/image/zuoshang1.png\");\n      background-size: 100% 100%;\n      background-repeat: no-repeat;\n      font-family: Source Han Sans SC;\n      font-weight: 400;\n      font-size: 12px;\n      color: #ffffff;\n      width: 330px;\n      height: 224px;\n\n      .titlest {\n        display: flex;\n\n        // shiyansimg.png\n        .itm {\n          cursor: pointer;\n          margin: 16px 9px 0 10px;\n          background: url(\"../assets/image/shiyansimg.png\");\n          background-size: 100% 100%;\n          background-repeat: no-repeat;\n          width: 100px;\n          height: 32px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n        }\n\n        .itms {\n          background: url(\"../assets/image/xuanzexuanzhong.png\") !important;\n          background-size: 100% 100%;\n          background-repeat: no-repeat;\n          width: 100px;\n          height: 41px !important;\n          padding-bottom: 10px;\n        }\n      }\n\n      .contentss {\n        display: flex;\n        align-items: center;\n        flex-wrap: wrap;\n        justify-content: space-around;\n        align-items: center;\n\n        .itm {\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          width: 112px;\n          height: 70px;\n          background: url(\"../assets/image/wendupng.png\");\n          background-size: 100% 100%;\n          background-repeat: no-repeat;\n          font-family: DIN;\n          font-weight: bold;\n          font-size: 22px;\n          color: #ffffff;\n\n          .danwei {\n            font-family: DIN;\n            font-weight: bold;\n            font-size: 12px;\n            color: #ffffff;\n          }\n        }\n\n        .wendyu {\n          font-family: Source Han Sans SC;\n          font-weight: 400;\n          font-size: 13px;\n          color: #ffffff;\n          margin-top: -7px;\n        }\n      }\n\n      .loudong {\n        margin-top: 21px;\n        margin-bottom: 25px;\n        width: 296px;\n        height: 178px;\n      }\n    }\n\n    .boxxxs {\n      margin-left: -10px;\n      margin-top: 1px;\n      margin-bottom: 18px;\n      // background: url(\"../assets/image/zuoshang1.png\");\n      background-size: 100% 100%;\n      background-repeat: no-repeat;\n\n      width: 366px;\n      cursor: pointer;\n      // height: 254px;\n    }\n  }\n\n  .boxxx {\n    // margin-top: 6px;\n    margin-bottom: 18px;\n    // background: url(\"../assets/image/zuoshang1.png\");\n    background-size: 100% 100%;\n    background-repeat: no-repeat;\n\n    width: 350px;\n    height: 284px;\n  }\n\n  .no-animation {\n    transition: none;\n  }\n\n  .right-panel-active {\n    transform: translate(0%);\n    // animation: slideIn 1s ease-in-out ;\n  }\n\n  .right-panel-active1 {\n    // transform: translate(0%);\n    animation: slideIn 1s ease-in-out forwards;\n  }\n\n  .right-panel-active11 {\n    transform: translate(0%) !important;\n    // animation: slideIn 1s ease-in-out ;\n  }\n\n  .right-panel-active12 {\n    // transform: translate(0%);\n    animation: slideIn 1s ease-in-out forwards !important;\n  }\n\n  @keyframes slideIn {\n    0% {\n      transform: translateX(100%);\n    }\n\n    // 30% {\n    //   transform: translateX(65%);\n    // }\n\n    // 40% {\n    //   transform: translateX(40%);\n    // }\n\n    // 65% {\n    //   transform: translateX(15%);\n    // }\n\n    // 85% {\n    //   transform: translateX(25%);\n    // }\n\n    100% {\n      transform: translateX(0%);\n    }\n  }\n\n  .completed {\n    background: #7ad0ff;\n  }\n\n  .incomplete {\n    background: #ff6041;\n  }\n\n  .warning {\n    background: #00ffc0;\n  }\n\n  .completeds {\n    color: #7ad0ff;\n  }\n\n  .incompletes {\n    color: #ff6041;\n  }\n\n  .warnings {\n    color: #00ffc0;\n  }\n}\n\n.ql-center {\n  display: flex;\n  // margin-top: 20px;\n  justify-content: space-around;\n  margin-top: 4px;\n  margin-bottom: 4px;\n\n  .ql-Box {\n    width: 46%;\n    height: 49px;\n    border: 1px solid #7ad0ff;\n    // opacity: 0.6;\n    border-radius: 2px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n\n    .ql-box1 {\n      font-family: Alibaba PuHuiTi;\n      font-weight: bold;\n      font-size: 19px;\n      color: #7ad0ff;\n    }\n\n    .ql-box {\n      display: flex;\n      // padding-left: 23px;\n      padding-right: 9px;\n      flex-direction: row;\n      justify-content: space-between;\n      align-items: center;\n      // width: 100%;\n      height: 24px;\n\n      .left_ql {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        color: #ffffff;\n\n        .yuan {\n          width: 8px;\n          height: 8px;\n          border-radius: 50%;\n          background-color: #b93851;\n          margin-right: 5px;\n        }\n\n        .yuan1 {\n          width: 8px;\n          height: 8px;\n          border-radius: 50%;\n          background-color: #84edc3;\n          margin-right: 5px;\n        }\n\n        .pp {\n          margin-left: 5px;\n          color: #fff;\n          font-size: 18px;\n        }\n      }\n\n      img {\n        height: 12px;\n        width: 8px;\n      }\n    }\n  }\n}\n\n.warn1 {\n  // background: url(\"../assets/image/warnred.png\");\n}\n\n.warn2 {\n  // background: url(\"../assets/image/warnyellow.png\");\n}\n\n.warn3 {\n  // background: url(\"../assets/image/warngreen.png\");\n}\n\n.unfixed-warnings {\n  height: 180px;\n  overflow-y: auto;\n}\n\n.warning12 {\n  background-size: 100% 100%;\n  height: 47px;\n  margin-bottom: 8px;\n\n  .info {\n    display: flex;\n    justify-content: space-between;\n    align-items: flex-start;\n    text-align: left;\n    font-size: 13px;\n    padding: 8px 12px;\n    background: rgba(25, 37, 60, 0.1);\n    border-radius: 4px;\n\n    .zongduan {\n      display: flex;\n      align-items: center;\n      min-width: 80px;\n\n      .yuan {\n        width: 8px;\n        height: 8px;\n        border-radius: 50%;\n        margin-right: 6px;\n      }\n\n      .cjhulizhong {\n        font-family: Microsoft YaHei;\n        font-weight: bold;\n        font-size: 14px;\n      }\n    }\n\n    .info1 {\n      flex: 1;\n      // margin: 0 12px;\n\n      .time {\n        font-family: Microsoft YaHei;\n        font-size: 14px;\n        color: #ffffff;\n        margin-bottom: 4px;\n      }\n\n      .location {\n        font-family: Microsoft YaHei;\n        font-size: 14px;\n        color: #ffffff;\n        white-space: nowrap;\n        overflow: hidden;\n        text-overflow: ellipsis;\n      }\n    }\n\n    .info2 {\n      cursor: pointer;\n      font-size: 14px;\n      font-family: Microsoft YaHei;\n      font-weight: 400;\n      color: #b93851;\n      // white-space: nowrap;\n      margin-left: 10px;\n    }\n  }\n}\n\n.zonghe {\n  // margin-bottom: 10px;\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center;\n  justify-content: center;\n\n  .boxsty {\n    width: 50%;\n    margin-top: 12px;\n\n    .mianji {\n      display: flex;\n      align-items: center;\n\n      .img {\n        width: 50px;\n        height: 49px;\n      }\n\n      .wenzi {\n        text-align: left;\n        margin-left: 5px;\n\n        .top {\n          // margin-bottom: 9px;\n          font-family: Alibaba PuHuiTi;\n          font-weight: 500;\n          font-size: 20px;\n          color: #ffffff;\n        }\n\n        .bottom {\n          font-family: Source Han Sans SC;\n          font-weight: 500;\n          font-size: 21px;\n          color: #59ffc4;\n        }\n      }\n    }\n  }\n}\n\n.gongneng {\n  margin-top: 12px;\n\n  display: flex;\n  flex-direction: column;\n  // align-items: center;\n  // font-family: Source Han Sans SC;\n  font-family: Alibaba PuHuiTi;\n  // font-weight: bold;\n  font-size: 22px;\n  color: #59ffc4;\n  text-align: left;\n\n  .yuan {\n    margin-right: 7px;\n    width: 16px;\n    height: 16px;\n    border-radius: 50%;\n    background-color: #85fdca;\n  }\n\n  .value {\n    font-family: Alibaba PuHuiTi;\n    font-weight: 500;\n    font-size: 20px;\n    color: #fff;\n    width: 100%;\n    margin-right: 3px;\n    text-indent: 40px;\n  }\n\n  .name {\n    // width: 58px;\n    font-size: 22px;\n  }\n}\n\n.zongheqt {\n  .left1 {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    margin-right: 20px;\n    margin-top: 7px;\n\n    .mianji {\n      background: url(\"../assets/image/zengfangti.png\");\n      background-repeat: no-repeat;\n      background-size: 100% 100%;\n      width: 106px;\n      height: 58px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n    .img {\n      width: 50px;\n      height: 49px;\n    }\n\n    .wenzis {\n      .top {\n        font-family: Alibaba PuHuiTi;\n        font-weight: 400;\n        font-size: 18px;\n        color: #ffffff;\n      }\n\n      .bottom {\n        display: flex;\n        align-items: flex-end;\n        font-family: Alibaba PuHuiTi;\n        font-weight: 400;\n        font-size: 13px;\n        color: #fff;\n        margin-left: 7px;\n      }\n    }\n  }\n}\n\n.boxswq {\n  width: 365px;\n  height: 242px;\n}\n\n.huangxing {\n  width: 359px;\n  height: 238px;\n}\n\n.cjhulizhong {\n  font-family: Microsoft YaHei;\n  font-weight: bold;\n  font-size: 14px;\n  color: #64f8bb;\n  margin-left: 8px;\n}\n\n.yuan {\n  width: 10px;\n  height: 10px;\n  background-color: #518acd;\n  border-radius: 50%;\n}\n\n.zongduan {\n  display: flex;\n  align-items: center;\n  font-size: 13px;\n}\n\n.titleimgs {\n  display: flex;\n  align-items: center;\n  justify-content: space-around;\n  margin-right: 10px;\n\n  .bgu {\n    background-color: #95871cbf !important;\n\n    // background: url(\"../assets/image/titlessimg.png\");\n    background-repeat: no-repeat;\n    background-size: 100% 100%;\n    width: 171px;\n    height: 38px;\n    font-family: Alibaba PuHuiTi;\n    font-weight: 400;\n    font-size: 22px;\n    color: #ffffff;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 0 12px 0 14px;\n  }\n\n  .bgu1 {\n    background-color: rgb(28, 128, 149) !important;\n\n    // background: url(\"../assets/image/titlessimg.png\");\n    background-repeat: no-repeat;\n    background-size: 100% 100%;\n    width: 171px;\n    height: 38px;\n    font-family: Alibaba PuHuiTi;\n    font-weight: 400;\n    font-size: 22px;\n    color: #ffffff;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 0 12px 0 14px;\n  }\n}\n\n.titlesscontents {\n  overflow: auto;\n  height: 154px;\n}\n\n/* 设置滚动条的样式 */\n.titlesscontents::-webkit-scrollbar {\n  width: 5px;\n  /* 设置滚动条的宽度 */\n}\n\n/* 设置滚动条轨道的样式 */\n.titlesscontents::-webkit-scrollbar-track {\n  background-color: #454f5d;\n  /* 设置滚动条轨道的背景色 */\n}\n\n/* 设置滚动条的样式 */\n.unfixed-warnings::-webkit-scrollbar {\n  width: 5px;\n  /* 设置滚动条的宽度 */\n}\n\n/* 设置滚动条轨道的样式 */\n.unfixed-warnings::-webkit-scrollbar-track {\n  background-color: #454f5d;\n  /* 设置滚动条轨道的背景色 */\n}\n\n/* 设置滚动条滑块的样式 */\n.unfixed-warnings::-webkit-scrollbar-thumb {\n  background-color: #f1f1f1;\n  /* 设置滚动条滑块的背景色 */\n}\n\n.pagination-container {\n  margin-top: 20px;\n  display: flex;\n  justify-content: flex-end;\n  padding: 10px;\n}\n\n:deep(.el-pagination) {\n\n  .el-pagination__total,\n  .el-pagination__sizes,\n  .el-pagination__jump,\n  .btn-prev,\n  .btn-next,\n  .el-pager li {\n    background-color: transparent;\n    color: #fff;\n  }\n\n  .el-pagination__total,\n  .el-pagination__jump {\n    color: #fff;\n  }\n\n  .el-select .el-input .el-input__inner {\n    color: #fff;\n    background-color: transparent;\n  }\n\n  .el-pager li.active {\n    background-color: #409eff;\n    color: #fff;\n  }\n}\n\n.loading-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100%;\n  width: 100%;\n}\n\n:deep(.el-loading-spinner) {\n  .el-loading-text {\n    color: #fff;\n    margin: 3px 0;\n    font-size: 14px;\n  }\n\n  .circular {\n    .path {\n      stroke: #3ba1f4;\n    }\n  }\n}\n\n/* 设置滚动条滑块的样式 */\n.titlesscontents::-webkit-scrollbar-thumb {\n  background-color: #f1f1f1;\n  /* 设置滚动条滑块的背景色 */\n}\n\n.titless {\n  margin-right: 10px;\n  width: 96%;\n  background: rgba(25, 37, 60, 0.5);\n  height: 32px;\n  margin-top: 8px;\n  display: flex;\n  font-family: Alibaba PuHuiTi;\n  font-weight: 400;\n  font-size: 17px;\n  color: #40d7ff;\n  display: flex;\n  align-items: center;\n  justify-content: space-around;\n\n  .item {\n    width: 100%;\n    flex: 1.1;\n  }\n\n  .item1 {\n    width: 100%;\n    flex: 1.9;\n  }\n}\n\n.contents {\n  border-bottom: 1px solid #3b5471;\n  margin-right: 10px;\n  width: 96%;\n  background: rgba(45, 58, 79, 0.2);\n  height: 32px;\n  display: flex;\n  font-family: Alibaba PuHuiTi;\n  font-weight: 400;\n  font-size: 15px;\n  color: #fff;\n  display: flex;\n  align-items: center;\n\n  .item {\n    width: 100%;\n    flex: 1;\n  }\n\n  .item1 {\n    width: 100%;\n    flex: 1.9;\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    position: relative;\n    cursor: pointer;\n\n    &:hover::after {\n      content: attr(title);\n      position: absolute;\n      left: 0;\n      top: 100%;\n      background: rgba(0, 0, 0, 0.8);\n      color: #fff;\n      padding: 4px 8px;\n      border-radius: 4px;\n      z-index: 999;\n      white-space: normal;\n    }\n  }\n}\n\n.contents:nth-child(odd) {\n  background: rgba(46, 61, 83, 0.4);\n}\n\n.contents:nth-child(even) {\n  background: rgba(37, 50, 69, 0.2);\n}\n\n.popup-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.7);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 9999;\n}\n\n.popup-content {\n  background: rgba(25, 37, 60, 0.95);\n  border: 1px solid #3ba1f4;\n  border-radius: 8px;\n  width: 80%;\n  max-width: 1000px;\n  max-height: 80vh;\n  overflow-y: auto;\n  padding: 20px;\n}\n\n.popup-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid #3ba1f4;\n}\n\n.popup-title {\n  font-family: Alibaba PuHuiTi;\n  font-size: 24px;\n  color: #40d7ff;\n}\n\n.close-btn {\n  font-size: 28px;\n  color: #fff;\n  cursor: pointer;\n  padding: 0 10px;\n\n  &:hover {\n    color: #40d7ff;\n  }\n}\n\n.popup-table {\n  .table-header {\n    display: flex;\n    background: rgba(25, 37, 60, 0.8);\n    padding: 12px;\n    color: #40d7ff;\n    font-family: Alibaba PuHuiTi;\n    font-size: 20px;\n\n    .col-1 {\n      flex: 1;\n      padding: 0 2px;\n      text-align: center;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n\n    .col-2 {\n      flex: 2.3;\n      padding: 0 2px;\n      text-align: center;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n  }\n\n  .table-row {\n    display: flex;\n    padding: 12px;\n    font-size: 12px;\n    border-bottom: 1px solid rgba(59, 161, 244, 0.2);\n    color: #fff;\n    font-family: Alibaba PuHuiTi;\n\n    &:hover {\n      background: rgba(59, 161, 244, 0.1);\n    }\n\n    .col-1 {\n      flex: 1;\n      padding: 0 2px;\n      text-align: center;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n\n    .col-2 {\n      flex: 2.3;\n      padding: 0 2px;\n      text-align: center;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n  }\n}\n\n.pagination-container {\n  margin-top: 20px;\n  display: flex;\n  justify-content: flex-end;\n  padding: 10px;\n}\n\n:deep(.el-pagination) {\n\n  .el-pagination__total,\n  .el-pagination__sizes,\n  .el-pagination__jump {\n    color: #fff !important;\n  }\n\n  &.is-background {\n\n    .btn-prev,\n    .btn-next,\n    .el-pager li {\n      background-color: rgba(25, 37, 60, 0.8) !important;\n      color: #fff !important;\n      border: 1px solid #3ba1f4;\n      margin: 0 3px;\n\n      &:hover {\n        color: #409eff !important;\n        background-color: rgba(37, 50, 69, 0.4) !important;\n      }\n\n      &.is-active {\n        background-color: #409eff !important;\n        color: #fff !important;\n        border-color: #409eff;\n      }\n\n      &:disabled {\n        background-color: rgba(25, 37, 60, 0.4) !important;\n        color: #606266 !important;\n      }\n    }\n  }\n}\n\n.loading-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100%;\n  width: 100%;\n}\n\n:deep(.el-loading-spinner) {\n  .el-loading-text {\n    color: #fff;\n    margin: 3px 0;\n    font-size: 14px;\n  }\n\n  .circular {\n    .path {\n      stroke: #3ba1f4;\n    }\n  }\n}\n</style>\n"]}]}