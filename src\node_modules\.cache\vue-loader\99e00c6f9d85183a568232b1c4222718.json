{"remainingRequest": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\views\\floor.vue?vue&type=style&index=0&id=75f2e462&lang=less&scoped=true", "dependencies": [{"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\views\\floor.vue", "mtime": 1751448014712}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\views\\floor.vue"], "names": [], "mappings": ";AA6iBA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;;EAEpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9B;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACd;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB;;EAEA,CAAC,CAAC,CAAC,EAAE;IACH,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAElB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACf;;IAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAClB;;IAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAClB;;IAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACvB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACd;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAChB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACnB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAChB;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACV,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxC;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1B;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAClB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7C;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAClB,CAAC,CAAC,CAAC,EAAE;MACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B;;IAEA,CAAC,EAAE,CAAC,CAAC,EAAE;IACP,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC,EAAE;;IAEH,CAAC,EAAE,CAAC,CAAC,EAAE;IACP,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC,EAAE;;IAEH,CAAC,EAAE,CAAC,CAAC,EAAE;IACP,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC,EAAE;;IAEH,CAAC,EAAE,CAAC,CAAC,EAAE;IACP,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC,EAAE;;IAEH,CAAC,EAAE;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,EAAE;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B;;IAEA,CAAC,EAAE,CAAC,CAAC,EAAE;IACP,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC,EAAE;;IAEH,CAAC,EAAE,CAAC,CAAC,EAAE;IACP,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC,EAAE;;IAEH,CAAC,EAAE,CAAC,CAAC,EAAE;IACP,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC,EAAE;;IAEH,CAAC,EAAE,CAAC,CAAC,EAAE;IACP,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC,EAAE;;IAEH,CAAC,CAAC,CAAC,EAAE;MACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEtC,CAAC,CAAC,EAAE;MACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACf;;IAEA,CAAC,CAAC,CAAC,EAAE;MACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACV,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACrB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAElB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB;MACF;;MAEA,CAAC,EAAE;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACZ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;MAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;QAER,CAAC,CAAC,CAAC,CAAC,EAAE;UACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;UAEZ,CAAC,CAAC,CAAC,CAAC,EAAE;YACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEtB,CAAC,CAAC,EAAE;cACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;cAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB;;YAEA,CAAC,CAAC,EAAE;cACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;cAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB;UACF;QACF;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB;MACF;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACV,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACf;;IAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAClB;;IAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAClB;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACvC;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5C;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,EAAE;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B;;IAEA,CAAC,EAAE,CAAC,CAAC,EAAE;IACP,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC,EAAE;;IAEH,CAAC,EAAE,CAAC,CAAC,EAAE;IACP,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC,EAAE;;IAEH,CAAC,EAAE,CAAC,CAAC,EAAE;IACP,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC,EAAE;;IAEH,CAAC,EAAE,CAAC,CAAC,EAAE;IACP,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC,EAAE;;IAEH,CAAC,CAAC,CAAC,EAAE;MACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;IAElB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEd,CAAC,CAAC,CAAC,CAAC,EAAE;UACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACpB;;QAEA,CAAC,CAAC,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjB;MACF;;MAEA,CAAC,CAAC,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACZ;IACF;EACF;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChD;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClD;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC1B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAEZ,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACnB;;MAEA,CAAC,CAAC,CAAC,EAAE;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB;EACF;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,EAAE;EACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAChB;;AAEA,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;AACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACf;;AAEA,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;AACT,CAAC,CAAC,CAAC,CAAC,EAAE;EACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACpB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACpB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACjB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACjB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACjB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5B;;AAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAClB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5B;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACf,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9B;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACP,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACpB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3B;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3B;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACnB;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAEjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACb;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACb;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACjB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACV,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEvB,CAAC,CAAC,CAAC,EAAE;IACH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB;;EAEA,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB;AACF", "file": "E:/svn/***********-0186-SFTianjinUniversityH5/src/src/views/floor.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div>\r\n    <component\r\n      :is=\"componentTag\"\r\n      @fatherMethoddd=\"fatherMethoddd\"\r\n      v-if=\"isshowwhat\"\r\n    ></component>\r\n    <div class=\"botbtn\">\r\n      <div\r\n        v-for=\"(item, index) in changeTitle\"\r\n        :key=\"index\"\r\n        :class=\"titactive == index ? 'btt1' : 'btt'\"\r\n        @click=\"changetit(index)\"\r\n      >\r\n        {{ item }}\r\n      </div>\r\n    </div>\r\n    <tedai\r\n      :ids=\"ids\"\r\n      :selectedItem=\"selectedItem\"\r\n      class=\"sbdetails\"\r\n      :zengtiimg=\"zengtiimg\"\r\n      v-if=\"isshowsss\"\r\n      @hidedetails=\"hidedetailsss\"\r\n    ></tedai>\r\n    <biaoGe\r\n      :Title=\"Title\"\r\n      @xuanze-dialog=\"xuanzedialog\"\r\n      v-if=\"isshow\"\r\n      @hidedetails=\"hidedetails\"\r\n      :tableTitle=\"tableTitle\"\r\n      :tableDataItem=\"devicedata\"\r\n    ></biaoGe>\r\n    <div class=\"container\" v-if=\"!isshowwhat\">\r\n      <div\r\n        class=\"left-panel\"\r\n        :class=\"{\r\n          'left-panel-active': showdh,\r\n          'no-animation': noAnimation,\r\n          'left-panel-active1': showdh1,\r\n        }\"\r\n      >\r\n        <Title2 @open-dialog=\"opendialog\" class=\"ltitle1\" tit=\"大仪管理\">\r\n          <el-cascader\r\n            class=\"sect\"\r\n            placeholder=\"请选择类别\"\r\n            :options=\"equipmentTags\"\r\n            :show-all-levels=\"true\"\r\n            @change=\"handleCascaderChange\"\r\n          ></el-cascader>\r\n\r\n          <el-input\r\n            class=\"el-input\"\r\n            v-model=\"input\"\r\n            placeholder=\"请输入关键字\"\r\n          ></el-input>\r\n          <img class=\"suosuo\" src=\"../assets/image/suosuo.png\" alt=\"\" />\r\n          <div class=\"box\">\r\n            <!-- <div class=\"xiaobox\">\r\n              <img class=\"siqiu\" src=\"../assets/image/shixinqiu.png\" alt=\"\" />\r\n              <div class=\"shuru\">全部设备</div>\r\n            </div> -->\r\n            <div\r\n              class=\"menu-container\"\r\n              v-loading=\"loading\"\r\n              element-loading-text=\"拼命加载中\"\r\n              element-loading-background=\"rgba(0, 0, 0, 0)\"\r\n            >\r\n              <!-- 动态生成菜单 -->\r\n              <div class=\"menu\">\r\n                <div\r\n                  v-for=\"(menu, index) in devicedata\"\r\n                  :key=\"index\"\r\n                  class=\"menu-group\"\r\n                >\r\n                  <div class=\"qiuqiu\">\r\n                    <img\r\n                      class=\"siqiu\"\r\n                      src=\"../assets/image/shixinqiu.png\"\r\n                      alt=\"\"\r\n                    />\r\n                    <div class=\"menu-item\" @click=\"toggleSubMenu(menu)\">\r\n                      {{ menu.name }}\r\n                    </div>\r\n                    <!-- <div class=\"listtypes\" @click=\"showdetails(menu)\">详情</div> -->\r\n                  </div>\r\n\r\n                  <!-- <div v-show=\"activeSubmenu === menu.id\" class=\"submenu\">\r\n                    <div v-for=\"(item, subIndex) in menu.items\" :key=\"subIndex\" class=\"submenu-item\"\r\n                      @click=\"setContent(item)\">\r\n                      <p class=\"ellipsis\" :title=\"item.name\">{{ item.name }}</p>\r\n                    </div>\r\n                  </div> -->\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <el-pagination\r\n            @size-change=\"handleSizeChange\"\r\n            hide-on-single-page=\"true\"\r\n            @current-change=\"handleCurrentChange\"\r\n            :page-size=\"16\"\r\n            :pager-count=\"4\"\r\n            layout=\"prev, pager, next,total\"\r\n            :total=\"total\"\r\n          >\r\n          </el-pagination>\r\n          <!-- shebei12.png -->\r\n        </Title2>\r\n      </div>\r\n      <!-- shebeibgc.png -->\r\n      <!-- 右侧内容 -->\r\n      <!-- 右侧内容 -->\r\n\r\n      <div\r\n        class=\"right-panel\"\r\n        :class=\"{\r\n          'right-panel-active': showdh,\r\n          'no-animation': noAnimation,\r\n          'right-panel-active1': showdh1,\r\n        }\"\r\n      ></div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）\r\n// 例如：import 《组件名称》 from '《组件路径》';\r\nimport { mapActions, mapGetters } from \"vuex\";\r\nimport component0 from \"@/views/dayi/zichan.vue\";\r\nimport huanxing from \"@/components/echarts/huanxing.vue\";\r\nimport zhexian from \"@/components/echarts/zhexian.vue\";\r\nimport zhexian1 from \"@/components/echarts/zhexian1.vue\";\r\nimport SystemDete from \"@/components/echarts/SystemDete.vue\";\r\nimport echarts1 from \"@/components/echarts/bingjifang/echarts3.vue\";\r\nimport tedai from \"@/components/common/cl_details.vue\";\r\nimport biaoGe from \"@/components/common/biaoGes.vue\";\r\nimport shuangxiang from \"@/components/echarts/shuangxiang.vue\";\r\nimport axios from \"axios\";\r\n// import details from \"@/components/common/details.vue\";\r\n// 使用环境变量设置基础 API 地址\r\nconst baseURL = process.env.VUE_APP_BASE_API || \"/lims/api\";\r\n\r\nconst api = axios.create({\r\n  baseURL,\r\n});\r\nexport default {\r\n  // import引入的组件需要注入到对象中才能使用\r\n  components: {\r\n    tedai,\r\n    huanxing,\r\n    zhexian,\r\n    zhexian1,\r\n    SystemDete,\r\n    echarts1,\r\n    shuangxiang,\r\n    biaoGe,\r\n    component0,\r\n  },\r\n  props: [\"tabledata\", \"zengtiimg\"],\r\n\r\n  data() {\r\n    // 这里存放数据\r\n    return {\r\n      loading: true,\r\n      currentPage1: 5,\r\n      isshowwhat: false,\r\n      isshowsss: false,\r\n      titactive: 0,\r\n      total: null,\r\n      changeTitle: [\"数据列表\", \"数据统计\"],\r\n      activeSubmenu: null, // 当前激活的子菜单\r\n      activeContent: null, // 当前显示的内容\r\n      newArr: [],\r\n      isshow: false,\r\n      xxxx: false,\r\n      cgqlist: [],\r\n      listtable: [],\r\n      options: [\r\n        {\r\n          value: \"zhinan\",\r\n          label: \"指南\",\r\n          children: [\r\n            {\r\n              value: \"shejiyuanze\",\r\n              label: \"设计原则\",\r\n            },\r\n            {\r\n              value: \"daohang\",\r\n              label: \"导航\",\r\n            },\r\n          ],\r\n        },\r\n      ],\r\n      devicedata: [],\r\n      input: \"\",\r\n      activeTab: \"today\",\r\n      listst: [\r\n        {\r\n          name: \"广东质检中诚认证有限公司到中广...\",\r\n        },\r\n        { name: \"材料科学、化学工程及医药研发成...\" },\r\n        { name: \"酒精代谢能力检测：您的酒量，基...\" },\r\n        { name: \"植酸检测方法及作用\" },\r\n        { name: \"兔拉检测：行业中芯片造假技术...\" },\r\n        { name: \"酒精代谢能力检测：您的酒量，基...\" },\r\n      ],\r\n      showdh: true,\r\n      showdh1: false,\r\n      noAnimation: false,\r\n      selectedItem: {\r\n        name: \"离子溅射仪\", //产品名称\r\n        imgurl: \"https://3d.dddtask.cn/enginner-xufeng/image/tianjin/lizi.png\",\r\n        location: \"北洋园校区54楼, E105\", //位置\r\n        status: \"已领用\", //仪器状态  可领用\r\n        details: [\r\n          {\r\n            name: \"产品名称\",\r\n            value: \"离子溅射仪\",\r\n          },\r\n          {\r\n            name: \"原值\",\r\n            value: \"--\",\r\n          },\r\n          {\r\n            name: \"购入时间\",\r\n            value: \"2020/09/02\",\r\n          },\r\n          {\r\n            name: \"品牌\",\r\n            value: \"--\",\r\n          },\r\n          {\r\n            name: \"生产厂家\",\r\n            value: \"--\",\r\n          },\r\n          {\r\n            name: \"供应商名称\",\r\n            value: \"--\",\r\n          },\r\n          {\r\n            name: \"供应商联系信息\",\r\n            value: \"--\",\r\n          },\r\n        ],\r\n        maintenance_records: {\r\n          maintenance_content: \"校准和系统升级\", //维护内容\r\n          date: \"2024-01-10\", //维护时间\r\n          next_maintenance_date: \"2022-01-10\", //下次维护时间\r\n        },\r\n        management_name: \"王工\",\r\n        management_contact_info: \"15698567542\",\r\n      },\r\n      tableDataItem: [],\r\n      jlURL,\r\n      Title: \"资产管理\",\r\n      tableTitle: [\r\n        { key: \"楼层\" },\r\n        { key: \"设备编号\" },\r\n        { key: \"设备名称\" },\r\n        { key: \"房间号\" },\r\n        { key: \"模型\" },\r\n        { key: \"设备状态\" },\r\n        { key: \"状态说明\" },\r\n      ],\r\n      ids: null,\r\n      nhlist: [\r\n        {\r\n          title: \"供气压力\",\r\n          status: \"0.3Mpa\",\r\n          unit: \"℃\",\r\n        },\r\n\r\n        {\r\n          title: \"供气流量\",\r\n          status: \"6M3/min\",\r\n          unit: \"㎡\",\r\n        },\r\n        {\r\n          title: \"露点温度\",\r\n          status: \"6℃\",\r\n          unit: \"℃\",\r\n        },\r\n        {\r\n          title: \"含氧量\",\r\n          status: \"6PPM\",\r\n          unit: \"㎡\",\r\n        },\r\n      ],\r\n      warnlist1: [\r\n        {\r\n          type: 1,\r\n          name: \"检测到烟雾，可能有着火灾的发生...\",\r\n          value: \"\",\r\n          time: \"09:13:59  2023-06-07\",\r\n        },\r\n        {\r\n          type: 2,\r\n          name: \"检测到烟雾，可能预示着火灾的发生..\",\r\n          value: \"\",\r\n          time: \"10:13:18  2023-06-12\",\r\n        },\r\n        {\r\n          type: 3,\r\n          name: \"实验室内检测到漏水，可能来自冷凝水..\",\r\n          value: \"\",\r\n          time: \"10:13:18  2023-06-12\",\r\n        },\r\n      ],\r\n      isButton2Active: false,\r\n      status: \"巡检中\",\r\n      status1: \"已完成\",\r\n      status2: \"待巡检\",\r\n      selectedIndex: 0,\r\n      componentTag: \"component0\",\r\n      token: \"\",\r\n    };\r\n  },\r\n  // 计算属性类似于data概念\r\n  computed: {\r\n    ...mapGetters(\"equipment\", [\"equipmentTags\"]),\r\n  },\r\n  // 监控data中的数据变化\r\n  watch: {},\r\n  // 方法集合\r\n  methods: {\r\n    handleSizeChange(val) {\r\n      console.log(`每页 ${val} 条`);\r\n      this.getyiqidetails(this.token, val - 1);\r\n    },\r\n    handleCurrentChange(val) {\r\n      console.log(`当前页: ${val}`);\r\n      this.getyiqidetails(this.token, val - 1);\r\n    },\r\n    async gettoken(id) {\r\n      try {\r\n        const response = await api.post(\"\", {\r\n          method: \"equipment/searchEquipments\",\r\n          params: {\r\n            criteria: {\r\n              cat: id,\r\n              location:'58楼ACE区',\r\n              // \"group\": 1,\r\n              // \"searchtext\": \"搜索内容\"\r\n            },\r\n          },\r\n        });\r\n\r\n        // 检查是否成功拿到 token\r\n        if (response.data && response.data.response.token) {\r\n          const token = response.data.response.token;\r\n          this.token = token;\r\n          // 将 token 存入 localStorage\r\n          // localStorage.setItem('authToken', token);\r\n          console.log(\"535:\", response.data.response);\r\n          this.total = response.data.response.total;\r\n          this.handleSizeChange(1);\r\n        } else {\r\n          console.error(\"登录成功但未返回 token:\", response.data.response);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"登录失败:\", error);\r\n      }\r\n    },\r\n    getyiqidetails(token, start) {\r\n      const headers = {\r\n        clientid: \"5a298e93-158d-4e22-83cf-6ceb62e9b4f1\",\r\n        clientsecret: \"2c8ec39e-9887-482a-b28b-e64c496b601c\",\r\n      };\r\n      const body = {\r\n        method: \"equipment/getEquipments\",\r\n        params: {\r\n          token: token,\r\n          start: start ? start * 16 : 0,\r\n          num: 16,\r\n        },\r\n      };\r\n      axios\r\n        .post(jlURL, body, {})\r\n        .then((response) => {\r\n          console.log(response.data, 535);\r\n          this.devicedata = response.data.response;\r\n          this.loading = false;\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"Error:\", error);\r\n        });\r\n    },  \r\n    ...mapActions(\"equipment\", [\"fetchEquipmentTags\"]),\r\n    changetit(index) {\r\n      this.titactive = index;\r\n      this.isshowwhat = index;\r\n      if (index == 1) {\r\n        this.showdh = false;\r\n        this.showdh1 = true;\r\n        this.noAnimation = true;\r\n      } else {\r\n        this.showdh = true;\r\n        this.showdh1 = false;\r\n        this.noAnimation = false;\r\n      }\r\n    },\r\n    handleCascaderChange(value) {\r\n      console.log(\"选中的值:\", value[1]);\r\n      this.gettoken(value[1]);\r\n    },\r\n    hidedetailsss() {\r\n      this.isshowsss = false;\r\n    },\r\n    opendialog(payload) {\r\n      if (payload == 1) {\r\n        this.isshow = true;\r\n\r\n        this.data.forEach((item) => {\r\n          if (item.category == \"电子显微镜\") {\r\n            this.isshow = true;\r\n            this.tableDataItem = item.items;\r\n            console.log(this.tableDataItem);\r\n          }\r\n        });\r\n      }\r\n\r\n      // 在这里处理事件\r\n    },\r\n    toggleSubMenu(item) {\r\n      this.isshowsss = true;\r\n      console.log(item, \"选中的设备信息\");\r\n      this.selectedItem = {\r\n        id: item.id,\r\n        name: item.name,\r\n        imgurl: item.iconreal_url,\r\n        location: item.location + item.location2, //位置\r\n        status: !item.is_using ? \"当前使用\" : \"可使用\", //仪器状态  可领用\r\n        details: [\r\n          {\r\n            name: \"产品名称\",\r\n            value: item.name,\r\n          },\r\n          {\r\n            name: \"价格\",\r\n            value: item.price,\r\n          },\r\n          {\r\n            name: \"购入时间\",\r\n            value: `${new Date(item.purchased_date * 1000).getFullYear()}/${\r\n              new Date(item.purchased_date * 1000).getMonth() + 1\r\n            }/${new Date(item.purchased_date * 1000).getDate()}`,\r\n          },\r\n          {\r\n            name: \"制造国家\",\r\n            value: item.manu_at,\r\n          },\r\n          {\r\n            name: \"生产厂家\",\r\n            value: item.manufacturer,\r\n          },\r\n          {\r\n            name: \"负责人\",\r\n            value: item.contact,\r\n          },\r\n          {\r\n            name: \"联系电话\",\r\n            value: item.phone,\r\n          },\r\n        ],\r\n        maintenance_records: {\r\n          maintenance_content: \"校准和系统升级\", //维护内容\r\n          date: \"2024-01-10\", //维护时间\r\n          next_maintenance_date: \"2022-01-10\", //下次维护时间\r\n        },\r\n        management_name: \"王工\",\r\n        management_contact_info: \"15698567542\",\r\n      };\r\n    },\r\n    // // 设置内容\r\n    // setContent(content) {\r\n    //   this.isshowsss = true;\r\n    //   this.selectedItem = content;\r\n    //   console.log(this.selectedItem);\r\n\r\n    //   this.activeContent = content;\r\n    // },\r\n    showdetails(item) {\r\n      // item.items.forEach((item) => {\r\n      //   this.newArr.push({ name: item.name });\r\n      // });\r\n      // console.log(this.newArr);\r\n\r\n      this.isshow = true;\r\n      this.tableDataItem = item.items;\r\n    },\r\n    hidedetails() {\r\n      this.isshow = false;\r\n    },\r\n\r\n    oc(value) {\r\n      console.log(value, \"收到的值\");\r\n      this.showdh = value;\r\n    },\r\n    xuanzedialog(value) {\r\n      const optionMapping = {\r\n        选项1: 0,\r\n        选项2: 1,\r\n        选项3: 2,\r\n        选项4: 3,\r\n        选项5: 4,\r\n        选项6: 5,\r\n        选项7: 6,\r\n        选项8: 7,\r\n        选项9: 8,\r\n        选项10: 9,\r\n        选项11: 10,\r\n        选项12: 11,\r\n        选项13: 12,\r\n      };\r\n\r\n      const index = optionMapping[value];\r\n      if (index !== undefined) {\r\n        this.tableDataItem = this.data[index].items;\r\n      } else {\r\n        console.error(\"无效的选项: \", value);\r\n      }\r\n    },\r\n  },\r\n  // 生命周期 - 创建完成（可以访问当前this实例）\r\n  created() {},\r\n  // 生命周期 - 挂载完成（可以访问DOM元素）\r\n  mounted() {\r\n    this.gettoken(\"\");\r\n    this.showdh1 = true;\r\n    this.fetchEquipmentTags();\r\n    // setTimeout(() => {\r\n    //   this.showdh1 = false;\r\n    //   this.noAnimation = false;\r\n    // }, 1000); // 动画持续时间为1秒\r\n    console.log(1222);\r\n  },\r\n  beforeCreate() {}, // 生命周期 - 创建之前\r\n  beforeMount() {}, // 生命周期 - 挂载之前\r\n  beforeUpdate() {}, // 生命周期 - 更新之前\r\n  updated() {}, // 生命周期 - 更新之后\r\n  beforeUnmount() {\r\n    // 在组件销毁之前清除定时器\r\n    console.log(1111);\r\n  },\r\n\r\n  unmounted() {\r\n    console.log(2222);\r\n  }, // 生命周期 - 销毁之前\r\n  destroyed() {\r\n    console.log(1221);\r\n  }, // 生命周期 - 销毁完成\r\n  activated() {}, // 如果页面有keep-alive缓存功能，这个函数会触发\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.container {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-content: space-between;\r\n  align-items: stretch;\r\n  height: 1080px;\r\n\r\n  // text-align: center;\r\n  /* 定位 el-cascader 的 placeholder 样式 */\r\n\r\n  /deep/.sect.el-tooltip__trigger {\r\n    text-align: left;\r\n    width: 200px;\r\n    color: #fff;\r\n    // background-color: #00ffc0;\r\n  }\r\n\r\n  /deep/.el-input__wrapper {\r\n    box-shadow: none;\r\n    border: none;\r\n    background-color: #5a6972;\r\n    color: #fff;\r\n  }\r\n\r\n  /deep/.sect .el-input__inner {\r\n    color: #fff;\r\n  }\r\n\r\n  .sbdetails {\r\n    position: absolute;\r\n    top: 50%;\r\n    left: 50%;\r\n    transform: translate(-50%, -50%);\r\n    z-index: 9999;\r\n  }\r\n\r\n  .el-input {\r\n    margin-left: 5px;\r\n    width: 145px;\r\n    height: 34px;\r\n    color: #fff !important;\r\n\r\n    ::v-deep .el-input__wrapper {\r\n      background: url(\"../assets/image/inputss.png\");\r\n      background-size: 100% 100%;\r\n      background-repeat: no-repeat;\r\n      box-shadow: none !important;\r\n      color: #fff !important;\r\n    }\r\n\r\n    /deep/.el-input__inner {\r\n      color: #fff !important;\r\n    }\r\n\r\n    .el-input__inner::placeholder {\r\n      color: #fff;\r\n      /* 设置占位符颜色 */\r\n    }\r\n  }\r\n\r\n  .suosuo {\r\n    position: absolute;\r\n    top: 62px;\r\n    right: -32px;\r\n    cursor: pointer;\r\n  }\r\n\r\n  .box {\r\n    // margin-top: 6px;\r\n    padding-top: 5px;\r\n    margin-bottom: 0.225rem;\r\n    max-height: 745px;\r\n    width: 330px;\r\n    // height: 800px;\r\n\r\n    overflow-y: scroll;\r\n\r\n    /* 设置垂直滚动条 */\r\n    /* 设置滚动条的样式 */\r\n    &::-webkit-scrollbar {\r\n      width: 0.1px;\r\n      /* 设置滚动条的宽度 */\r\n    }\r\n\r\n    /* 设置滚动条轨道的样式 */\r\n    &::-webkit-scrollbar-track {\r\n      background-color: #f1f1f1;\r\n      /* 设置滚动条轨道的背景色 */\r\n    }\r\n\r\n    /* 设置滚动条滑块的样式 */\r\n    &::-webkit-scrollbar-thumb {\r\n      background-color: #334f6e;\r\n      /* 设置滚动条滑块的背景色 */\r\n    }\r\n\r\n    /* 鼠标悬停在滚动条上时的样式 */\r\n    &::-webkit-scrollbar-thumb:hover {\r\n      background-color: #555;\r\n      /* 设置鼠标悬停时滚动条滑块的背景色 */\r\n    }\r\n\r\n    .xiaobox {\r\n      margin-top: 20px;\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .siqiu {\r\n        width: 16px;\r\n        height: 16px;\r\n      }\r\n\r\n      .shuru {\r\n        display: flex;\r\n        align-items: center;\r\n        padding-left: 10px;\r\n        background: url(\"../assets/image/shebei12.png\");\r\n        background-size: 100% 100%;\r\n        background-repeat: no-repeat;\r\n        width: 261px;\r\n        height: 32px;\r\n        font-family: Source Han Sans SC;\r\n        font-weight: bold;\r\n        font-size: 17px;\r\n        color: #ffffff;\r\n      }\r\n    }\r\n\r\n    .xiaoboxs {\r\n      cursor: pointer;\r\n      margin-top: 14px;\r\n      display: flex;\r\n      margin-left: 5px;\r\n      align-items: center;\r\n\r\n      .nihaowo {\r\n        width: 78px;\r\n        font-family: Source Han Sans SC;\r\n        font-weight: bold;\r\n        font-size: 10px;\r\n        color: #ffffff;\r\n        display: flex;\r\n        white-space: nowrap;\r\n        /* 不换行 */\r\n        overflow: hidden;\r\n        /* 超出部分隐藏 */\r\n        text-overflow: ellipsis;\r\n        /* 超出部分显示省略号 */\r\n      }\r\n\r\n      .siqiu {\r\n        width: 16px;\r\n        height: 16px;\r\n        margin-left: 10px;\r\n        margin-right: 7px;\r\n      }\r\n\r\n      .shuru {\r\n        display: flex;\r\n        align-items: center;\r\n        padding-left: 10px;\r\n        background: url(\"../assets/image/shebei12.png\");\r\n        background-size: 100% 100%;\r\n        background-repeat: no-repeat;\r\n        width: 261px;\r\n        height: 32px;\r\n        font-family: Source Han Sans SC;\r\n        font-weight: bold;\r\n        font-size: 17px;\r\n        color: #ffffff;\r\n        white-space: nowrap;\r\n        /* 不换行 */\r\n        overflow: hidden;\r\n        /* 超出部分隐藏 */\r\n        text-overflow: ellipsis;\r\n        /* 超出部分显示省略号 */\r\n      }\r\n    }\r\n  }\r\n\r\n  .left-panel {\r\n    position: fixed;\r\n    z-index: 1;\r\n    top: 100px;\r\n    left: 22px;\r\n    width: 330px;\r\n    height: 937px;\r\n\r\n    background-size: 100% 100%;\r\n    transform: translate(-122%);\r\n    transition: transform 0.5s ease-in-out;\r\n  }\r\n\r\n  .left-panel-active {\r\n    transform: translate(0%);\r\n  }\r\n\r\n  .left-panel-active1 {\r\n    // transform: translate(0%);\r\n    animation: slideOut 1s ease-in-out forwards;\r\n  }\r\n\r\n  @keyframes slideOut {\r\n    100% {\r\n      transform: translateX(0%);\r\n    }\r\n\r\n    // 85% {\r\n    //   transform: translateX(-25%);\r\n    // }\r\n\r\n    // 65% {\r\n    //   transform: translateX(-15%);\r\n    // }\r\n\r\n    // 40% {\r\n    //   transform: translateX(-55%);\r\n    // }\r\n\r\n    // 30% {\r\n    //   transform: translateX(-40%);\r\n    // }\r\n\r\n    0% {\r\n      transform: translateX(-100%);\r\n    }\r\n  }\r\n\r\n  .rtitle {\r\n    margin-top: 16px;\r\n  }\r\n\r\n  @keyframes slideIn {\r\n    0% {\r\n      transform: translateX(100%);\r\n    }\r\n\r\n    // 30% {\r\n    //   transform: translateX(65%);\r\n    // }\r\n\r\n    // 40% {\r\n    //   transform: translateX(40%);\r\n    // }\r\n\r\n    // 65% {\r\n    //   transform: translateX(15%);\r\n    // }\r\n\r\n    // 85% {\r\n    //   transform: translateX(25%);\r\n    // }\r\n\r\n    100% {\r\n      transform: translateX(0%);\r\n    }\r\n  }\r\n\r\n  .right-panel {\r\n    position: fixed;\r\n    z-index: 1;\r\n    right: 83px;\r\n    width: 330px;\r\n    top: 100px;\r\n    height: 937px;\r\n\r\n    background-size: 100% 100%;\r\n    transform: translate(122%);\r\n    transition: transform 0.5s ease-in-out;\r\n\r\n    .jk {\r\n      margin-top: 12px;\r\n      width: 90%;\r\n      height: 200px;\r\n    }\r\n\r\n    .box {\r\n      margin-top: 6px;\r\n      margin-bottom: 18px;\r\n      // background: url(\"../assets/image/zuoshang1.png\");\r\n      background-size: 100% 100%;\r\n      background-repeat: no-repeat;\r\n\r\n      width: 94%;\r\n      // height: 428px;\r\n\r\n      .loudong {\r\n        margin-top: 21px;\r\n        margin-bottom: 25px;\r\n      }\r\n\r\n      .wenzi {\r\n        font-family: Microsoft YaHei;\r\n        font-weight: 400;\r\n        font-size: 10px;\r\n        color: #bdecf9;\r\n        text-align: left;\r\n        margin-left: 20px;\r\n        margin-right: 20px;\r\n\r\n        .h2biaoti {\r\n          margin-bottom: 15px;\r\n          font-family: Microsoft YaHei;\r\n          font-weight: 400;\r\n          font-size: 12px;\r\n          color: #00ffff;\r\n        }\r\n      }\r\n\r\n      .p {\r\n        text-indent: 2em;\r\n        margin-bottom: 1em;\r\n        letter-spacing: 0.05em;\r\n      }\r\n    }\r\n\r\n    .boxxx {\r\n      margin-top: 6px;\r\n      margin-bottom: 18px;\r\n      background: url(\"../assets/image/zuoshang1.png\");\r\n      background-size: 100% 100%;\r\n      background-repeat: no-repeat;\r\n\r\n      width: 333px;\r\n      height: 420px;\r\n      overflow-y: scroll;\r\n      /* 设置垂直滚动条 */\r\n      // overflow: hidden;\r\n      /* 设置滚动条的样式 */\r\n\r\n      .zengti {\r\n        margin: 10px 0;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        width: 315px;\r\n        height: 38px;\r\n        gap: 5px;\r\n\r\n        .left {\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: space-evenly;\r\n          width: 84px;\r\n          height: 27px;\r\n\r\n          .yuan {\r\n            width: 12px;\r\n            height: 12px;\r\n            border-radius: 50%;\r\n            background-color: #08f7f7;\r\n          }\r\n\r\n          .wenziss {\r\n            display: flex;\r\n            align-items: flex-start;\r\n            flex-direction: column;\r\n\r\n            .p1 {\r\n              font-family: Source Han Sans SC;\r\n              font-weight: 500;\r\n              font-size: 10px;\r\n              color: #ffffff;\r\n            }\r\n\r\n            .p2 {\r\n              font-family: Source Han Sans SC;\r\n              font-weight: 500;\r\n              font-size: 10px;\r\n              color: #55cff9;\r\n            }\r\n          }\r\n        }\r\n\r\n        .right {\r\n          background: url(\"../assets/image/rightbeij.png\");\r\n          background-size: 100% 100%;\r\n          background-repeat: no-repeat;\r\n\r\n          width: 217px;\r\n          height: 38px;\r\n          font-family: Source Han Sans SC;\r\n          font-weight: 500;\r\n          font-size: 11px;\r\n          color: #ffffff;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n        }\r\n      }\r\n    }\r\n\r\n    .boxxx::-webkit-scrollbar {\r\n      width: 1px;\r\n      /* 设置滚动条的宽度 */\r\n    }\r\n\r\n    /* 设置滚动条轨道的样式 */\r\n    .boxxx::-webkit-scrollbar-track {\r\n      background-color: #f1f1f1;\r\n      /* 设置滚动条轨道的背景色 */\r\n    }\r\n\r\n    /* 设置滚动条滑块的样式 */\r\n    .boxxx::-webkit-scrollbar-thumb {\r\n      background-color: #013363;\r\n      /* 设置滚动条滑块的背景色 */\r\n    }\r\n  }\r\n\r\n  .no-animation {\r\n    transition: none;\r\n  }\r\n\r\n  .right-panel-active {\r\n    transform: translate(0%);\r\n    // animation: slideIn 1s ease-in-out ;\r\n  }\r\n\r\n  .right-panel-active1 {\r\n    // transform: translate(0%);\r\n    animation: slideIn 1s ease-in-out forwards;\r\n  }\r\n\r\n  @keyframes slideIn {\r\n    0% {\r\n      transform: translateX(100%);\r\n    }\r\n\r\n    // 30% {\r\n    //   transform: translateX(65%);\r\n    // }\r\n\r\n    // 40% {\r\n    //   transform: translateX(40%);\r\n    // }\r\n\r\n    // 65% {\r\n    //   transform: translateX(15%);\r\n    // }\r\n\r\n    // 85% {\r\n    //   transform: translateX(25%);\r\n    // }\r\n\r\n    100% {\r\n      transform: translateX(0%);\r\n    }\r\n  }\r\n\r\n  .completed {\r\n    background: #7ad0ff;\r\n  }\r\n\r\n  .incomplete {\r\n    background: #ff6041;\r\n  }\r\n\r\n  .warning {\r\n    background: #00ffc0;\r\n  }\r\n\r\n  .completeds {\r\n    color: #7ad0ff;\r\n  }\r\n\r\n  .incompletes {\r\n    color: #ff6041;\r\n  }\r\n\r\n  .warnings {\r\n    color: #00ffc0;\r\n  }\r\n}\r\n\r\n.ql-center {\r\n  display: flex;\r\n  margin-top: 20px;\r\n  justify-content: space-around;\r\n  margin-top: 14px;\r\n\r\n  .ql-Box {\r\n    width: 75px;\r\n    height: 49px;\r\n    border: 1px solid #7ad0ff;\r\n    // opacity: 0.6;\r\n    border-radius: 2px;\r\n\r\n    .ql-box1 {\r\n      font-family: Alibaba PuHuiTi;\r\n      font-weight: bold;\r\n      font-size: 16px;\r\n      color: #7ad0ff;\r\n      margin-top: -10px;\r\n    }\r\n\r\n    .ql-box {\r\n      display: flex;\r\n      padding-left: 8px;\r\n      padding-right: 9px;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      width: 100%;\r\n      height: 34px;\r\n\r\n      .left_ql {\r\n        width: 49px;\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        color: #ffffff;\r\n\r\n        .yuan {\r\n          width: 7px;\r\n          height: 7px;\r\n          border-radius: 50%;\r\n        }\r\n\r\n        .pp {\r\n          color: #fff;\r\n          font-size: 12px;\r\n        }\r\n      }\r\n\r\n      img {\r\n        height: 12px;\r\n        width: 7px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.warn1 {\r\n  background: url(\"../assets/image/warnred.png\");\r\n}\r\n\r\n.warn2 {\r\n  background: url(\"../assets/image/warnyellow.png\");\r\n}\r\n\r\n.warn3 {\r\n  background: url(\"../assets/image/warngreen.png\");\r\n}\r\n\r\n.warning12 {\r\n  background-size: 100% 100%;\r\n  // width: 365px;\r\n  height: 47px;\r\n\r\n  .info {\r\n    margin-top: 5px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    text-align: left;\r\n\r\n    margin-left: 50px;\r\n\r\n    .info1 {\r\n      display: flex;\r\n      flex-direction: column;\r\n\r\n      p:nth-of-type(1) {\r\n        font-size: 13px;\r\n        font-family: Alibaba PuHuiTi;\r\n        font-weight: bold;\r\n      }\r\n\r\n      .red {\r\n        color: #ff0000;\r\n      }\r\n\r\n      .green {\r\n        color: #00ffcc;\r\n      }\r\n\r\n      .yellow {\r\n        color: #ffff00;\r\n      }\r\n\r\n      p:nth-of-type(2) {\r\n        font-size: 16px;\r\n        font-family: Alibaba PuHuiTi;\r\n        font-weight: 400;\r\n        color: #ffffff;\r\n      }\r\n    }\r\n\r\n    .info2 {\r\n      margin-right: 10px;\r\n      font-size: 15px;\r\n      font-family: Alibaba PuHuiTi;\r\n      font-weight: 400;\r\n      color: #cffff8;\r\n    }\r\n  }\r\n}\r\n\r\n.xxxx {\r\n  position: absolute;\r\n  top: 1%;\r\n  right: 1%;\r\n  width: 25px;\r\n  height: 25px;\r\n  z-index: 99999;\r\n}\r\n\r\n/* 菜单容器 */\r\n.menu-container {\r\n  display: flex;\r\n  width: 330px;\r\n  height: 736px;\r\n}\r\n\r\n/* 菜单样式 */\r\n.menu {\r\n  width: 100%;\r\n  // background-color: #fff;\r\n  // border: 1px solid #ddd;\r\n  border-radius: 4px;\r\n}\r\n\r\n/deep/.el-pagination {\r\n  --el-pagination-bg-color: none;\r\n  --el-pagination-button-color: #fff;\r\n  --el-pagination-font-size: 17px;\r\n  margin-left: -13px;\r\n}\r\n\r\n/deep/.el-pagination__total {\r\n  color: #fff;\r\n  font-size: 15px;\r\n}\r\n\r\n/deep/.el-pagination button:disabled {\r\n  background: none;\r\n  font-size: 17px;\r\n}\r\n\r\n/deep/.el-pagination button {\r\n  background: none;\r\n  font-size: 17px;\r\n}\r\n\r\n/deep/.el-icon {\r\n  font-size: 17px !important;\r\n}\r\n\r\n/* 菜单项样式 */\r\n.menu-group {\r\n  margin-top: 14px;\r\n}\r\n\r\n.menu-item {\r\n  cursor: pointer;\r\n  background: url(\"../assets/image/rightbeij.png\");\r\n  background-size: 100% 100%;\r\n  background-repeat: no-repeat;\r\n\r\n  width: 100%;\r\n  height: 32px;\r\n  font-family: Source Han Sans SC;\r\n  font-weight: 500;\r\n  font-size: 17px;\r\n  color: #ffffff;\r\n  display: flex;\r\n  align-items: center;\r\n  padding-left: 10px;\r\n  // justify-content: center;\r\n}\r\n\r\n.menu-item:hover {\r\n  // background-color: #f0f0f0;\r\n}\r\n\r\n.submenu {\r\n  // background-color: #f9f9f9;\r\n  padding-left: 20px;\r\n}\r\n\r\n.submenu-item {\r\n  padding: 3px;\r\n  padding-left: 12px;\r\n  margin: 8px;\r\n  font-size: 16px;\r\n  color: #ffffff;\r\n  cursor: pointer;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background-color: #2c343f;\r\n}\r\n\r\n.submenu-item:hover {\r\n  background-color: #163561;\r\n}\r\n\r\n.qiuqiu {\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  .siqiu {\r\n    width: 16px;\r\n    height: 16px;\r\n    margin-left: 10px;\r\n    margin-right: 7px;\r\n  }\r\n}\r\n\r\n.listtype {\r\n  background: url(\"../assets/image/xuanzhotiemmoon.png\");\r\n  background-repeat: no-repeat;\r\n  background-size: 100% 100%;\r\n  width: 80px;\r\n  height: 26px;\r\n  text-align: center;\r\n  line-height: 26px;\r\n\r\n  font-size: 13px;\r\n  font-family: Alibaba PuHuiTi;\r\n  font-weight: 400;\r\n  color: #fff;\r\n}\r\n\r\n.listtypes {\r\n  background: url(\"../assets/image/xuanzhotiemmoon.png\");\r\n  background-repeat: no-repeat;\r\n  background-size: 100% 100%;\r\n  width: 100px;\r\n  height: 32px;\r\n  text-align: center;\r\n  line-height: 32px;\r\n  cursor: pointer;\r\n  font-size: 13px;\r\n  font-family: Alibaba PuHuiTi;\r\n  font-weight: 400;\r\n  color: #fff;\r\n}\r\n\r\n.ellipsis {\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  max-width: 190px;\r\n  /* 你可以根据需要调整宽度 */\r\n  font-size: 16px;\r\n}\r\n\r\n.botbtn {\r\n  position: fixed;\r\n  top: 978px;\r\n  // left: 228px;\r\n  left: 355px;\r\n  width: 200px;\r\n  height: 43px;\r\n  background: #022d56;\r\n  border-radius: 8px;\r\n  z-index: 20;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n\r\n  .btt {\r\n    color: #fff;\r\n    font-size: 17px;\r\n    line-height: 44px;\r\n    width: 88px;\r\n    text-align: center;\r\n    height: 43px;\r\n    cursor: pointer;\r\n  }\r\n\r\n  .btt1 {\r\n    color: rgb(8, 207, 241);\r\n    font-size: 17px;\r\n    line-height: 44px;\r\n    width: 88px;\r\n    text-align: center;\r\n    height: 43px;\r\n    cursor: pointer;\r\n  }\r\n}\r\n</style>\r\n"]}]}