{"remainingRequest": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\views\\index.vue?vue&type=style&index=0&id=a83bd3b0&lang=less&scoped=true", "dependencies": [{"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\views\\index.vue", "mtime": 1751448706819}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\views\\index.vue"], "names": [], "mappings": ";AAiiCA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAChB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC5B;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAE1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACf;;EAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAClB;;EAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAClB;;EAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACvB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAElB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;IAEb,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAClB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAClB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACT,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAElB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACjC;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ;EACF;AACF;;AAEA,CAAC,CAAC,CAAC,EAAE;EACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACf;;AAEA,CAAC,CAAC,CAAC,CAAC,EAAE;EACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACX,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAElB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAElB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACV,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEtC,CAAC,CAAC,CAAC,EAAE;MACH,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE5B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACf;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACpB;;MAEA,CAAC,EAAE;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1B;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAClB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7C;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAClB,CAAC,CAAC,CAAC,EAAE;MACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B;;IAEA,CAAC,EAAE,CAAC,CAAC,EAAE;IACP,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC,EAAE;;IAEH,CAAC,EAAE,CAAC,CAAC,EAAE;IACP,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC,EAAE;;IAEH,CAAC,EAAE,CAAC,CAAC,EAAE;IACP,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC,EAAE;;IAEH,CAAC,EAAE,CAAC,CAAC,EAAE;IACP,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC,EAAE;;IAEH,CAAC,EAAE;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEtC,CAAC,CAAC,CAAC,EAAE;MACH,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEb,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,EAAE;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB;;QAEA,CAAC,CAAC,CAAC,CAAC,EAAE;UACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACtB;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEnB,CAAC,CAAC,CAAC,EAAE;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB;QACF;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAClB;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACf;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACf;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACvC;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5C;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACvC;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvD;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,EAAE;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B;;IAEA,CAAC,EAAE,CAAC,CAAC,EAAE;IACP,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC,EAAE;;IAEH,CAAC,EAAE,CAAC,CAAC,EAAE;IACP,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC,EAAE;;IAEH,CAAC,EAAE,CAAC,CAAC,EAAE;IACP,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC,EAAE;;IAEH,CAAC,EAAE,CAAC,CAAC,EAAE;IACP,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC,EAAE;;IAEH,CAAC,CAAC,CAAC,EAAE;MACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;EAElB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEd,CAAC,CAAC,CAAC,CAAC,EAAE;UACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACnB;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACnB;;QAEA,CAAC,CAAC,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjB;MACF;;MAEA,CAAC,CAAC,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACZ;IACF;EACF;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtD;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrD;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAClB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;EAElB,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;IAElB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEf,CAAC,CAAC,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACnB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjB;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACP,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEjB,CAAC,CAAC,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACpB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB;EACF;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACN,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEnB,CAAC,CAAC,CAAC,EAAE;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACd;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;QAEhB,CAAC,CAAC,CAAC,EAAE;UACH,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB;MACF;IACF;EACF;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAEhB,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3B;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB;;EAEA,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;IAEf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB;;IAEA,CAAC,CAAC,CAAC,EAAE;MACH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACd;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,EAAE;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAClB;IACF;EACF;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACf;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACf;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAClB;;AAEA,CAAC,CAAC,CAAC,CAAC,EAAE;EACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACpB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACjB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAElB,CAAC,CAAC,CAAC,EAAE;IACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEtC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EACxB;;EAEA,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE9C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EACxB;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACf;;AAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAClC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACV,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACf;;AAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAClB;;AAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACnC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACV,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACf;;AAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAClB;;AAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAClB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACf;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;EAEpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACpC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/B;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACb;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB;EACF;AACF;;AAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAClB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE7B,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACX;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACX;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEnB,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACT;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB;EACF;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACnC;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACnC;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,EAAE,CAAC;EACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACf;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACf;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClC;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;EAEf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACrC;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB;EACF;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACf;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;EAEpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;IAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;MAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpD;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B;IACF;EACF;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACb;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB;EACF;AACF", "file": "E:/svn/***********-0186-SFTianjinUniversityH5/src/src/views/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div>\n    <component :is=\"componentTag\" :tabledata=\"tabledata\" :zengtiimg=\"zengtiimg\" @fatherMethoddd=\"fatherMethoddd\"\n      ref=\"child\"></component>\n    <div class=\"container\" v-if=\"isshow\">\n      <div class=\"left-panel\" :class=\"{\n        'left-panel-active': showdh,\n        'no-animation': noAnimation,\n        'left-panel-active1': showdh1,\n      }\">\n        <Title class=\"ltitle1\" tit=\"平台介绍\" :isshow=\"true\">\n          <div class=\"zonghe\">\n            <div class=\"boxsty\" v-for=\"item in tablelist\" :key=\"item\">\n              <div class=\"mianji\">\n                <img :src=\"item.img\" class=\"img\" alt=\"\" />\n                <div class=\"wenzi\">\n                  <div class=\"top\">{{ item.name }}</div>\n                  <div class=\"bottom\">{{ item.value }}</div>\n                </div>\n              </div>\n            </div>\n          </div>\n          <div class=\"gongneng\" v-for=\"item in wenzilist\" :key=\"item\">\n            <div style=\"display: flex; align-items: center\">\n              <div class=\"yuan\"></div>\n              <div class=\"name\">{{ item.name }}</div>\n            </div>\n            <div class=\"value\">{{ item.value }}</div>\n          </div>\n        </Title>\n        <Title class=\"ltitle1\" tit=\"报警统计\" :isshow=\"true\">\n          <div class=\"boxxx\">\n            <huanxing :warningData=\"warningStats\"></huanxing>\n          </div>\n        </Title>\n        <!-- <Title class=\"ltitle1\" tit=\"能耗统计\" :isshow=\"true\">\n          <div class=\"box\">\n            <div class=\"zongheqt\">\n              <div class=\"left1\">\n                <div class=\"mianji\" v-for=\"item in dianlist\" :key=\"item\">\n                  <img :src=\"item.img\" class=\"img\" alt=\"\" />\n                  <div class=\"wenzis\">\n                    <div class=\"top\">12346</div>\n                    <div class=\"bottom\">\n                      <div style=\"\n                          font-family: Alibaba PuHuiTi;\n                          font-weight: 400;\n                          font-size: 13px;\n                          color: #3ba1f4;\n                        \">\n                        本日\n                      </div>\n                      /Kwh\n                    </div>\n                  </div>\n                </div>\n              </div>\n              <biao1></biao1>\n            </div>\n          </div>\n        </Title> -->\n      </div>\n\n      <!-- 弹出层 -->\n      <div class=\"popup-overlay\" v-if=\"showPopup\" @click=\"closePopup\">\n        <div class=\"popup-content\" @click.stop>\n          <div class=\"popup-header\">\n            <div class=\"popup-title\">历史预约详情</div>\n            <div class=\"close-btn\" @click=\"closePopup\">×</div>\n          </div>\n          <div class=\"popup-body\" v-loading=\"isAllLoading\" element-loading-text=\"加载中...\"\n            element-loading-background=\"rgba( 28, 37, 56, 0.8)\">\n            <div class=\"popup-table\">\n              <div class=\"table-header\">\n                <div class=\"col-2\">仪器名</div>\n                <div class=\"col-2\">组织机构</div>\n                <div class=\"col-2\">仪器位置</div>\n                <div class=\"col-2\">预约时长</div>\n                <div class=\"col-1\">预约人</div>\n                <div class=\"col-1\">预约状态</div>\n              </div>\n              <template v-if=\"tableDatass && tableDatass.length > 0\">\n                <div class=\"table-row\" v-for=\"item in tableDatass\" :key=\"item\">\n                  <div class=\"col-2\" :title=\"item.name\">{{ item.name }}</div>\n                  <div class=\"col-2\" :title=\"item.equipment_group\">\n                    {{ item.equipment_group }}\n                  </div>\n                  <div class=\"col-2\" :title=\"item.equipment_location\">\n                    {{ item.equipment_location }}\n                  </div>\n                  <div class=\"col-2\">{{ item.duration1 }}</div>\n                  <div class=\"col-1\">{{ item.roomNumber }}</div>\n                  <div class=\"col-1\">{{ item.status }}</div>\n                </div>\n              </template>\n              <template v-else>\n                <div class=\"empty-message\">暂无预约记录</div>\n              </template>\n            </div>\n            <!-- 分页组件 -->\n            <div class=\"pagination-container\">\n              <el-pagination :current-page=\"currentPage\" :page-size=\"pageSize\" :page-sizes=\"pageSizes\" :total=\"total\"\n                @size-change=\"handleSizeChange\" @current-change=\"handleCurrentChange\"\n                layout=\"total, sizes, prev, pager, next, jumper\" background />\n            </div>\n          </div>\n        </div>\n      </div>\n      <!-- 右侧内容 -->\n\n      <div class=\"right-panel\" :class=\"{\n        'right-panel-active': showdh,\n        'no-animation': noAnimation,\n        'right-panel-active1': showdh1,\n      }\">\n        <Title1 class=\"rtitle\" tit=\"今日预约\">\n          <div class=\"boxswq\" @click=\"showLargeTable\">\n            <div class=\"titleimgs\">\n              <div class=\"bgu\">\n                <div>预约仪器数</div>\n                <div>{{ yytotal }}</div>\n              </div>\n              <div class=\"bgu1\">\n                <div>预约总数</div>\n                <div>{{ todayReservations.length }}</div>\n              </div>\n            </div>\n            <div class=\"titless\">\n              <div class=\"item1\">仪器名</div>\n              <div class=\"item1\">预约时长</div>\n              <div class=\"item\">预约人</div>\n              <div class=\"item1\">预约状态</div>\n            </div>\n            <div class=\"titlesscontents\" v-loading=\"isLoading\" element-loading-text=\"加载中...\"\n              element-loading-spinner=\"el-icon-loading\" element-loading-background=\"rgba(0, 0, 0, 0.8)\">\n              <div class=\"contents\" v-for=\"item in todayReservations\" :key=\"item\">\n                <div class=\"item1\" :title=\"item.name\">{{ item.name }}</div>\n                <div class=\"item1\">{{ item.duration }}</div>\n                <div class=\"item\">{{ item.roomNumber }}</div>\n                <div class=\"item1\">{{ item.status }}</div>\n              </div>\n              <div v-if=\"!todayReservations.length\" class=\"empty-message\">\n                暂无预约记录\n              </div>\n            </div>\n          </div>\n        </Title1>\n\n        <Title1 class=\"rtitle\" tit=\"仪器状态\">\n          <div class=\"huangxing\">\n            <SystemDete></SystemDete>\n            <!-- <SystemDete></SystemDete> -->\n          </div>\n        </Title1>\n        <Title1 class=\"rtitle\" tit=\"异常跟踪处理\" :isshow=\"true\">\n          <div class=\"boxxxs\" @click=\"openbj()\">\n            <div class=\"ql-center\">\n              <div class=\"ql-Box\">\n                <div class=\"ql-box\">\n                  <div class=\"left_ql\">\n                    <div class=\"yuan status\" style=\"color: #5c9dee\"></div>\n                    <div class=\"pp\">未修复</div>\n                  </div>\n                </div>\n                <div class=\"ql-box1\" style=\"color: #b93851\">\n                  {{ unfixedCount }}\n                </div>\n              </div>\n              <div class=\"ql-Box\">\n                <div class=\"ql-box\">\n                  <div class=\"left_ql\">\n                    <div class=\"yuan1 status\" style=\"color: #89f6c1\"></div>\n                    <div class=\"pp\">已修复</div>\n                  </div>\n                </div>\n                <div class=\"ql-box1 status\" style=\"color: #89f6c1\">\n                  {{ fixedCount }}\n                </div>\n              </div>\n            </div>\n            <div class=\"unfixed-warnings\">\n              <!-- 未修复警告列表 -->\n              <div v-for=\"(warning, index) in warningData.unfixed\" :key=\"'unfixed-' + index\" class=\"warning12\">\n                <div class=\"info\">\n                  <div>\n                    <div class=\"zongduan\">\n                      <div class=\"yuan\" style=\"background-color: #b93851\"></div>\n                      <div class=\"cjhulizhong\" style=\"color: #b93851\">\n                        未修复\n                      </div>\n                    </div>\n                    <p class=\"info2\">{{ warning.warningCategory }}</p>\n                  </div>\n\n                  <div class=\"info1\">\n                    <p class=\"time\">{{ formatDate(warning.createdAt) }}</p>\n                    <p class=\"location\">{{ warning.errMsg }}</p>\n                  </div>\n                  <p class=\"info2\">\n                    {{ warning.deviceName }}\n                  </p>\n                </div>\n              </div>\n            </div>\n            <!-- 已修复警告列表 -->\n            <!-- <div v-for=\"(warning, index) in warningData.fixed\" :key=\"'fixed-'+index\" class=\"warning12\">\n              <div class=\"info\">\n                <div class=\"zongduan\">\n                  <div class=\"yuan\" style=\"background-color: #64f8bb\"></div>\n                  <div class=\"cjhulizhong\" style=\"color: #64f8bb\">已处理</div>\n                </div>\n                <div class=\"info1\">\n                  <p class=\"time\">{{ formatDate(warning.createdAt) }}</p>\n                  <p class=\"location\">{{ warning.errMsg }}</p>\n                </div>\n                <p class=\"info2\" style=\"color: #64f8bb\" @click=\"openbj()\">{{ warning.deviceName }}</p>\n              </div>\n            </div> -->\n          </div>\n        </Title1>\n      </div>\n    </div>\n    <table2 @close=\"closetan\" v-if=\"opentable2\"></table2>\n    <!-- <table-2 class=\"table2\" @close=\"closetan\" v-if=\"opentable2\"></table-2> -->\n    <!-- <div\n      class=\"center_container\"\n      :class=\"{\n        'right-panel-active11': showdh,\n        'no-animation': noAnimation,\n        'right-panel-active12': showdh1,\n      }\"\n    >\n      <img\n        class=\"btn\"\n        src=\"../assets/image/shang.png\"\n        @click=\"scrollUp\"\n        alt=\"向上\"\n      />\n      <div class=\"content\" ref=\"content\">\n        <div\n          :class=\"activef == index ? 'itema' : 'item'\"\n          v-for=\"(item, index) in resItems\"\n          :key=\"index\"\n          @click=\"switchactivef(item, index)\"\n          @mouseover=\"hoveredRoom = item\"\n          @mouseleave=\"hoveredRoom = null\"\n        >\n          {{\n            index === 0\n              ? title + \"F-\" + \"整体\"\n              : title + \"F-\" + (index < 10 ? \"10\" + index : \"1\" + index)\n          }}\n\n          <div class=\"tooltip\" v-if=\"hoveredRoom === item\">{{ item.name }}</div>\n        </div>\n      </div>\n      <img\n        class=\"btn\"\n        src=\"../assets/image/xia.png\"\n        @click=\"scrollDown\"\n        alt=\"向下\"\n      />\n    </div>\n    <div\n      @click=\"returnhome()\"\n      class=\"return\"\n      :class=\"{\n        'right-panel-active11': showdh,\n        'no-animation': noAnimation,\n        'right-panel-active12': showdh1,\n      }\"\n    >\n      返回\n    </div> -->\n  </div>\n</template>\n\n<script>\n// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）\n// 例如：import 《组件名称》 from '《组件路径》';\nimport huanxing from \"@/components/echarts/huanxing.vue\";\nimport zhexian from \"@/components/echarts/zhexian.vue\";\nimport zhexian1 from \"@/components/echarts/zhexian1.vue\";\nimport SystemDete from \"@/components/echarts/SystemDete.vue\";\nimport echarts1 from \"@/components/echarts/bingjifang/echarts3.vue\";\nimport echarts2 from \"@/components/echarts/bingjifang/echarts5.vue\";\nimport table2 from \"@/components/common/table2.vue\";\nimport shuangxiang from \"@/components/echarts/shuangxiang.vue\";\nimport shebei from \"@/views/shebei.vue\";\nimport { resourceDeviceList } from \"@/api/admin.js\";\nimport biao1 from \"../components/echarts/biao1.vue\";\nimport biao1ss from \"../components/echarts/biao1ss.vue\";\nimport axios from \"axios\";\nimport {\n  getDeviceData,\n  getDevicedetails,\n  getDeviceWarningList,\n} from \"@/api/device.js\";\n\n// resourceDeviceList\nexport default {\n  // import引入的组件需要注入到对象中才能使用\n  components: {\n    table2,\n    huanxing,\n    zhexian,\n    zhexian1,\n    SystemDete,\n    echarts1,\n    echarts2,\n    shuangxiang,\n    shebei,\n    biao1ss,\n    biao1,\n  },\n  props: [\"title\", \"resItems\"],\n  data() {\n    // 这里存放数据\n    return {\n      jlURL,\n      dstime,\n      responseData: null, // 存储返回的数据\n      error: null, // 存储错误信息\n      todayReservations: [], // 今日预约数据\n      allReservations: [], // 所有预约数据\n      allTableData: [], // 存储所有数据\n      allTableData1: [], // 存储所有数据\n      currentPage: 1, // 当前页码\n      pageSize: 10, // 每页显示条数\n      total: 0, // 总数据条数\n      yytotal: 0,\n      pageSizes: [10, 20, 50, 100], // 每页显示条数选项\n      opentable2: false,\n      hoveredRoom: null,\n      scrollPosition: 0,\n      flag: true,\n      localtitle: \"\",\n      dianlist: [\n        {\n          name: \"总用地面积\",\n          value: \"57874.1㎡\",\n          img: require(\"../assets/image/ri.png\"),\n        },\n        {\n          name: \"总建筑面积\",\n          value: \"7802.54㎡\",\n          img: require(\"../assets/image/zhou.png\"),\n        },\n        {\n          name: \"地上建筑面积\",\n          value: \"14085㎡\",\n          img: require(\"../assets/image/yue.png\"),\n        },\n      ],\n      tablelist: [\n        {\n          name: \"总用地面积\",\n          value: \"4423.8㎡\",\n          img: require(\"../assets/image/mianji1.png\"),\n        },\n        {\n          name: \"总建筑面积\",\n          value: \"16845㎡\",\n          img: require(\"../assets/image/mianji2.png\"),\n        },\n        {\n          name: \"地上建筑面积\",\n          value: \"14085㎡\",\n          img: require(\"../assets/image/mianji3.png\"),\n        },\n        {\n          name: \"地下建筑面积\",\n          value: \"2760㎡\",\n          img: require(\"../assets/image/mianji4.png\"),\n        },\n      ],\n      wenzilist: [\n        {\n          name: \"平台概述\",\n          value: \"天津大学大型仪器平台是天津大学批准设立的校级公共技术服务平台，聚焦兼顾多学科需求的保障学校基础能力，促进跨平台和交叉新兴学科能力的建设,以'专管共用'的管理模式，为科学研究提供高质量的开放式测试服务，开展仪器设备创新性功能开发与技术研发。\",\n        },\n      ],\n\n      activef: 0,\n      isshow: true,\n      isactive: 0,\n      tabledata: [],\n      zengtiimg: \"\",\n      lrdata: [\n        {\n          title1: \"温度\",\n          title2: \"22℃\",\n          title3: \"2022-04-01 12:00:00\",\n        },\n      ],\n      deviceTypes: \"CQQ11\",\n      activeTab: \"today\",\n      botlist: [\n        { name: \"总览\", code: \"\" },\n        { name: \"设备列表\", code: \"\" },\n        {\n          name: \"环境温湿度\",\n          code: \"CGQ11\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/wensiduchuanganqi.png\",\n        },\n        {\n          name: \"防爆温湿度\",\n          code: \"CGQ10\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/fangbao.png\",\n        },\n        {\n          name: \"冰箱状态\",\n          code: \"LRY193\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/bingxiang.png\",\n        },\n        {\n          name: \"培养箱状态\",\n          code: \"CGQ13\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/bingxiang.png\",\n        },\n        {\n          name: \"乙炔气体\",\n          code: \"CGQ7\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/fangbao.png\",\n        },\n        {\n          name: \"环境CO2\",\n          code: \"CGQ9\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/huanjingco2.png\",\n        },\n        {\n          name: \"环境O2\",\n          code: \"CGQ3\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/huanjingco2.png\",\n        },\n        {\n          name: \"甲烷气体\",\n          code: \"CGQ8\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/jiawan.png\",\n        },\n        {\n          name: \"房间压差\",\n          code: \"CGQ2\",\n          img: \"http://3d.dddtask.cn/engineer-qilei/fengxian/kqyc.png\",\n        },\n      ],\n      listst: [\n        {\n          name: \"广东质检中诚认证有限公司到中广...\",\n        },\n        { name: \"材料科学、化学工程及医药研发成...\" },\n        { name: \"酒精代谢能力检测：您的酒量，基...\" },\n        { name: \"植酸检测方法及作用\" },\n        { name: \"兔拉检测：行业中芯片造假技术...\" },\n        { name: \"酒精代谢能力检测：您的酒量，基...\" },\n      ],\n      showdh: true,\n      showdh1: false,\n      noAnimation: false,\n      localTitle: this.title, // 初始化本地数据属性\n      nhlist: [\n        {\n          title: \"供气压力\",\n          status: \"0.3Mpa\",\n          unit: \"℃\",\n        },\n\n        {\n          title: \"供气流量\",\n          status: \"6M3/min\",\n          unit: \"㎡\",\n        },\n        {\n          title: \"露点温度\",\n          status: \"6℃\",\n          unit: \"℃\",\n        },\n        {\n          title: \"含氧量\",\n          status: \"6PPM\",\n          unit: \"㎡\",\n        },\n      ],\n      warnlist1: [\n        {\n          type: 1,\n          time: \"视频监控报警-3号楼-3F-101\",\n          value: \"\",\n          name: \"2024-06-16   12:34:09\",\n        },\n        {\n          type: 2,\n          time: \"视频监控报警-3号楼-3F-101\",\n          value: \"\",\n          name: \"2024-06-16   12:34:09\",\n        },\n        {\n          type: 3,\n          name: \"2024-06-16   12:34:09\",\n          value: \"\",\n          time: \"视频监控报警-3号楼-3F-101\",\n        },\n      ],\n      isButton2Active: false,\n      status: \"巡检中\",\n      status1: \"已完成\",\n      status2: \"待巡检\",\n      selectedIndex: 0,\n      componentTag: \"\",\n      dectid: \"\",\n      showPopup: false,\n      isLoading: false, // 今日预约加载状态\n      isAllLoading: false, // 全部预约加载状态\n      warningData: {\n        unfixed: [],\n        fixed: [],\n        unfixedtotal: 0,\n        fixedtotal: 0,\n      },\n      baseURL: \"https://tjdx.yuankong.org.cn\",\n      token: localStorage.getItem(\"token\") || \"\",\n      warningStats: [],\n    };\n  },\n  // 计算属性类似于data概念\n  computed: {\n    formattedTitle() {\n      return {\n        title: `${this.localTitle}F实验室介绍`,\n        img: require(`../assets/img/floor/1Fbig.png`),\n      };\n    },\n    formattedTitle1() {\n      return `${this.localTitle}F实验室总览`;\n    },\n    formattedTitle2() {\n      return `实验室${this.localTitle}F环境信息`;\n    },\n    formattedTitle3() {\n      return `实验室${this.localTitle}F设备信息`;\n    },\n    formattedTitle4() {\n      return `实验室${this.localTitle}F事件详情`;\n    },\n    formatted1Title() {\n      return {\n        title: `${this.localTitle}实验室介绍`,\n        img: require(`../assets/img/floor/${this.title}Fbig.png`),\n      };\n    },\n    formatted1Title1() {\n      return `${this.localTitle}实验室总览`;\n    },\n    formatted1Title2() {\n      return `${this.localTitle}环境信息`;\n    },\n    formatted1Title3() {\n      return `${this.localTitle}设备信息`;\n    },\n    formatted1Title4() {\n      return `${this.localTitle}事件详情`;\n    },\n    unfixedCount() {\n      return this.warningData.unfixedtotal;\n    },\n    fixedCount() {\n      return this.warningData.fixedtotal;\n    },\n  },\n  // 监控data中的数据变化\n  watch: {\n    title(newVal) {\n      this.localTitle = newVal;\n    },\n    resItems(newVal) {\n      console.log(newVal);\n\n      // this.resItems = newVal;\n    },\n  },\n  // 方法集合\n  methods: {\n    // 获取当前日期的00:00:01的时间戳\n    getStartOfDayTimestamp() {\n      const now = new Date();\n      now.setHours(0, 0, 1, 0); // 设置时间为当天的 00:00:01\n      return Math.floor(now.getTime() / 1000); // 转换为 Unix 时间戳（秒）\n    },\n\n    // 获取当前日期的23:59:59的时间戳\n    getEndOfDayTimestamp() {\n      const now = new Date();\n      now.setHours(23, 59, 59, 0);\n      return Math.floor(now.getTime() / 1000);\n    },\n\n    // 格式化预约数据\n    formatReservationData(item) {\n      return {\n        name: item.equipment_name,\n        date: new Date(item.start * 1000).toLocaleDateString(),\n        date1: new Date(item.start * 1000).toLocaleString(),\n        duration: `${new Date(item.start * 1000).getHours()}:00-${new Date(\n          item.end * 1000\n        ).getHours()}:00`,\n        duration1: `${new Date(\n          item.start * 1000\n        ).toLocaleDateString()} ${new Date(\n          item.start * 1000\n        ).getHours()}:00 - ${new Date(\n          item.end * 1000\n        ).toLocaleDateString()} ${new Date(item.end * 1000).getHours()}:00`,\n        roomNumber: item.user_name,\n        status: item.is_using === \"1\" ? \"已预约\" : \"已预约\",\n        equipment_group: item.equipment_group || \"未设置\", // 添加组织机构字段\n        equipment_location: item.equipment_location || \"未设置\", // 添加仪器位置字段\n      };\n    },\n\n    // 获取今日预约数据\n    async fetchTodayReservations() {\n      this.isLoading = true;\n      const headers = {\n        clientid: \"5a298e93-158d-4e22-83cf-6ceb62e9b4f1\",\n        clientsecret: \"2c8ec39e-9887-482a-b28b-e64c496b601c\",\n      };\n\n      const requestBody = {\n        method: \"gpui/eq_reserv/reservList\",\n        params: {\n          dtstart: this.getStartOfDayTimestamp(),\n          dtend: this.getEndOfDayTimestamp(),\n          params: {\n            location: \"58\",\n            limit: [0, 10000],\n          },\n        },\n      };\n\n      try {\n        const response = await axios.post(\n          jlURL,\n          requestBody,\n          { headers }\n        );\n\n        this.todayReservations = response.data.response.map(\n          this.formatReservationData\n        );\n        // 使用去重后的仪器个数\n        this.$nextTick(() => {\n          this.yytotal = this.uniqueEquipmentCount;\n        });\n      } catch (err) {\n        console.error(\"获取今日预约数据失败:\", err);\n        this.error = err;\n      } finally {\n        this.isLoading = false;\n      }\n    },\n\n    // 获取所有预约数据\n    async fetchAllReservations() {\n      this.isAllLoading = true;\n      const headers = {\n        clientid: \"5a298e93-158d-4e22-83cf-6ceb62e9b4f1\",\n        clientsecret: \"2c8ec39e-9887-482a-b28b-e64c496b601c\",\n      };\n\n      const requestBody = {\n        method: \"gpui/eq_reserv/reservList\",\n        params: {\n          dtstart: 1704844800,\n          dtend: this.getEndOfDayTimestamp(),\n          params: {\n            location: \"58\",\n            limit: [0, 10000],\n          },\n        },\n      };\n\n      try {\n        const response = await axios.post(\n          jlURL,\n          requestBody,\n          { headers }\n        );\n\n        // 对数据进行时间倒序排序\n        const sortedData = response.data.response.sort(\n          (a, b) => b.start - a.start\n        );\n        this.allReservations = sortedData.map(this.formatReservationData);\n        this.total = this.allReservations.length;\n        this.handleCurrentChange(1);\n      } catch (err) {\n        console.error(\"获取所有预约数据失败:\", err);\n        this.error = err;\n      } finally {\n        this.isAllLoading = false;\n      }\n    },\n\n    // 显示大表格\n    async showLargeTable() {\n      this.showPopup = true;\n      // 只在没有数据或数据过期的情况下重新获取\n      if (!this.allReservations.length) {\n        await this.fetchAllReservations();\n      } else {\n        // 如果已有数据，直接更新分页\n        this.handleCurrentChange(1);\n      }\n    },\n\n    closetan() {\n      this.opentable2 = false;\n    },\n    openbj() {\n      this.opentable2 = true;\n    },\n    handleOpenDialog() {\n      console.log(1111);\n      this.$emit(\"open-bj\");\n    },\n    scrollUp() {\n      const content = this.$refs.content;\n      content.scrollTop -= 38; // 每次向上滑动25px\n    },\n    scrollDown() {\n      const content = this.$refs.content;\n      content.scrollTop += 38; // 每次向下滑动25px\n    },\n    returnhome() {\n      this.$emit(\"returnhome\");\n    },\n    async switchactivef(item, index) {\n      this.dectid = item.id;\n      const res = await resourceDeviceList({\n        resourceId: item.id,\n        deviceTypes: this.deviceTypes,\n      });\n\n      this.tabledata = res.data;\n\n      if (index) {\n        this.flag = false;\n        this.localTitle = item.roomid;\n      } else {\n        this.localTitle = this.title;\n        this.flag = true;\n      }\n      console.log(item);\n      this.activef = index;\n      // this.$emit(\"childEvent\", title, index);\n    },\n    slideUp() {\n      const contentHeight = this.$refs.content.scrollHeight;\n      if (this.position > -contentHeight + this.containerHeight) {\n        this.position -= this.step;\n        this.$refs.content.style.transform = `translateY(${this.position}px)`;\n      }\n    },\n    slideDown() {\n      if (this.position < 0) {\n        this.position += this.step;\n        this.$refs.content.style.transform = `translateY(${this.position}px)`;\n      }\n    },\n\n    //  this.dectid = item.id;\n    //     const res = await resourceDeviceList({\n    //       resourceId: item.id,\n    //       deviceTypes: this.deviceTypes,\n    //     });\n    //     console.log(res.data, \"qilei\");\n    //     this.tabledata = res.data;\n\n    async switchTab1(item, index) {\n      console.log(item.img);\n      this.zengtiimg = item.img;\n\n      this.deviceTypes = item.code;\n      const res = await resourceDeviceList({\n        resourceId: this.dectid,\n        deviceTypes: this.deviceTypes,\n      });\n\n      this.tabledata = res.data;\n\n      // this.switchactivef(item, item.code);\n      this.isactive = index;\n      if (index) {\n        this.componentTag = \"shebei\";\n        this.isshow = false;\n        this.showdh = true;\n        this.showdh1 = false;\n      } else {\n        this.componentTag = \"\";\n        this.isshow = true;\n        this.showdh = false;\n        this.showdh1 = true;\n      }\n    },\n    switchTab(tab) {\n      this.activeTab = tab;\n    },\n    qeihuan(index) {\n      console.log(index, \"123123\");\n    },\n\n    qiehuanyans(index) {\n      console.log(index, \"123123\");\n      this.currentIndex = index;\n    },\n    getClassForStatus(status) {\n      if (status === \"告警总数\") {\n        return \"completed\";\n      } else if (status === \"处理完\") {\n        return \"incomplete\";\n      } else if (status === \"未处理\") {\n        return \"warning\";\n      } else {\n        return \"default\"; // 没有匹配的状态时可以添加一个默认类\n      }\n    },\n    getClassForStatuss(status) {\n      if (status === \"告警总数\") {\n        return \"completeds\";\n      } else if (status === \"处理完\") {\n        return \"incompletes\";\n      } else if (status === \"未处理\") {\n        return \"warnings\";\n      } else {\n        return \"default\"; // 没有匹配的状态时可以添加一个默认类\n      }\n    },\n    qiehuanyans(index) {\n      console.log(index, \"123123\");\n      this.currentIndex = index;\n    },\n    oc(value) {\n      console.log(value, \"floor收到的值\");\n      this.showdh = value;\n    },\n    getClassForStatus(status) {\n      if (status === \"巡检中\") {\n        return \"completed\";\n      } else if (status === \"待巡检\") {\n        return \"incomplete\";\n      } else if (status === \"已完成\") {\n        return \"warning\";\n      } else {\n        return \"default\"; // 没有匹配的状态时可以添加一个默认类\n      }\n    },\n    getClassForStatuss(status) {\n      if (status === \"巡检中\") {\n        return \"completeds\";\n      } else if (status === \"待巡检\") {\n        return \"incompletes\";\n      } else if (status === \"已完成\") {\n        return \"warnings\";\n      } else {\n        return \"default\"; // 没有匹配的状态时可以添加一个默认类\n      }\n    },\n    closePopup() {\n      this.showPopup = false;\n    },\n    // 处理页码改变\n    handleCurrentChange(page) {\n      this.currentPage = page;\n      const start = (page - 1) * this.pageSize;\n      const end = start + this.pageSize;\n      this.tableDatass = this.allReservations.slice(start, end);\n      console.log(this.tableDatass, \"tableDatass\");\n    },\n\n    // 处理每页显示条数改变\n    handleSizeChange(size) {\n      this.pageSize = size;\n      this.handleCurrentChange(1);\n    },\n    async getWarningList(hasFixed) {\n      try {\n        // 在关键请求前检查token是否需要刷新\n        if (this.$auth && this.$auth.checkAndRefreshToken) {\n          await this.$auth.checkAndRefreshToken();\n        }\n\n        const response = await getDeviceWarningList({\n          hasFixed: hasFixed,\n        });\n\n        if (response.code === 200) {\n          if (hasFixed === \"N\") {\n            this.warningData.unfixed = response.rows;\n            this.warningData.unfixedtotal = response.total;\n          } else {\n            this.warningData.fixed = response.rows;\n            this.warningData.fixedtotal = response.total;\n          }\n        } else {\n          console.error(\"获取警告数据失败:\", response.msg);\n          // 只有在明确的认证错误时才清除token并跳转\n          if (response.code === 401) {\n            localStorage.removeItem(\"token\");\n            this.$router.push(\"/\");\n          }\n        }\n      } catch (error) {\n        console.error(\"请求警告数据出错:\", error);\n        // 请求错误时不要立即清除token，让拦截器处理\n      }\n    },\n    async fetchAllWarningData() {\n      await Promise.all([this.getWarningList(\"N\"), this.getWarningList(\"Y\")]);\n    },\n    formatDate(timestamp) {\n      const date = new Date(timestamp);\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(\n        2,\n        \"0\"\n      )}-${String(date.getDate()).padStart(2, \"0\")} ${String(\n        date.getHours()\n      ).padStart(2, \"0\")}:${String(date.getMinutes()).padStart(2, \"0\")}`;\n    },\n    async fetchWarningStats() {\n      try {\n        const res = await getDeviceWarningList({\n          pageSize: 9999,\n          currentPage: 1,\n          hasFixed: \"N\",\n        });\n\n        if (res.code === 200 && res.rows) {\n          // 定义所有可能的报警类型及其阈值（简化后的名称）\n          const allWarningTypes = {\n            压力: 14,\n            氧气: 48,\n            温度: 67,\n            湿度: 67,\n            // '气体泄漏': 50\n          };\n\n          // 统计各类型报警数量\n          const stats = {};\n          // 初始化所有报警类型的计数为0\n          Object.keys(allWarningTypes).forEach((type) => {\n            stats[type] = {\n              total: 0,\n              unresolved: 0,\n            };\n          });\n\n          // 统计实际数据（使用包含匹配）\n          res.rows.forEach((item) => {\n            // 查找匹配的报警类型（只要包含关键字就匹配）\n            const matchedType = Object.keys(allWarningTypes).find(type =>\n              item.warningCategory && item.warningCategory.includes(type)\n            );\n\n            if (matchedType) {\n              stats[matchedType].total++;\n              if (item.status === \"N\") {\n                stats[matchedType].unresolved++;\n              }\n            }\n          });\n\n          // 转换为图表所需格式\n          this.warningStats = Object.entries(stats).map(\n            ([category, count]) => ({\n              label: `${category}(${count.total}/${allWarningTypes[category]})`,\n              value: count.total,\n            })\n          );\n\n          console.log(this.warningStats, \"报警统计数据\");\n        }\n      } catch (error) {\n        console.error(\"获取报警统计数据失败:\", error);\n      }\n    },\n  },\n  // 生命周期 - 创建完成（可以访问当前this实例）\n  created() {\n    this.fetchAllWarningData();\n    // 每5分钟刷新一次数据\n    setInterval(this.fetchAllWarningData, 5 * 60 * 1000);\n  },\n  // 生命周期 - 挂载完成（可以访问DOM元素）\n  mounted() {\n    this.fetchTodayReservations();\n    this.showdh1 = true;\n    setTimeout(() => {\n      this.showdh1 = false;\n      this.noAnimation = false;\n    }, 1000);\n\n    // 定时刷新今日预约数据\n    setInterval(() => {\n      this.fetchTodayReservations();\n    }, 1000 * this.dstime);\n    ue.interface.setSliderValue = (value) => {\n      console.log(value, \"ue点击拿到的值\");\n      if (!isNaN(Number(value.data))) {\n        // let did = value.data; // 如果是数字，则赋值\n        // const result = this.sblist.filter(item => item.id == did);\n        // this.deviceId = result[0].deviceId\n        // console.log(this.deviceId, 'ue点击拿到的id');\n      }\n      // this.deid = JSON.parse(value.data) - 43846\n      // console.log(this.deid);\n      // if (!isNaN(parseInt(value.data, 10))) {\n      //   var dtdata1 = JSON.parse(JSON.stringify(this.dtdata))\n      //   console.log(dtdata1);\n      //   this.showdet = false\n      //   // this.did = dtdata1.find(item => item.id == value.data)?.deviceid;\n      //   // console.log(this.did);\n      //   var didata = JSON.parse(dtdata1.find(item => item.id == value.data).channelCode);\n      //   let data1 = dtdata1.find(item => item.id == value.data)\n      //   // this.details = didata\n      //   this.bid = data1.bid\n      //   this.fid = data1.fid\n      //   // this.hlsurl\n      //   // this.bm = data1.note\n      //   console.log(data1, 1111111);\n      //   // this.getCameraData(did)\n      // }\n    };\n    this.fetchWarningStats();\n    // 每30秒更新一次数据\n    setInterval(() => {\n      this.fetchWarningStats();\n    }, 30000);\n  },\n  beforeCreate() { }, // 生命周期 - 创建之前\n  beforeMount() { }, // 生命周期 - 挂载之前\n  beforeUpdate() { }, // 生命周期 - 更新之前\n  updated() { }, // 生命周期 - 更新之后\n  beforeUnmount() {\n    // 在组件销毁之前清除定时器\n    console.log(1111);\n  },\n\n  unmounted() {\n    console.log(2222);\n  }, // 生命周期 - 销毁之前\n  destroyed() {\n    console.log(1221);\n  }, // 生命周期 - 销毁完成\n  activated() { }, // 如果页面有keep-alive缓存功能，这个函数会触发\n};\n</script>\n<style lang=\"less\" scoped>\n.table2 {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  z-index: 99999;\n}\n\n.return {\n  position: fixed;\n  right: 373px;\n  top: 100px;\n  height: 44px;\n  width: 46px;\n  // overflow: hidden;\n  transform: translate(720%);\n  transition: transform 0.5s ease-in-out;\n\n  z-index: 999;\n  cursor: pointer;\n  text-align: center;\n  line-height: 67px;\n  font-family: Source Han Sans SC;\n  font-weight: 400;\n  font-size: 11px;\n  color: #ffffff;\n  background: url(\"../assets/image/return.png\");\n  background-size: 100% 100%;\n}\n\n.center_container {\n  position: fixed;\n  right: 359px;\n  top: 352px;\n  height: 401px;\n  width: 70px;\n  // overflow: hidden;\n  transform: translate(470%);\n  transition: transform 0.5s ease-in-out;\n  z-index: 999;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  background: url(\"../assets/image/louceng.png\");\n  background-size: 100% 100%;\n\n  .content::-webkit-scrollbar {\n    width: 0px;\n    display: none;\n    /* 设置滚动条的宽度 */\n  }\n\n  /* 设置滚动条轨道的样式 */\n  .content::-webkit-scrollbar-track {\n    background-color: #f1f1f1;\n    /* 设置滚动条轨道的背景色 */\n  }\n\n  /* 设置滚动条滑块的样式 */\n  .content::-webkit-scrollbar-thumb {\n    background-color: #888;\n    /* 设置滚动条滑块的背景色 */\n  }\n\n  /* 鼠标悬停在滚动条上时的样式 */\n  .content::-webkit-scrollbar-thumb:hover {\n    background-color: #555;\n    /* 设置鼠标悬停时滚动条滑块的背景色 */\n  }\n\n  .content {\n    height: 330px;\n    /* 内容区的总高度，视实际内容而定 */\n    transition: transform 0.5s ease;\n    overflow-y: auto;\n    text-align: center;\n\n    /* 设置滚动条的样式 */\n\n    .item {\n      cursor: pointer;\n      width: 75px;\n      height: 25px;\n      font-family: YouSheBiaoTiHei;\n      font-weight: 400;\n      font-size: 14px;\n      color: #86a6b7;\n      line-height: 25px;\n      margin-top: 12px;\n    }\n\n    .itema {\n      background: url(\"../assets/image/lcactive.png\");\n      background-size: 100% 100%;\n      cursor: pointer;\n      width: 66px;\n      height: 25px;\n      font-family: YouSheBiaoTiHei;\n      font-weight: 400;\n      font-size: 14px;\n      color: #ffffff;\n      line-height: 25px;\n      margin-top: 12px;\n    }\n\n    .tooltip {\n      position: absolute;\n      left: 80%;\n      // top: 15px;\n      background-color: #1a3867;\n      border: 1px solid #7ba6eb;\n      color: #fff;\n      padding: 5px;\n      z-index: 1;\n      white-space: nowrap;\n      font-size: 12px;\n      visibility: hidden;\n\n      opacity: 0;\n      transition: opacity 0.5s, visibility 0.5s;\n      z-index: 999;\n      font-family: Source Han Sans SC;\n    }\n\n    .item:hover .tooltip {\n      visibility: visible;\n      /* 当鼠标悬停时显示 */\n      opacity: 1;\n    }\n\n    .itema:hover .tooltip {\n      visibility: visible;\n      /* 当鼠标悬停时显示 */\n      opacity: 1;\n    }\n  }\n}\n\n.btn {\n  margin-top: 13px;\n  width: 27px;\n  height: 14px;\n  cursor: pointer;\n}\n\n.echart2 {\n  height: 180px;\n}\n\n.bott {\n  position: fixed;\n  z-index: 1;\n  bottom: 4px;\n  // left: 6px;\n  width: 1920px;\n  height: 50px;\n  display: flex;\n  flex-direction: row;\n  cursor: pointer;\n  text-align: center;\n\n  .bottit {\n    width: 153px;\n    height: 45px;\n    background: url(\"../assets/image/bot_b.png\");\n    background-size: 100% 100%;\n    margin-left: 19.496px;\n    font-family: Source Han Sans SC;\n    font-weight: bold;\n    font-size: 17px;\n    color: #ffffff;\n    line-height: 43px;\n    cursor: pointer;\n  }\n\n  .bottit1 {\n    width: 153px;\n    height: 69px;\n    background: url(\"../assets/image/bot_a.png\");\n    background-size: 100% 100%;\n    margin-left: 19.496px;\n    font-family: Source Han Sans SC;\n    font-weight: bold;\n    font-size: 17px;\n    color: #ffffff;\n    line-height: 87px;\n    cursor: pointer;\n    margin-top: -23px;\n  }\n}\n\n.container {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: space-between;\n  align-items: stretch;\n  height: 1080px;\n  text-align: center;\n\n  .left-panel {\n    position: fixed;\n    z-index: 1;\n    top: 75px;\n    left: 22px;\n    width: 387px;\n    height: 937px;\n    background-size: 100% 100%;\n    transform: translate(-122%);\n    transition: transform 0.5s ease-in-out;\n\n    .box {\n      // margin-top: 6px;\n      margin-bottom: 18px;\n      // background: url(\"../assets/image/zuoshang1.png\");\n      background-size: 100% 100%;\n      background-repeat: no-repeat;\n\n      // width: 330px;\n      // height: 404px;\n\n      .loudong {\n        margin-top: 21px;\n        margin-bottom: 25px;\n        width: 296px;\n        height: 178px;\n      }\n\n      .wenzi {\n        font-family: Microsoft YaHei;\n        font-weight: 400;\n        font-size: 10px;\n        color: #bdecf9;\n        text-align: left;\n        margin-left: 20px;\n        margin-right: 20px;\n      }\n\n      .p {\n        text-indent: 2em;\n        margin-bottom: 1em;\n        letter-spacing: 0.05em;\n      }\n    }\n  }\n\n  .left-panel-active {\n    transform: translate(0%);\n  }\n\n  .left-panel-active1 {\n    // transform: translate(0%);\n    animation: slideOut 1s ease-in-out forwards;\n  }\n\n  @keyframes slideOut {\n    100% {\n      transform: translateX(0%);\n    }\n\n    // 85% {\n    //   transform: translateX(-25%);\n    // }\n\n    // 65% {\n    //   transform: translateX(-15%);\n    // }\n\n    // 40% {\n    //   transform: translateX(-55%);\n    // }\n\n    // 30% {\n    //   transform: translateX(-40%);\n    // }\n\n    0% {\n      transform: translateX(-100%);\n    }\n  }\n\n  .rtitle {\n    margin-top: 16px;\n  }\n\n  .ltitle1 {\n    margin-top: 16px;\n  }\n\n  .right-panel {\n    position: fixed;\n    z-index: 1;\n    right: 22px;\n    width: 387px;\n    top: 75px;\n    height: 937px;\n\n    background-size: 100% 100%;\n    transform: translate(122%);\n    transition: transform 0.5s ease-in-out;\n\n    .box {\n      // margin-top: 6px;\n      margin-bottom: 18px;\n      // background: url(\"../assets/image/zuoshang1.png\");\n      background-size: 100% 100%;\n      background-repeat: no-repeat;\n      font-family: Source Han Sans SC;\n      font-weight: 400;\n      font-size: 12px;\n      color: #ffffff;\n      width: 330px;\n      height: 224px;\n\n      .titlest {\n        display: flex;\n\n        // shiyansimg.png\n        .itm {\n          cursor: pointer;\n          margin: 16px 9px 0 10px;\n          background: url(\"../assets/image/shiyansimg.png\");\n          background-size: 100% 100%;\n          background-repeat: no-repeat;\n          width: 100px;\n          height: 32px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n        }\n\n        .itms {\n          background: url(\"../assets/image/xuanzexuanzhong.png\") !important;\n          background-size: 100% 100%;\n          background-repeat: no-repeat;\n          width: 100px;\n          height: 41px !important;\n          padding-bottom: 10px;\n        }\n      }\n\n      .contentss {\n        display: flex;\n        align-items: center;\n        flex-wrap: wrap;\n        justify-content: space-around;\n        align-items: center;\n\n        .itm {\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          width: 112px;\n          height: 70px;\n          background: url(\"../assets/image/wendupng.png\");\n          background-size: 100% 100%;\n          background-repeat: no-repeat;\n          font-family: DIN;\n          font-weight: bold;\n          font-size: 22px;\n          color: #ffffff;\n\n          .danwei {\n            font-family: DIN;\n            font-weight: bold;\n            font-size: 12px;\n            color: #ffffff;\n          }\n        }\n\n        .wendyu {\n          font-family: Source Han Sans SC;\n          font-weight: 400;\n          font-size: 13px;\n          color: #ffffff;\n          margin-top: -7px;\n        }\n      }\n\n      .loudong {\n        margin-top: 21px;\n        margin-bottom: 25px;\n        width: 296px;\n        height: 178px;\n      }\n    }\n\n    .boxxxs {\n      margin-left: -10px;\n      margin-top: 1px;\n      margin-bottom: 18px;\n      // background: url(\"../assets/image/zuoshang1.png\");\n      background-size: 100% 100%;\n      background-repeat: no-repeat;\n\n      width: 366px;\n      cursor: pointer;\n      // height: 254px;\n    }\n  }\n\n  .boxxx {\n    // margin-top: 6px;\n    margin-bottom: 18px;\n    // background: url(\"../assets/image/zuoshang1.png\");\n    background-size: 100% 100%;\n    background-repeat: no-repeat;\n\n    width: 350px;\n    height: 284px;\n  }\n\n  .no-animation {\n    transition: none;\n  }\n\n  .right-panel-active {\n    transform: translate(0%);\n    // animation: slideIn 1s ease-in-out ;\n  }\n\n  .right-panel-active1 {\n    // transform: translate(0%);\n    animation: slideIn 1s ease-in-out forwards;\n  }\n\n  .right-panel-active11 {\n    transform: translate(0%) !important;\n    // animation: slideIn 1s ease-in-out ;\n  }\n\n  .right-panel-active12 {\n    // transform: translate(0%);\n    animation: slideIn 1s ease-in-out forwards !important;\n  }\n\n  @keyframes slideIn {\n    0% {\n      transform: translateX(100%);\n    }\n\n    // 30% {\n    //   transform: translateX(65%);\n    // }\n\n    // 40% {\n    //   transform: translateX(40%);\n    // }\n\n    // 65% {\n    //   transform: translateX(15%);\n    // }\n\n    // 85% {\n    //   transform: translateX(25%);\n    // }\n\n    100% {\n      transform: translateX(0%);\n    }\n  }\n\n  .completed {\n    background: #7ad0ff;\n  }\n\n  .incomplete {\n    background: #ff6041;\n  }\n\n  .warning {\n    background: #00ffc0;\n  }\n\n  .completeds {\n    color: #7ad0ff;\n  }\n\n  .incompletes {\n    color: #ff6041;\n  }\n\n  .warnings {\n    color: #00ffc0;\n  }\n}\n\n.ql-center {\n  display: flex;\n  // margin-top: 20px;\n  justify-content: space-around;\n  margin-top: 4px;\n  margin-bottom: 4px;\n\n  .ql-Box {\n    width: 46%;\n    height: 49px;\n    border: 1px solid #7ad0ff;\n    // opacity: 0.6;\n    border-radius: 2px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n\n    .ql-box1 {\n      font-family: Alibaba PuHuiTi;\n      font-weight: bold;\n      font-size: 19px;\n      color: #7ad0ff;\n    }\n\n    .ql-box {\n      display: flex;\n      // padding-left: 23px;\n      padding-right: 9px;\n      flex-direction: row;\n      justify-content: space-between;\n      align-items: center;\n      // width: 100%;\n      height: 24px;\n\n      .left_ql {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        color: #ffffff;\n\n        .yuan {\n          width: 8px;\n          height: 8px;\n          border-radius: 50%;\n          background-color: #b93851;\n          margin-right: 5px;\n        }\n\n        .yuan1 {\n          width: 8px;\n          height: 8px;\n          border-radius: 50%;\n          background-color: #84edc3;\n          margin-right: 5px;\n        }\n\n        .pp {\n          margin-left: 5px;\n          color: #fff;\n          font-size: 18px;\n        }\n      }\n\n      img {\n        height: 12px;\n        width: 8px;\n      }\n    }\n  }\n}\n\n.warn1 {\n  // background: url(\"../assets/image/warnred.png\");\n}\n\n.warn2 {\n  // background: url(\"../assets/image/warnyellow.png\");\n}\n\n.warn3 {\n  // background: url(\"../assets/image/warngreen.png\");\n}\n\n.unfixed-warnings {\n  height: 180px;\n  overflow-y: auto;\n}\n\n.warning12 {\n  background-size: 100% 100%;\n  height: 47px;\n  margin-bottom: 8px;\n\n  .info {\n    display: flex;\n    justify-content: space-between;\n    align-items: flex-start;\n    text-align: left;\n    font-size: 13px;\n    padding: 8px 12px;\n    background: rgba(25, 37, 60, 0.1);\n    border-radius: 4px;\n\n    .zongduan {\n      display: flex;\n      align-items: center;\n      min-width: 80px;\n\n      .yuan {\n        width: 8px;\n        height: 8px;\n        border-radius: 50%;\n        margin-right: 6px;\n      }\n\n      .cjhulizhong {\n        font-family: Microsoft YaHei;\n        font-weight: bold;\n        font-size: 14px;\n      }\n    }\n\n    .info1 {\n      flex: 1;\n      // margin: 0 12px;\n\n      .time {\n        font-family: Microsoft YaHei;\n        font-size: 14px;\n        color: #ffffff;\n        margin-bottom: 4px;\n      }\n\n      .location {\n        font-family: Microsoft YaHei;\n        font-size: 14px;\n        color: #ffffff;\n        white-space: nowrap;\n        overflow: hidden;\n        text-overflow: ellipsis;\n      }\n    }\n\n    .info2 {\n      cursor: pointer;\n      font-size: 14px;\n      font-family: Microsoft YaHei;\n      font-weight: 400;\n      color: #b93851;\n      // white-space: nowrap;\n      margin-left: 10px;\n    }\n  }\n}\n\n.zonghe {\n  // margin-bottom: 10px;\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center;\n  justify-content: center;\n\n  .boxsty {\n    width: 50%;\n    margin-top: 12px;\n\n    .mianji {\n      display: flex;\n      align-items: center;\n\n      .img {\n        width: 50px;\n        height: 49px;\n      }\n\n      .wenzi {\n        text-align: left;\n        margin-left: 5px;\n\n        .top {\n          // margin-bottom: 9px;\n          font-family: Alibaba PuHuiTi;\n          font-weight: 500;\n          font-size: 20px;\n          color: #ffffff;\n        }\n\n        .bottom {\n          font-family: Source Han Sans SC;\n          font-weight: 500;\n          font-size: 21px;\n          color: #59ffc4;\n        }\n      }\n    }\n  }\n}\n\n.gongneng {\n  margin-top: 12px;\n\n  display: flex;\n  flex-direction: column;\n  // align-items: center;\n  // font-family: Source Han Sans SC;\n  font-family: Alibaba PuHuiTi;\n  // font-weight: bold;\n  font-size: 22px;\n  color: #59ffc4;\n  text-align: left;\n\n  .yuan {\n    margin-right: 7px;\n    width: 16px;\n    height: 16px;\n    border-radius: 50%;\n    background-color: #85fdca;\n  }\n\n  .value {\n    font-family: Alibaba PuHuiTi;\n    font-weight: 500;\n    font-size: 20px;\n    color: #fff;\n    width: 100%;\n    margin-right: 3px;\n    text-indent: 40px;\n  }\n\n  .name {\n    // width: 58px;\n    font-size: 22px;\n  }\n}\n\n.zongheqt {\n  .left1 {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    margin-right: 20px;\n    margin-top: 7px;\n\n    .mianji {\n      background: url(\"../assets/image/zengfangti.png\");\n      background-repeat: no-repeat;\n      background-size: 100% 100%;\n      width: 106px;\n      height: 58px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n    .img {\n      width: 50px;\n      height: 49px;\n    }\n\n    .wenzis {\n      .top {\n        font-family: Alibaba PuHuiTi;\n        font-weight: 400;\n        font-size: 18px;\n        color: #ffffff;\n      }\n\n      .bottom {\n        display: flex;\n        align-items: flex-end;\n        font-family: Alibaba PuHuiTi;\n        font-weight: 400;\n        font-size: 13px;\n        color: #fff;\n        margin-left: 7px;\n      }\n    }\n  }\n}\n\n.boxswq {\n  width: 365px;\n  height: 242px;\n}\n\n.huangxing {\n  width: 359px;\n  height: 238px;\n}\n\n.cjhulizhong {\n  font-family: Microsoft YaHei;\n  font-weight: bold;\n  font-size: 14px;\n  color: #64f8bb;\n  margin-left: 8px;\n}\n\n.yuan {\n  width: 10px;\n  height: 10px;\n  background-color: #518acd;\n  border-radius: 50%;\n}\n\n.zongduan {\n  display: flex;\n  align-items: center;\n  font-size: 13px;\n}\n\n.titleimgs {\n  display: flex;\n  align-items: center;\n  justify-content: space-around;\n  margin-right: 10px;\n\n  .bgu {\n    background-color: #95871cbf !important;\n\n    // background: url(\"../assets/image/titlessimg.png\");\n    background-repeat: no-repeat;\n    background-size: 100% 100%;\n    width: 171px;\n    height: 38px;\n    font-family: Alibaba PuHuiTi;\n    font-weight: 400;\n    font-size: 22px;\n    color: #ffffff;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 0 12px 0 14px;\n  }\n\n  .bgu1 {\n    background-color: rgb(28, 128, 149) !important;\n\n    // background: url(\"../assets/image/titlessimg.png\");\n    background-repeat: no-repeat;\n    background-size: 100% 100%;\n    width: 171px;\n    height: 38px;\n    font-family: Alibaba PuHuiTi;\n    font-weight: 400;\n    font-size: 22px;\n    color: #ffffff;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 0 12px 0 14px;\n  }\n}\n\n.titlesscontents {\n  overflow: auto;\n  height: 154px;\n}\n\n/* 设置滚动条的样式 */\n.titlesscontents::-webkit-scrollbar {\n  width: 5px;\n  /* 设置滚动条的宽度 */\n}\n\n/* 设置滚动条轨道的样式 */\n.titlesscontents::-webkit-scrollbar-track {\n  background-color: #454f5d;\n  /* 设置滚动条轨道的背景色 */\n}\n\n/* 设置滚动条的样式 */\n.unfixed-warnings::-webkit-scrollbar {\n  width: 5px;\n  /* 设置滚动条的宽度 */\n}\n\n/* 设置滚动条轨道的样式 */\n.unfixed-warnings::-webkit-scrollbar-track {\n  background-color: #454f5d;\n  /* 设置滚动条轨道的背景色 */\n}\n\n/* 设置滚动条滑块的样式 */\n.unfixed-warnings::-webkit-scrollbar-thumb {\n  background-color: #f1f1f1;\n  /* 设置滚动条滑块的背景色 */\n}\n\n.pagination-container {\n  margin-top: 20px;\n  display: flex;\n  justify-content: flex-end;\n  padding: 10px;\n}\n\n:deep(.el-pagination) {\n\n  .el-pagination__total,\n  .el-pagination__sizes,\n  .el-pagination__jump,\n  .btn-prev,\n  .btn-next,\n  .el-pager li {\n    background-color: transparent;\n    color: #fff;\n  }\n\n  .el-pagination__total,\n  .el-pagination__jump {\n    color: #fff;\n  }\n\n  .el-select .el-input .el-input__inner {\n    color: #fff;\n    background-color: transparent;\n  }\n\n  .el-pager li.active {\n    background-color: #409eff;\n    color: #fff;\n  }\n}\n\n.loading-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100%;\n  width: 100%;\n}\n\n:deep(.el-loading-spinner) {\n  .el-loading-text {\n    color: #fff;\n    margin: 3px 0;\n    font-size: 14px;\n  }\n\n  .circular {\n    .path {\n      stroke: #3ba1f4;\n    }\n  }\n}\n\n/* 设置滚动条滑块的样式 */\n.titlesscontents::-webkit-scrollbar-thumb {\n  background-color: #f1f1f1;\n  /* 设置滚动条滑块的背景色 */\n}\n\n.titless {\n  margin-right: 10px;\n  width: 96%;\n  background: rgba(25, 37, 60, 0.5);\n  height: 32px;\n  margin-top: 8px;\n  display: flex;\n  font-family: Alibaba PuHuiTi;\n  font-weight: 400;\n  font-size: 17px;\n  color: #40d7ff;\n  display: flex;\n  align-items: center;\n  justify-content: space-around;\n\n  .item {\n    width: 100%;\n    flex: 1.1;\n  }\n\n  .item1 {\n    width: 100%;\n    flex: 1.9;\n  }\n}\n\n.contents {\n  border-bottom: 1px solid #3b5471;\n  margin-right: 10px;\n  width: 96%;\n  background: rgba(45, 58, 79, 0.2);\n  height: 32px;\n  display: flex;\n  font-family: Alibaba PuHuiTi;\n  font-weight: 400;\n  font-size: 15px;\n  color: #fff;\n  display: flex;\n  align-items: center;\n\n  .item {\n    width: 100%;\n    flex: 1;\n  }\n\n  .item1 {\n    width: 100%;\n    flex: 1.9;\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    position: relative;\n    cursor: pointer;\n\n    &:hover::after {\n      content: attr(title);\n      position: absolute;\n      left: 0;\n      top: 100%;\n      background: rgba(0, 0, 0, 0.8);\n      color: #fff;\n      padding: 4px 8px;\n      border-radius: 4px;\n      z-index: 999;\n      white-space: normal;\n    }\n  }\n}\n\n.contents:nth-child(odd) {\n  background: rgba(46, 61, 83, 0.4);\n}\n\n.contents:nth-child(even) {\n  background: rgba(37, 50, 69, 0.2);\n}\n\n.popup-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.7);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 9999;\n}\n\n.popup-content {\n  background: rgba(25, 37, 60, 0.95);\n  border: 1px solid #3ba1f4;\n  border-radius: 8px;\n  width: 80%;\n  max-width: 1000px;\n  max-height: 80vh;\n  overflow-y: auto;\n  padding: 20px;\n}\n\n.popup-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid #3ba1f4;\n}\n\n.popup-title {\n  font-family: Alibaba PuHuiTi;\n  font-size: 24px;\n  color: #40d7ff;\n}\n\n.close-btn {\n  font-size: 28px;\n  color: #fff;\n  cursor: pointer;\n  padding: 0 10px;\n\n  &:hover {\n    color: #40d7ff;\n  }\n}\n\n.popup-table {\n  .table-header {\n    display: flex;\n    background: rgba(25, 37, 60, 0.8);\n    padding: 12px;\n    color: #40d7ff;\n    font-family: Alibaba PuHuiTi;\n    font-size: 20px;\n\n    .col-1 {\n      flex: 1;\n      padding: 0 2px;\n      text-align: center;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n\n    .col-2 {\n      flex: 2.3;\n      padding: 0 2px;\n      text-align: center;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n  }\n\n  .table-row {\n    display: flex;\n    padding: 12px;\n    font-size: 12px;\n    border-bottom: 1px solid rgba(59, 161, 244, 0.2);\n    color: #fff;\n    font-family: Alibaba PuHuiTi;\n\n    &:hover {\n      background: rgba(59, 161, 244, 0.1);\n    }\n\n    .col-1 {\n      flex: 1;\n      padding: 0 2px;\n      text-align: center;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n\n    .col-2 {\n      flex: 2.3;\n      padding: 0 2px;\n      text-align: center;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n  }\n}\n\n.pagination-container {\n  margin-top: 20px;\n  display: flex;\n  justify-content: flex-end;\n  padding: 10px;\n}\n\n:deep(.el-pagination) {\n\n  .el-pagination__total,\n  .el-pagination__sizes,\n  .el-pagination__jump {\n    color: #fff !important;\n  }\n\n  &.is-background {\n\n    .btn-prev,\n    .btn-next,\n    .el-pager li {\n      background-color: rgba(25, 37, 60, 0.8) !important;\n      color: #fff !important;\n      border: 1px solid #3ba1f4;\n      margin: 0 3px;\n\n      &:hover {\n        color: #409eff !important;\n        background-color: rgba(37, 50, 69, 0.4) !important;\n      }\n\n      &.is-active {\n        background-color: #409eff !important;\n        color: #fff !important;\n        border-color: #409eff;\n      }\n\n      &:disabled {\n        background-color: rgba(25, 37, 60, 0.4) !important;\n        color: #606266 !important;\n      }\n    }\n  }\n}\n\n.loading-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100%;\n  width: 100%;\n}\n\n:deep(.el-loading-spinner) {\n  .el-loading-text {\n    color: #fff;\n    margin: 3px 0;\n    font-size: 14px;\n  }\n\n  .circular {\n    .path {\n      stroke: #3ba1f4;\n    }\n  }\n}\n</style>\n"]}]}