{"remainingRequest": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\views\\nenghao.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\views\\nenghao.vue", "mtime": 1751446100602}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\views\\nenghao.vue"], "names": [], "mappings": ";AA6WA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEpD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1E,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1E,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1E,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1E,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1E,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1E,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1E,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAErD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACT,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACT;YACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;cACrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACpC,CAAC;UACH,CAAC;UACD;YACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;cACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACpC,CAAC;UACH,CAAC;UACD;YACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;cACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACpC,CAAC;UACH,CAAC;QACH,CAAC;MACH,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC;MACH,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC;MACH,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC;MACH,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC;MACH,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;MACH,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACV;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC/C,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC/C,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC/C,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC/C,CAAC;MACH,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC/C,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC/C,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC/C,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC/C,CAAC;MACH,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAChB,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MAClB,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACvE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACX,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC3B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE7B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC;;MAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;UAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;UACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;UACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC;MACT;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC9B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACV;;MAEA,CAAC,CAAC,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACb,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC9B,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAC/E;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACV;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC;YACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/C,CAAC;UACH;QACF,CAAC;;QAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;UAC/B,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;YAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;UAExB,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE7D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxD,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B;MACF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE1C,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,EAAE;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QAC7C,EAAE;UACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb;MACF;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC5C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB;;MAEA,CAAC,CAAC,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5C;YACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAC1E;QACF,CAAC;;QAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;UAC/B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YAC5C,CAAC;YACD;UACF,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACV;MACF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACV;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC1D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACnD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACtD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC1D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjE,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC7C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB;;MAEA,CAAC,CAAC,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7C;YACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB;QACF,CAAC;;QAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;UAC/B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UACxC,CAAC,EAAE,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACV;MACF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACV;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE1B,CAAC,CAAC,EAAE;QACF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,EAAE,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAErE,CAAC,EAAE,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAErE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEvE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEzE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjD,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5C;IACF,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MAC1C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MACtC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;MAEzC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC;;MAED,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC;;MAED,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC;;MAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE3D,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MAC1C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACrE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAErD,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC;;MAED,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC;;MAED,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB;;MAEA,CAAC,CAAC,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;QAEhD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7C;YACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB;QACF,CAAC;;QAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC;QACH,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtE;MACF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvD;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC9B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB;;MAEA,CAAC,CAAC,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;QAEhD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5C;YACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB;QACF,CAAC;;QAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;cACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACxC,CAAC,CAAC;YACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC;QACH,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC;QACH;MACF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1D;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MAC5B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;QAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR;;MAEA,CAAC,CAAC,EAAE;QACF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB;;QAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5C;YACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB;QACF,CAAC;;QAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;UAC/B,CAAC,EAAE;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;UACrC,EAAE;YACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;gBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACtB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACtC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAClB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACpC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAClB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACpC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;cAC9D,CAAC,CAAC;cACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,EAAE,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC;QACF,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACtB;MACF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MACtB;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR;;MAEA,CAAC,CAAC,EAAE;QACF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;UAChD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC1B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAClE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;;QAEH,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE7C,CAAC,EAAE,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;UACZ,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;UACnB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;UACnB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;UACjB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;UACjB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAClB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;UACnB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACrB,CAAC;;QAED,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1D;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC5B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB;;MAEA,CAAC,CAAC,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;QAEhD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7C;YACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB;QACF,CAAC;;QAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;cACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YAChC,CAAC,CAAC;YACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC;QACH,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC;QACH;MACF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7D;IACF,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEjB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;IAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAElD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC9B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB;;IAEA,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAElC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEjB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC;AACH,CAAC", "file": "E:/svn/***********-0186-SFTianjinUniversityH5/src/src/views/nenghao.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div class=\"contents\" v-if=\"isshow\">\n    <div class=\"toubu\">\n      <div\n        style=\"margin-left: 20px; display: flex; align-items: center\"\n        v-if=\"false\"\n      >\n        <div style=\"display: flex; width: 100%; align-items: center\">\n          <span class=\"sp\">当前位置：</span>\n          <el-select\n            class=\"el-select\"\n            v-model=\"selectvalue1\"\n            placeholder=\"selectvalue1\"\n            style=\"width: 64px; height: 35.1px\"\n            @change=\"handleChange\"\n          >\n            <el-option\n              v-for=\"item in options1\"\n              :key=\"item.value\"\n              :label=\"item.label\"\n              :value=\"item.value\"\n            />\n          </el-select>\n          <el-select\n            class=\"el-select\"\n            v-model=\"selectvalue2\"\n            placeholder=\"selectvalue2\"\n            style=\"width: 64px; height: 35.1px\"\n            @change=\"handleChange\"\n          >\n            <el-option\n              v-for=\"item in options2\"\n              :key=\"item.value\"\n              :label=\"item.label\"\n              :value=\"item.value\"\n            />\n          </el-select>\n          <el-select\n            class=\"el-select\"\n            v-model=\"selectvalue3\"\n            placeholder=\"selectvalue3\"\n            style=\"width: 78px; height: 35.1px\"\n            @change=\"handleChange\"\n          >\n            <el-option\n              v-for=\"item in options3\"\n              :key=\"item.value\"\n              :label=\"item.label\"\n              :value=\"item.value\"\n            />\n          </el-select>\n        </div>\n        <img\n          v-if=\"isshow\"\n          class=\"img1sss\"\n          @click=\"anniu()\"\n          src=\"../assets/image/table-x.png\"\n          alt=\"\"\n        />\n      </div>\n\n      <div class=\"all\">\n        <div class=\"all1\">\n          <Titles class=\"ltitle\" tit=\"大型仪器平台概况\">\n            <div class=\"nenghao\">累计总能耗:</div>\n            <p class=\"nhp\">{{ totalConsumption.total.toFixed(1) }} kwh</p>\n            <div class=\"nh\">\n              <img class=\"nhimg\" src=\"../assets/image/nenghao1.png\" alt=\"\" />\n              <div class=\"nhtit\">\n                <p class=\"p11\">{{ totalConsumption.daily.toFixed(1) }}kwh</p>\n                <p class=\"p2\">本日累计能耗</p>\n              </div>\n              <!-- <div class=\"nhtit1\">\n                <img class=\"nhimg1\" src=\"../assets/image/nhxia.png\" alt=\"\" />\n                <p class=\"pp1\">{{ ((totalConsumption.daily - totalConsumption.daily) / totalConsumption.daily * 100).toFixed(1) }}%</p>\n              </div> -->\n            </div>\n            <div class=\"nh\">\n              <img class=\"nhimg\" src=\"../assets/image/nenghao1.png\" alt=\"\" />\n              <div class=\"nhtit\">\n                <p class=\"p11\">{{ totalConsumption.weekly.toFixed(1) }}kwh</p>  \n                <p class=\"p2\">近7日累计能耗</p>\n              </div>\n              <!-- <div class=\"nhtit1\">\n                <img class=\"nhimg1\" src=\"../assets/image/nhxia.png\" alt=\"\" />\n                <p class=\"pp1\">{{ ((totalConsumption.weekly - totalConsumption.weekly) / totalConsumption.weekly * 100).toFixed(1) }}%</p>\n              </div> -->\n            </div>\n            <div class=\"nh\">\n              <img class=\"nhimg\" src=\"../assets/image/nenghao2.png\" alt=\"\" />\n              <div class=\"nhtit\">\n                <p class=\"p12\">{{ totalConsumption.monthly.toFixed(1) }}kwh</p>\n                <p class=\"p2\">近30日累计能耗</p>\n              </div>\n              <div class=\"nht\">\n                <!-- <div class=\"nhtit1\">\n                  <img\n                    class=\"nhimg1\"\n                    src=\"../assets/image/nhshang.png\"\n                    alt=\"\"\n                  />\n                  <p class=\"pp2\">{{ ((totalConsumption.monthly - totalConsumption.monthly) / totalConsumption.monthly * 100).toFixed(1) }}%</p>\n                </div> -->\n                <!-- <p class=\"pp\">环比</p> -->\n              </div>\n            </div>\n          </Titles>\n          <!-- <Titles class=\"ltitle\" style=\"margin-top: 20px\" tit=\"电耗费用\">\n            <div class=\"shinei\">\n              <Electricity2\n                :yesterday-fee=\"electricityFees.yesterday\"\n                :monthly-fee=\"electricityFees.monthly\"\n                :yearly-fee=\"electricityFees.yearly\"\n              ></Electricity2>\n            </div>\n          </Titles> -->\n        </div>\n        <!-- <div class=\"line1\"></div> -->\n        <div class=\"all2\">\n          <!-- <Titles class=\"ltitle1\" tit=\"峰平谷用电量\">\n            <div class=\"shinei\">\n              <Electricity8></Electricity8>\n            </div>\n          </Titles> -->\n\n          <Titles class=\"ltitle1\" tit=\"用电量排名\">\n            <div class=\"shinei\">\n              <Electricity3\n                :electricity-data=\"electricityUsageData\"\n              ></Electricity3>\n            </div>\n          </Titles>\n          <!-- <Titles class=\"ltitle1\" tit=\"分区用电量\">\n            <div class=\"shinei\">\n              <Electricity4 :fee-data=\"electricityFeeData\"></Electricity4>\n            </div>\n          </Titles> -->\n        </div>\n        <div class=\"all3\">\n          <div>\n            <Titles class=\"ltitle1\" tit=\"抄电表记录\">\n              <div class=\"shinei\">\n                <div class=\"table-container\">\n                  <el-table\n                    :data=\"meterReadings\"\n                    style=\"width: 100%; background: transparent\"\n                    :header-cell-style=\"{\n                      background: '#2A363F',\n                      color: '#fff',\n                      borderColor: '#1e415c',\n                    }\"\n                    :cell-style=\"{\n                      background: '#2A363F',\n                      color: '#fff',\n                      borderColor: '#1e415c',\n                    }\"\n                    height=\"320\"\n                  >\n                    <el-table-column\n                      prop=\"roomtag\"\n                      label=\"房间标识\"\n                      align=\"center\"\n                      width=\"120\"\n                    />\n                    <el-table-column\n                      prop=\"zhaddress\"\n                      label=\"住户地址\"\n                      align=\"center\"\n                      width=\"180\"\n                    />\n                    <el-table-column\n                      prop=\"readvalue\"\n                      label=\"抄表值\"\n                      align=\"center\"\n                      width=\"120\"\n                    />\n                    <el-table-column\n                      prop=\"readtime\"\n                      label=\"抄表时间\"\n                      align=\"center\"\n                      width=\"180\"\n                    />\n                  </el-table>\n                </div>\n              </div>\n            </Titles>\n          </div>\n          <Titles class=\"ltitle1\" style=\"margin-top:15px\" tit=\"用电量记录\">\n            <div class=\"shinei\">\n              <div class=\"title-container\">\n                <div class=\"more-btn\" @click=\"showDetailDialog\">\n                  <span>更多</span>\n                  <i class=\"el-icon-arrow-right\"></i>\n                </div>\n              </div>\n              <div class=\"table-container\">\n                <el-table\n                  :data=\"electricityUsageData\"\n                  style=\"width: 100%; background: transparent\"\n                  :header-cell-style=\"{\n                    background: '#2A363F',\n                    color: '#fff',\n                    borderColor: '#38444C',\n                  }\"\n                  :cell-style=\"{\n                    background: '#2A363F',\n                    color: '#fff',\n                    borderColor: '#1e415c',\n                  }\"\n                  height=\"400\"\n                >\n                  <el-table-column\n                    prop=\"roomtag\"\n                    label=\"房间标识\"\n                    align=\"center\"\n                    width=\"120\"\n                  />\n                  <el-table-column\n                    prop=\"zhaddress\"\n                    label=\"住户地址\"\n                    align=\"center\"\n                    width=\"180\"\n                  />\n                  <el-table-column\n                    prop=\"ylvalue\"\n                    label=\"用电量(kwh)\"\n                    align=\"center\"\n                    width=\"120\"\n                  >\n                    <!-- <template slot-scope=\"scope\">\n                      {{ parseFloat(scope.row.ylvalue).toFixed(2) }}\n                    </template> -->\n                  </el-table-column>\n                  <el-table-column\n                    prop=\"endtime\"\n                    label=\"抄表时间\"\n                    align=\"center\"\n                    width=\"180\"\n                  />\n                </el-table>\n              </div>\n            </div>\n          </Titles>\n        </div>\n      </div>\n    </div>\n\n    <!-- 自定义弹窗 -->\n    <div\n      v-if=\"dialogVisible\"\n      class=\"custom-modal-overlay\"\n      @click.self=\"dialogVisible = false\"\n    >\n      <div class=\"custom-modal\">\n        <div class=\"modal-header\">\n          <span class=\"modal-title\">用电量详细记录</span>\n          <div class=\"header-buttons\">\n            <el-button\n              type=\"text\"\n              class=\"close-text\"\n              @click=\"dialogVisible = false\"\n              >关闭</el-button\n            >\n            <i\n              class=\"el-icon-close close-btn\"\n              @click=\"dialogVisible = false\"\n            ></i>\n          </div>\n        </div>\n        <div class=\"modal-content\">\n          <!-- 搜索条件 -->\n          <div class=\"search-container\">\n            <el-date-picker\n              v-model=\"dateRange\"\n              type=\"daterange\"\n              range-separator=\"至\"\n              start-placeholder=\"开始日期\"\n              end-placeholder=\"结束日期\"\n              value-format=\"YYYY-MM-DD\"\n              style=\"width: 380px; margin-right: 15px\"\n            >\n            </el-date-picker>\n            <!-- <el-date-picker\n                v-model=\"dateRange\"\n                type=\"daterange\"\n                align=\"right\"\n                unlink-panels\n                range-separator=\"至\"\n                start-placeholder=\"开始日期\"\n                end-placeholder=\"结束日期\"\n                :picker-options=\"pickerOptions\"\n              > -->\n            <!-- </el-date-picker> -->\n\n            <el-button type=\"primary\" @click=\"searchData\">查询</el-button>\n            <!-- <el-button type=\"success\" @click=\"exportToExcel\">导出</el-button> -->\n          </div>\n\n          <!-- 详细数据表格 -->\n          <el-table\n            :data=\"detailData\"\n            style=\"width: 100%; margin-top: 20px\"\n            :header-cell-style=\"{\n              background: '#2C4255',\n              color: '#fff',\n              borderColor: '#1e415c',\n            }\"\n            :cell-style=\"{\n              background: '#2C4255',\n              color: '#fff',\n              borderColor: '#1e415c',\n            }\"\n            height=\"500\"\n          >\n            <el-table-column\n              prop=\"roomtag\"\n              label=\"房间标识\"\n              align=\"center\"\n              width=\"180\"\n            />\n            <el-table-column\n              prop=\"zhaddress\"\n              label=\"住户地址\"\n              align=\"center\"\n              width=\"180\"\n            />\n            <el-table-column\n              prop=\"startcode\"\n              label=\"起码\"\n              align=\"center\"\n              width=\"180\"\n            />\n            <el-table-column\n              prop=\"endcode\"\n              label=\"止码\"\n              align=\"center\"\n              width=\"180\"\n            />\n            <el-table-column\n              prop=\"ylvalue\"\n              label=\"用电量(kwh)\"\n              align=\"center\"\n              width=\"180\"\n            />\n            <el-table-column\n              prop=\"jfmx\"\n              label=\"缴费明细\"\n              align=\"center\"\n              width=\"180\"\n            />\n\n            <el-table-column\n              prop=\"endtime\"\n              label=\"抄表时间\"\n              align=\"center\"\n              width=\"181\"\n            />\n          </el-table>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as XLSX from \"xlsx\";\nimport Electricity from \"../components/echarts/dianbiao/biao1.vue\";\nimport biao1s from \"../components/echarts/dianbiao/biao1s.vue\";\nimport biao1ss from \"../components/echarts/dianbiao/biao1ss.vue\";\nimport Titles from \"../components/common/Titles.vue\";\n\nimport Electricity2 from \"../components/echarts/dianbiao/Electricity2.vue\";\nimport Electricity3 from \"../components/echarts/dianbiao/Electricity3.vue\";\nimport Electricity4 from \"../components/echarts/dianbiao/Electricity4.vue\";\nimport Electricity5 from \"../components/echarts/dianbiao/Electricity5.vue\";\nimport Electricity6 from \"../components/echarts/dianbiao/Electricity6.vue\";\nimport Electricity7 from \"../components/echarts/dianbiao/Electricity7.vue\";\nimport Electricity8 from \"../components/echarts/dianbiao/Electricity8.vue\";\nimport huanxing from \"@/components/echarts/xiaobingtu.vue\";\nimport axios from \"axios\";\nimport { buildingEnergyDataList } from \"@/api/device\";\n\nexport default {\n  components: {\n    Titles,\n    Electricity,\n    Electricity2,\n    Electricity3,\n    Electricity4,\n    Electricity5,\n    Electricity6,\n    Electricity7,\n    Electricity8,\n    huanxing,\n    biao1s,\n    biao1ss,\n  },\n  data() {\n    return {\n      isshow: true,\n      pickerOptions: {\n        shortcuts: [\n          {\n            text: \"最近一周\",\n            onClick(picker) {\n              const end = new Date();\n              const start = new Date();\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);\n              picker.$emit(\"pick\", [start, end]);\n            },\n          },\n          {\n            text: \"最近一个月\",\n            onClick(picker) {\n              const end = new Date();\n              const start = new Date();\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);\n              picker.$emit(\"pick\", [start, end]);\n            },\n          },\n          {\n            text: \"最近三个月\",\n            onClick(picker) {\n              const end = new Date();\n              const start = new Date();\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);\n              picker.$emit(\"pick\", [start, end]);\n            },\n          },\n        ],\n      },\n      options: [\n        {\n          value: \"总览\",\n          label: \"总览\",\n        },\n        {\n          value: \"能耗分析\",\n          label: \"能耗分析\",\n        },\n        {\n          value: \"能流分析\",\n          label: \"能流分析\",\n        },\n        {\n          value: \"设备状态\",\n          label: \"设备状态\",\n        },\n        {\n          value: \"一键抄表\",\n          label: \"一键抄表\",\n        },\n        {\n          value: \"费用管理\",\n          label: \"费用管理\",\n        },\n        {\n          value: \"碳排放管理\",\n          label: \"碳排放管理\",\n        },\n      ],\n      selectvalue2: \"B3\",\n      options2: [\n        {\n          value: \"B3\",\n          label: \"B3\",\n        },\n      ],\n      selectvalue3: \"B3\",\n      options3: [\n        {\n          value: \"B3\",\n          label: \"B3\",\n        },\n      ],\n      selectvalue1: \"B3\",\n      options1: [\n        {\n          value: \"B3\",\n          label: \"B3\",\n        },\n      ],\n      selectvalue3: \"B1栋\",\n      options4: [\n        {\n          value: \"B1栋\",\n          label: \"B1栋\",\n        },\n        {\n          value: \"B2栋\",\n          label: \"B2栋\",\n        },\n        {\n          value: \"B3栋\",\n          label: \"B3栋\",\n        },\n        {\n          value: \"B4栋\",\n          label: \"B4栋\",\n        },\n        {\n          value: \"W1栋\",\n          label: \"W1栋\",\n        },\n        {\n          value: \"W2栋\",\n          label: \"W2栋\",\n        },\n      ],\n      selectvalue4: \"B1栋\",\n      optionData: [\n        {\n          name: \"一级告警\",\n          value: 16,\n          itemStyle: { color: \"#EB6877\", opacity: 0.9 },\n        },\n        {\n          name: \"二级告警\",\n          value: 27,\n          itemStyle: { color: \"#F8B551\", opacity: 0.9 },\n        },\n        {\n          name: \"三级告警\",\n          value: 17,\n          itemStyle: { color: \"#B954E8\", opacity: 0.9 },\n        },\n        {\n          name: \"四级告警\",\n          value: 40,\n          itemStyle: { color: \"#0284F0\", opacity: 0.9 },\n        },\n      ],\n      optionData1: [\n        {\n          name: \"一级告警\",\n          value: 16,\n          itemStyle: { color: \"#EB6877\", opacity: 0.9 },\n        },\n        {\n          name: \"二级告警\",\n          value: 27,\n          itemStyle: { color: \"#F8B551\", opacity: 0.9 },\n        },\n        {\n          name: \"三级告警\",\n          value: 17,\n          itemStyle: { color: \"#B954E8\", opacity: 0.9 },\n        },\n        {\n          name: \"四级告警\",\n          value: 40,\n          itemStyle: { color: \"#0284F0\", opacity: 0.9 },\n        },\n      ],\n      token: {\n        systemnum: \"\",\n        tokenvalue: \"\",\n        expiretime: \"\",\n      },\n      baseURL: \"/power\", // Replace with actual server address\n      // baseURL: 'http://*************:8080',\n      isTokenValid: false,\n      totalConsumption: {\n        daily: 0,\n        weekly: 0,\n        monthly: 0,\n        total: 0, // 累计用量\n      },\n      curBuilding: null, // 当前建筑信息\n      totalElectricityFee: 0,\n      roomtag: \"\", // Add your roomtag here if you want to query specific user\n      electricityFees: {\n        yesterday: 0,\n        monthly: 0,\n        yearly: 0,\n      },\n      meterReadings: [], // 新增抄表记录数据\n      electricityUsageData: [], // 新增用电量数据\n      \n      dialogVisible: false, // 修改为 false，默认关闭\n      dateRange: \"\",\n      detailData: [],\n      electricityFeeData: [], // 新增电费数据\n    };\n  },\n  methods: {\n    anniu() {\n      this.isshow = false;\n    },\n\n    // 构建API参数 - 参考 BimEnergyOverview.vue 的实现\n    buildParams(type) {\n      // 格式化日期为 YYYY-MM-DD 格式\n      const formatDate = (date) => {\n        const year = date.getFullYear();\n        const month = String(date.getMonth() + 1).padStart(2, '0');\n        const day = String(date.getDate()).padStart(2, '0');\n        return `${year}-${month}-${day}`;\n      };\n\n      const now = new Date();\n      const today = formatDate(now);\n\n      const param = {\n        buildingId: 1, // 直接写死为1\n        deviceType: 'electricity',\n        type: 'electricity',\n        displayType: \"day\",\n        from: today,\n        to: today,\n      };\n\n      // 根据类型调整参数\n      switch (type) {\n        case 'daily':\n          // 本日用量：昨天到今天\n          param.displayType = 'day';\n          const yesterday = new Date(now);\n          yesterday.setDate(yesterday.getDate() - 1);\n          param.from = formatDate(yesterday);\n          param.to = today;\n          break;\n        case 'weekly':\n          // 近7日用量：7天前到今天\n          param.displayType = 'day';\n          const weekAgo = new Date(now);\n          weekAgo.setDate(weekAgo.getDate() - 7);\n          param.from = formatDate(weekAgo);\n          param.to = today;\n          break;\n        case 'monthly':\n          // 近30日用量：30天前到今天\n          param.displayType = 'day';\n          const monthAgo = new Date(now);\n          monthAgo.setDate(monthAgo.getDate() - 30);\n          param.from = formatDate(monthAgo);\n          param.to = today;\n          break;\n        case 'total':\n          // 累计用量：从很久以前到今天\n          param.displayType = 'total';\n          const tenYearsAgo = new Date(now);\n          tenYearsAgo.setFullYear(tenYearsAgo.getFullYear() - 10);\n          param.from = formatDate(tenYearsAgo);\n          param.to = today;\n          break;\n      }\n\n      return param;\n    },\n\n    // 使用新API获取能耗数据\n    async getEnergyDataByType(type) {\n      const param = this.buildParams(type);\n      if (!param) {\n        return 0;\n      }\n\n      try {\n        const res = await buildingEnergyDataList(param);\n        let total = 0;\n        if (res.data && res.data.datas) {\n          total = res.data.datas.reduce((sum, d) => sum + parseInt(d.totalVal || 0), 0);\n        }\n        return total;\n      } catch (error) {\n        console.error(`获取${type}数据失败:`, error);\n        return 0;\n      }\n    },\n    async getToken() {\n      try {\n        const response = await axios.get(\n          `${this.baseURL}/api/ztwyPower/getToken`,\n          {\n            params: {\n              systemnum: \"346E473FD1EF46E3A2EE43F393BCAF7C\",\n            },\n          }\n        );\n\n        if (response.data.errcode === 0) {\n          const { systemnum, tokenvalue, expiretime } =\n            response.data.resultvalue;\n          this.token = {\n            systemnum,\n            tokenvalue,\n            expiretime,\n          };\n          this.isTokenValid = true;\n\n          // 存储 token 和获取时间\n          const tokenData = {\n            ...this.token,\n            timestamp: new Date().getTime(),\n          };\n          localStorage.setItem(\"powerToken\", JSON.stringify(tokenData));\n\n          console.log(\"Token updated successfully:\", this.token);\n        } else {\n          console.error(\"Failed to get token:\", response.data.errmsg);\n          this.isTokenValid = false;\n        }\n      } catch (error) {\n        console.error(\"Error getting token:\", error);\n        this.isTokenValid = false;\n      }\n    },\n    checkTokenValidity() {\n      const storedToken = localStorage.getItem(\"powerToken\");\n      if (storedToken) {\n        const tokenData = JSON.parse(storedToken);\n        const expireTime = new Date(tokenData.expiretime).getTime();\n        const currentTime = new Date().getTime();\n        const tokenTimestamp = tokenData.timestamp;\n\n        // 检查 token 是否过期或距离上次获取是否超过5分钟\n        if (\n          currentTime < expireTime &&\n          currentTime - tokenTimestamp < 5 * 60 * 1000\n        ) {\n          this.token = {\n            systemnum: tokenData.systemnum,\n            tokenvalue: tokenData.tokenvalue,\n            expiretime: tokenData.expiretime,\n          };\n          this.isTokenValid = true;\n          return true;\n        }\n      }\n      return false;\n    },\n    async getElectricityUsage(startTime, endTime) {\n      if (!this.isTokenValid) {\n        await this.getToken();\n      }\n\n      try {\n        const response = await axios.post(\n          `${this.baseURL}/api/ztwyPower/getYdlByTime`,\n          {\n            tokenvalue: this.token.tokenvalue,\n            starttime: startTime,\n            endtime: endTime,\n            roomtag: this.roomtag, // Optional: if empty, will return all users' data\n          }\n        );\n\n        if (response.data.errcode === 0) {\n          // Calculate total consumption by summing ylvalue\n          const totalConsumption = response.data.resultvalue.reduce(\n            (sum, item) => {\n              return sum + parseFloat(item.ylvalue || 0);\n            },\n            0\n          );\n          return totalConsumption;\n        } else {\n          console.error(\n            \"Failed to get electricity usage:\",\n            response.data.errmsg\n          );\n          return 0;\n        }\n      } catch (error) {\n        console.error(\"Error getting electricity usage:\", error);\n        return 0;\n      }\n    },\n    formatDate(date) {\n      const year = date.getFullYear();\n      const month = String(date.getMonth() + 1).padStart(2, \"0\");\n      const day = String(date.getDate()).padStart(2, \"0\");\n      const hours = String(date.getHours()).padStart(2, \"0\");\n      const minutes = String(date.getMinutes()).padStart(2, \"0\");\n      const seconds = String(date.getSeconds()).padStart(2, \"0\");\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\n    },\n    async getPaymentStatistics(startTime, endTime) {\n      if (!this.isTokenValid) {\n        await this.getToken();\n      }\n\n      try {\n        const response = await axios.post(\n          `${this.baseURL}/api/ztwyPower/getJfAllInfos`,\n          {\n            tokenvalue: this.token.tokenvalue,\n            starttime: startTime,\n            endtime: endTime,\n            roomtag: this.roomtag,\n          }\n        );\n\n        if (response.data.errcode === 0) {\n          const totalFee = response.data.resultvalue.reduce((sum, item) => {\n            return sum + parseFloat(item.zdf || 0);\n          }, 0);\n          return totalFee;\n        } else {\n          console.error(\n            \"Failed to get payment statistics:\",\n            response.data.errmsg\n          );\n          return 0;\n        }\n      } catch (error) {\n        console.error(\"Error getting payment statistics:\", error);\n        return 0;\n      }\n    },\n    async updateConsumptionData() {\n      console.log(5685);\n      console.log('开始获取能耗数据...');\n\n      try {\n        // 使用新的API获取四个数据项\n        // 累计用量\n        this.totalConsumption.total = await this.getEnergyDataByType('total');\n\n        // 本日用量\n        this.totalConsumption.daily = await this.getEnergyDataByType('daily');\n\n        // 近7日用量\n        this.totalConsumption.weekly = await this.getEnergyDataByType('weekly');\n\n        // 近30日用量\n        this.totalConsumption.monthly = await this.getEnergyDataByType('monthly');\n\n        console.log('能耗数据更新成功:', this.totalConsumption);\n      } catch (error) {\n        console.error('更新能耗数据失败:', error);\n        // 如果新API失败，回退到原来的方法\n        await this.updateConsumptionDataFallback();\n      }\n    },\n\n    // 原来的数据获取方法作为备用\n    async updateConsumptionDataFallback() {\n      const now = new Date();\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n      const yesterday = new Date(today);\n      yesterday.setDate(yesterday.getDate() - 1);\n      const weekAgo = new Date(today);\n      weekAgo.setDate(weekAgo.getDate() - 7);\n      const monthAgo = new Date(today);\n      monthAgo.setDate(monthAgo.getDate() - 30);\n\n      // Get daily consumption\n      this.totalConsumption.daily = await this.getElectricityUsage(\n        this.formatDate(yesterday),\n        this.formatDate(now)\n      );\n\n      // Get weekly consumption\n      this.totalConsumption.weekly = await this.getElectricityUsage(\n        this.formatDate(weekAgo),\n        this.formatDate(now)\n      );\n\n      // Get monthly consumption\n      this.totalConsumption.monthly = await this.getElectricityUsage(\n        this.formatDate(monthAgo),\n        this.formatDate(now)\n      );\n\n      // 累计用量使用近30日数据作为近似值\n      this.totalConsumption.total = this.totalConsumption.monthly;\n\n      // Get total electricity fee\n      this.totalElectricityFee = await this.getPaymentStatistics(\n        this.formatDate(monthAgo),\n        this.formatDate(now)\n      );\n    },\n    async updateFeeData() {\n      const now = new Date();\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n      const yesterday = new Date(today);\n      yesterday.setDate(yesterday.getDate() - 1);\n      const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);\n      const yearStart = new Date(today.getFullYear(), 0, 1);\n\n      // Get yesterday's fee\n      this.electricityFees.yesterday = await this.getPaymentStatistics(\n        this.formatDate(yesterday),\n        this.formatDate(today)\n      );\n\n      // Get monthly fee\n      this.electricityFees.monthly = await this.getPaymentStatistics(\n        this.formatDate(monthStart),\n        this.formatDate(now)\n      );\n\n      // Get yearly fee\n      this.electricityFees.yearly = await this.getPaymentStatistics(\n        this.formatDate(yearStart),\n        this.formatDate(now)\n      );\n    },\n    async getMeterReadings() {\n      if (!this.isTokenValid) {\n        await this.getToken();\n      }\n\n      try {\n        const now = new Date();\n        const sevenDaysAgo = new Date(now);\n        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);\n\n        const response = await axios.post(\n          `${this.baseURL}/api/ztwyPower/getAllDbValue`,\n          {\n            tokenvalue: this.token.tokenvalue,\n            starttime: this.formatDate(sevenDaysAgo),\n            endtime: this.formatDate(now),\n            tag: 0, // 返回最后一次抄表记录\n            nodeid: 0, // 默认为0，返回全部\n            roomtag: this.roomtag,\n          }\n        );\n\n        if (response.data.errcode === 0) {\n          this.meterReadings = response.data.resultvalue;\n          console.log(\n            \"Meter readings retrieved successfully:\",\n            this.meterReadings\n          );\n        } else {\n          console.error(\"Failed to get meter readings:\", response.data.errmsg);\n        }\n      } catch (error) {\n        console.error(\"Error getting meter readings:\", error);\n      }\n    },\n    async getElectricityUsageData() {\n      if (!this.isTokenValid) {\n        await this.getToken();\n      }\n\n      try {\n        const now = new Date();\n        const sevenDaysAgo = new Date(now);\n        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);\n\n        const response = await axios.post(\n          `${this.baseURL}/api/ztwyPower/getYdlByTime`,\n          {\n            tokenvalue: this.token.tokenvalue,\n            starttime: this.formatDate(sevenDaysAgo),\n            endtime: this.formatDate(now),\n            roomtag: this.roomtag,\n          }\n        );\n\n        if (response.data.errcode === 0) {\n          this.electricityUsageData = response.data.resultvalue\n            .map((item) => ({\n              ...item,\n              ylvalue: parseFloat(item.ylvalue || 0),\n            }))\n            .sort((a, b) => new Date(a.readtime) - new Date(b.readtime));\n          console.log(\n            \"Electricity usage data retrieved successfully:\",\n            this.electricityUsageData\n          );\n        } else {\n          console.error(\n            \"Failed to get electricity usage:\",\n            response.data.errmsg\n          );\n        }\n      } catch (error) {\n        console.error(\"Error getting electricity usage:\", error);\n      }\n    },\n    showDetailDialog() {\n      this.dialogVisible = true;\n      this.detailData = []; // 清空数据\n      // 默认显示最近一天的数据\n      const end = new Date();\n      const start = new Date();\n      start.setTime(start.getTime() - 3600 * 1000 * 24); // 最近一天\n      this.dateRange = [\n        this.formatDate(start).split(\" \")[0],\n        this.formatDate(end).split(\" \")[0],\n      ];\n      this.searchData(); // 自动查询最近一天的数据\n    },\n    async searchData() {\n      if (!this.dateRange || this.dateRange.length !== 2) {\n        this.$message.warning(\"请选择日期范围\");\n        return;\n      }\n\n      try {\n        if (!this.isTokenValid) {\n          await this.getToken();\n        }\n\n        const response = await axios.post(\n          `${this.baseURL}/api/ztwyPower/getYdlByTime`,\n          {\n            tokenvalue: this.token.tokenvalue,\n            starttime: `${this.dateRange[0]} 00:00:00`,\n            endtime: `${this.dateRange[1]} 23:59:59`,\n            roomtag: this.roomtag,\n          }\n        );\n\n        if (response.data.errcode === 0) {\n          if (\n            response.data.resultvalue &&\n            response.data.resultvalue.length > 0\n          ) {\n            this.detailData = response.data.resultvalue\n              .map((item) => ({\n                roomtag: item.roomtag || \"\",\n                zhaddress: item.zhaddress || \"\",\n                startcode: item.startcode\n                  ? parseFloat(item.startcode).toFixed(1)\n                  : \"0.0\",\n                endcode: item.endcode\n                  ? parseFloat(item.endcode).toFixed(1)\n                  : \"0.0\",\n                ylvalue: item.ylvalue\n                  ? parseFloat(item.ylvalue).toFixed(2)\n                  : \"0.00\",\n                jfmx: item.jfmx || \"\",\n                endtime: item.endtime ? item.endtime.replace(/-/g, \"/\") : \"\",\n              }))\n              .sort((a, b) => new Date(b.endtime) - new Date(a.endtime));\n            this.$message.success(\"查询成功\");\n          } else {\n            this.detailData = [];\n            this.$message.warning(\"所选时间范围内无数据\");\n          }\n        } else {\n          this.$message.error(\"获取数据失败：\" + response.data.errmsg);\n          this.detailData = [];\n        }\n      } catch (error) {\n        this.$message.error(\"获取数据失败：\" + (error.message || \"未知错误\"));\n        this.detailData = [];\n      }\n    },\n    exportToExcel() {\n      if (!this.detailData || !this.detailData.length) {\n        this.$message.warning(\"暂无数据可导出\");\n        return;\n      }\n\n      try {\n        // 准备要导出的数据\n        const exportData = this.detailData.map((item) => ({\n          房间标识: item.roomtag || \"\",\n          住户地址: item.zhaddress || \"\",\n          起码: item.startcode ? parseFloat(item.startcode).toFixed(1) : \"0.0\",\n          止码: item.endcode ? parseFloat(item.endcode).toFixed(1) : \"0.0\",\n          \"用电量(kwh)\": item.ylvalue\n            ? parseFloat(item.ylvalue).toFixed(2)\n            : \"0.00\",\n          缴费明细: item.jfmx || \"\",\n          抄表时间: item.endtime ? item.endtime.replace(/-/g, \"/\") : \"\",\n        }));\n\n        // 创建工作簿并设置数据\n        const ws = XLSX.utils.json_to_sheet(exportData);\n        const wb = XLSX.utils.book_new();\n        XLSX.utils.book_append_sheet(wb, ws, \"用电量记录\");\n\n        // 设置列宽\n        ws[\"!cols\"] = [\n          { wch: 15 }, // 房间标识\n          { wch: 20 }, // 住户地址\n          { wch: 12 }, // 起码\n          { wch: 12 }, // 止码\n          { wch: 15 }, // 用电量\n          { wch: 15 }, // 缴费明细\n          { wch: 20 }, // 抄表时间\n        ];\n\n        // 直接使用 XLSX.writeFile 导出文件\n        const fileName = `用电量记录_${this.dateRange[0]}_${this.dateRange[1]}.xlsx`;\n        XLSX.writeFile(wb, fileName);\n\n        this.$message.success(\"导出成功\");\n      } catch (error) {\n        console.error(\"Export error:\", error);\n        this.$message.error(\"导出失败：\" + (error.message || \"未知错误\"));\n      }\n    },\n    async getElectricityFeeData() {\n      if (!this.isTokenValid) {\n        await this.getToken();\n      }\n\n      try {\n        const now = new Date();\n        const sevenDaysAgo = new Date(now);\n        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);\n\n        const response = await axios.post(\n          `${this.baseURL}/api/ztwyPower/getJfAllInfos`,\n          {\n            tokenvalue: this.token.tokenvalue,\n            starttime: this.formatDate(sevenDaysAgo),\n            endtime: this.formatDate(now),\n            roomtag: this.roomtag,\n          }\n        );\n\n        if (response.data.errcode === 0) {\n          this.electricityFeeData = response.data.resultvalue\n            .map((item) => ({\n              ...item,\n              zdf: parseFloat(item.zdf || 0),\n            }))\n            .sort((a, b) => new Date(a.readtime) - new Date(b.readtime));\n          console.log(\n            \"Electricity fee data retrieved successfully:\",\n            this.electricityFeeData\n          );\n        } else {\n          console.error(\n            \"Failed to get electricity fee data:\",\n            response.data.errmsg\n          );\n        }\n      } catch (error) {\n        console.error(\"Error getting electricity fee data:\", error);\n      }\n    },\n  },\n  async created() {\n    // 初始化建筑信息\n    console.log(5685);\n\n    // 直接设置建筑ID为1\n    this.curBuilding = { id: 1 };\n    console.log('curBuilding 已初始化:', this.curBuilding);\n\n    // 初始化获取 token\n    if (!this.checkTokenValidity()) {\n      await this.getToken();\n    }\n\n    // 更新数据\n    await this.updateConsumptionData();\n    await this.updateFeeData();\n    await this.getMeterReadings();\n    await this.getElectricityUsageData();\n    await this.getElectricityFeeData();\n\n    // 每5分钟更新一次 token\n    setInterval(async () => {\n      await this.getToken();\n    }, 5 * 60 * 1000);\n\n    // 每5分钟更新一次数据\n    setInterval(async () => {\n      await this.updateConsumptionData();\n      await this.updateFeeData();\n      await this.getMeterReadings();\n      await this.getElectricityUsageData();\n      await this.getElectricityFeeData();\n    }, 5 * 60 * 1000);\n  },\n};\n</script>\n\n<style lang=\"less\" scoped>\n.all {\n  display: flex;\n  flex-direction: row;\n  margin-top: 5px;\n\n  .zong {\n    display: flex;\n    flex-direction: row;\n    margin-top: 10px;\n    .echart1,\n    .echart2 {\n      flex: 1;\n\n      .center {\n        margin-top: -24px;\n        font-family: \"Source Han Sans SC\", sans-serif;\n        font-weight: 400;\n        font-size: 17px;\n        color: #00ffb6;\n        text-align: center;\n        margin-bottom: 10px;\n      }\n\n      .btn {\n        width: 133px;\n        height: 31px;\n        border: 1px solid #2d6cb0;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-family: \"Source Han Sans SC\", sans-serif;\n        font-weight: bold;\n        font-size: 15px;\n        color: #ffffff;\n        border-radius: 30px;\n        margin-left: 7%;\n      }\n    }\n  }\n\n  .ltitle1 {\n    margin-top: 10px;\n    position: relative;\n  }\n\n  .line1 {\n    width: 2px;\n    height: 823px;\n    opacity: 0.64;\n    background-color: #204964;\n  }\n\n  .all1 {\n    flex: 557;\n\n    .nenghao {\n      width: 257px;\n      height: 183px;\n      background: url(\"../assets/image/nenghao.png\");\n      background-size: 100% 100%;\n      margin-left: 100px;\n      margin-top: 45px;\n      text-align: center;\n      font-family: Alibaba PuHuiTi;\n      font-weight: 400;\n      font-size: 20px;\n      color: #ffffff;\n      line-height: 213px;\n    }\n\n    .nhp {\n      text-align: center;\n      font-family: Alibaba PuHuiTi;\n      font-weight: 500;\n      font-size: 52px;\n      color: #2cc1ff;\n      margin-top: 8px;\n    }\n\n    .nh {\n      margin-left: 24px;\n      margin-top: 32px;\n      width: 423px;\n      height: 105px;\n      border: 1px solid #364d5a;\n      background-size: 100% 100%;\n      display: flex;\n      flex-direction: row;\n      align-items: center;\n      padding-left: 72px;\n      margin-bottom: 5px;\n      // justify-content: space-evenly;\n\n      .nhimg {\n        width: 107px;\n        height: 90px;\n        margin-right: 35px;\n      }\n\n      .nhtit {\n        width: 148px;\n        margin-left: 10px;\n        margin-top: 3px;\n\n        .p11 {\n          font-family: Alibaba PuHuiTi;\n          font-weight: 400;\n          font-size: 30px;\n          color: #7acfff;\n        }\n\n        .p12 {\n          font-family: Alibaba PuHuiTi;\n          font-weight: 400;\n          font-size: 30px;\n          color: #ffa170;\n        }\n\n        .p2 {\n          font-family: Alibaba PuHuiTi;\n          font-weight: 400;\n          font-size: 20px;\n          color: #ffffff;\n        }\n      }\n\n      .nhtit1 {\n        display: flex;\n        flex-direction: row;\n        align-items: center;\n        margin-left: 35px;\n\n        .nhimg1 {\n          width: 16px;\n          height: 20px;\n        }\n\n        .pp1 {\n          font-family: Alibaba PuHuiTi;\n          font-weight: 400;\n          font-size: 17px;\n          color: #0df29b;\n        }\n\n        .pp2 {\n          font-family: Alibaba PuHuiTi;\n          font-weight: 400;\n          font-size: 17px;\n          color: #ffa170;\n        }\n      }\n\n      .nht {\n        margin-top: 10px;\n        display: flex;\n        flex-direction: column;\n\n        .pp {\n          margin-left: 35px;\n          font-family: Alibaba PuHuiTi;\n          font-weight: 400;\n          font-size: 17px;\n\n          color: #cccccc;\n        }\n      }\n    }\n  }\n\n  .all2 {\n    margin-left: -52px;\n    flex: 627;\n    display: flex;\n    flex-direction: column;\n    .shinei {\n      .itemshei {\n        display: flex;\n        justify-content: space-around;\n        .nenghaos {\n          width: 227px;\n          height: 173px;\n          background: url(\"../assets/image/nenghao.png\");\n          background-size: 100% 100%;\n          text-align: center;\n          margin-left: 10px;\n          margin-top: 33px;\n          text-align: center;\n          font-family: Alibaba PuHuiTi;\n          font-weight: 400;\n          font-size: 14px;\n          color: #ffffff;\n          line-height: 144px;\n        }\n        .nhps {\n          text-align: center;\n          font-family: Alibaba PuHuiTi;\n          font-weight: 500;\n          font-size: 21px;\n          color: #2cc1ff;\n          margin-top: 8px;\n        }\n      }\n    }\n  }\n\n  .all3 {\n    flex: 658;\n    margin-left: 15px;\n  }\n}\n\n.shinei {\n  width: 100%;\n  height: 100%;\n}\n.shuantitle {\n  width: 100%;\n  display: flex;\n  margin-top: 10px;\n  .title {\n    width: 95%;\n    background: url(\"../assets/image/title.png\");\n    background-size: 100% 100%;\n\n    height: 25px;\n    font-family: Source Han Sans SC;\n    font-weight: 400;\n    font-size: 25px;\n    color: #ffffff;\n    text-shadow: 2px 2px 3px rgba(0, 0, 0, 0.47);\n    font-style: italic;\n    text-align: left;\n    line-height: 4px;\n    padding-left: 33px;\n  }\n}\n.nenghao {\n  width: 167px;\n  height: 113px;\n  background: url(\"../assets/image/nenghao.png\");\n  background-size: 100% 100%;\n  text-align: center;\n  margin-left: 83px;\n  // margin-top: 63px;\n  text-align: center;\n  font-family: Alibaba PuHuiTi;\n  font-weight: 400;\n  font-size: 14px;\n  color: #ffffff;\n  line-height: 144px;\n}\n.nhp {\n  text-align: center;\n  font-family: Alibaba PuHuiTi;\n  font-weight: 500;\n  font-size: 25px;\n  color: #2cc1ff;\n  margin-top: 8px;\n  width: 79%;\n}\n\n.contents {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  background: url(\"../assets/image/zichanbeijin.png\");\n  width: 1863px;\n  height: 868px;\n  z-index: 99999;\n  padding-left: 34px;\n  padding-right: 22px;\n  padding-top: 21px;\n}\n.toubu {\n  width: 100%;\n\n  position: relative;\n}\n.el-select {\n  margin-top: -1px;\n  margin-left: 10px;\n  background: #00203d;\n  border-radius: 3px;\n  border: 1px solid #3e89db;\n\n  /deep/.el-select__wrapper {\n    background: #00203d !important;\n    box-shadow: none;\n  }\n\n  /deep/.el-select__wrapper .is-hovering:not {\n    box-shadow: none;\n  }\n\n  /deep/.el-select__wrapper:hover {\n    box-shadow: none;\n  }\n\n  /deep/.el-select__placeholder.is-transparent {\n    color: #2cc1ff;\n  }\n\n  /deep/.el-select__placeholder {\n    color: #2cc1ff;\n  }\n\n  /deep/.el-select-dropdown__item.is-hovering {\n    background-color: #2cc1ff !important;\n  }\n}\n.sp {\n  margin-top: -5px;\n  margin-left: 12px;\n  font-family: Alibaba PuHuiTi;\n  font-weight: bold;\n  font-size: 21px;\n  color: #2cc1ff;\n}\n.img1sss {\n  cursor: pointer;\n  width: 15px;\n  height: 15px;\n}\n\n.table-container {\n  cursor: pointer;\n  .el-table {\n    background-color: transparent !important;\n\n    // 设置滚动条样式\n    ::-webkit-scrollbar {\n      width: 6px;\n      height: 6px;\n    }\n\n    ::-webkit-scrollbar-thumb {\n      background: #0a3054;\n      border-radius: 3px;\n    }\n\n    ::-webkit-scrollbar-track {\n      background: #1e415c;\n      border-radius: 3px;\n    }\n\n    // 设置表格背景透明\n    ::v-deep .el-table__body-wrapper {\n      background-color: transparent;\n\n      &::-webkit-scrollbar {\n        width: 6px;\n        height: 6px;\n      }\n\n      &::-webkit-scrollbar-thumb {\n        background: #0a3054;\n        border-radius: 3px;\n      }\n\n      &::-webkit-scrollbar-track {\n        background: #1e415c;\n        border-radius: 3px;\n      }\n    }\n  }\n}\n\n.custom-modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 9999;\n}\n\n.custom-modal {\n  width: 1300px;\n  background: #1B2A47;\n  border: 1px solid #00E4FF;\n  border-radius: 8px;\n  padding: 0;\n  \n  .modal-header {\n    background: #1B2A47;\n    border-bottom: 1px solid #00E4FF;\n    padding: 15px 20px;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n\n    .modal-title {\n      color: #00E4FF;\n      font-size: 18px;\n      font-weight: bold;\n    }\n\n    .header-buttons {\n      display: flex;\n      align-items: center;\n      \n      .close-text {\n        color: #00E4FF;\n        margin-right: 15px;\n      }\n\n      .close-btn {\n        color: #00E4FF;\n        font-size: 20px;\n        cursor: pointer;\n        &:hover {\n          opacity: 0.8;\n        }\n      }\n    }\n  }\n\n  .modal-content {\n    padding: 20px;\n    background: #1B2A47;\n\n    .search-container {\n      margin-bottom: 20px;\n      \n      .el-button--primary {\n        background: #1B2A47;\n        border: 1px solid #00E4FF;\n        color: #00E4FF;\n        \n        &:hover {\n          opacity: 0.8;\n        }\n      }\n    }\n\n    .el-table {\n      background: #1B2A47 !important;\n      border: 1px solid #00E4FF;\n      \n      &::before {\n        display: none;\n      }\n\n      th {\n        background: #162442 !important;\n        border-bottom: 1px solid #00E4FF !important;\n        color: #00E4FF !important;\n        font-weight: bold;\n      }\n\n      td {\n        background: #1B2A47 !important;\n        border-bottom: 1px solid rgba(0, 228, 255, 0.2) !important;\n        color: #fff !important;\n      }\n\n      .el-table__row:hover > td {\n        background: #243B6B !important;\n      }\n    }\n\n    .el-table--border::after {\n      display: none;\n    }\n  }\n}\n\n// 修改日期选择器样式\n:deep(.el-date-editor) {\n  background: #1B2A47;\n  border: 1px solid #00E4FF;\n  \n  .el-range-input {\n    background: #1B2A47;\n    color: #fff;\n  }\n  \n  .el-range-separator {\n    color: #00E4FF;\n  }\n}\n\n// 修改滚动条样式\n:deep(.el-table__body-wrapper::-webkit-scrollbar) {\n  width: 6px;\n  height: 6px;\n}\n\n:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {\n  background: #00E4FF;\n  border-radius: 3px;\n}\n\n:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {\n  background: #1B2A47;\n}\n\n.title-container {\n  position: absolute;\n  top: -9px;\n  left: 57.5%;\n  width: 100%;\n  z-index: 1000;\n\n  .more-btn {\n    display: flex;\n    align-items: center;\n    cursor: pointer;\n    color: #2cc1ff;\n    font-size: 20px;\n\n    &:hover {\n      opacity: 0.8;\n    }\n\n    i {\n      margin-left: 5px;\n    }\n  }\n}\n</style>\n"]}]}