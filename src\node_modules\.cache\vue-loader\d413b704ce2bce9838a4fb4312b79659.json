{"remainingRequest": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\views\\dayi\\zichan.vue?vue&type=style&index=0&id=79dbed4e&lang=less&scoped=true", "dependencies": [{"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\views\\dayi\\zichan.vue", "mtime": 1751448864722}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\views\\dayi\\zichan.vue"], "names": [], "mappings": ";AAihBA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACrB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEnB;;AAEA,CAAC,CAAC,CAAC,EAAE;EACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;EAEf,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;MAEP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACrB;;MAEA,CAAC,CAAC,CAAC,EAAE;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACjB;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB;;EAEA,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAElB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3B;;EAEA,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;IAET,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB;;IAEA,CAAC,CAAC,CAAC,EAAE;MACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACjB;;IAEA,CAAC,CAAC,EAAE;MACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAElB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACd;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEhB,CAAC,CAAC,CAAC,EAAE;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB;;QAEA,CAAC,CAAC,CAAC,EAAE;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB;;QAEA,CAAC,CAAC,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACd;;QAEA,CAAC,CAAC,CAAC,EAAE;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB;;QAEA,CAAC,CAAC,CAAC,EAAE;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB;MACF;;MAEA,CAAC,CAAC,CAAC,EAAE;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEtB,CAAC,CAAC,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;UAEf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB;MACF;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB;;QAEA,CAAC,CAAC,CAAC,CAAC,EAAE;UACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjB;MACF;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACd;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAE1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACpB;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACpB;;AAEA,CAAC,CAAC,CAAC,EAAE;EACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACjB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACnB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAEX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtC;AACF;;AAEA,CAAC,CAAC,EAAE;EACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACd", "file": "E:/svn/***********-0186-SFTianjinUniversityH5/src/src/views/dayi/zichan.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <keep-alive>\r\n    <div class=\"contents\" v-if=\"isshow\" v-loading=\"false\" element-loading-text=\"Loading...\"\r\n      :element-loading-spinner=\"svg\" element-loading-background=\"rgba(0, 0, 0, 1)\">\r\n      <div class=\"toubu\">\r\n        <div style=\"margin-left: 20px; display: flex; align-items: center\" v-if=\"false\">\r\n          <div style=\"display: flex; width: 100%; align-items: center\">\r\n            <span class=\"sp\">当前位置：</span>\r\n            <el-select class=\"el-select\" v-model=\"selectvalue1\" placeholder=\"selectvalue1\"\r\n              style=\"width: 64px; height: 35.1px\" @change=\"handleChange\">\r\n              <el-option v-for=\"item in options1\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n            </el-select>\r\n            <el-select class=\"el-select\" v-model=\"selectvalue2\" placeholder=\"selectvalue2\"\r\n              style=\"width: 64px; height: 35.1px\" @change=\"handleChange\">\r\n              <el-option v-for=\"item in options2\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n            </el-select>\r\n            <el-select class=\"el-select\" v-model=\"selectvalue3\" placeholder=\"selectvalue3\"\r\n              style=\"width: 78px; height: 35.1px\" @change=\"handleChange\">\r\n              <el-option v-for=\"item in options3\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n            </el-select>\r\n          </div>\r\n          <img v-if=\"isshow\" class=\"img1sss\" @click=\"anniu()\" src=\"../../assets/image/table-x.png\" alt=\"\" />\r\n        </div>\r\n\r\n        <div class=\"all\">\r\n          <div class=\"all1\">\r\n            <!-- <Titles class=\"ltitle1\" tit=\"仪器实时状态\">\r\n              <div class=\"dayi\">\r\n                <span>共</span>\r\n                <span>{{ sbnum }}</span>\r\n                <span>台仪器安装客户端</span>\r\n              </div>\r\n              <Electricity1 v-if=\"yiqiStatus\" class=\"zhuzhuangtu\" :chartData=\"yiqiStatus\"></Electricity1>         \r\n            </Titles> -->\r\n            <Titles class=\"ltitle11\" tit=\"人员分布统计\">\r\n\r\n              <!-- <zhuzhuangtu class=\"zhuzhuangtu\" :chartData=\"chartData1\"></zhuzhuangtu> -->\r\n              <huanxing style=\"margin-top: 150px;\" v-if=\"userDistribution\" :chartData=\"userDistribution\"></huanxing>\r\n\r\n            </Titles>\r\n          </div>\r\n          <div class=\"line1\"></div>\r\n          <div class=\"all2\">\r\n            <!-- <Titles class=\"ltitle1\" tit=\"仪器设备使用情况\">\r\n            <div class=\"shinei\">\r\n              <Electricity8></Electricity8>\r\n            </div>\r\n          </Titles>\r\n          <Titles class=\"ltitle\" tit=\"办公设备使用情况\">\r\n            <div class=\"shinei\">\r\n              <Electricity8></Electricity8>\r\n            </div>\r\n          </Titles> -->\r\n            <div>\r\n              <Titles class=\"ltitle1\" tit=\"课题测试统计\">\r\n                <div class=\"shinei\">\r\n                  <Electricity3 v-if=\"testStatistics\" :chartData=\"testStatistics\"></Electricity3>\r\n                </div>\r\n              </Titles>\r\n            </div>\r\n          </div>\r\n          <div class=\"all3\">\r\n\r\n            <Titles class=\"ltitle1\" tit=\"仪器使用排行\">\r\n              <div class=\"shinei\">\r\n                <!-- <Electricity6></Electricity6> -->\r\n                <zhuzhuangtu v-if=\"equipmentRank\" class=\"zhuzhuangtu1\" :chartData=\"equipmentRank\"></zhuzhuangtu>\r\n              </div>\r\n            </Titles>\r\n            <Titles class=\"ltitle1\" tit=\"课题组使用统计\">\r\n              <div class=\"shinei\">\r\n                <!-- <huanxing :chartData=\"chartData\"></huanxing> -->\r\n                <zhuzhuangtu1 v-if=\"topUsers\" class=\"zhuzhuangtu1\" :chartData=\"topUsers\"></zhuzhuangtu1>\r\n              </div>\r\n            </Titles>\r\n            <!-- <div class=\"shuantitle\">\r\n            <div style=\"width: 50%\">\r\n              <div class=\"title\">实时负载率</div>\r\n              <div class=\"nenghao\">实时负载率:</div>\r\n              <p class=\"nhp\">30%</p>\r\n            </div>\r\n            <div style=\"width: 50%\">\r\n              <div class=\"title\">实时总功率</div>\r\n              <div class=\"nenghao\">实时总功率:</div>\r\n              <p class=\"nhp\">200Kw</p>\r\n            </div>\r\n          </div>\r\n      -->\r\n\r\n\r\n\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </keep-alive>\r\n</template>\r\n\r\n<script>\r\nimport Electricity from \"@/components/echarts/dianbiao/biao1.vue\";\r\nimport biao1s from \"@/components/echarts/dianbiao/biao1s.vue\";\r\nimport biao1ss from \"@/components/echarts/dianbiao/biao1ss.vue\";\r\nimport Titles from \"@/components/common/Titles.vue\";\r\nimport Electricity1 from \"@/components/dayi/Electricity1.vue\";\r\nimport Electricity2 from \"@/components/echarts/dianbiao/Electricity2.vue\";\r\nimport Electricity3 from \"@/components/dayi//zhexiantu.vue\";\r\nimport Electricity4 from \"@/components/echarts/dianbiao/Electricity4.vue\";\r\nimport Electricity5 from \"@/components/echarts/dianbiao/Electricity5.vue\";\r\nimport Electricity6 from \"@/components/dayi/Electricity6.vue\";\r\nimport Electricity7 from \"@/components/echarts/dianbiao/Electricity7.vue\";\r\nimport Electricity8 from \"@/components/dayi/Electricity8.vue\";\r\nimport huanxing from \"@/components/dayi/xiaobingtu.vue\";\r\nimport zhuzhuangtu from \"@/components/dayi/zhuzhuangtu.vue\";\r\nimport zhuzhuangtu1 from \"@/components/dayi/zhuzhuangtu1.vue\";\r\nimport axios from \"axios\";\r\n// 使用环境变量设置基础 API 地址\r\nconst baseURL = process.env.VUE_APP_BASE_API || '/lims/api';\r\n\r\nconst api = axios.create({\r\n  baseURL\r\n});\r\nconst headers = {\r\n  clientid: '5a298e93-158d-4e22-83cf-6ceb62e9b4f1',\r\n  clientsecret: '2c8ec39e-9887-482a-b28b-e64c496b601c'\r\n};\r\nexport default {\r\n  components: {\r\n    Titles,\r\n    Electricity1,\r\n    Electricity,\r\n    Electricity2,\r\n    Electricity3,\r\n    Electricity4,\r\n    Electricity5,\r\n    Electricity6,\r\n    Electricity7,\r\n    Electricity8,\r\n    huanxing,\r\n    biao1s,\r\n    biao1ss,\r\n    zhuzhuangtu,\r\n    zhuzhuangtu1\r\n  },\r\n  data() {\r\n    return {\r\n      loading: true,\r\n      loading1: true,\r\n      loading2: true,\r\n      loading3: true,\r\n      loading4: true,\r\n      loading5: true,\r\n      //svg: 'el-icon-loading' ,// 或者自定义 SVG 图标\r\n      sbnum: 723,\r\n      chartDatazz: {\r\n        title: [\"已领用\", \"未领用\"],\r\n        xAxisdata1: [80, 80, 97, 53, 95, 70, 88],\r\n        xAxisdata2: [100, 100, 100, 100, 100, 100, 100],\r\n        yAxisdata: [\"一月\", \"二月\", \"三月\", \"四月\", \"五月\", \"六月\", \"七月\"],\r\n      },\r\n      chartDatazz1: {\r\n        title: [\"已领用\", \"未领用\"],\r\n        xAxisdata1: [80, 80, 97, 53, 95, 70, 88],\r\n        xAxisdata2: [100, 100, 100, 100, 100, 100, 100],\r\n        yAxisdata: [\"一月\", \"二月\", \"三月\", \"四月\", \"五月\", \"六月\", \"七月\"],\r\n      },\r\n      chartData: {\r\n        value: [1321, 18582, 651],\r\n        legend: [\r\n          \"校外人员\",\r\n          \"校内人员\",\r\n          \"管理员\",\r\n        ],\r\n      },\r\n      chartData1: {\r\n        title: [\"已领用\", \"未领用\"],\r\n        xAxisdata: [\"20日\", \"21日\", \"22日\", \"23日\", \"25日\", \"24日\",],\r\n        yAxisdata1: [4, 7, 5, 9, 6, 5],\r\n        yAxisdata2: [4, 7, 5, 9, 6, 5],\r\n      },\r\n      chartData2: [\r\n        {\r\n          name: \"正在使用\",\r\n          value: 271,\r\n        },\r\n        {\r\n          name: \"待机中\",\r\n          value: 452,\r\n        }],\r\n      chartData3: [\r\n        {\r\n          name: \"正在使用\",\r\n          value: 271,\r\n        },\r\n        {\r\n          name: \"待机中\",\r\n          value: 452,\r\n\r\n        },\r\n        {\r\n          name: \"故障\",\r\n          value: 21,\r\n\r\n        }],\r\n      isshow: true,\r\n      options: [\r\n        {\r\n          value: \"总览\",\r\n          label: \"总览\",\r\n        },\r\n        {\r\n          value: \"能耗分析\",\r\n          label: \"能耗分析\",\r\n        },\r\n        {\r\n          value: \"能流分析\",\r\n          label: \"能流分析\",\r\n        },\r\n        {\r\n          value: \"设备状态\",\r\n          label: \"设备状态\",\r\n        },\r\n        {\r\n          value: \"一键抄表\",\r\n          label: \"一键抄表\",\r\n        },\r\n        {\r\n          value: \"费用管理\",\r\n          label: \"费用管理\",\r\n        },\r\n        {\r\n          value: \"碳排放管理\",\r\n          label: \"碳排放管理\",\r\n        },\r\n      ],\r\n      selectvalue2: \"B3\",\r\n      options2: [\r\n        {\r\n          value: \"B3\",\r\n          label: \"B3\",\r\n        },\r\n      ],\r\n      selectvalue3: \"B3\",\r\n      options3: [\r\n        {\r\n          value: \"B3\",\r\n          label: \"B3\",\r\n        },\r\n      ],\r\n      selectvalue1: \"B3\",\r\n      options1: [\r\n        {\r\n          value: \"B3\",\r\n          label: \"B3\",\r\n        },\r\n      ],\r\n      selectvalue3: \"B1栋\",\r\n      options4: [\r\n        {\r\n          value: \"B1栋\",\r\n          label: \"B1栋\",\r\n        },\r\n        {\r\n          value: \"B2栋\",\r\n          label: \"B2栋\",\r\n        },\r\n        {\r\n          value: \"B3栋\",\r\n          label: \"B3栋\",\r\n        },\r\n        {\r\n          value: \"B4栋\",\r\n          label: \"B4栋\",\r\n        },\r\n        {\r\n          value: \"W1栋\",\r\n          label: \"W1栋\",\r\n        },\r\n        {\r\n          value: \"W2栋\",\r\n          label: \"W2栋\",\r\n        },\r\n      ],\r\n      selectvalue4: \"B1栋\",\r\n      optionData: [\r\n        {\r\n          name: \"一级告警\",\r\n          value: 16,\r\n          itemStyle: { color: \"#EB6877\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"二级告警\",\r\n          value: 27,\r\n          itemStyle: { color: \"#F8B551\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"三级告警\",\r\n          value: 17,\r\n          itemStyle: { color: \"#B954E8\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"四级告警\",\r\n          value: 40,\r\n          itemStyle: { color: \"#0284F0\", opacity: 0.9 },\r\n        },\r\n      ],\r\n      optionData1: [\r\n        {\r\n          name: \"一级告警\",\r\n          value: 16,\r\n          itemStyle: { color: \"#EB6877\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"二级告警\",\r\n          value: 27,\r\n          itemStyle: { color: \"#F8B551\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"三级告警\",\r\n          value: 17,\r\n          itemStyle: { color: \"#B954E8\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"四级告警\",\r\n          value: 40,\r\n          itemStyle: { color: \"#0284F0\", opacity: 0.9 },\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  computed: {\r\n    loading() {\r\n      return this.loading1 || this.loading2 || this.loading3 || this.loading4 || this.loading5;\r\n    },\r\n    // 使用 Vuex 的 getters 获取持久化的数据\r\n    equipmentRank() {\r\n      return this.$store.getters[\"equipment/equipmentRank\"];\r\n    },\r\n    yiqiStatus() {\r\n      return this.$store.getters[\"equipment/yiqiStatus\"];\r\n    },\r\n    userDistribution() {\r\n      return this.$store.getters[\"equipment/userDistribution\"];\r\n    },\r\n    testStatistics() {\r\n      return this.$store.getters[\"equipment/testStatistics\"];\r\n    },\r\n    topUsers() {\r\n      return this.$store.getters[\"equipment/topUsers\"];\r\n    },\r\n  },\r\n  mounted() {\r\n\r\n    this.$store.dispatch('equipment/fetchEquipmentRank');\r\n    this.$store.dispatch('equipment/getdata2');\r\n    this.$store.dispatch('equipment/getdata3');\r\n    this.$store.dispatch('equipment/getdata4');\r\n    this.$store.dispatch('equipment/getdata5');\r\n\r\n    if (!this.equipmentRank.length) {\r\n      this.$store.dispatch('equipment/fetchEquipmentRank'); // 如果没有缓存，获取数据\r\n    }\r\n    if (!this.yiqiStatus.length) {\r\n      this.$store.dispatch('equipment/getdata2'); // 如果没有缓存，获取数据\r\n    }\r\n    if (!this.userDistribution.length) {\r\n      this.$store.dispatch('equipment/getdata3'); // 如果没有缓存，获取数据\r\n    }\r\n    if (!this.testStatistics.length) {\r\n      this.$store.dispatch('equipment/getdata4'); // 如果没有缓存，获取数据\r\n    }\r\n    if (!this.topUsers.length) {\r\n      this.$store.dispatch('equipment/getdata5'); // 如果没有缓存，获取数据\r\n    }\r\n    setInterval(() => {\r\n      this.$store.dispatch('equipment/fetchEquipmentRank');\r\n      this.$store.dispatch('equipment/getdata2');\r\n      this.$store.dispatch('equipment/getdata3');\r\n      this.$store.dispatch('equipment/getdata4');\r\n      this.$store.dispatch('equipment/getdata5');\r\n    }, 36000000);\r\n    // this.getdata1()\r\n    this.getdata2()\r\n    // this.getdata3()\r\n    // this.getdata4()\r\n    // this.getdata5()\r\n    // setInterval(() => {\r\n    //   this.getdata1()\r\n    //   this.getdata2()\r\n    //   this.getdata3()\r\n    //   this.getdata4()\r\n    //   this.getdata5()\r\n    // }, 10000);\r\n\r\n  },\r\n\r\n  methods: {\r\n\r\n    async getdata1() {  //仪器使用排行\r\n      try {\r\n        const response = await api.post('', {\r\n\r\n          \"method\": \"equipment/time_rank\",\r\n          \"params\": {\r\n            \"num\": 10,\r\n            \"start\": 1704038400,\r\n            \"end\": 1735660800\r\n          }\r\n\r\n        }, { headers });\r\n        // 检查是否成功拿到 token\r\n        if (response.data) {\r\n          console.log('仪器使用排行:', response.data);\r\n          let data = response.data.response\r\n          // 提取 names 和 times\r\n          const names = data.map(item => item.name).reverse();\r\n          const times = data.map(item => item.time).reverse();\r\n          this.chartDatazz1.yAxisdata = names\r\n          this.chartDatazz1.xAxisdata1 = times\r\n          this.loading1 = false\r\n        }\r\n      } catch (error) {\r\n        console.error('登录失败:', error);\r\n      }\r\n    },\r\n\r\n    async getdata2() {  //仪器使用情况\r\n      try {\r\n        const response = await api.post('', {\r\n          \"method\": \"equipment/getSummaryInfo\",\r\n          \"params\": {}\r\n        }, { headers });\r\n        // 检查是否成功拿到 token\r\n        if (response.data) {\r\n          this.sbnum = response.data.response.controlCount,\r\n            console.log('仪器使用情况:', response.data.response);\r\n\r\n\r\n        }\r\n\r\n      } catch (error) {\r\n        console.error('登录失败:', error);\r\n      }\r\n    },\r\n    async getdata3() {  //人员分布情况\r\n      try {\r\n        const response = await api.post('', {\r\n          \"method\": \"summarize/userStatus\",\r\n          \"params\": {}\r\n        }, { headers });\r\n        // 检查是否成功拿到 token\r\n        if (response.data) {\r\n          console.log('人员分布情况:', response.data);\r\n          this.chartData = {\r\n            value: [response.data.response.outer, response.data.response.inner, response.data.response.incharge],\r\n            legend: [\r\n              \"校外人员\",\r\n              \"校内人员\",\r\n              \"管理员\",\r\n            ],\r\n          }\r\n          this.loading3 = false\r\n        }\r\n      } catch (error) {\r\n        console.error('登录失败:', error);\r\n      }\r\n    },\r\n    async getdata4() {  //课题测试情况\r\n\r\n      try {\r\n        const response = await api.post('', {\r\n          \"method\": \"summarize/labStatus\",\r\n          \"params\": {}\r\n        }, { headers });\r\n        // 检查是否成功拿到 token\r\n        if (response) {\r\n          console.log('课题测试情况:', response.data.response);\r\n          this.chartData3 = [\r\n            {\r\n              name: \"总课题数\",\r\n              value: response.data.response.project,\r\n            },\r\n            {\r\n              name: \"课题数\",\r\n              value: response.data.response.lab,\r\n\r\n            },\r\n            {\r\n              name: \"测试数\",\r\n              value: response.data.response.test,\r\n\r\n            }]\r\n          this.loading4 = false\r\n        }\r\n      } catch (error) {\r\n        console.error('登录失败:', error);\r\n      }\r\n    },\r\n    async getdata5() {  //用户排行\r\n      try {\r\n        const response = await api.post('', {\r\n          \"method\": \"eq_reserv/getTopUsers\",\r\n          \"params\": {\r\n            \"num\": 9,\r\n            \"year\": 2024\r\n          }\r\n        }, { headers });\r\n        // 检查是否成功拿到 token\r\n        if (response.data) {\r\n          console.log('用户排行:', response.data.response);\r\n          let data = response.data.response\r\n          // 提取 names 和 times\r\n          const names = data.map(item => item.name).reverse();\r\n          const times = data.map(item => item.time).reverse();\r\n          this.chartDatazz.yAxisdata = names\r\n          this.chartDatazz.xAxisdata1 = times\r\n        }\r\n        this.loading5 = false\r\n      } catch (error) {\r\n        console.error('登录失败:', error);\r\n      }\r\n    },\r\n    anniu() {\r\n      this.isshow = false;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.zhuzhuangtu {\r\n  margin-top: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.zhuzhuangtu1 {\r\n  margin-top: -26px;\r\n\r\n}\r\n\r\n.all {\r\n  display: flex;\r\n  flex-direction: row;\r\n  margin-top: 5px;\r\n\r\n  .zong {\r\n    display: flex;\r\n    flex-direction: row;\r\n    margin-top: 10px;\r\n\r\n    .echart1,\r\n    .echart2 {\r\n      flex: 1;\r\n\r\n      .center {\r\n        margin-top: -24px;\r\n        font-family: \"Source Han Sans SC\", sans-serif;\r\n        font-weight: 400;\r\n        font-size: 17px;\r\n        color: #00ffb6;\r\n        text-align: center;\r\n        margin-bottom: 10px;\r\n      }\r\n\r\n      .btn {\r\n        width: 133px;\r\n        height: 31px;\r\n        border: 1px solid #2d6cb0;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        font-family: \"Source Han Sans SC\", sans-serif;\r\n        font-weight: bold;\r\n        font-size: 15px;\r\n        color: #ffffff;\r\n        border-radius: 30px;\r\n        margin-left: 7%;\r\n      }\r\n    }\r\n  }\r\n\r\n  .ltitle1 {\r\n    margin-top: 10px;\r\n  }\r\n\r\n  .ltitle11 {\r\n    margin-top: 30px;\r\n    position: relative;\r\n  }\r\n\r\n  .dayi {\r\n    position: absolute;\r\n    top: 42px;\r\n    left: 68px;\r\n    z-index: 20;\r\n    font-size: 22px;\r\n    color: #fff;\r\n    text-align: center;\r\n\r\n    span:nth-child(2) {\r\n      font-size: 32px;\r\n    }\r\n  }\r\n\r\n  .line1 {\r\n    width: 2px;\r\n    height: 823px;\r\n    opacity: 0.64;\r\n    background-color: #204964;\r\n  }\r\n\r\n  .all1 {\r\n    flex: 462;\r\n\r\n    .nenghao {\r\n      width: 227px;\r\n      height: 173px;\r\n      background: url(\"../../assets/image/nenghao.png\");\r\n      background-size: 100% 100%;\r\n      margin-left: 120px;\r\n      margin-top: 21px;\r\n      text-align: center;\r\n      font-family: Alibaba PuHuiTi;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #ffffff;\r\n      line-height: 213px;\r\n    }\r\n\r\n    .nhp {\r\n      text-align: center;\r\n      font-family: Alibaba PuHuiTi;\r\n      font-weight: 500;\r\n      font-size: 30px;\r\n      color: #2cc1ff;\r\n      margin-top: 8px;\r\n    }\r\n\r\n    .nh {\r\n      margin-left: 24px;\r\n      margin-top: 5px;\r\n      width: 423px;\r\n      height: 93px;\r\n      border: 1px solid #364d5a;\r\n      background-size: 100% 100%;\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      padding-left: 42px;\r\n\r\n      .nhimg {\r\n        width: 96.6px;\r\n        height: 70px;\r\n      }\r\n\r\n      .nhtit {\r\n        width: 148px;\r\n        margin-left: 10px;\r\n        margin-top: 10px;\r\n\r\n        .p11 {\r\n          font-family: PangMenZhengDao;\r\n          font-weight: 400;\r\n          font-size: 30px;\r\n          color: #7acfff;\r\n        }\r\n\r\n        .p12 {\r\n          font-family: PangMenZhengDao;\r\n          font-weight: 400;\r\n          font-size: 30px;\r\n          color: #ffa170;\r\n        }\r\n\r\n        .p2 {\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 400;\r\n          font-size: 17px;\r\n          color: #ffffff;\r\n        }\r\n      }\r\n\r\n      .nhtit1 {\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: center;\r\n        margin-left: 35px;\r\n\r\n        .nhimg1 {\r\n          width: 16px;\r\n          height: 20px;\r\n        }\r\n\r\n        .pp1 {\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 400;\r\n          font-size: 17px;\r\n          color: #0df29b;\r\n        }\r\n\r\n        .pp2 {\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 400;\r\n          font-size: 17px;\r\n          color: #ffa170;\r\n        }\r\n      }\r\n\r\n      .nht {\r\n        margin-top: 10px;\r\n        display: flex;\r\n        flex-direction: column;\r\n\r\n        .pp {\r\n          margin-left: 35px;\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 400;\r\n          font-size: 17px;\r\n\r\n          color: #cccccc;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .all2 {\r\n    margin-left: 38px;\r\n    flex: 667;\r\n    display: flex;\r\n    flex-direction: column;\r\n\r\n    .shinei {\r\n      .itemshei {\r\n        display: flex;\r\n        justify-content: space-around;\r\n\r\n        .nenghaos {\r\n          width: 227px;\r\n          height: 173px;\r\n          background: url(\"../../assets/image/nenghao.png\");\r\n          background-size: 100% 100%;\r\n          text-align: center;\r\n          margin-left: 10px;\r\n          margin-top: 23px;\r\n          text-align: center;\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 400;\r\n          font-size: 14px;\r\n          color: #ffffff;\r\n          line-height: 144px;\r\n        }\r\n\r\n        .nhps {\r\n          text-align: center;\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 500;\r\n          font-size: 21px;\r\n          color: #2cc1ff;\r\n          margin-top: 8px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .all3 {\r\n    flex: 668;\r\n    margin-left: 45px;\r\n  }\r\n}\r\n\r\n.shinei {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.shuantitle {\r\n  width: 100%;\r\n  display: flex;\r\n  margin-top: 10px;\r\n\r\n  .title {\r\n    width: 95%;\r\n    background: url(\"../../assets/image/title.png\");\r\n    background-size: 100% 100%;\r\n\r\n    height: 25px;\r\n    font-family: Source Han Sans SC;\r\n    font-weight: 400;\r\n    font-size: 25px;\r\n    color: #ffffff;\r\n    text-shadow: 2px 2px 3px rgba(0, 0, 0, 0.47);\r\n    font-style: italic;\r\n    text-align: left;\r\n    line-height: 4px;\r\n    padding-left: 33px;\r\n  }\r\n}\r\n\r\n.nenghao {\r\n  width: 167px;\r\n  height: 113px;\r\n  background: url(\"../../assets/image/nenghao.png\");\r\n  background-size: 100% 100%;\r\n  text-align: center;\r\n  margin-left: 83px;\r\n  margin-top: 63px;\r\n  text-align: center;\r\n  font-family: Alibaba PuHuiTi;\r\n  font-weight: 400;\r\n  font-size: 14px;\r\n  color: #ffffff;\r\n  line-height: 144px;\r\n}\r\n\r\n.nhp {\r\n  text-align: center;\r\n  font-family: Alibaba PuHuiTi;\r\n  font-weight: 500;\r\n  font-size: 21px;\r\n  color: #2cc1ff;\r\n  margin-top: 8px;\r\n}\r\n\r\n.contents {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  background: url(\"../../assets/image/zichanbeijin.png\");\r\n  background-size: 100% 100%;\r\n  width: 1863px;\r\n  height: 868px;\r\n  z-index: 99999;\r\n  padding-left: 34px;\r\n  padding-right: 22px;\r\n  padding-top: 21px;\r\n}\r\n\r\n.toubu {\r\n  width: 100%;\r\n\r\n  position: relative;\r\n}\r\n\r\n.el-select {\r\n  margin-top: -1px;\r\n  margin-left: 10px;\r\n  background: #00203d;\r\n  border-radius: 3px;\r\n  border: 1px solid #3e89db;\r\n\r\n  /deep/.el-select__wrapper {\r\n    background: #00203d !important;\r\n    box-shadow: none;\r\n  }\r\n\r\n  /deep/.el-select__wrapper .is-hovering:not {\r\n    box-shadow: none;\r\n  }\r\n\r\n  /deep/.el-select__wrapper:hover {\r\n    box-shadow: none;\r\n  }\r\n\r\n  /deep/.el-select__placeholder.is-transparent {\r\n    color: #2cc1ff;\r\n  }\r\n\r\n  /deep/.el-select__placeholder {\r\n    color: #2cc1ff;\r\n  }\r\n\r\n  /deep/.el-select-dropdown__item.is-hovering {\r\n    background-color: #2cc1ff !important;\r\n  }\r\n}\r\n\r\n.sp {\r\n  margin-top: -5px;\r\n  margin-left: 12px;\r\n  font-family: Alibaba PuHuiTi;\r\n  font-weight: bold;\r\n  font-size: 21px;\r\n  color: #2cc1ff;\r\n}\r\n\r\n.img1sss {\r\n  cursor: pointer;\r\n  width: 15px;\r\n  height: 15px;\r\n}\r\n</style>"]}]}