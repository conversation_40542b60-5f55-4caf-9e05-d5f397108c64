{"remainingRequest": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\src\\views\\nenghao.vue?vue&type=template&id=302736bc&scoped=true", "dependencies": [{"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\src\\views\\nenghao.vue", "mtime": 1751446100602}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 499162500000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\src\\views\\nenghao.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb;QACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB;YACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB;YACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB;YACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7D,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cAC9D,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACzB,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC5D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cAC9D,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1B,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC5D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3H,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cAC9D,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC9D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3B,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACtB,CAAC,CAAC,CAAC;oBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACP,CAAC;kBACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC9H,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACT,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cAC9B,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7D,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;UAEZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7D,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC;YACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACxB,CAAC;oBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACxB,CAAC;oBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACb;oBACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACZ,CAAC;oBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACZ,CAAC;oBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACZ,CAAC;oBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACZ,CAAC;kBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACZ,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC1B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpC,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACxB,CAAC;kBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACxB,CAAC;kBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACb;kBACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACZ,CAAC;kBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACZ,CAAC;kBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACZ;oBACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAC/B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;kBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACZ,CAAC;gBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACZ,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACb,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC;MACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf;YACA,CAAC;cACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/B,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACzC;YACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAChC,EAAE,CAAC,CAAC;YACN,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;YAEzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3D,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACzE,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb;YACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC;;YAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "E:/svn/SH-20240918-0186-SFTianjinUniversityH5/src/src/views/nenghao.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div class=\"contents\" v-if=\"isshow\">\n    <div class=\"toubu\">\n      <div\n        style=\"margin-left: 20px; display: flex; align-items: center\"\n        v-if=\"false\"\n      >\n        <div style=\"display: flex; width: 100%; align-items: center\">\n          <span class=\"sp\">当前位置：</span>\n          <el-select\n            class=\"el-select\"\n            v-model=\"selectvalue1\"\n            placeholder=\"selectvalue1\"\n            style=\"width: 64px; height: 35.1px\"\n            @change=\"handleChange\"\n          >\n            <el-option\n              v-for=\"item in options1\"\n              :key=\"item.value\"\n              :label=\"item.label\"\n              :value=\"item.value\"\n            />\n          </el-select>\n          <el-select\n            class=\"el-select\"\n            v-model=\"selectvalue2\"\n            placeholder=\"selectvalue2\"\n            style=\"width: 64px; height: 35.1px\"\n            @change=\"handleChange\"\n          >\n            <el-option\n              v-for=\"item in options2\"\n              :key=\"item.value\"\n              :label=\"item.label\"\n              :value=\"item.value\"\n            />\n          </el-select>\n          <el-select\n            class=\"el-select\"\n            v-model=\"selectvalue3\"\n            placeholder=\"selectvalue3\"\n            style=\"width: 78px; height: 35.1px\"\n            @change=\"handleChange\"\n          >\n            <el-option\n              v-for=\"item in options3\"\n              :key=\"item.value\"\n              :label=\"item.label\"\n              :value=\"item.value\"\n            />\n          </el-select>\n        </div>\n        <img\n          v-if=\"isshow\"\n          class=\"img1sss\"\n          @click=\"anniu()\"\n          src=\"../assets/image/table-x.png\"\n          alt=\"\"\n        />\n      </div>\n\n      <div class=\"all\">\n        <div class=\"all1\">\n          <Titles class=\"ltitle\" tit=\"大型仪器平台概况\">\n            <div class=\"nenghao\">累计总能耗:</div>\n            <p class=\"nhp\">{{ totalConsumption.total.toFixed(1) }} kwh</p>\n            <div class=\"nh\">\n              <img class=\"nhimg\" src=\"../assets/image/nenghao1.png\" alt=\"\" />\n              <div class=\"nhtit\">\n                <p class=\"p11\">{{ totalConsumption.daily.toFixed(1) }}kwh</p>\n                <p class=\"p2\">本日累计能耗</p>\n              </div>\n              <!-- <div class=\"nhtit1\">\n                <img class=\"nhimg1\" src=\"../assets/image/nhxia.png\" alt=\"\" />\n                <p class=\"pp1\">{{ ((totalConsumption.daily - totalConsumption.daily) / totalConsumption.daily * 100).toFixed(1) }}%</p>\n              </div> -->\n            </div>\n            <div class=\"nh\">\n              <img class=\"nhimg\" src=\"../assets/image/nenghao1.png\" alt=\"\" />\n              <div class=\"nhtit\">\n                <p class=\"p11\">{{ totalConsumption.weekly.toFixed(1) }}kwh</p>  \n                <p class=\"p2\">近7日累计能耗</p>\n              </div>\n              <!-- <div class=\"nhtit1\">\n                <img class=\"nhimg1\" src=\"../assets/image/nhxia.png\" alt=\"\" />\n                <p class=\"pp1\">{{ ((totalConsumption.weekly - totalConsumption.weekly) / totalConsumption.weekly * 100).toFixed(1) }}%</p>\n              </div> -->\n            </div>\n            <div class=\"nh\">\n              <img class=\"nhimg\" src=\"../assets/image/nenghao2.png\" alt=\"\" />\n              <div class=\"nhtit\">\n                <p class=\"p12\">{{ totalConsumption.monthly.toFixed(1) }}kwh</p>\n                <p class=\"p2\">近30日累计能耗</p>\n              </div>\n              <div class=\"nht\">\n                <!-- <div class=\"nhtit1\">\n                  <img\n                    class=\"nhimg1\"\n                    src=\"../assets/image/nhshang.png\"\n                    alt=\"\"\n                  />\n                  <p class=\"pp2\">{{ ((totalConsumption.monthly - totalConsumption.monthly) / totalConsumption.monthly * 100).toFixed(1) }}%</p>\n                </div> -->\n                <!-- <p class=\"pp\">环比</p> -->\n              </div>\n            </div>\n          </Titles>\n          <!-- <Titles class=\"ltitle\" style=\"margin-top: 20px\" tit=\"电耗费用\">\n            <div class=\"shinei\">\n              <Electricity2\n                :yesterday-fee=\"electricityFees.yesterday\"\n                :monthly-fee=\"electricityFees.monthly\"\n                :yearly-fee=\"electricityFees.yearly\"\n              ></Electricity2>\n            </div>\n          </Titles> -->\n        </div>\n        <!-- <div class=\"line1\"></div> -->\n        <div class=\"all2\">\n          <!-- <Titles class=\"ltitle1\" tit=\"峰平谷用电量\">\n            <div class=\"shinei\">\n              <Electricity8></Electricity8>\n            </div>\n          </Titles> -->\n\n          <Titles class=\"ltitle1\" tit=\"用电量排名\">\n            <div class=\"shinei\">\n              <Electricity3\n                :electricity-data=\"electricityUsageData\"\n              ></Electricity3>\n            </div>\n          </Titles>\n          <!-- <Titles class=\"ltitle1\" tit=\"分区用电量\">\n            <div class=\"shinei\">\n              <Electricity4 :fee-data=\"electricityFeeData\"></Electricity4>\n            </div>\n          </Titles> -->\n        </div>\n        <div class=\"all3\">\n          <div>\n            <Titles class=\"ltitle1\" tit=\"抄电表记录\">\n              <div class=\"shinei\">\n                <div class=\"table-container\">\n                  <el-table\n                    :data=\"meterReadings\"\n                    style=\"width: 100%; background: transparent\"\n                    :header-cell-style=\"{\n                      background: '#2A363F',\n                      color: '#fff',\n                      borderColor: '#1e415c',\n                    }\"\n                    :cell-style=\"{\n                      background: '#2A363F',\n                      color: '#fff',\n                      borderColor: '#1e415c',\n                    }\"\n                    height=\"320\"\n                  >\n                    <el-table-column\n                      prop=\"roomtag\"\n                      label=\"房间标识\"\n                      align=\"center\"\n                      width=\"120\"\n                    />\n                    <el-table-column\n                      prop=\"zhaddress\"\n                      label=\"住户地址\"\n                      align=\"center\"\n                      width=\"180\"\n                    />\n                    <el-table-column\n                      prop=\"readvalue\"\n                      label=\"抄表值\"\n                      align=\"center\"\n                      width=\"120\"\n                    />\n                    <el-table-column\n                      prop=\"readtime\"\n                      label=\"抄表时间\"\n                      align=\"center\"\n                      width=\"180\"\n                    />\n                  </el-table>\n                </div>\n              </div>\n            </Titles>\n          </div>\n          <Titles class=\"ltitle1\" style=\"margin-top:15px\" tit=\"用电量记录\">\n            <div class=\"shinei\">\n              <div class=\"title-container\">\n                <div class=\"more-btn\" @click=\"showDetailDialog\">\n                  <span>更多</span>\n                  <i class=\"el-icon-arrow-right\"></i>\n                </div>\n              </div>\n              <div class=\"table-container\">\n                <el-table\n                  :data=\"electricityUsageData\"\n                  style=\"width: 100%; background: transparent\"\n                  :header-cell-style=\"{\n                    background: '#2A363F',\n                    color: '#fff',\n                    borderColor: '#38444C',\n                  }\"\n                  :cell-style=\"{\n                    background: '#2A363F',\n                    color: '#fff',\n                    borderColor: '#1e415c',\n                  }\"\n                  height=\"400\"\n                >\n                  <el-table-column\n                    prop=\"roomtag\"\n                    label=\"房间标识\"\n                    align=\"center\"\n                    width=\"120\"\n                  />\n                  <el-table-column\n                    prop=\"zhaddress\"\n                    label=\"住户地址\"\n                    align=\"center\"\n                    width=\"180\"\n                  />\n                  <el-table-column\n                    prop=\"ylvalue\"\n                    label=\"用电量(kwh)\"\n                    align=\"center\"\n                    width=\"120\"\n                  >\n                    <!-- <template slot-scope=\"scope\">\n                      {{ parseFloat(scope.row.ylvalue).toFixed(2) }}\n                    </template> -->\n                  </el-table-column>\n                  <el-table-column\n                    prop=\"endtime\"\n                    label=\"抄表时间\"\n                    align=\"center\"\n                    width=\"180\"\n                  />\n                </el-table>\n              </div>\n            </div>\n          </Titles>\n        </div>\n      </div>\n    </div>\n\n    <!-- 自定义弹窗 -->\n    <div\n      v-if=\"dialogVisible\"\n      class=\"custom-modal-overlay\"\n      @click.self=\"dialogVisible = false\"\n    >\n      <div class=\"custom-modal\">\n        <div class=\"modal-header\">\n          <span class=\"modal-title\">用电量详细记录</span>\n          <div class=\"header-buttons\">\n            <el-button\n              type=\"text\"\n              class=\"close-text\"\n              @click=\"dialogVisible = false\"\n              >关闭</el-button\n            >\n            <i\n              class=\"el-icon-close close-btn\"\n              @click=\"dialogVisible = false\"\n            ></i>\n          </div>\n        </div>\n        <div class=\"modal-content\">\n          <!-- 搜索条件 -->\n          <div class=\"search-container\">\n            <el-date-picker\n              v-model=\"dateRange\"\n              type=\"daterange\"\n              range-separator=\"至\"\n              start-placeholder=\"开始日期\"\n              end-placeholder=\"结束日期\"\n              value-format=\"YYYY-MM-DD\"\n              style=\"width: 380px; margin-right: 15px\"\n            >\n            </el-date-picker>\n            <!-- <el-date-picker\n                v-model=\"dateRange\"\n                type=\"daterange\"\n                align=\"right\"\n                unlink-panels\n                range-separator=\"至\"\n                start-placeholder=\"开始日期\"\n                end-placeholder=\"结束日期\"\n                :picker-options=\"pickerOptions\"\n              > -->\n            <!-- </el-date-picker> -->\n\n            <el-button type=\"primary\" @click=\"searchData\">查询</el-button>\n            <!-- <el-button type=\"success\" @click=\"exportToExcel\">导出</el-button> -->\n          </div>\n\n          <!-- 详细数据表格 -->\n          <el-table\n            :data=\"detailData\"\n            style=\"width: 100%; margin-top: 20px\"\n            :header-cell-style=\"{\n              background: '#2C4255',\n              color: '#fff',\n              borderColor: '#1e415c',\n            }\"\n            :cell-style=\"{\n              background: '#2C4255',\n              color: '#fff',\n              borderColor: '#1e415c',\n            }\"\n            height=\"500\"\n          >\n            <el-table-column\n              prop=\"roomtag\"\n              label=\"房间标识\"\n              align=\"center\"\n              width=\"180\"\n            />\n            <el-table-column\n              prop=\"zhaddress\"\n              label=\"住户地址\"\n              align=\"center\"\n              width=\"180\"\n            />\n            <el-table-column\n              prop=\"startcode\"\n              label=\"起码\"\n              align=\"center\"\n              width=\"180\"\n            />\n            <el-table-column\n              prop=\"endcode\"\n              label=\"止码\"\n              align=\"center\"\n              width=\"180\"\n            />\n            <el-table-column\n              prop=\"ylvalue\"\n              label=\"用电量(kwh)\"\n              align=\"center\"\n              width=\"180\"\n            />\n            <el-table-column\n              prop=\"jfmx\"\n              label=\"缴费明细\"\n              align=\"center\"\n              width=\"180\"\n            />\n\n            <el-table-column\n              prop=\"endtime\"\n              label=\"抄表时间\"\n              align=\"center\"\n              width=\"181\"\n            />\n          </el-table>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as XLSX from \"xlsx\";\nimport Electricity from \"../components/echarts/dianbiao/biao1.vue\";\nimport biao1s from \"../components/echarts/dianbiao/biao1s.vue\";\nimport biao1ss from \"../components/echarts/dianbiao/biao1ss.vue\";\nimport Titles from \"../components/common/Titles.vue\";\n\nimport Electricity2 from \"../components/echarts/dianbiao/Electricity2.vue\";\nimport Electricity3 from \"../components/echarts/dianbiao/Electricity3.vue\";\nimport Electricity4 from \"../components/echarts/dianbiao/Electricity4.vue\";\nimport Electricity5 from \"../components/echarts/dianbiao/Electricity5.vue\";\nimport Electricity6 from \"../components/echarts/dianbiao/Electricity6.vue\";\nimport Electricity7 from \"../components/echarts/dianbiao/Electricity7.vue\";\nimport Electricity8 from \"../components/echarts/dianbiao/Electricity8.vue\";\nimport huanxing from \"@/components/echarts/xiaobingtu.vue\";\nimport axios from \"axios\";\nimport { buildingEnergyDataList } from \"@/api/device\";\n\nexport default {\n  components: {\n    Titles,\n    Electricity,\n    Electricity2,\n    Electricity3,\n    Electricity4,\n    Electricity5,\n    Electricity6,\n    Electricity7,\n    Electricity8,\n    huanxing,\n    biao1s,\n    biao1ss,\n  },\n  data() {\n    return {\n      isshow: true,\n      pickerOptions: {\n        shortcuts: [\n          {\n            text: \"最近一周\",\n            onClick(picker) {\n              const end = new Date();\n              const start = new Date();\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);\n              picker.$emit(\"pick\", [start, end]);\n            },\n          },\n          {\n            text: \"最近一个月\",\n            onClick(picker) {\n              const end = new Date();\n              const start = new Date();\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);\n              picker.$emit(\"pick\", [start, end]);\n            },\n          },\n          {\n            text: \"最近三个月\",\n            onClick(picker) {\n              const end = new Date();\n              const start = new Date();\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);\n              picker.$emit(\"pick\", [start, end]);\n            },\n          },\n        ],\n      },\n      options: [\n        {\n          value: \"总览\",\n          label: \"总览\",\n        },\n        {\n          value: \"能耗分析\",\n          label: \"能耗分析\",\n        },\n        {\n          value: \"能流分析\",\n          label: \"能流分析\",\n        },\n        {\n          value: \"设备状态\",\n          label: \"设备状态\",\n        },\n        {\n          value: \"一键抄表\",\n          label: \"一键抄表\",\n        },\n        {\n          value: \"费用管理\",\n          label: \"费用管理\",\n        },\n        {\n          value: \"碳排放管理\",\n          label: \"碳排放管理\",\n        },\n      ],\n      selectvalue2: \"B3\",\n      options2: [\n        {\n          value: \"B3\",\n          label: \"B3\",\n        },\n      ],\n      selectvalue3: \"B3\",\n      options3: [\n        {\n          value: \"B3\",\n          label: \"B3\",\n        },\n      ],\n      selectvalue1: \"B3\",\n      options1: [\n        {\n          value: \"B3\",\n          label: \"B3\",\n        },\n      ],\n      selectvalue3: \"B1栋\",\n      options4: [\n        {\n          value: \"B1栋\",\n          label: \"B1栋\",\n        },\n        {\n          value: \"B2栋\",\n          label: \"B2栋\",\n        },\n        {\n          value: \"B3栋\",\n          label: \"B3栋\",\n        },\n        {\n          value: \"B4栋\",\n          label: \"B4栋\",\n        },\n        {\n          value: \"W1栋\",\n          label: \"W1栋\",\n        },\n        {\n          value: \"W2栋\",\n          label: \"W2栋\",\n        },\n      ],\n      selectvalue4: \"B1栋\",\n      optionData: [\n        {\n          name: \"一级告警\",\n          value: 16,\n          itemStyle: { color: \"#EB6877\", opacity: 0.9 },\n        },\n        {\n          name: \"二级告警\",\n          value: 27,\n          itemStyle: { color: \"#F8B551\", opacity: 0.9 },\n        },\n        {\n          name: \"三级告警\",\n          value: 17,\n          itemStyle: { color: \"#B954E8\", opacity: 0.9 },\n        },\n        {\n          name: \"四级告警\",\n          value: 40,\n          itemStyle: { color: \"#0284F0\", opacity: 0.9 },\n        },\n      ],\n      optionData1: [\n        {\n          name: \"一级告警\",\n          value: 16,\n          itemStyle: { color: \"#EB6877\", opacity: 0.9 },\n        },\n        {\n          name: \"二级告警\",\n          value: 27,\n          itemStyle: { color: \"#F8B551\", opacity: 0.9 },\n        },\n        {\n          name: \"三级告警\",\n          value: 17,\n          itemStyle: { color: \"#B954E8\", opacity: 0.9 },\n        },\n        {\n          name: \"四级告警\",\n          value: 40,\n          itemStyle: { color: \"#0284F0\", opacity: 0.9 },\n        },\n      ],\n      token: {\n        systemnum: \"\",\n        tokenvalue: \"\",\n        expiretime: \"\",\n      },\n      baseURL: \"/power\", // Replace with actual server address\n      // baseURL: 'http://*************:8080',\n      isTokenValid: false,\n      totalConsumption: {\n        daily: 0,\n        weekly: 0,\n        monthly: 0,\n        total: 0, // 累计用量\n      },\n      curBuilding: null, // 当前建筑信息\n      totalElectricityFee: 0,\n      roomtag: \"\", // Add your roomtag here if you want to query specific user\n      electricityFees: {\n        yesterday: 0,\n        monthly: 0,\n        yearly: 0,\n      },\n      meterReadings: [], // 新增抄表记录数据\n      electricityUsageData: [], // 新增用电量数据\n      \n      dialogVisible: false, // 修改为 false，默认关闭\n      dateRange: \"\",\n      detailData: [],\n      electricityFeeData: [], // 新增电费数据\n    };\n  },\n  methods: {\n    anniu() {\n      this.isshow = false;\n    },\n\n    // 构建API参数 - 参考 BimEnergyOverview.vue 的实现\n    buildParams(type) {\n      // 格式化日期为 YYYY-MM-DD 格式\n      const formatDate = (date) => {\n        const year = date.getFullYear();\n        const month = String(date.getMonth() + 1).padStart(2, '0');\n        const day = String(date.getDate()).padStart(2, '0');\n        return `${year}-${month}-${day}`;\n      };\n\n      const now = new Date();\n      const today = formatDate(now);\n\n      const param = {\n        buildingId: 1, // 直接写死为1\n        deviceType: 'electricity',\n        type: 'electricity',\n        displayType: \"day\",\n        from: today,\n        to: today,\n      };\n\n      // 根据类型调整参数\n      switch (type) {\n        case 'daily':\n          // 本日用量：昨天到今天\n          param.displayType = 'day';\n          const yesterday = new Date(now);\n          yesterday.setDate(yesterday.getDate() - 1);\n          param.from = formatDate(yesterday);\n          param.to = today;\n          break;\n        case 'weekly':\n          // 近7日用量：7天前到今天\n          param.displayType = 'day';\n          const weekAgo = new Date(now);\n          weekAgo.setDate(weekAgo.getDate() - 7);\n          param.from = formatDate(weekAgo);\n          param.to = today;\n          break;\n        case 'monthly':\n          // 近30日用量：30天前到今天\n          param.displayType = 'day';\n          const monthAgo = new Date(now);\n          monthAgo.setDate(monthAgo.getDate() - 30);\n          param.from = formatDate(monthAgo);\n          param.to = today;\n          break;\n        case 'total':\n          // 累计用量：从很久以前到今天\n          param.displayType = 'total';\n          const tenYearsAgo = new Date(now);\n          tenYearsAgo.setFullYear(tenYearsAgo.getFullYear() - 10);\n          param.from = formatDate(tenYearsAgo);\n          param.to = today;\n          break;\n      }\n\n      return param;\n    },\n\n    // 使用新API获取能耗数据\n    async getEnergyDataByType(type) {\n      const param = this.buildParams(type);\n      if (!param) {\n        return 0;\n      }\n\n      try {\n        const res = await buildingEnergyDataList(param);\n        let total = 0;\n        if (res.data && res.data.datas) {\n          total = res.data.datas.reduce((sum, d) => sum + parseInt(d.totalVal || 0), 0);\n        }\n        return total;\n      } catch (error) {\n        console.error(`获取${type}数据失败:`, error);\n        return 0;\n      }\n    },\n    async getToken() {\n      try {\n        const response = await axios.get(\n          `${this.baseURL}/api/ztwyPower/getToken`,\n          {\n            params: {\n              systemnum: \"346E473FD1EF46E3A2EE43F393BCAF7C\",\n            },\n          }\n        );\n\n        if (response.data.errcode === 0) {\n          const { systemnum, tokenvalue, expiretime } =\n            response.data.resultvalue;\n          this.token = {\n            systemnum,\n            tokenvalue,\n            expiretime,\n          };\n          this.isTokenValid = true;\n\n          // 存储 token 和获取时间\n          const tokenData = {\n            ...this.token,\n            timestamp: new Date().getTime(),\n          };\n          localStorage.setItem(\"powerToken\", JSON.stringify(tokenData));\n\n          console.log(\"Token updated successfully:\", this.token);\n        } else {\n          console.error(\"Failed to get token:\", response.data.errmsg);\n          this.isTokenValid = false;\n        }\n      } catch (error) {\n        console.error(\"Error getting token:\", error);\n        this.isTokenValid = false;\n      }\n    },\n    checkTokenValidity() {\n      const storedToken = localStorage.getItem(\"powerToken\");\n      if (storedToken) {\n        const tokenData = JSON.parse(storedToken);\n        const expireTime = new Date(tokenData.expiretime).getTime();\n        const currentTime = new Date().getTime();\n        const tokenTimestamp = tokenData.timestamp;\n\n        // 检查 token 是否过期或距离上次获取是否超过5分钟\n        if (\n          currentTime < expireTime &&\n          currentTime - tokenTimestamp < 5 * 60 * 1000\n        ) {\n          this.token = {\n            systemnum: tokenData.systemnum,\n            tokenvalue: tokenData.tokenvalue,\n            expiretime: tokenData.expiretime,\n          };\n          this.isTokenValid = true;\n          return true;\n        }\n      }\n      return false;\n    },\n    async getElectricityUsage(startTime, endTime) {\n      if (!this.isTokenValid) {\n        await this.getToken();\n      }\n\n      try {\n        const response = await axios.post(\n          `${this.baseURL}/api/ztwyPower/getYdlByTime`,\n          {\n            tokenvalue: this.token.tokenvalue,\n            starttime: startTime,\n            endtime: endTime,\n            roomtag: this.roomtag, // Optional: if empty, will return all users' data\n          }\n        );\n\n        if (response.data.errcode === 0) {\n          // Calculate total consumption by summing ylvalue\n          const totalConsumption = response.data.resultvalue.reduce(\n            (sum, item) => {\n              return sum + parseFloat(item.ylvalue || 0);\n            },\n            0\n          );\n          return totalConsumption;\n        } else {\n          console.error(\n            \"Failed to get electricity usage:\",\n            response.data.errmsg\n          );\n          return 0;\n        }\n      } catch (error) {\n        console.error(\"Error getting electricity usage:\", error);\n        return 0;\n      }\n    },\n    formatDate(date) {\n      const year = date.getFullYear();\n      const month = String(date.getMonth() + 1).padStart(2, \"0\");\n      const day = String(date.getDate()).padStart(2, \"0\");\n      const hours = String(date.getHours()).padStart(2, \"0\");\n      const minutes = String(date.getMinutes()).padStart(2, \"0\");\n      const seconds = String(date.getSeconds()).padStart(2, \"0\");\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\n    },\n    async getPaymentStatistics(startTime, endTime) {\n      if (!this.isTokenValid) {\n        await this.getToken();\n      }\n\n      try {\n        const response = await axios.post(\n          `${this.baseURL}/api/ztwyPower/getJfAllInfos`,\n          {\n            tokenvalue: this.token.tokenvalue,\n            starttime: startTime,\n            endtime: endTime,\n            roomtag: this.roomtag,\n          }\n        );\n\n        if (response.data.errcode === 0) {\n          const totalFee = response.data.resultvalue.reduce((sum, item) => {\n            return sum + parseFloat(item.zdf || 0);\n          }, 0);\n          return totalFee;\n        } else {\n          console.error(\n            \"Failed to get payment statistics:\",\n            response.data.errmsg\n          );\n          return 0;\n        }\n      } catch (error) {\n        console.error(\"Error getting payment statistics:\", error);\n        return 0;\n      }\n    },\n    async updateConsumptionData() {\n      console.log(5685);\n      console.log('开始获取能耗数据...');\n\n      try {\n        // 使用新的API获取四个数据项\n        // 累计用量\n        this.totalConsumption.total = await this.getEnergyDataByType('total');\n\n        // 本日用量\n        this.totalConsumption.daily = await this.getEnergyDataByType('daily');\n\n        // 近7日用量\n        this.totalConsumption.weekly = await this.getEnergyDataByType('weekly');\n\n        // 近30日用量\n        this.totalConsumption.monthly = await this.getEnergyDataByType('monthly');\n\n        console.log('能耗数据更新成功:', this.totalConsumption);\n      } catch (error) {\n        console.error('更新能耗数据失败:', error);\n        // 如果新API失败，回退到原来的方法\n        await this.updateConsumptionDataFallback();\n      }\n    },\n\n    // 原来的数据获取方法作为备用\n    async updateConsumptionDataFallback() {\n      const now = new Date();\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n      const yesterday = new Date(today);\n      yesterday.setDate(yesterday.getDate() - 1);\n      const weekAgo = new Date(today);\n      weekAgo.setDate(weekAgo.getDate() - 7);\n      const monthAgo = new Date(today);\n      monthAgo.setDate(monthAgo.getDate() - 30);\n\n      // Get daily consumption\n      this.totalConsumption.daily = await this.getElectricityUsage(\n        this.formatDate(yesterday),\n        this.formatDate(now)\n      );\n\n      // Get weekly consumption\n      this.totalConsumption.weekly = await this.getElectricityUsage(\n        this.formatDate(weekAgo),\n        this.formatDate(now)\n      );\n\n      // Get monthly consumption\n      this.totalConsumption.monthly = await this.getElectricityUsage(\n        this.formatDate(monthAgo),\n        this.formatDate(now)\n      );\n\n      // 累计用量使用近30日数据作为近似值\n      this.totalConsumption.total = this.totalConsumption.monthly;\n\n      // Get total electricity fee\n      this.totalElectricityFee = await this.getPaymentStatistics(\n        this.formatDate(monthAgo),\n        this.formatDate(now)\n      );\n    },\n    async updateFeeData() {\n      const now = new Date();\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n      const yesterday = new Date(today);\n      yesterday.setDate(yesterday.getDate() - 1);\n      const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);\n      const yearStart = new Date(today.getFullYear(), 0, 1);\n\n      // Get yesterday's fee\n      this.electricityFees.yesterday = await this.getPaymentStatistics(\n        this.formatDate(yesterday),\n        this.formatDate(today)\n      );\n\n      // Get monthly fee\n      this.electricityFees.monthly = await this.getPaymentStatistics(\n        this.formatDate(monthStart),\n        this.formatDate(now)\n      );\n\n      // Get yearly fee\n      this.electricityFees.yearly = await this.getPaymentStatistics(\n        this.formatDate(yearStart),\n        this.formatDate(now)\n      );\n    },\n    async getMeterReadings() {\n      if (!this.isTokenValid) {\n        await this.getToken();\n      }\n\n      try {\n        const now = new Date();\n        const sevenDaysAgo = new Date(now);\n        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);\n\n        const response = await axios.post(\n          `${this.baseURL}/api/ztwyPower/getAllDbValue`,\n          {\n            tokenvalue: this.token.tokenvalue,\n            starttime: this.formatDate(sevenDaysAgo),\n            endtime: this.formatDate(now),\n            tag: 0, // 返回最后一次抄表记录\n            nodeid: 0, // 默认为0，返回全部\n            roomtag: this.roomtag,\n          }\n        );\n\n        if (response.data.errcode === 0) {\n          this.meterReadings = response.data.resultvalue;\n          console.log(\n            \"Meter readings retrieved successfully:\",\n            this.meterReadings\n          );\n        } else {\n          console.error(\"Failed to get meter readings:\", response.data.errmsg);\n        }\n      } catch (error) {\n        console.error(\"Error getting meter readings:\", error);\n      }\n    },\n    async getElectricityUsageData() {\n      if (!this.isTokenValid) {\n        await this.getToken();\n      }\n\n      try {\n        const now = new Date();\n        const sevenDaysAgo = new Date(now);\n        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);\n\n        const response = await axios.post(\n          `${this.baseURL}/api/ztwyPower/getYdlByTime`,\n          {\n            tokenvalue: this.token.tokenvalue,\n            starttime: this.formatDate(sevenDaysAgo),\n            endtime: this.formatDate(now),\n            roomtag: this.roomtag,\n          }\n        );\n\n        if (response.data.errcode === 0) {\n          this.electricityUsageData = response.data.resultvalue\n            .map((item) => ({\n              ...item,\n              ylvalue: parseFloat(item.ylvalue || 0),\n            }))\n            .sort((a, b) => new Date(a.readtime) - new Date(b.readtime));\n          console.log(\n            \"Electricity usage data retrieved successfully:\",\n            this.electricityUsageData\n          );\n        } else {\n          console.error(\n            \"Failed to get electricity usage:\",\n            response.data.errmsg\n          );\n        }\n      } catch (error) {\n        console.error(\"Error getting electricity usage:\", error);\n      }\n    },\n    showDetailDialog() {\n      this.dialogVisible = true;\n      this.detailData = []; // 清空数据\n      // 默认显示最近一天的数据\n      const end = new Date();\n      const start = new Date();\n      start.setTime(start.getTime() - 3600 * 1000 * 24); // 最近一天\n      this.dateRange = [\n        this.formatDate(start).split(\" \")[0],\n        this.formatDate(end).split(\" \")[0],\n      ];\n      this.searchData(); // 自动查询最近一天的数据\n    },\n    async searchData() {\n      if (!this.dateRange || this.dateRange.length !== 2) {\n        this.$message.warning(\"请选择日期范围\");\n        return;\n      }\n\n      try {\n        if (!this.isTokenValid) {\n          await this.getToken();\n        }\n\n        const response = await axios.post(\n          `${this.baseURL}/api/ztwyPower/getYdlByTime`,\n          {\n            tokenvalue: this.token.tokenvalue,\n            starttime: `${this.dateRange[0]} 00:00:00`,\n            endtime: `${this.dateRange[1]} 23:59:59`,\n            roomtag: this.roomtag,\n          }\n        );\n\n        if (response.data.errcode === 0) {\n          if (\n            response.data.resultvalue &&\n            response.data.resultvalue.length > 0\n          ) {\n            this.detailData = response.data.resultvalue\n              .map((item) => ({\n                roomtag: item.roomtag || \"\",\n                zhaddress: item.zhaddress || \"\",\n                startcode: item.startcode\n                  ? parseFloat(item.startcode).toFixed(1)\n                  : \"0.0\",\n                endcode: item.endcode\n                  ? parseFloat(item.endcode).toFixed(1)\n                  : \"0.0\",\n                ylvalue: item.ylvalue\n                  ? parseFloat(item.ylvalue).toFixed(2)\n                  : \"0.00\",\n                jfmx: item.jfmx || \"\",\n                endtime: item.endtime ? item.endtime.replace(/-/g, \"/\") : \"\",\n              }))\n              .sort((a, b) => new Date(b.endtime) - new Date(a.endtime));\n            this.$message.success(\"查询成功\");\n          } else {\n            this.detailData = [];\n            this.$message.warning(\"所选时间范围内无数据\");\n          }\n        } else {\n          this.$message.error(\"获取数据失败：\" + response.data.errmsg);\n          this.detailData = [];\n        }\n      } catch (error) {\n        this.$message.error(\"获取数据失败：\" + (error.message || \"未知错误\"));\n        this.detailData = [];\n      }\n    },\n    exportToExcel() {\n      if (!this.detailData || !this.detailData.length) {\n        this.$message.warning(\"暂无数据可导出\");\n        return;\n      }\n\n      try {\n        // 准备要导出的数据\n        const exportData = this.detailData.map((item) => ({\n          房间标识: item.roomtag || \"\",\n          住户地址: item.zhaddress || \"\",\n          起码: item.startcode ? parseFloat(item.startcode).toFixed(1) : \"0.0\",\n          止码: item.endcode ? parseFloat(item.endcode).toFixed(1) : \"0.0\",\n          \"用电量(kwh)\": item.ylvalue\n            ? parseFloat(item.ylvalue).toFixed(2)\n            : \"0.00\",\n          缴费明细: item.jfmx || \"\",\n          抄表时间: item.endtime ? item.endtime.replace(/-/g, \"/\") : \"\",\n        }));\n\n        // 创建工作簿并设置数据\n        const ws = XLSX.utils.json_to_sheet(exportData);\n        const wb = XLSX.utils.book_new();\n        XLSX.utils.book_append_sheet(wb, ws, \"用电量记录\");\n\n        // 设置列宽\n        ws[\"!cols\"] = [\n          { wch: 15 }, // 房间标识\n          { wch: 20 }, // 住户地址\n          { wch: 12 }, // 起码\n          { wch: 12 }, // 止码\n          { wch: 15 }, // 用电量\n          { wch: 15 }, // 缴费明细\n          { wch: 20 }, // 抄表时间\n        ];\n\n        // 直接使用 XLSX.writeFile 导出文件\n        const fileName = `用电量记录_${this.dateRange[0]}_${this.dateRange[1]}.xlsx`;\n        XLSX.writeFile(wb, fileName);\n\n        this.$message.success(\"导出成功\");\n      } catch (error) {\n        console.error(\"Export error:\", error);\n        this.$message.error(\"导出失败：\" + (error.message || \"未知错误\"));\n      }\n    },\n    async getElectricityFeeData() {\n      if (!this.isTokenValid) {\n        await this.getToken();\n      }\n\n      try {\n        const now = new Date();\n        const sevenDaysAgo = new Date(now);\n        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);\n\n        const response = await axios.post(\n          `${this.baseURL}/api/ztwyPower/getJfAllInfos`,\n          {\n            tokenvalue: this.token.tokenvalue,\n            starttime: this.formatDate(sevenDaysAgo),\n            endtime: this.formatDate(now),\n            roomtag: this.roomtag,\n          }\n        );\n\n        if (response.data.errcode === 0) {\n          this.electricityFeeData = response.data.resultvalue\n            .map((item) => ({\n              ...item,\n              zdf: parseFloat(item.zdf || 0),\n            }))\n            .sort((a, b) => new Date(a.readtime) - new Date(b.readtime));\n          console.log(\n            \"Electricity fee data retrieved successfully:\",\n            this.electricityFeeData\n          );\n        } else {\n          console.error(\n            \"Failed to get electricity fee data:\",\n            response.data.errmsg\n          );\n        }\n      } catch (error) {\n        console.error(\"Error getting electricity fee data:\", error);\n      }\n    },\n  },\n  async created() {\n    // 初始化建筑信息\n    console.log(5685);\n\n    // 直接设置建筑ID为1\n    this.curBuilding = { id: 1 };\n    console.log('curBuilding 已初始化:', this.curBuilding);\n\n    // 初始化获取 token\n    if (!this.checkTokenValidity()) {\n      await this.getToken();\n    }\n\n    // 更新数据\n    await this.updateConsumptionData();\n    await this.updateFeeData();\n    await this.getMeterReadings();\n    await this.getElectricityUsageData();\n    await this.getElectricityFeeData();\n\n    // 每5分钟更新一次 token\n    setInterval(async () => {\n      await this.getToken();\n    }, 5 * 60 * 1000);\n\n    // 每5分钟更新一次数据\n    setInterval(async () => {\n      await this.updateConsumptionData();\n      await this.updateFeeData();\n      await this.getMeterReadings();\n      await this.getElectricityUsageData();\n      await this.getElectricityFeeData();\n    }, 5 * 60 * 1000);\n  },\n};\n</script>\n\n<style lang=\"less\" scoped>\n.all {\n  display: flex;\n  flex-direction: row;\n  margin-top: 5px;\n\n  .zong {\n    display: flex;\n    flex-direction: row;\n    margin-top: 10px;\n    .echart1,\n    .echart2 {\n      flex: 1;\n\n      .center {\n        margin-top: -24px;\n        font-family: \"Source Han Sans SC\", sans-serif;\n        font-weight: 400;\n        font-size: 17px;\n        color: #00ffb6;\n        text-align: center;\n        margin-bottom: 10px;\n      }\n\n      .btn {\n        width: 133px;\n        height: 31px;\n        border: 1px solid #2d6cb0;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-family: \"Source Han Sans SC\", sans-serif;\n        font-weight: bold;\n        font-size: 15px;\n        color: #ffffff;\n        border-radius: 30px;\n        margin-left: 7%;\n      }\n    }\n  }\n\n  .ltitle1 {\n    margin-top: 10px;\n    position: relative;\n  }\n\n  .line1 {\n    width: 2px;\n    height: 823px;\n    opacity: 0.64;\n    background-color: #204964;\n  }\n\n  .all1 {\n    flex: 557;\n\n    .nenghao {\n      width: 257px;\n      height: 183px;\n      background: url(\"../assets/image/nenghao.png\");\n      background-size: 100% 100%;\n      margin-left: 100px;\n      margin-top: 45px;\n      text-align: center;\n      font-family: Alibaba PuHuiTi;\n      font-weight: 400;\n      font-size: 20px;\n      color: #ffffff;\n      line-height: 213px;\n    }\n\n    .nhp {\n      text-align: center;\n      font-family: Alibaba PuHuiTi;\n      font-weight: 500;\n      font-size: 52px;\n      color: #2cc1ff;\n      margin-top: 8px;\n    }\n\n    .nh {\n      margin-left: 24px;\n      margin-top: 32px;\n      width: 423px;\n      height: 105px;\n      border: 1px solid #364d5a;\n      background-size: 100% 100%;\n      display: flex;\n      flex-direction: row;\n      align-items: center;\n      padding-left: 72px;\n      margin-bottom: 5px;\n      // justify-content: space-evenly;\n\n      .nhimg {\n        width: 107px;\n        height: 90px;\n        margin-right: 35px;\n      }\n\n      .nhtit {\n        width: 148px;\n        margin-left: 10px;\n        margin-top: 3px;\n\n        .p11 {\n          font-family: Alibaba PuHuiTi;\n          font-weight: 400;\n          font-size: 30px;\n          color: #7acfff;\n        }\n\n        .p12 {\n          font-family: Alibaba PuHuiTi;\n          font-weight: 400;\n          font-size: 30px;\n          color: #ffa170;\n        }\n\n        .p2 {\n          font-family: Alibaba PuHuiTi;\n          font-weight: 400;\n          font-size: 20px;\n          color: #ffffff;\n        }\n      }\n\n      .nhtit1 {\n        display: flex;\n        flex-direction: row;\n        align-items: center;\n        margin-left: 35px;\n\n        .nhimg1 {\n          width: 16px;\n          height: 20px;\n        }\n\n        .pp1 {\n          font-family: Alibaba PuHuiTi;\n          font-weight: 400;\n          font-size: 17px;\n          color: #0df29b;\n        }\n\n        .pp2 {\n          font-family: Alibaba PuHuiTi;\n          font-weight: 400;\n          font-size: 17px;\n          color: #ffa170;\n        }\n      }\n\n      .nht {\n        margin-top: 10px;\n        display: flex;\n        flex-direction: column;\n\n        .pp {\n          margin-left: 35px;\n          font-family: Alibaba PuHuiTi;\n          font-weight: 400;\n          font-size: 17px;\n\n          color: #cccccc;\n        }\n      }\n    }\n  }\n\n  .all2 {\n    margin-left: -52px;\n    flex: 627;\n    display: flex;\n    flex-direction: column;\n    .shinei {\n      .itemshei {\n        display: flex;\n        justify-content: space-around;\n        .nenghaos {\n          width: 227px;\n          height: 173px;\n          background: url(\"../assets/image/nenghao.png\");\n          background-size: 100% 100%;\n          text-align: center;\n          margin-left: 10px;\n          margin-top: 33px;\n          text-align: center;\n          font-family: Alibaba PuHuiTi;\n          font-weight: 400;\n          font-size: 14px;\n          color: #ffffff;\n          line-height: 144px;\n        }\n        .nhps {\n          text-align: center;\n          font-family: Alibaba PuHuiTi;\n          font-weight: 500;\n          font-size: 21px;\n          color: #2cc1ff;\n          margin-top: 8px;\n        }\n      }\n    }\n  }\n\n  .all3 {\n    flex: 658;\n    margin-left: 15px;\n  }\n}\n\n.shinei {\n  width: 100%;\n  height: 100%;\n}\n.shuantitle {\n  width: 100%;\n  display: flex;\n  margin-top: 10px;\n  .title {\n    width: 95%;\n    background: url(\"../assets/image/title.png\");\n    background-size: 100% 100%;\n\n    height: 25px;\n    font-family: Source Han Sans SC;\n    font-weight: 400;\n    font-size: 25px;\n    color: #ffffff;\n    text-shadow: 2px 2px 3px rgba(0, 0, 0, 0.47);\n    font-style: italic;\n    text-align: left;\n    line-height: 4px;\n    padding-left: 33px;\n  }\n}\n.nenghao {\n  width: 167px;\n  height: 113px;\n  background: url(\"../assets/image/nenghao.png\");\n  background-size: 100% 100%;\n  text-align: center;\n  margin-left: 83px;\n  // margin-top: 63px;\n  text-align: center;\n  font-family: Alibaba PuHuiTi;\n  font-weight: 400;\n  font-size: 14px;\n  color: #ffffff;\n  line-height: 144px;\n}\n.nhp {\n  text-align: center;\n  font-family: Alibaba PuHuiTi;\n  font-weight: 500;\n  font-size: 25px;\n  color: #2cc1ff;\n  margin-top: 8px;\n  width: 79%;\n}\n\n.contents {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  background: url(\"../assets/image/zichanbeijin.png\");\n  width: 1863px;\n  height: 868px;\n  z-index: 99999;\n  padding-left: 34px;\n  padding-right: 22px;\n  padding-top: 21px;\n}\n.toubu {\n  width: 100%;\n\n  position: relative;\n}\n.el-select {\n  margin-top: -1px;\n  margin-left: 10px;\n  background: #00203d;\n  border-radius: 3px;\n  border: 1px solid #3e89db;\n\n  /deep/.el-select__wrapper {\n    background: #00203d !important;\n    box-shadow: none;\n  }\n\n  /deep/.el-select__wrapper .is-hovering:not {\n    box-shadow: none;\n  }\n\n  /deep/.el-select__wrapper:hover {\n    box-shadow: none;\n  }\n\n  /deep/.el-select__placeholder.is-transparent {\n    color: #2cc1ff;\n  }\n\n  /deep/.el-select__placeholder {\n    color: #2cc1ff;\n  }\n\n  /deep/.el-select-dropdown__item.is-hovering {\n    background-color: #2cc1ff !important;\n  }\n}\n.sp {\n  margin-top: -5px;\n  margin-left: 12px;\n  font-family: Alibaba PuHuiTi;\n  font-weight: bold;\n  font-size: 21px;\n  color: #2cc1ff;\n}\n.img1sss {\n  cursor: pointer;\n  width: 15px;\n  height: 15px;\n}\n\n.table-container {\n  cursor: pointer;\n  .el-table {\n    background-color: transparent !important;\n\n    // 设置滚动条样式\n    ::-webkit-scrollbar {\n      width: 6px;\n      height: 6px;\n    }\n\n    ::-webkit-scrollbar-thumb {\n      background: #0a3054;\n      border-radius: 3px;\n    }\n\n    ::-webkit-scrollbar-track {\n      background: #1e415c;\n      border-radius: 3px;\n    }\n\n    // 设置表格背景透明\n    ::v-deep .el-table__body-wrapper {\n      background-color: transparent;\n\n      &::-webkit-scrollbar {\n        width: 6px;\n        height: 6px;\n      }\n\n      &::-webkit-scrollbar-thumb {\n        background: #0a3054;\n        border-radius: 3px;\n      }\n\n      &::-webkit-scrollbar-track {\n        background: #1e415c;\n        border-radius: 3px;\n      }\n    }\n  }\n}\n\n.custom-modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 9999;\n}\n\n.custom-modal {\n  width: 1300px;\n  background: #1B2A47;\n  border: 1px solid #00E4FF;\n  border-radius: 8px;\n  padding: 0;\n  \n  .modal-header {\n    background: #1B2A47;\n    border-bottom: 1px solid #00E4FF;\n    padding: 15px 20px;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n\n    .modal-title {\n      color: #00E4FF;\n      font-size: 18px;\n      font-weight: bold;\n    }\n\n    .header-buttons {\n      display: flex;\n      align-items: center;\n      \n      .close-text {\n        color: #00E4FF;\n        margin-right: 15px;\n      }\n\n      .close-btn {\n        color: #00E4FF;\n        font-size: 20px;\n        cursor: pointer;\n        &:hover {\n          opacity: 0.8;\n        }\n      }\n    }\n  }\n\n  .modal-content {\n    padding: 20px;\n    background: #1B2A47;\n\n    .search-container {\n      margin-bottom: 20px;\n      \n      .el-button--primary {\n        background: #1B2A47;\n        border: 1px solid #00E4FF;\n        color: #00E4FF;\n        \n        &:hover {\n          opacity: 0.8;\n        }\n      }\n    }\n\n    .el-table {\n      background: #1B2A47 !important;\n      border: 1px solid #00E4FF;\n      \n      &::before {\n        display: none;\n      }\n\n      th {\n        background: #162442 !important;\n        border-bottom: 1px solid #00E4FF !important;\n        color: #00E4FF !important;\n        font-weight: bold;\n      }\n\n      td {\n        background: #1B2A47 !important;\n        border-bottom: 1px solid rgba(0, 228, 255, 0.2) !important;\n        color: #fff !important;\n      }\n\n      .el-table__row:hover > td {\n        background: #243B6B !important;\n      }\n    }\n\n    .el-table--border::after {\n      display: none;\n    }\n  }\n}\n\n// 修改日期选择器样式\n:deep(.el-date-editor) {\n  background: #1B2A47;\n  border: 1px solid #00E4FF;\n  \n  .el-range-input {\n    background: #1B2A47;\n    color: #fff;\n  }\n  \n  .el-range-separator {\n    color: #00E4FF;\n  }\n}\n\n// 修改滚动条样式\n:deep(.el-table__body-wrapper::-webkit-scrollbar) {\n  width: 6px;\n  height: 6px;\n}\n\n:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {\n  background: #00E4FF;\n  border-radius: 3px;\n}\n\n:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {\n  background: #1B2A47;\n}\n\n.title-container {\n  position: absolute;\n  top: -9px;\n  left: 57.5%;\n  width: 100%;\n  z-index: 1000;\n\n  .more-btn {\n    display: flex;\n    align-items: center;\n    cursor: pointer;\n    color: #2cc1ff;\n    font-size: 20px;\n\n    &:hover {\n      opacity: 0.8;\n    }\n\n    i {\n      margin-left: 5px;\n    }\n  }\n}\n</style>\n"]}]}