{"remainingRequest": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\src\\components\\echarts\\SystemDete.vue?vue&type=template&id=b4584e34&scoped=true", "dependencies": [{"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\src\\components\\echarts\\SystemDete.vue", "mtime": 1751449256775}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 499162500000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdiBjbGFzcz0iZWNoYXJ0IiByZWY9ImVjaGFydCI+PC9kaXY+Cg=="}, {"version": 3, "sources": ["E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\src\\components\\echarts\\SystemDete.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "E:/svn/SH-20240918-0186-SFTianjinUniversityH5/src/src/components/echarts/SystemDete.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div class=\"echart\" ref=\"echart\"></div>\n</template>\n\n<script>\nimport * as echarts from \"echarts\";\nimport { mapGetters } from 'vuex';\n\nexport default {\n  name: \"IoTequip\",\n  props: {\n    equipmentStatus: {\n      type: Array,\n      default: () => []\n    }\n  },\n  computed: {\n    ...mapGetters({\n      yiqiStatus: 'equipment/yiqiStatus'\n    })\n  },\n  data() {\n    return {\n      chart: null\n    };\n  },\n  watch: {\n    yiqiStatus: {\n      handler(newVal) {\n        this.updateChart();\n      },\n      deep: true\n    }\n  },\n  mounted() {\n    this.initChart();\n    this.updateChart();\n  },\n  beforeDestroy() {\n    if (this.chart) {\n      this.chart.dispose();\n      this.chart = null;\n    }\n  },\n  methods: {\n    initChart() {\n      this.chart = echarts.init(this.$refs.echart);\n    },\n    updateChart() {\n      if (!this.chart) return;\n\n      // 过滤掉故障状态，只保留正在使用和待机中\n      const filteredData = this.yiqiStatus && this.yiqiStatus.length ?\n        this.yiqiStatus.filter(item => item.name !== \"故障\") : [];\n\n      const data = filteredData.length ? filteredData : [\n        { name: \"正在使用\", value: 0 },\n        { name: \"待机中\", value: 0 },\n      ];\n\n      const colors = [\n        \"37, 171, 200\",\n        \"214, 128, 120\",\n        \"252, 182, 53\",\n        \"47, 255, 242\",\n        \"42, 191, 191\"\n      ];\n\n      const option = {\n        legend: {\n          top: \"10\",\n          right: \"18%\",\n          data: data.map((it) => it.name),\n          textStyle: {\n            color: \"#fff\",\n            fontSize: 16,\n            fontFamily: \"Alibaba PuHuiTi\",\n          },\n          itemWidth: 13,\n          itemHeight: 13,\n        },\n        tooltip: {\n          trigger: \"item\",\n          formatter: \"{a} <br/>{b} : {c} ({d}%)\",\n          textStyle: {\n            fontSize: 15,\n          },\n        },\n        series: [\n          {\n            name: \"仪器状态\",\n            type: \"pie\",\n            radius: [\"30%\", \"80%\"],\n            center: [\"50%\", \"60%\"],\n            roseType: \"radius\",\n            label: {\n              show: true,\n              normal: {\n                position: \"outside\",\n                fontSize: 18,\n                formatter: \"{d}%\",\n                color: \"#fff\",\n              },\n            },\n            labelLine: {\n              length: 2,\n              length2: 7,\n            },\n            data: data.map((it, i) => {\n              return {\n                value: it.value,\n                name: it.name,\n                itemStyle: {\n                  color: `rgba(${colors[i]},0.7)`,\n                  borderColor: `rgba(${colors[i]},1)`,\n                  borderWidth: 1,\n                },\n              };\n            }),\n          },\n        ],\n      };\n\n      this.chart.setOption(option);\n    },\n  },\n};\n</script>\n\n<style lang=\"less\" scoped>\n.echart {\n  width: 100%;\n  height: 100%;\n}\n\n@media (max-height: 1080px) {\n  .echart {\n    width: 100%;\n    height: 100% !important;\n  }\n}\n</style>"]}]}