// /* eslint-disable */
const config = {
  dstime: 300, // 定时时间 单位秒
  titledoc: "天津大学大型仪器平台智慧实验室系统",
  captions: "天津大学大型仪器平台智慧实验室系统",
  // URL: "http://luxsanapi.wocyd.com/",
  // iframe:
  //   "http://diy.3dzhanting.cn/engineer-xf/tianjin/scidh3dview-share/index.html?ids=YIHuaomzuSKUtXFCRYbdqA==",
  iframe: "scidh3dview-share/index.html?ids=YIHuaomzuSKUtXFCRYbdqA==",

  // iframe: "",
  // baseURL: "ykapi",
  baseURL: "https://tjdx.yuankong.org.cn",
  //baseURL: "http://192.168.3.249:8066",
  jkurl: "http://192.168.1.202",

  jlURL:'http://121.193.130.40/lims/api',
  // jlURL:'http://yiqi.tju.edu.cn/lims/api',
  hkurl: "https://diy.3dzhanting.cn/engineer-xf/tianjin/img/jk.jpg",
  intervaltime: 1,
  videoUrl: 'http://192.168.3.250:9080/TD/86.live.flv'
};

// 将配置挂载到window对象上
Object.keys(config).forEach((key) => {
  window[key] = config[key];
});
