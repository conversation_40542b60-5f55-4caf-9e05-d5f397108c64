// 在函数外部定义一个数组来存储labelName

let labelNamesArray = [];

function addlable(lablelist) {
  console.log(lablelist, 111);
  lablelist.forEach((item) => {
    // 将每个对象的center属性中的y值加上0.2
    item.center.y += 3;
    // 隐藏所有标签
    Object.keys(deviceLabels).forEach((key) => {
      console.log(key, 12121);
      console.log(deviceLabels, 111);
      //view.nameVisible(key, false);
      view.removeObjByNames([key]);
    });
    deviceLabels = [];
    if (
      item.name.includes("lqt") ||
      item.name.includes("flrb") ||
      item.name.includes("dwsb") ||
      item.name.includes("zwsb")
    ) {
      console.log(labelNamesArray);
      let labelName = "lable" + item.name;
      console.log("Current labelName:", labelName); // 添加打印语句记录labelName
      // 检查数组中是否已经存在相同的labelName
      if (!labelNamesArray.includes(labelName)) {
        // 如果不存在，将labelName添加到数组中
        labelNamesArray.push(labelName);
      } else {
        console.log("相同的labelName已存在，不添加到数组中。");
      }
      console.log(labelNamesArray, 2121);
      let labeltit;
      if (item.name.includes("lqt")) {
        labeltit = "冷却塔";
      } else if (item.name.includes("flrb")) {
        labeltit = "风冷热泵";
      } else if (item.name.includes("sb")) {
        labeltit = "水泵";
      }
      // 检查当前设备是否已经有标签
      if (deviceLabels[labelName]) {
        console.log("标签已存在，只显示当前设备的标签");
        // view.nameVisible(labelName, true);
      } else {
        console.log("执行了");
        // 如果还没有标签，创建新的标签并显示
        let earthMassDiv = document.createElement("div");
        earthMassDiv.className = "label";
        earthMassDiv.id = labelName;
        let infoDiv = document.createElement("div");
        infoDiv.style.backgroundImage = "url('./images/ba1.png')";
        infoDiv.style.pointerEvents = "none";
        infoDiv.style.backgroundSize = "100% 100%"; // 背景图充满整个div
        infoDiv.style.width = "65px"; // 调整宽度
        infoDiv.style.height = "35px"; // 调整高度
        infoDiv.style.color = "white"; // 文本颜色
        infoDiv.style.fontSize = "6px"; // 字体大小
        infoDiv.style.display = "flex"; // 使用Flex布局使内容居中
        infoDiv.style.flexDirection = "column"; // 垂直排列内容
        infoDiv.style.justifyContent = "center"; // 在div中垂直居中内容
        infoDiv.style.alignItems = "center"; // 在div中水平居中内容
        //infoDiv.style.padding = "-2px 5px"; // 调整内边距，左右各10px
        infoDiv.style.paddingTop = "-10px";
        // 设置设备名称和运行状态的文本内容
        infoDiv.innerHTML = `<div class='divtit1'><span>设备名称：</span>
          <span style="color:#8bfac4;">${labeltit}</span></div>
          <div><span>运行状态：</span>
          <span style="color:#8bfac4;">运行中</span></div> `;

        // 将infoDiv添加到earthMassDiv中
        earthMassDiv.appendChild(infoDiv);

        // 调用3D视图库的方法来添加这个新创建的标签到视图中
        view.add3dSprite(earthMassDiv, {
          position: item.center,
          name: labelName,
          scale: 0.1,
        });

        // 更新全局deviceLabels映射，以跟踪这个新的标签
        deviceLabels[labelName] = earthMassDiv;
      }
      view.nameVisible(labelName, true);
    }
  });
}

function addlable1(lablelist, value) {
  lablelist.forEach((item) => {
    // 将每个对象的center属性中的y值加上0.2
    item.center.y += 0.08;
    // 隐藏所有标签
    if (value) {
      let labelName = "lable11" + item.name;
      let labeltit;
      if (item.name.includes("lengqueta")) {
        labeltit = "冷却塔";
      } else if (item.name.includes("flrb")) {
        labeltit = "风冷热泵";
      } else if (item.name.includes("shuibeng")) {
        labeltit = "水泵";
      }
      // 检查当前设备是否已经有标签
      if (deviceLabels[labelName]) {
        console.log("标签已存在，只显示当前设备的标签");
        view.nameVisible(labelName, true);
      } else {
        // 如果还没有标签，创建新的标签并显示
        let earthMassDiv = document.createElement("div");
        earthMassDiv.className = "label";

        let infoDiv = document.createElement("div");
        infoDiv.style.backgroundImage = "url('./images/ba2.png')";
        infoDiv.style.pointerEvents = "none";
        infoDiv.style.backgroundSize = "100% 100%"; // 背景图充满整个div
        infoDiv.style.width = "35px"; // 调整宽度
        infoDiv.style.height = "22px"; // 调整高度
        infoDiv.style.color = "white"; // 文本颜色
        infoDiv.style.fontSize = "4px"; // 字体大小
        infoDiv.style.display = "flex"; // 使用Flex布局使内容居中

        infoDiv.style.justifyContent = "center"; // 在div中垂直居中内容
        //infoDiv.style.alignItems = "center"; // 在div中水平居中内容
        infoDiv.style.paddingTop = "3px"; // 调整内边距，左右各10px

        // 设置设备名称和运行状态的文本内容
        infoDiv.innerHTML = `<div  class='icon'><span style="color:#ea803b;">${labeltit}</span>
          <span>运行中</span><div class='icon1'></div></div>
          `;

        // 将infoDiv添加到earthMassDiv中
        earthMassDiv.appendChild(infoDiv);
        // 调用3D视图库的方法来添加这个新创建的标签到视图中
        view.add3dSprite(earthMassDiv, {
          position: item.center,
          name: labelName,
          scale: 0.006,
        });

        // 更新全局deviceLabels映射，以跟踪这个新的标签
        deviceLabels[labelName] = earthMassDiv;
      }
    } else {
      Object.keys(deviceLabels).forEach((key) => {
        view.nameVisible(key, false);
      });
    }
  });
}
//添加楼顶标签

// 创建一个数组来存储已创建的标签名称
let createdLabels = [];

function addlableldbq(labelList,title,Temperature,Humidity) {
  console.log(Temperature,Humidity,"Temperature,Humidity");
  
  // 先销毁之前创建的所有标签
  createdLabels.forEach((labelName) => {
    view.removeObjByNames([labelName]);
  });
  // 清空数组
  createdLabels = [];

  // 遍历 labelList 中的每一个项目
  labelList.forEach((item) => {
    item.center.y += 3;
    let labelName = "lable11" + item.name;
    // 将新创建的标签名称添加到数组中
    createdLabels.push(labelName);

    let earthMassDiv = document.createElement("div");
    earthMassDiv.className = "label";
    earthMassDiv.id = labelName;

    // 创建容器div
    let containerDiv = document.createElement("div");
    containerDiv.style.position = "relative";

    // 创建图片元素
    let imgElement = document.createElement("img");
    imgElement.src = `./images/ba1.png`;
    imgElement.style.pointerEvents = "none";
    imgElement.style.width = "75px";
    imgElement.style.height = "42px";

    // 创建文本信息div
    let textDiv = document.createElement("div");
    textDiv.style.position = "absolute";
    textDiv.style.top = "3px";
    textDiv.style.left = "0px";
    textDiv.style.right = "0px";
    textDiv.style.color = "#fff";
    textDiv.style.fontSize = "6px";
    //textDiv.style.display = "table"; // 使用表格布局实现对齐
    textDiv.innerHTML = `
    <div style="display: flex; justify-content: center; align-items: center; width: 100%; margin-bottom: 1px; color: #8bfac4;">${title}</div>
    <div style="display: flex; justify-content: center; align-items: center; width: 100%;">
        <span style="display: table-cell; padding-right: 2px; text-align: right; color: #ffffff;">${item.name.includes('ktx')?'温度：':'当前排风量：'}</span>
        <span style="display: table-cell; color: #8bfac4;">${item.name.includes('ktx')?Temperature+'℃':'500m³/h'}</span>
    </div>
    <div style="display: flex; justify-content: center; align-items: center; width: 100%;">
        <span style="display: table-cell; padding-right: 2px; text-align: right; color: #ffffff;">湿度：</span>
        <span style="display: table-cell; color: #8bfac4;">${Humidity+'%'}</span>
    </div>
    `;

    // 创建更多按钮
    let moreButton = document.createElement("button");
    moreButton.innerText = "更多";
    moreButton.style.position = "absolute";
    moreButton.style.bottom = "11px";
    moreButton.style.right = "1px";
    moreButton.style.padding = "1px 4px";
    moreButton.style.backgroundColor = "rgba(0,0,0,0.5)";
    moreButton.style.border = "0.31px solid #fff";
    moreButton.style.color = "#fff";
    moreButton.style.borderRadius = "2px";
    moreButton.style.cursor = "pointer";
    moreButton.style.fontSize = "4px";

    // 添加点击事件
    moreButton.onclick = function (e) {
      e.stopPropagation(); // 阻止事件冒泡
      console.log(`设备${item.name}的详细信息：`, {
        name: item.name,
        position: item.center,
        airflow: "500m³/h",
        frequency: "55Hz",
      });
      window.parent.postMessage({
        type: "xfg",
        data: item.name,
      });
    };

    // 组装所有元素
    containerDiv.appendChild(imgElement);
    containerDiv.appendChild(textDiv);
    containerDiv.appendChild(moreButton);
    earthMassDiv.appendChild(containerDiv);

    // 添加到3D视图
    view.add3dSprite(earthMassDiv, {
      position: item.center,
      name: labelName,
      scale: 0.15,
    });
  });
}
