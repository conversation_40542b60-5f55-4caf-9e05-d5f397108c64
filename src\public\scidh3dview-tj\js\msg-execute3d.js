var comdata;

window.addEventListener("message", function (event) {
  //event.data获取传过来的数据
  console.log(event, "收到了a");
  let name = event.data.name;
  let comdata = event.data.param;

  if (event.data.type == "function") {
    console.log(event.data, "收到了");
  } else if (event.data.type == "xfj") {
    console.log(event.data.param.data, "xfg11");

    addlableldbq(view.getObjCenterByNames([comdata.data]),event.data.param.title,event.data.param.Temperature,event.data.param.Humidity);
    console.log(view.searchByName(comdata.data), "xfg11");

    view.setOutlineModel([view.searchByName(comdata.data)]);
  } else {
    let comdata = event.data.param;
    console.log(comdata, "收到了");

    if (comdata.data == "zutai1") {
      view.setLayer([
        "floor",
        "JZ1_B1_SN_Model_pipeline_sf_01",
        "JZ1_F1_SN_Model_pipeline_sf_01",
        "JZ1_F2_SN_Model_pipeline_sf_01",
        "JZ1_F3_SN_Model_pipeline_sf_01",
        "JZ1_F4_SN_Model_pipeline_sf_01",
        "JZ1_F5_SN_Model_pipeline_sf_01",
        "JZ1_Roof_SN_Model_pipeline_sf_01",
        "JZ1_F1_SN_Model_pipeline_xf_01",
        "JZ1_F2_SN_Model_pipeline_xf_01",
        "JZ1_F3_SN_Model_pipeline_xf_01",
        "JZ1_F4_SN_Model_pipeline_xf_01",
        "JZ1_F5_SN_Model_pipeline_xf_01",
        "JZ1_Roof_SN_Model_pipeline_xf_01",
      ]);

      // view.animateCamera(
      //   {
      //     x: 19.86918084985126,
      //     y: 6.012298248396115,
      //     z: 11.386288472414392,
      //   },
      //   {
      //     x: 19.86585560501809,
      //     y: 0.18774235567265957,
      //     z: 7.154502216058601,
      //   },
      //   0
      // );
    } else if (comdata.data == "zutai2") {
      view.setLayer([
        "floor",
        "JZ1_B1_SN_Model_pipeline_pf_01",
        "JZ1_F1_SN_Model_pipeline_pf_01",
        "JZ1_F2_SN_Model_pipeline_pf_01",
        "JZ1_F3_SN_Model_pipeline_pf_01",
        "JZ1_F4_SN_Model_pipeline_pf_01",
        "JZ1_F5_SN_Model_pipeline_pf_01",
        "JZ1_Roof_SN_Model_pipeline_pf_01",
      ]);
    } else if (comdata.data == "zutai3") {
      view.setLayer([
        "floor",
        "JZ1_B1_SN_Model_pipeline_sf_01",
        "JZ1_F1_SN_Model_pipeline_sf_01",
        "JZ1_F2_SN_Model_pipeline_sf_01",
        "JZ1_F3_SN_Model_pipeline_sf_01",
        "JZ1_F4_SN_Model_pipeline_sf_01",
        "JZ1_F5_SN_Model_pipeline_sf_01",
        "JZ1_Roof_SN_Model_pipeline_sf_01",
        "JZ1_F1_SN_Model_pipeline_xf_01",
        "JZ1_F2_SN_Model_pipeline_xf_01",
        "JZ1_F3_SN_Model_pipeline_xf_01",
        "JZ1_F4_SN_Model_pipeline_xf_01",
        "JZ1_F5_SN_Model_pipeline_xf_01",
        "JZ1_Roof_SN_Model_pipeline_xf_01",
      ]);
    } else if (comdata.data == "zutai") {
      view.setLayer([
        "floor",
        "JZ1_Building_01"
      ]);
    }
    view.clearAllLight(); // 清除所有灯光
    let lightConfig = [
      {
        type: "AmbientLight",
        color: "#aaaaff",
        intensity: 1,
      },
      {
        intensity: 3,
        type: "DirectionalLight",
        color: "#fff",
        position: [353.1440322709692, 32.118162337619367, 415.14587542705004],
      },
    ];
    view.setLight(lightConfig);
  }
});
//生成标签
function createLabel(imageSrc, width, className, position, scale, name) {
  const container = document.createElement("div");
  const img = document.createElement("img");
  img.src = imageSrc;
  img.style.width = width + "px";
  img.draggable = false;
  container.appendChild(img);
  container.className = className;
  view.add3dSprite(container, {
    position,
    scale,
    name,
  });
}

function addLabel() {
  const labels = view
    .getObjCenterByNames([
      "B1",
      "B2",
      "B3",
      "B4",
      "W1",
      "W2",
      "C1",
      "C2",
      "sushe_01",
      "sushe_02",
      "sushe_03",
      "sushe_04",
      "sushe_05",
      "sushe_06",
      "sushe_07",
      "sushe_08",
      "sushe_09",
      "sushe_10",
    ])
    .map((item) => {
      return {
        name: item.name,
        pos: item.center,
        src: item.name + ".png",
        scale: 0.6,
      };
    });
  console.log(labels);
  labels.forEach((label) => {
    createLabel(
      `./images/${label.src}`,
      95,
      "label",
      label.pos,
      label.scale,
      label.name
    );
  });
}
