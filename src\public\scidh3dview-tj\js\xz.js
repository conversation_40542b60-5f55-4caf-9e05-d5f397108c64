var xzrecord = "复位";
function recordxz() {
  if (xzrecord) {
    if (xzrecord == "整体场景" || xzrecord == "复位"|| xzrecord == "首页") {
      view.animateCamera(
        {
          x: 38.445209419827535,
          y: 16.35071638132214,
          z: 21.116036236135905,
        },
        { x: 17.23984419316314, y: -1.4487692169220217, z: 5.042548852005354 },
        0
      );
    } else if (xzrecord == "智慧管理" || xzrecord == "智慧管理D3栋空调机房") {
      view.animateCamera(
        { x: -119.06792131597734, y: 91.665553516907, z: 0.9944175324010589 },
        {
          x: -119.07817017642886,
          y: 9.060043887729519,
          z: -0.44617906578022454,
        },
        0
      );
    } else if (
      xzrecord == "配电房" ||
      xzrecord == "冰机房" ||
      xzrecord == "车间空调" ||
      xzrecord == "冰机中控" ||
      xzrecord == "工艺冰水" ||
      xzrecord == "空压站" ||
      xzrecord == "制氮站" ||
      xzrecord == "软水" ||
      xzrecord == "自来供水" ||
      xzrecord == "中央空调" ||
      xzrecord == "电表系统"
    ) {
      view.animateCamera(
        { x: 24.2304923234475, y: 3.692547321660585, z: 11.95085201461945 },
        {
          x: 19.889287288841953,
          y: -0.08686537405454774,
          z: 6.967183957642744,
        },
        0
      );
    }
  }
}
