<template>
  <div class="echart" ref="echart"></div>
</template>
  
<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  methods: {
    initData() { },
    init() {
      const myChart = echarts.init(this.$refs.echart);

 
const option = {
  
    grid: {
		left: '12%',
		top: '5%',
		bottom: '12%',
		right: '8%'
	},
    xAxis: {
        data: ['一楼', '二楼', '三楼', '四楼', '五楼', ],
       axisTick: {
			show: false
		},
		axisLine: {
			lineStyle: {
				color: 'rgba(255, 129, 109,.1)',
				width: 1 //这里是为了突出显示加上的
			}
		},
		axisLabel: {
			textStyle: {
				color: '#fff',
				fontSize: 12
			}
		}
    },
    yAxis: [{
			axisTick: {
				show: false
			},
			axisLine: {
				lineStyle: {
					color: 'rgba(255, 129, 109, 0.1)',
					width: 1 //这里是为了突出显示加上的
				}
			},
			axisLabel: {
				textStyle: {
					color: '#fff'
				}
			},
			splitArea: {
				areaStyle: {
					color: 'rgba(255,255,255,.5)'
				}
			},
			splitLine: {
				show: true,
				lineStyle: {
					color: 'rgba(255, 129, 109, 0.1)',
					width: 0.5,
					type: 'dashed'
				}
			}
		}
	],
    series: [{
        type: 'pictorialBar',
        barCategoryGap: '0%',
        symbol: 'path://M0,10 L10,10 C5.5,10 5.5,5 5,0 C4.5,5 4.5,10 0,10 z',
        label: {
			show: true,
			position: 'top',
			distance: 15,
			color: '#08DFFE',
			fontWeight: 'bolder',
			fontSize: 15,
		},
        itemStyle: {
            normal: {
				color: {
					type: 'linear',
					x: 0,
					y: 0,
					x2: 0,
					y2: 1,
					colorStops: [{
							offset: 0,
							color:'#9A11FF'
						},
						{
							offset: 1,
							color:'#08DFFE'
						}
					],
					global: false //  缺省为  false
				}
			},
			emphasis: {
				opacity: 1
			}
        },
        data: [123, 160, 100, 128, 112,]
    }]
};
      myChart.setOption(option);
    },
  },
};
</script>
  
<style lang="less" scoped>
.echart {
  width: 593px;
  height: 430px;
}
</style>