<template>
  <div class="table-home">
    <div class="box">
      <div class="title">
        <div class="img">{{ selectedItem }}</div>
        <p class="local">位置：{{ selectedItem }}</p>
        <div class="wenzhixuanz">
          <!-- <div class="left">
            <div style="color: #08f9f9">使用中</div>
            /
            <div style="color: #a2abb0">停用</div>
          </div> -->
          <div class="right">
            <div>状态:</div>
            <div class="item">运行</div>
            <!-- <div>规格:</div>
            500*850*2350mm -->
            <!-- @click="switchBackground" -->
          </div>
        </div>
        <!-- this.selectedItem = item; -->
        <img
          class="x"
          src="../../assets/image/table-x.png"
          alt=""
          @click="close()"
        />
      </div>
      <div class="content">
        <div class="iframe">
          <!-- <img class="tupianimg" src="../../assets/tongfeng.png" alt="" /> -->
          <!-- <img class="tupianimg" :src="zengtiimg" alt="" /> -->
          <iframe
            :src="`https://qiye.3dzhanting.cn/share-model.html?ids=TJUYmY66EbVCqi_Ab_12mA==&res=1&isshow=1`"
            frameborder="0"
          ></iframe>
        </div>
        <div class="rigth">
          <div>
            <!-- <div class="qiehuan">
              <div
                v-for="(item, index) in items"
                :key="index"
                :class="{ xuanze: true, selected: selectedIndex === index }"
                @click="toggleSelection(index)"
              >
                {{ item }}
              </div>
            </div> -->

            <div class="biaot" v-for="item in tfdata" :key="item">
              <img src="../../assets/image/table-qiu.png" alt="" />
              <div class="name">{{ item.dmName }}</div>
              <div class="value">{{ item.dVal }}{{ item.dDataUnit }}</div>
            </div>
          </div>
          <hr class="hr" />
          <div class="xiabox">
            <div class="biaotss">
              <img src="../../assets/image/table-qiu.png" alt="" />
              <div class="name">设备状态统计分析</div>

              <el-select
                v-model="selectedMonitor"
                placeholder="请选择监控项"
                @change="handleMonitorChange"
                class="monitor-select"
              >
                <el-option
                  v-for="item in monitorOptions"
                  :key="item.dItemId"
                  :label="item.dmName"
                  :value="item.dmName"
                />
              </el-select>
            </div>
            <echarts1 :chartData="chartData"></echarts1>
          </div>
          <hr class="hr" />

          <!-- <div class="local">
            <span>维护人：王工</span>
            <span class="lianxi">联系方式：173****5896</span>
          </div> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import echarts1 from "@/components/echarts/bingjifang/echarts41.vue";
import { getDeviceData } from "@/api/device";

export default {
  components: {
    echarts1,
  },
  props: ["selectedItem", "ids", "zengtiimg", "tfdata", "tfchart"],

  data() {
    return {
      selectedMonitor: "",
      monitorOptions: [],
      chartData: null,
      dist: false,
      items: ["T1", "T2"],
      backgroundClasses: ["bg-image-1", "bg-image-2", "bg-image-3"],
      backgrounds: [
        { backgroundImage: `url(${require("../../assets/image/image1.jpg")})` },
        { backgroundImage: `url(${require("../../assets/image/image2.jpg")})` },
        { backgroundImage: `url(${require("../../assets/image/image3.jpg")})` },
      ],
      currentIndex: 0,
      selectedIndex: 0,
      inputs: [
        {
          name: "面风速",
          value: "0.46m/s",
          type: "排风量",
          ztaqi: "m³/h",
        },
        {
          name: "排风量",
          value: "910m³/h",
          type: "门状态",
          ztaqi: "已开启",
        },
      ],
    };
  },
  watch: {
    tfdata: {
      immediate: true,
      handler(newData) {
        if (newData && newData.length > 0) {
          this.monitorOptions = newData.filter(
            (item) => item.dmTag === "monitor"
          );
          if (this.monitorOptions.length > 0) {
            this.selectedMonitor = this.monitorOptions[0].dmName;
            this.handleMonitorChange(this.selectedMonitor);
          }
        }
      },
    },
  },
  computed: {
    // Get the current class name
    currentClass() {
      return this.backgroundClasses[this.currentIndex];
    },
    // Get the current background style
    currentBackground() {
      return this.backgrounds[this.currentIndex];
    },
  },
  methods: {
    async handleMonitorChange(selectedName) {
      if (!selectedName) return;

      // 根据选中的名称找到对应的完整对象
      const selected = this.monitorOptions.find(
        (item) => item.dmName === selectedName
      );
      if (!selected) return;

      try {
        const res = await getDeviceData(selected.dmItemId, selected.dmId);
        console.log(res, "rgetDeviceDataes");
        if (res.code === 200 && res.data) {
          console.log(res.data, "res.data");
          const chartData = res.data.data || [];
          const times = chartData.map((item) => item.recordedAt.slice(11, 16));
          const values = chartData.map((item) => item.indication);
          console.log(times, values, "times,values");
          this.chartData = {
            title: {
              text: `${selected.dmName}${
                selected.dDataUnit ? ` (${selected.dDataUnit})` : ""
              }`,
              textStyle: {
                color: "#fff",
                fontSize: 14,
              },
            },
            tooltip: {
              trigger: "axis",
              formatter: function (params) {
                const data = params[0];
                return `${data.name}<br/>${data.seriesName}: ${data.value}${
                  selected.dDataUnit || ""
                }`;
              },
            },

            xAxis: {
              type: "category",
              data: times,
              axisLabel: {
                color: "#fff",
              },
            },
            grid: {
              left: "3%",
              right: "4%",
              bottom: "6%",
              top: "15%",
              containLabel: true,
            },
            yAxis: {
              type: "value",
              axisLabel: {
                color: "#fff",
                formatter: function (value) {
                  return value + (selected.dDataUnit || "");
                },
              },
            },
            series: [
              {
                data: values,
                type: "line",
                smooth: true,
                name: selected.dmName,
                lineStyle: {
                  color: "#2cc1ff",
                },
                itemStyle: {
                  color: "#2cc1ff",
                },
              },
            ],
          };
        }
      } catch (error) {
        console.error("获取图表数据失败:", error);
      }
    },
    switchBackground() {
      this.currentIndex = (this.currentIndex + 1) % this.backgrounds.length;
    },
    toggleSelection(index) {
      if (index == 0) {
        this.inputs = [
          {
            name: "温度",
            value: "4℃",
          },
          // {
          //   name: "分辨率",
          //   value: "0.01℃",
          // },
          // {
          //   name: "精度",
          //   value: "±0.5℃(-100℃~100℃);±1.0℃(其他范围);",
          // },
          {
            name: "门状态",
            value: "关",
          },
        ];
      } else if (index == 1) {
        this.inputs = [
          {
            name: "温度",
            value: "4.1℃",
          },
          // {
          //   name: "分辨率",
          //   value: "0.01℃",
          // },
          // {
          //   name: "精度",
          //   value: "±0.5℃(-100℃~100℃);±1.0℃(其他范围);",
          // },
          {
            name: "门状态",
            value: "关",
          },
        ];
      } else if (index == 2) {
        this.inputs = [];
      } else if (index == 3) {
        this.inputs = [];
      }

      this.selectedIndex = index; // 否则选中当前的
    },
    close() {
      this.$emit("hidedetails");
    },
  },
};
</script>

<style lang="less" scoped>
.wenzhixuanz {
  display: flex;
  align-items: center;

  .left {
    display: flex;
    font-family: Source Han Sans SC;
    font-weight: 400;
    font-size: 17px;
  }

  .right {
    width: 148px;
    margin-left: 35px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-family: Source Han Sans SC;
    font-weight: bold;
    font-size: 15px;
    color: #fff;

    .item {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 90px;
      height: 35px;
      background: url("../../assets/image/lanse.png");
      font-family: Source Han Sans SC;
      font-weight: bold;
      font-size: 15px;
      color: #64dbfa;
      // cursor: pointer;
      display: flex;
      align-items: center;
    }

    .item1 {
      width: 90px;
      height: 35px;
    }
  }
}
.local {
  font-family: Source Han Sans SC;
  font-weight: bold;
  font-size: 17px;
  color: #ffffff;
  // display: flex;
  // justify-content: space-between;
  .lianxi {
    margin-left: 118px;
  }
}

.table-home {
  .box {
    color: #fff;
    background: url("../../assets/image/table-beijing.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 1221px;
    height: 810px;
    padding-top: 34px;

    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-right: 28px;
      margin-left: 31px;

      .img {
        letter-spacing: 0.5px;
        background: url("../../assets/image/title.png");
        background-size: 100% 100%;
        width: 354px;
        height: 34px;
        font-family: Alibaba PuHuiTi;
        font-weight: 500;
        font-style: italic;
        font-size: 18px;
        color: #ffffff;
        text-align: left;
        line-height: 22px;
        padding-left: 38px;
        white-space: nowrap; /* 防止文本换行 */
        overflow: hidden; /* 隐藏超出容器的内容 */
        text-overflow: ellipsis; /* 超出部分显示为省略号 */
        display: flex;
        // padding-top: 10px;

        justify-content: space-between;
      }

      .x {
        cursor: pointer;
      }
    }

    .content {
      display: flex;

      justify-content: space-between;
      margin: 23px 29px 0 67px;

      .iframe {
        // background: url("../../assets/image/iframbeijintu.png");
        // background-repeat: no-repeat;
        // background-size: 100% 100%;
        width: 745px;
        height: 662px;

        iframe {
          width: 100%;
          height: 100%;
        }
      }

      .rigth {
        margin-left: 33px;
        display: flex;
        align-items: center;
        flex-direction: column;
        // justify-content: center;
        // gap: 62px;

        .qiehuan {
          display: flex;
          align-items: center;
          // justify-content: space-between;
          width: 291px;
          height: 57px;
          // margin-left: 15px;
          margin-right: 15px;

          .xuanze {
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            background: url("../../assets/image/xiaoqiehuan.png");
            background-repeat: no-repeat;
            background-size: 100% 100%;
            width: 63px;
            height: 48px;
            font-family: Source Han Sans SC;
            font-weight: 400;
            font-size: 17px;
            color: #ffffff;
          }

          .selected {
            padding-bottom: 8px !important;
            margin-top: 9px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            background: url("../../assets/image/xiaoxuanzhong.png");
            background-repeat: no-repeat;
            background-size: 100% 100%;
            width: 63px;
            height: 57px;
            font-family: Source Han Sans SC;
            font-weight: 400;
            font-size: 17px;
            color: #ffffff;
          }
        }

        .biaot {
          width: 607px;
          height: 47px;
          margin-top: 12px;
          display: flex;
          align-items: center;

          .name {
            width: 125px;
            font-family: "Roboto", sans-serif;

            font-weight: bold;
            font-size: 17px;
            color: #b1f2f2;
            margin-left: 6px;
          }

          .value {
            background: url("../../assets/image/tableinptubox.png");
            background-repeat: no-repeat;
            background-size: 100% 100%;
            // 418px x 47px
            width: 418px;
            height: 47px;
            font-family: "Roboto", sans-serif;
            font-weight: 400;
            font-size: 17px;
            color: #ffffff;
            line-height: 47px;
            padding-left: 22px;
            margin-left: 24px;
          }
        }
      }

      .hr {
        margin-top: 24px;
        margin-bottom: 25px;
        width: 100%;
        background-color: rgba(36, 101, 138, 1);
        color: rgba(36, 101, 138, 1);
      }
    }

    .biaotss {
      height: 47px;
      margin-top: 12px;
      display: flex;
      align-items: center;

      .name {
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 17px;
        color: #b1f2f2;
        margin-left: 6px;
      }
    }
  }
}
.xiabox {
  width: 100% !important;
}
.bg-image-1 {
  transition: background 0.3s ease-in-out;
}

.bg-image-2 {
  transition: background 0.3s ease-in-out;
}

.bg-image-3 {
  transition: background 0.3s ease-in-out;
}
.tupianimg {
  width: 100%;
  height: 100%;
}

.monitor-select {
  width: 200px;
  margin-left: 20px;

  :deep(.el-input__wrapper) {
    background-color: transparent;
    box-shadow: none;
    border: 1px solid #134b7e;
  }

  :deep(.el-input__inner) {
    color: #fff;
    background: transparent;
  }
}

.xiabox {
  width: 100% !important;

  .biaotss {
    display: flex;
    align-items: center;
  }
}
</style>
