<template>
  <div class="table-home" v-if="isshow">
    <div class="box">
      <div class="title">
        <div class="img">异常跟踪处理</div>
        <div class="filter-select">
          <el-select
            v-model="warningStatus"
            placeholder="请选择状态"
            @change="handleStatusChange"
          >
            <el-option label="未修复" value="N"></el-option>
            <el-option label="已修复" value="Y"></el-option>
          </el-select>
        </div>
        <img
          class="x"
          src="../../assets/image/table-x.png"
          alt=""
          @click="close"
        />
      </div>
      <div class="content1">
        <div class="warning-list" v-loading="loading">
          <div class="table-header">
            <div class="col">设备名称</div>
            <div class="col">报警类型</div>
            <div class="col">报警详情</div>
            <div class="col">报警时间</div>
            <div class="col">修复时间</div>
            <div class="col">状态</div>
          </div>
          <div class="table-body">
            <div
              v-for="warning in warningList"
              :key="warning.id"
              class="warning-item"
            >
              <div class="col" :title="warning.deviceName">
                {{ warning.deviceName }}
              </div>
              <div class="col" :title="warning.warningCategory">
                {{ warning.warningCategory }}
              </div>
              <div class="col" :title="warning.errMsg">
                {{ warning.errMsg }}
              </div>
              <div class="col">{{ warning.createdAt }}</div>
              <div class="col">
                {{ warning.updatedAt ? warning.updatedAt : "-" }}
              </div>
              <div class="col">
                <span
                  :class="
                    warning.hasFixed === 'Y' ? 'status-fixed' : 'status-unfixed'
                  "
                >
                  {{ warning.hasFixed === "Y" ? "已修复" : "未修复" }}
                </span>
              </div>
            </div>
          </div>
          <div v-if="!warningList.length" class="empty-message">暂无数据</div>
        </div>
        <div class="table-footer">
          <div class="total-info">共{{ total }}条</div>
          <div class="page-select">
            <el-select
              v-model="pageSize"
              @change="handleSizeChange"
              class="page-select"
              popper-class="blue-select"
            >
              <el-option
                v-for="size in [10, 20, 50, 100]"
                :key="size"
                :label="`${size}条/页`"
                :value="size"
              />
            </el-select>
          </div>
          <el-pagination
            :current-page="currentPage"
            :page-size="pageSize"
            :total="total"
            :pager-count="7"
            layout="prev, pager, next"
            @current-change="handleCurrentChange"
            background
            class="custom-pagination"
          />
          <div class="page-jump">
            前往<el-input
              v-model.number="currentPage"
              @change="handleCurrentChange"
              class="page-input"
            />页
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getDeviceWarningList } from "@/api/device.js";

export default {
  data() {
    return {
      isshow: true,
      loading: false,
      warningStatus: "N",
      warningList: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
    };
  },
  methods: {
    close() {
      this.$emit("close");
    },
    async fetchWarningList() {
      this.loading = true;
      try {
        const response = await getDeviceWarningList({
          hasFixed: this.warningStatus,
          pageNum: this.currentPage,
          pageSize: this.pageSize,
        });
        if (response.code === 200) {
          console.log(response, "response");

          this.warningList = response.rows;
          this.total = response.total;
        }
      } catch (error) {
        console.error("获取警告列表失败:", error);
      } finally {
        this.loading = false;
      }
    },
    handleStatusChange() {
      this.currentPage = 1;
      this.fetchWarningList();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.fetchWarningList();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.fetchWarningList();
    },
    formatDate(timestamp) {
      const date = new Date(timestamp);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
        2,
        "0"
      )}-${String(date.getDate()).padStart(2, "0")} ${String(
        date.getHours()
      ).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
    },
  },
  created() {
    this.fetchWarningList();
  },
};
</script>

<style lang="less" scoped>
.table-home {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.3);

  .box {
    color: #fff;
    background-color: #1a2234;
    width: 1020px;
    height: 680px;
    border: 1px solid #3ba1f4;
    border-radius: 10px;

    .title {
      height: 55px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 15px;
      border-bottom: 1px solid #3ba1f4;

      .img {
        font-size: 20px;
        color: #3ba1f4;
      }

      .filter-select {
        margin-right: auto;
        margin-left: 20px;
        width: 120px;
      }

      .x {
        cursor: pointer;
        width: 14px;
        height: 14px;
        &:hover {
          opacity: 0.8;
        }
      }
    }
  }
}

.content1 {
  height: calc(100% - 41px);
  padding: 5px 15px 15px 15px;

  .warning-list {
    height: calc(100% - 50px);
    background: #1a2234;
    overflow-y: auto;

    .table-header {
      display: flex;
      height: 45px;
      line-height: 45px;
      background: #1a2234;
      border-bottom: 1px solid #3ba1f4;
      position: sticky;
      top: 0;
      z-index: 1;

      .col {
        flex: 1;
        color: #3ba1f4;
        font-size: 18px;
        text-align: center;
      }
    }

    .table-body {
      .warning-item {
        display: flex;
        height: 50px;
        line-height: 50px;
        border-bottom: 1px solid rgba(59, 161, 244, 0.3);

        &:nth-child(odd) {
          background: rgba(59, 161, 244, 0.05);
        }

        &:hover {
          background: rgba(59, 161, 244, 0.1);
        }

        .col {
          flex: 1;
          text-align: center;
          color: #fff;
          font-size: 14px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          padding: 0 5px;
        }

        .status-fixed {
          color: #64f8bb;
        }

        .status-unfixed {
          color: #b93851;
        }
      }
    }
  }
}

.table-footer {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0 16px;
  background: #1a2234;
  color: #fff;
  font-size: 16px;

  .total-info {
    margin-right: 16px;
  }

  .page-select {
    margin-right: 16px;
    width: 90px;
  }

  .page-jump {
    display: flex;
    align-items: center;
    margin-left: 16px;

    .page-input {
      width: 32px;
      margin: 0 4px;
      text-align: center;
      // /deep/ .el-input__inner {
      //   height: 24px;
      //   line-height: 24px;
      //   padding: 0 4px;
      //   text-align: center;
      //   background: #1a2234;
      //   border: 1px solid #3ba1f4;
      //   color: #fff;
      //   border-radius: 2px;
      //   font-size: 12px;
      // }
    }
  }
}

:deep(.el-pagination) {
  .el-pagination__total,
  .el-pagination__sizes,
  .el-pagination__jump {
    color: #fff !important;
  }

  &.is-background {
    .btn-prev,
    .btn-next,
    .el-pager li {
      background-color: rgba(25, 37, 60, 0.8) !important;
      color: #fff !important;
      border: 1px solid #3ba1f4;
      margin: 0 3px;

      &:hover {
        color: #409eff !important;
        background-color: rgba(37, 50, 69, 0.4) !important;
      }

      &.is-active {
        background-color: #409eff !important;
        color: #fff !important;
        border-color: #409eff;
      }

      &:disabled {
        background-color: rgba(25, 37, 60, 0.4) !important;
        color: #606266 !important;
      }
    }
  }

  // .el-input__inner {
  //   background-color: rgba(25, 37, 60, 0.8) !important;
  //   border: 1px solid #3ba1f4 !important;
  //   color: #fff !important;
  // }
}

// :deep(.el-select) {
//   .el-input__inner {
//     height: 24px;
//     line-height: 24px;
//     padding: 0 8px;
//     background: #1a2234;
//     border: 1px solid #3ba1f4;
//     color: #fff;
//     border-radius: 2px;
//     font-size: 12px;
//   }
// }

// :deep(.el-pagination) {
//   .btn-prev,
//   .btn-next,
//   .el-pager li {
//     background: transparent;
//     color: #fff;
//     border: 1px solid #3ba1f4;
//     margin: 0 4px;
//     min-width: 24px;
//     height: 24px;
//     line-height: 22px;
//     border-radius: 2px;
//     font-weight: normal;
//     font-size: 12px;
//     padding: 0;

//     &:not(.disabled):hover {
//       color: #fff;
//       background: #3ba1f4;
//       border-color: #3ba1f4;
//     }

//     &.disabled {
//       background: transparent;
//       border-color: rgba(59, 161, 244, 0.3);
//       color: rgba(255, 255, 255, 0.5);
//     }
//   }

//   .el-pager li.active {
//     background: transparent;
//     color: #fff;
//     border-color: #3ba1f4;
//   }

//   .btn-prev {
//     margin-right: 4px;
//   }

//   .btn-next {
//     margin-left: 4px;
//   }
// }

// :deep(.el-select-dropdown) {
//   background-color: #192338;
//   border: 1px solid #3ba1f4;

//   .el-select-dropdown__item {
//     color: #fff;
//     font-size: 12px;
//     height: 32px;
//     line-height: 32px;

//     &:hover, &.selected {
//       background-color: #3ba1f4;
//       color: #fff;
//     }
//   }
// }

.empty-message {
  text-align: center;
  padding: 40px;
  color: #fff;
  font-size: 14px;
}
</style>
