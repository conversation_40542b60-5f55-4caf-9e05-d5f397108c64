<template>
  <div class="echart" ref="echart"></div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  props: {
    chartData: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {};
  },

  mounted() {
    this.init();
  },
  watch: {
    chartData: {
      handler(newData) {
        // 数据变化时更新 ECharts
        this.init();
      },
      deep: true, // 深度监听对象内部数据
    },
  },
  methods: {
    initData() {},
    init() {
      console.log(this.chartData, 123);

      const myChart = echarts.init(this.$refs.echart);
      //   let xData = ["本年话务总量", "本年人工话务量", "每万客户呼入量"];
      //   let yData = [0, 1230, 425];
      //图形路径
      let xData = this.chartData.map((item) => item.name);
      let yData = this.chartData.map((item) => item.value);
      //顶部框数值

      const option = {
        // backgroundColor: "#0e202d",
        title: {
        //   text: "第三采油厂",
        //   subtext: "总数: 599",
          textStyle: {
            color: "#fff",
            fontSize: 20,
          },
          subtextStyle: {
            color: "#999",
            fontSize: 16,
          },
          x: "center",
          top: "0%",
        },
        grid: {
          top: 100,
          bottom: 150,
        },
        tooltip: {},
        xAxis: {
          data: xData,
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            interval: 0,
            textStyle: {
              color: "#beceff",
              fontSize: 20,
            },
            margin: 80, //刻度标签与轴线之间的距离。
          },
        },
        yAxis: {
          splitLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            show: false,
          },
        },
        series: [
    {
      name: "",
      type: "pictorialBar",  // 确保指定类型
      symbolSize: [100, 45],
      symbolOffset: [0, -20],
      z: 12,
      data: yData.map(value => ({
        value: value,
        trueVal: value,
        symbolPosition: "end",
        itemStyle: {
          normal: {
            color: "#00fff5",
          }
        }
      }))
    },
    {
      name: "",
      type: "pictorialBar",  // 确保指定类型
      symbolSize: [100, 45],
      symbolOffset: [0, 24],
      z: 12,
      data: yData.map(value => ({
        value: value,
        trueVal: value,
        itemStyle: {
          normal: {
            color: "#43bafe",
          }
        }
      }))
    },
    {
      name: "",
      type: "pictorialBar",  // 确保指定类型
      symbolSize: [150, 75],
      symbolOffset: [0, 44],
      z: 11,
      data: yData.map(value => ({
        value: value,
        trueVal: value,
        itemStyle: {
          normal: {
            color: "transparent",
            borderColor: "#43bafe",
            borderWidth: 5,
          }
        }
      }))
    },
    {
      name: "",
      type: "pictorialBar",  // 确保指定类型
      symbolSize: [200, 100],
      symbolOffset: [0, 62],
      z: 10,
      data: yData.map(value => ({
        value: value,
        trueVal: value,
        itemStyle: {
          normal: {
            color: "transparent",
            borderColor: "#43bafe",
            borderType: "dashed",
            borderWidth: 5,
          }
        }
      }))
    },
    {
      name: "",
      type: "bar",  // 确保指定类型
      silent: true,
      barWidth: 100,
      barGap: "-100%",
      data: yData.map(value => ({
        value: value,
        trueVal: value,
        label: {
          normal: {
            show: true,
            position: "top",
            distance: 40,
            textStyle: {
              color: "#00fff5",
              fontSize: 40,
            }
          }
        },
        itemStyle: {
          normal: {
            color: {
              type: "linear",
              x: 0, y: 0, x2: 0, y2: 1,
              colorStops: [{
                offset: 0,
                color: "rgba(0,255,245,0.5)"
              }, {
                offset: 1,
                color: "#43bafe"
              }]
            }
          }
        }
      }))
    }
  ]
      };
      myChart.setOption(option);
    },
  },
};
</script>

<style lang="less" scoped>
.echart {
  margin-top: 15px;

  width: 667px;
  height: 730px;
}
</style>
