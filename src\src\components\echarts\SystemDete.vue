<template>
  <div class="echart" ref="echart"></div>
</template>

<script>
import * as echarts from "echarts";
import { mapGetters } from 'vuex';

export default {
  name: "IoTequip",
  props: {
    equipmentStatus: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    ...mapGetters({
      yiqiStatus: 'equipment/yiqiStatus'
    })
  },
  data() {
    return {
      chart: null
    };
  },
  watch: {
    yiqiStatus: {
      handler() {
        this.updateChart();
      },
      deep: true
    },
    equipmentStatus: {
      handler() {
        this.updateChart();
      },
      deep: true
    }
  },
  mounted() {
    this.initChart();
    this.updateChart();
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.echart);
    },
    updateChart() {
      if (!this.chart) return;

      // 过滤掉故障状态，只保留正在使用和待机中
      const filteredData = this.yiqiStatus && this.yiqiStatus.length ?
        this.yiqiStatus.filter(item => item.name !== "故障") : [];

      const data = filteredData.length ? filteredData : [
        { name: "正在使用", value: 0 },
        { name: "待机中", value: 0 },
      ];

      const colors = [
        "37, 171, 200",
        "214, 128, 120",
        "252, 182, 53",
        "47, 255, 242",
        "42, 191, 191"
      ];

      const option = {
        legend: {
          top: "10",
          right: "18%",
          data: data.map((it) => it.name),
          textStyle: {
            color: "#fff",
            fontSize: 16,
            fontFamily: "Alibaba PuHuiTi",
          },
          itemWidth: 13,
          itemHeight: 13,
        },
        tooltip: {
          trigger: "item",
          formatter: "{a} <br/>{b} : {c} ({d}%)",
          textStyle: {
            fontSize: 15,
          },
        },
        series: [
          {
            name: "仪器状态",
            type: "pie",
            radius: ["30%", "80%"],
            center: ["50%", "60%"],
            roseType: "radius",
            label: {
              show: true,
              normal: {
                position: "outside",
                fontSize: 18,
                formatter: "{d}%",
                color: "#fff",
              },
            },
            labelLine: {
              length: 2,
              length2: 7,
            },
            data: data.map((it, i) => {
              return {
                value: it.value,
                name: it.name,
                itemStyle: {
                  color: `rgba(${colors[i]},0.7)`,
                  borderColor: `rgba(${colors[i]},1)`,
                  borderWidth: 1,
                },
              };
            }),
          },
        ],
      };

      this.chart.setOption(option);
    },
  },
};
</script>

<style lang="less" scoped>
.echart {
  width: 100%;
  height: 100%;
}

@media (max-height: 1080px) {
  .echart {
    width: 100%;
    height: 100% !important;
  }
}
</style>