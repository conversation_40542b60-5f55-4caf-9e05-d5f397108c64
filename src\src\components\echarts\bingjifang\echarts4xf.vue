<template>
  <div class="echart" ref="echart"></div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  data() {
    return {
      chart: null,
      timer: null
    };
  },

  mounted() {
    this.init();
    // 每分钟更新一次数据
    this.timer = setInterval(() => {
      this.updateData();
    }, 60000);
  },

  beforeUnmount() {
    // 组件销毁前清除定时器
    if (this.timer) {
      clearInterval(this.timer);
    }
    // 销毁图表实例
    if (this.chart) {
      this.chart.dispose();
    }
  },

  methods: {
    generateTimeData() {
      const now = new Date();
      const hours = now.getHours();
      const timeData = [];
      
      // 生成从0点到当前小时的时间数据
      for (let i = 0; i <= hours; i++) {
        timeData.push(`${i.toString().padStart(2, '0')}:00`);
      }
      return timeData;
    },

    generateSpeedData(length) {
      return Array.from({ length }, () => 
        +(500 + Math.random() * 5).toFixed(2)
      );
    },

    updateData() {
      const timeData = this.generateTimeData();
      const speedData = this.generateSpeedData(timeData.length);
      
      this.chart.setOption({
        xAxis: {
          data: timeData
        },
        series: [{
          data: speedData
        }]
      });
    },

    init() {
      this.chart = echarts.init(this.$refs.echart);
      const timeData = this.generateTimeData();
      const speedData = this.generateSpeedData(timeData.length);

      const option = {
        title: {
          text: "面风速m³/h",
          x: "3%",
          y: "0%",
          textStyle: {
            color: "#fff",
            fontSize: 12,
          },
        },
        legend: {
          data: ["面风速"],
          textStyle: {
            color: "#ffff",
            fontSize: 14,
          },
        },
        tooltip: {
          trigger: 'axis',
          formatter: '{b}<br />{a}: {c} m/s'
        },
        grid: {
          top: "14%",
          bottom: "16%",
          left: "5%",
          right: "6%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            margin: 10,
            textStyle: {
              fontSize: 12,
              color: "#fff",
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "#939ab6",
              opacity: 0.15,
            },
          },
          data: timeData,
        },
        yAxis: {
          type: "value",
          min: 500,
          max: 505,
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            margin: 10,
            textStyle: {
              fontSize: 12,
              color: "#fff",
            },
          },
          splitLine: {
            show: false,
          },
        },
        series: [
          {
            name: "面风速",
            type: "line",
            z: 3,
            showSymbol: false,
            smoothMonotone: "x",
            lineStyle: {
              width: 3,
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(59,102,246)",
                  },
                  {
                    offset: 1,
                    color: "rgba(118,237,252)",
                  },
                ],
              },
            },
            data: speedData,
          },
        ],
      };

      this.chart.setOption(option);
      
      // 添加窗口大小改变时的自适应
      window.addEventListener('resize', () => {
        this.chart.resize();
      });
    },
  },
};
</script>

<style lang="less" scoped>
.echart {
  width: 630px !important;
  height: 280px;
}
</style>
