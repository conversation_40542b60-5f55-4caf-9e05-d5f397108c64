<template>
  <div class="echart" ref="echart"></div>
</template>
  
<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",

  props: ["echartData1"],
  watch: {
    echartData1(newVal) {
      console.log(newVal, 111);
      this.init();
    },
  },
  data() {
    return {};
  },

  mounted() {
    this.init();
    console.log(this.echartData1, "111");
  },

  methods: {
    initData() {},
    init() {
      const myChart = echarts.init(this.$refs.echart);

      let datalist = this.echartData1.datalist;
      let linksData = this.echartData1.linksData;
      let Color = [
        "#61FEFF",
        "#61FEFF",
        "#937FE6",
        "#2B56D3",
        "#87E7AA",
        "#937FE6",
        "#FF9B97",
        "#8f23f5",
        "#0576ea",
        "#2cb8cf",
        "#8A7EE0",
        "#2cb8cf",
        "#4e70f0",
        "#1fa3de",
        "#bbc951",
        "#FFC14B",
        "#1fa3de",
        "#b785a6",
        "#1fa3de",
      ];
      let Color1 = [
        "#04E0F3",
        "#04E0F3",
        "#682EFC",
        "#35A7FE",
        "#0DC09F",
        "#682EFC",
        "#ED6663",
        "#8f23f5",
        "#0576ea",
        "#2cb8cf",
        "#8A7EE0",
        "#2cb8cf",
        "#4e70f0",
        "#1fa3de",
        "#bbc951",
        "#FFC14B",
        "#b785a6",
        "#1fa3de",
        "#1fa3de",
      ];
      let sourceLabel = [
        "right",
        "left",
        "left",
        "left",
        "left",
        "left",
        "left",
        "left",
        "left",
        "left",
        "left",
        "left",
        "left",
        "left",
        "left",
        "left",
        "left",
        "left",
        "left",
        "left",
        "left",
      ];
      let itemStyleColor = [];
      let labelSource = [];
      for (let i = 0; i < datalist.length; i++) {
        datalist[i].label = {
          normal: {
            position: sourceLabel[i],
          },
        };
        labelSource.push(sourceLabel[i]);
      }
      for (let d = 0; d < datalist.length; d++) {
        datalist[d].itemStyle = {
          normal: {
            // color: Color[d]
            color: {
              type: "linear",
              x: 0,
              y: 1,
              x2: 0,
              y2: 0,
              colorStops: [
                {
                  offset: 1,
                  color: Color[d], // 0% 处的颜色
                },
                {
                  offset: 0,
                  color: Color1[d], // 100% 处的颜色
                },
              ],
              global: false, // 缺省为 false
            },
          },
        };
        itemStyleColor.push(datalist[d]);
      }
      const option = {
        tooltip: {
          trigger: "item",
          triggerOn: "mousemove",
          formatter: function (params) {
            if (params.name == "公共平台") {
              return " 公共平台 ";
            } else {
              let value = params.data.value;
              if (!value && value !== 0) return 0;
              let str = value.toString();
              let reg =
                str.indexOf(".") > -1
                  ? /(\d)(?=(\d{3})+\.)/g
                  : /(\d)(?=(?:\d{3})+$)/g;
              if (params.data.source == "公共平台") {
                return params.data.target + " : " + str.replace(reg, "$1,");
              } else {
                return params.data.source + " : " + str.replace(reg, "$1,");
              }
            }
          },
        },
        series: [
          {
            type: "sankey",
            layout: "none",
            top: "4%",
            bottom: "12%",
            left: "20",
            right: "20",
            nodeGap: 15,
            nodeWidth: 25,
            focusNodeAdjacency: "allEdges",
            data: itemStyleColor,
            links: linksData,
            label: {
              normal: {
                color: "#fff",
                fontSize: 14,
                formatter: function (params) {
                  if (params.data.name == "公共平台") {
                    let strs = params.data.name.split(""); //字符串数组
                    let str = "";
                    for (let i = 0, s; (s = strs[i++]); ) {
                      //遍历字符串数组
                      str += s;
                      if (!(i % 1)) str += "\n"; //按需要求余
                    }
                    return "{white|" + str + "}";
                  } else {
                    return params.data.name;
                  }
                },
                rich: {
                  white: {
                    fontSize: 16,
                    lineHeight: 30,
                    padding: [0, 0, 0, -26],
                  },
                },
              },
            },
            lineStyle: {
              normal: {
                opacity: 0.4,
                color: "source",
                curveness: 0.5,
              },
            },
            itemStyle: {
              normal: {
                borderWidth: 1,
                borderColor: "transparent",
              },
            },
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>
  
<style lang="less" scoped>
.echart {
  width: 100%;
  height: 100%;
}

@media (max-height: 1080px) {
  .echart {
    width: 100%;
    height: 100% !important;
  }
}
</style>