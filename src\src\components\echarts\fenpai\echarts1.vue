<template>
  <div class="echart" ref="echart"></div>
</template>
    
  <script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  methods: {
    initData() {},
    init() {
      const myChart = echarts.init(this.$refs.echart);

      // app.title = "未优化用电量";

      const option = {
        //     legend: {
        //   textStyle: {
        //                 color:"#fff",
        //                 fontSize:14
        //             },
        //         top: '1%',
        //     right:"1%",
        //         data: ['未优化用电量', 'AI优化用电量'],
        //     },

        color: ["#3398DB"],
        title: {
          text: "个",
          x: "6%",
          y: "2%",
          textStyle: {
            color: "#fff",
            fontSize: 10,
          },
        },
        legend: {
          itemGap: 16,
          itemWidth: 10,
          itemHeight: 10,
          top: 0,
          right: "1%",
          textStyle: { fontSize: 10, color: "#fff" },
          data: ["设备总数", "运行总数"],
        },
        grid: {
          top: "18%",
          bottom: "0%",
          left: "6%",
          right: "6%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: ["风机1", "风机2", "风机3", "风机4"],

            axisLabel: {
              show: true,
              textStyle: {
                color: "#fff", // 将 X 轴标签字体颜色设置为白色
                fontSize: 13,
              },
            },
          },
        ],
        yAxis: [
          {
            axisTick: {
              alignWithLabel: true,
            },

            axisLabel: {
              show: true,
              textStyle: {
                color: "#fff", // 将 Y 轴标签字体颜色设置为白色
              },
            },
          },
        ],

        series: [
          {
            name: "设备总数",
            type: "bar",
            barWidth: "20%",
            data: [380, 380, 380, 380, 380, 380],
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#3F84BD",
                  },
                  {
                    offset: 1,
                    color: "#183049",
                  },
                ]),
                shadowColor: "rgba(0, 0, 0, 0.1)",
                shadowBlur: 10,
              },
            },
          },
          {
            name: "运行总数",
            type: "bar",
            barWidth: "20%",
            data: [240, 240, 240, 240, 240, 240],
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#77FDCD",
                  },
                  {
                    offset: 1,
                    color: "#173047",
                  },
                ]),
                shadowColor: "rgba(0, 0, 0, 0.1)",
                shadowBlur: 10,
              },
            },
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>
    
  <style lang="less" scoped>
.echart {
  width: 355px;
  height: 189px;
}

@media (max-height: 1080px) {
  .echart {
    width: 355px;
    height: 189px !important;
  }
}
</style>