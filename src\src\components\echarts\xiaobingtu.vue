<template>
  <div class="echart" ref="echart"></div>
</template>
    
  <script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  methods: {
    initData() {},
    init() {
      const myChart = echarts.init(this.$refs.echart);
      /**
       * 图标所需数据
       */
      var data = {
        id: "echartPie",
        value: [12, 32, 54, 12, 12, 12, 12, 12, 12, 12, 12],
        legend: [
          "一楼用电",
          "二楼用电",
          "三楼用电",
          "四楼用电",
          "五楼用电",
          "暖通风设备用电",
        ],

        // tooltipShow:false,    //设置悬浮提示显示              --默认显示true
        // hoverAnimation:false, //设置鼠标悬浮点击饼图动画效果  --默认开启动画true
        // title: "饼图",
      };

      ////////////////////////////////////////

      /**
       * 数据处理
       */
      var seriesData = [];
      data.value.forEach(function (item, index) {
        seriesData.push({
          value: item,
          name: data.legend[index],
        });
      });
      ////////////////////////////////////////

      var option = {
        title: {
          x: "2%",
          y: "2%",
          textStyle: {
            fontWeight: 400,
            fontSize: 16,
            color: "#fff",
          },
          text: data.title || "",
        },
        tooltip: {
          trigger: "item",
          show: data.tooltipShow === false ? false : true,
          //   formatter: "{b}: {c} ({d}%)"
        },
        legend: {
          right: "13%", // 水平居中
          top: "17%", // 水平居中

          orient: "vertical",
          top: 25,
          icon: "circle",
          selectedMode: false,
          itemWidth: 10,
          itemHeight: 10,
          itemGap: 23,
          borderRadius: 6,
          data: data.legend,
          textStyle: {
            color: "#fff",
            fontSize: 16,
          },
        },
        series: [
          {
            type: "pie",
            // clickable:false,
            // selectedMode: 'single',//单点击设置
            hoverAnimation: data.hoverAnimation === false ? false : true,
            radius: ["40%", "70%"],
            center: ["27%", "50%"],
            color: data.color,
            label: {
              normal: {
                position: "inner",
                // formatter: '{d}%',
                formatter: function (param) {
                  if (!param.percent) return "";
                  var f = Math.round(param.percent * 10) / 10;
                  var s = f.toString();
                  var rs = s.indexOf(".");
                  if (rs < 0) {
                    rs = s.length;
                    s += ".";
                  }
                  while (s.length <= rs + 1) {
                    s += "0";
                  }
                  return s + "%";
                },
                textStyle: {
                  color: "#fff",
                  fontSize: 10,
                },
              },
            },
            labelLine: {
              normal: {
                show: false,
              },
            },
            data: seriesData,
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>
    
  <style lang="less" scoped>
.echart {
  width: 100%;
  height: 260px;
}
</style>