<template>
  <div class="echart" ref="echart"></div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  methods: {
    initData() {},
    init() {
      const myChart = echarts.init(this.$refs.echart);

      const option = {
        color: ["#3398DB"],
        title: {
          text: "个",
          x: "6%",
          y: "2%",
          textStyle: {
            color: "#fff",
            fontSize: 16,
          },
        },
        legend: {
          data: ["历史故障数", "历史故障数"],
          top: "8%",
          right: "40px",
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            color: "#fff",
            fontSize: 16,
          },
        },
        grid: {
          top: "18%",
          bottom: "6%",
          left: "6%",
          right: "6%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: [
              "2/16", "2/17", "2/18", "2/19", "2/20", 
              "2/21", "2/22", "2/23", "2/24", "2/25", "2/26"
            ],
            axisLabel: {
              show: true,
              textStyle: {
                fontSize: 18,
                color: "#fff",
              },
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            axisTick: {
              alignWithLabel: true,
            },
            axisLabel: {
              show: true,
              textStyle: {
                fontSize: 18,
                color: "#fff",
              },
            },
            splitLine: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: "历史故障数",
            type: "bar",
            barWidth: "30%",
            data: [4, 2, 2, 1, 2, 1, 1, 1, 1, 1, 1],
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: "#66C4FC" },
                  { offset: 1, color: "#66C4FC" },
                ]),
                shadowColor: "rgba(0, 0, 0, 0.1)",
                shadowBlur: 10,
              },
            },
          },
          {
            name: "历史故障数",
            type: "line",
            smooth: true,
            data: [4, 2, 2, 1, 2, 1, 1, 1, 1, 1, 1],
            lineStyle: {
              width: 2,
              color: "#FF5733",
            },
            itemStyle: {
              color: "#FF5733",
            },
            symbol: "circle", // 数据点样式
            symbolSize: 8,
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>

<style lang="less" scoped>
.echart {
  width: 98%;
  margin-top: 20px;
  height: 340px;
}
</style>
