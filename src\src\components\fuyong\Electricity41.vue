<template>
  <div class="echart" ref="echart"></div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  methods: {
    // 获取最近7天的日期
    getLast7Days() {
      const dates = [];
      const today = new Date();

      // 往前推7天，包含今天
      for (let i = 6; i >= 0; i--) {
        const date = new Date(today);
        date.setDate(today.getDate() - i);

        // 格式化日期为 MM.DD
        const formattedDate = `${(date.getMonth() + 1).toString().padStart(2, '0')}.${date.getDate().toString().padStart(2, '0')}`;
        dates.push(formattedDate);
      }

      return dates;
    },

    initData() { },
    init() {
      const myChart = echarts.init(this.$refs.echart);
      const last7Days = this.getLast7Days(); // 获取最近7天的日期
      const option = {
        color: ["#3398DB"],
        title: {
          text: "个",
          x: "6%",
          y: "2%",
          textStyle: {
            color: "#fff",
            fontSize: 16,
          },
        },
        legend: {
          data: ["历史故障数", "历史故障数"],
          top: "8%",
          right: "40px",
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            color: "#fff",
            fontSize: 16,
          },
        },
        grid: {
          top: "18%",
          bottom: "6%",
          left: "6%",
          right: "6%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: last7Days,
            axisLabel: {
              show: true,
              textStyle: {
                fontSize: 18,
                color: "#fff",
              },
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            axisTick: {
              alignWithLabel: true,
            },
            axisLabel: {
              show: true,
              textStyle: {
                fontSize: 18,
                color: "#fff",
              },
            },
            splitLine: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: "历史故障数",
            type: "bar",
            barWidth: "30%",
            data: [0, 0, 0, 0, 0,0,0,0,0,0,0,],
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: "#FFA07A" },
                  { offset: 1, color: "#FFA07A" },
                ]),
                shadowColor: "rgba(0, 0, 0, 0.1)",
                shadowBlur: 10,
              },
            },
          },
          {
            name: "历史故障数",
            type: "line",
            smooth: true,
            data:  [0, 0, 0, 0, 0,0,0,0,0,0,0,],
            lineStyle: {
              width: 2,
              color: "#FF8C00",
            },
            itemStyle: {
              color: "#FF8C00",
            },
            symbol: "circle", // 数据点样式
            symbolSize: 8,
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>

<style lang="less" scoped>
.echart {
  width: 100%;
  margin-top: 20px;
  height: 340px;
}
</style>
