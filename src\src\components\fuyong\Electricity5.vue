<template>
  <div class="echart" ref="echart"></div>
</template>
  
<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  props: ["echartDatas"],
  data() {
    return {};
  },

  mounted() {
    this.init();
  },
  watch: {
    echartDatas: {
      handler(newData) {
        // 数据变化时更新 ECharts
        this.init();
      },
      deep: true // 深度监听对象内部数据
    }

  },
  methods: {
    getLast7Days() {
      // 获取当前时间的小时
      const currentHour = new Date().getHours();
      // 生成小时数组
      const hourArray = Array.from({ length: currentHour + 1 }, (_, i) => i);
      return hourArray;
    },
    initData() { },
    init() {
      const myChart = echarts.init(this.$refs.echart);
      const last7Days = this.getLast7Days(); // 获取最近7天的日期
      // app.title = "未优化用电量";
      let yAxisData = this.echartDatas.data1;
      var data1 = this.echartDatas.data2;
      const option = {
        color: [this.echartDatas.color, "#FFFF77", "#FF5511", "#FFD52E"],

        tooltip: {
          trigger: "axis",
          formatter: (params) => {
            let content = `${params[0].axisValueLabel}<br/>`;
            params.forEach((item) => {
              content += `${item.seriesName}: ${item.data} ${this.echartDatas.unit || ""}<br/>`;
            });
            return content;
          },
        },

        grid: {
          //图表的位置
          top: "20%",
          left: "3%",
          right: "4%",
          bottom: "5%",
          containLabel: true,
        },
        yAxis: [
          {
            axisLine: {
              show: true,
              lineStyle: {
                color: "#F3F4F4",
              },
            },
            type: "value",
            name: this.echartDatas.unit || "", // 设置单位名称
            nameTextStyle: {
              color: "#fff", // 设置单位文字的颜色
              fontSize: 16,  // 设置单位文字的大小
              align: "center", // 水平居中
              padding: [0, 0, 10, 0], // 调整与轴线的距离
            },
            axisLabel: {
              show: true,
              interval: "auto",
              formatter: "{value}",
              textStyle: {
                color: "#fff",
                fontSize: 16,
              },
            },
            splitLine: {
              show: false,
              lineStyle: {
                type: "dashed",
              },
            },
            show: true,
          },
        ],

        xAxis: [

          {
            axisLine: {
              show: true,
              lineStyle: {
                color: "#F3F4F4",
              },
            },
            type: "category",
            axisLabel: {
              interval: "auto",
              show: true,
              splitNumber: 15,
              textStyle: {
                fontSize: 16,
                color: "#fff",
              },
            },
            data: yAxisData,
          },
        ],
        series: [
          {
            name: "正常",
            type: "bar",
            stack: "sum",
            barWidth: "20px",
            data: data1,
          },


        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>
  
<style lang="less" scoped>
.echart {
  width: 98%;
  height: 351px;
}

@media (max-height: 1080px) {
  .echart {
    width: 98%;
    height: 351px !important;
  }
}
</style>