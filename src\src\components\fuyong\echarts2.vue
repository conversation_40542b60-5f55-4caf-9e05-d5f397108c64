<template>
  <div class="echart" ref="echart"></div>
</template>
    
<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  props: ["echartData"],
  data() {
    return {};
  },
  watch: {
    echartData(newVal) {
      this.init();
    },
  },
  mounted() {
    this.init();
  },

  methods: {
    init() {
      let data = this.echartData;

      const myChart = echarts.init(this.$refs.echart);

      const option = {
        title: {
          text: data.unit,
          left: "18px",
          top: "2px",
          textStyle: {
            color: "#23E1FD",
            fontSize: 14,
            fontWeight: "400",
          },
        },
        color: [
          "#FFFF33",
          "#01C8AE",
          "#0394B5",
          "#046CBB",
          "#2DF8FF",
          "#9AFFF0",
          "#9AFFF0",
        ],
        tooltip: {
          trigger: "axis",
          axisPointer: {
            // type: "cross",
            crossStyle: {
              color: "#999",
            },
            lineStyle: {
              type: "dashed",
            },
          },
        },
        grid: {
          left: "25",
          right: "25",
          bottom: "8",
          top: "40",
          containLabel: true,
        },
        legend: {
          data: data.legend,
          orient: "horizontal",
          icon: "rect",
          show: true,
          right: 20,
          top: -1,
          textStyle: {
            fontSize: 16,
            color: "#fff",
          },
        },
        xAxis: {
          type: "category",
          data: data.xdata,
          splitLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: true,
          },
          axisLabel: {
            color: "#999",
            textStyle: {
              fontSize: 18,
              color: "#78AFED",
            },
          },
        },
        yAxis: {
          type: "value",
          // max: max_value>=100? max_value + 100: max_value+10,
          // max: max_value > 100 ? max_value * 2 : max_value + 10,
          // interval: 10,
          // nameLocation: "center",
          axisLabel: {
            color: "#999",
            textStyle: {
              fontSize: 18,
              color: "#78AFED",
            },
          },
          splitLine: {
            show: true,
            type: "dashed",
            lineStyle: {
              color: "#172A4E",
            },
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: true,
          },
        },
        series: data.ydata,
      };

      myChart.setOption(option);
    },
  },
};
</script>
    
<style lang="less" scoped>
.echart {
  margin-top: 15px;
  margin-bottom: 5px;
  // margin-top: 40px;
  width: 100%;
  height: 335px;
}

// @media (max-height: 13.5rem) {
//   .echart {
//     width: 377px;
//     height: 160px !important;
//   }
// }
</style>