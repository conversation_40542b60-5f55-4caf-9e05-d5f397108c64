<template>
  <div class="echart" ref="echart"></div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  props: {
    chartData: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  methods: {
    initData() {},
    init() {
      const myChart = echarts.init(this.$refs.echart);
      let data = this.chartData
        ? this.chartData
        : {
            title: ["已领用", "未领用"],
            xAxisdata: ["20", "21", "22", "23", "25", "24"],
            yAxisdata1: [4, 7, 5, 9, 6, 5],
            yAxisdata2: [4, 7, 5, 9, 6, 5],
          };
      const option = {
        title: {
          text: "台",
          x: "3%",
          y: "-2%",
          textStyle: {
            color: "#fff",
            fontSize: 16,
          },
        },
        legend: {
          itemGap: 16,
          itemWidth: 12,
          itemHeight: 12,
          top: 0,
          right: "10%",
          textStyle: { fontSize: 18, color: "#fff" },
          data: data.title,
        },
        tooltip: {
          show: true, // 显示提示框
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        grid: {
          top: "18%",
          bottom: "1%",
          left: "3%",
          right: "15%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            axisTick: {
              show: false,
            },
            axisLabel: {
              margin: 10,
              textStyle: {
                fontSize: 16,
                color: "#fff",
              },
            },
            data: data.xAxisdata,
          },
        ],
        yAxis: [
          {
            type: "value",
            axisLine: {
              show: false,
            },
            axisLabel: {
              margin: 10,
              textStyle: {
                fontSize: 16,
                color: "#fff",
              },
            },
            splitLine: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: data.title[0],
            type: "bar", // 改为柱状图
            barWidth: "30%", // 柱子宽度
            itemStyle: {
              normal: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(59,102,246)", // 渐变开始颜色
                    },
                    {
                      offset: 1,
                      color: "rgba(118,237,252)", // 渐变结束颜色
                    },
                  ],
                },
              },
            },
            data: data.yAxisdata1,
          },
          {
            name: data.title[1],
            type: "bar", // 改为柱状图
            barWidth: "30%", // 柱子宽度
            itemStyle: {
              normal: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(18,137,152)", // 渐变开始颜色
                    },
                    {
                      offset: 1,
                      color: "rgba(18,237,152)", // 渐变结束颜色
                    },
                  ],
                },
              },
            },
            data: data.yAxisdata2,
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>

<style lang="less" scoped>
.echart {
  margin-top: 15px;

  width: 667px;
  height: 240px;
}

// @media (max-height: 1080px) {
//   .echart {
//     width: 95%;
//     height: 180px !important;
//   }
// }
</style>
