<template>
  <div class="echart" ref="echart"></div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  props: {
    gasWarningData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  watch: {
    gasWarningData: {
      handler() {
        this.$nextTick(() => {
          this.init();
        });
      },
      deep: true
    }
  },

  methods: {
    initData() { },
    init() {
      const myChart = echarts.init(this.$refs.echart);

      // 使用传入的数据或默认数据
      const chartData = this.gasWarningData && this.gasWarningData.length > 0
        ? this.gasWarningData
        : [
          { name: "氧气O₂", value: 1 },
          { name: "氢气H₂", value: 2 },
          { name: "二氧化碳CO₂", value: 1 },
          { name: "一氧化碳CO", value: 3 },
          { name: "氨气NH₃", value: 2 },
          { name: "甲烷", value: 1 }
        ];

      // 提取x轴标签和数据
      const xAxisData = chartData.map(item => item.name);
      const seriesData = chartData.map(item => item.value);

      const option = {
        color: ["#3398DB"],
        title: {
          text: "个",
          x: "6%",
          y: "8%",
          textStyle: {
            color: "#fff",
            fontSize: 14,
          },
        },
        legend: {
          data: ["报警数"],
          top: "8%",
          right: "40px",
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            color: "#fff",
            fontSize: 15
          },
        },
        grid: {
          top: "18%",
          bottom: "6%",
          left: "2%",
          right: "6%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: xAxisData,
            axisLabel: {
              show: true,
              rotate: 45,  // 设置标签旋转角度为45度
              textStyle: {
                color: "#fff", // 将 X 轴标签字体颜色设置为白色
              },
            },
          },
        ],
        yAxis: [
          {
            axisTick: {
              show:false,
              alignWithLabel: false,
            },
            // min:1,
            interval: 1,  // 固定刻度间隔为1
            axisLabel: {
              show: true,
              textStyle: {
                color: "#fff",
                fontSize: 15
                // 将 Y 轴标签字体颜色设置为白色
              },
            },
            
          },
        ],
        series: [
          {
            name: "报警数",
            type: "bar",
            barWidth: "40%",
            data: seriesData,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#66C4FC",
                  },
                  {
                    offset: 1,
                    color: "#66C4FC",
                  },
                ]),
                shadowColor: "rgba(0, 0, 0, 0.1)",
                shadowBlur: 10,
              },
            },
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>

<style lang="less" scoped>
.echart {
  margin-left: 10px;
  width: 95%;
  // margin-top: 20px;
  height: 320px;
}
</style>