<template>
  <div class="tedai">
    <div class="tedai-title">
      <div class="tedai-title-left">
        <img src="../assets/image/tedai.png" alt="" />
        <span>特殊待遇</span>
      </div>
      <div class="tedai-title-right">
        <el-select v-model="selectedMonitor" placeholder="请选择监控项" @change="handleMonitorChange" style="width: 200px;">
          <el-option v-for="option in monitorOptions" :key="option.dItemId" :label="option.dmName" :value="option"></el-option>
        </el-select>
      </div>
    </div>
    <div class="tedai-content">
      <div class="tedai-content-left">
        <div class="tedai-content-left-item" v-for="(item, index) in tfdata" :key="index">
          <div class="tedai-content-left-item-title">{{ item.dmName }}</div>
          <div class="tedai-content-left-item-value">{{ item.value }}{{ item.dDataUnit }}</div>
        </div>
      </div>
      <div class="tedai-content-right">
        <echarts1 :chartData="chartData" />
      </div>
    </div>
  </div>
</template>

<script>
import echarts1 from "./echarts/bingjifang/echarts41.vue";
import { getDeviceData } from "@/api/device";

export default {
  name: "tedai",
  components: {
    echarts1,
  },
  props: {
    tfdata: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      selectedMonitor: null,
      monitorOptions: [],
      chartData: null
    };
  },
  watch: {
    tfdata: {
      handler(newData) {
        if (newData && newData.length > 0) {
          this.monitorOptions = newData.filter(item => item.dmTag === "monitor");
        }
      },
      immediate: true
    }
  },
  methods: {
    async handleMonitorChange(selected) {
      if (!selected) return;
      
      try {
        const response = await getDeviceData({
          deviceId: selected.deviceId,
          dmId: selected.dmId
        });
        
        if (response.data && response.data.length > 0) {
          const xAxisData = response.data.map(item => this.formatTime(item.time));
          const seriesData = response.data.map(item => item.value);
          
          this.chartData = {
            title: {
              text: `${selected.dmName}${selected.dDataUnit ? ` (${selected.dDataUnit})` : ''}`,
              x: "3%",
              y: "0%",
              textStyle: {
                color: "#fff",
                fontSize: 14,
              },
            },
            legend: {
              data: [selected.dmName],
              textStyle: {
                color: "#ffff",
                fontSize: 14,
              },
            },
            tooltip: {
              show: true,
              trigger: 'axis',
              formatter: function(params) {
                const data = params[0];
                return `${data.name}<br/>${data.seriesName}: ${data.value}${selected.dDataUnit || ''}`
              }
            },
            grid: {
              top: "14%",
              bottom: "16%",
              left: "5%",
              right: "6%",
              containLabel: true,
            },
            xAxis: {
              type: "category",
              boundaryGap: false,
              axisLine: { show: false },
              axisTick: { show: false },
              axisLabel: {
                margin: 10,
                textStyle: {
                  fontSize: 12,
                  color: "#fff",
                },
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: "#939ab6",
                  opacity: 0.15,
                },
              },
              data: xAxisData
            },
            yAxis: {
              type: "value",
              axisTick: { show: false },
              axisLine: { show: false },
              axisLabel: {
                margin: 10,
                textStyle: {
                  fontSize: 12,
                  color: "#fff",
                },
                formatter: function(value) {
                  return value + (selected.dDataUnit || '');
                }
              },
              splitLine: { show: false },
            },
            series: [{
              name: selected.dmName,
              type: "line",
              z: 3,
              showSymbol: false,
              smoothMonotone: "x",
              lineStyle: {
                smooth: false,
                width: 3,
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(59,102,246)",
                    },
                    {
                      offset: 1,
                      color: "rgba(118,237,252)",
                    },
                  ],
                },
              },
              data: seriesData
            }]
          };
        }
      } catch (error) {
        console.error('获取图表数据失败:', error);
        this.$message.error('获取图表数据失败');
      }
    },
    formatTime(timestamp) {
      const date = new Date(timestamp);
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      return `${hours}:${minutes}`;
    }
  },
};
</script>

<style lang="less" scoped>
.tedai {
  width: 100%;
  height: 100%;
  background: url("../assets/image/tedai-bg.png") no-repeat;
  background-size: 100% 100%;
  padding: 16px;
  box-sizing: border-box;

  &-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    &-left {
      display: flex;
      align-items: center;

      img {
        width: 24px;
        height: 24px;
        margin-right: 8px;
      }

      span {
        font-size: 16px;
        color: #fff;
      }
    }
  }

  &-content {
    display: flex;
    height: calc(100% - 60px);

    &-left {
      width: 200px;
      margin-right: 20px;

      &-item {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        padding: 12px;
        margin-bottom: 12px;

        &-title {
          font-size: 14px;
          color: #fff;
          margin-bottom: 8px;
        }

        &-value {
          font-size: 20px;
          color: #2cc1ff;
        }
      }
    }

    &-right {
      flex: 1;
      height: 100%;
    }
  }
}

:deep(.el-select) {
  .el-input__inner {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
  }
}

:deep(.el-select-dropdown) {
  background-color: rgba(0, 32, 61, 0.9);
  border: 1px solid #134b7e;

  .el-select-dropdown__item {
    color: #fff;

    &.hover, &:hover {
      background-color: rgba(44, 193, 255, 0.1);
    }

    &.selected {
      color: #2cc1ff;
    }
  }
}
</style> 