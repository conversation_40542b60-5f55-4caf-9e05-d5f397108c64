<template>
    <div class="echart" ref="echart"></div>
</template>
  
<script>
import * as echarts from "echarts";

export default {
    name: "IoTequip",
    props: {
        chartData: {
            type: Object,
            required: true,
        },
    },
    data() {
        return {};
    },

    mounted() {
        this.init();
    },

    methods: {
        init() {
            const myChart = echarts.init(this.$refs.echart);
          
            const option = {

                // tooltip: { //提示框组件
                //     trigger: 'axis',
                //     formatter: '{b}<br />{a0}: {c0}<br />{a1}: {c1}',
                //     axisPointer: {
                //         type: 'shadow',
                //         label: {
                //             backgroundColor: '#6a7985'
                //         }
                //     },
                //     textStyle: {
                //         color: '#fff',
                //         fontStyle: 'normal',
                //         fontFamily: '微软雅黑',
                //         fontSize: 12,
                //     }
                // },
                grid: {
                    left: '12%',
                    right: '0%',
                    bottom: '15%',
                    top: '9%',
                    //	padding:'0 0 10 0',
                    // containLabel: true,
                },
                legend: {//图例组件，颜色和名字
                    right: '5%',
                    top: '2%',
                    itemGap: 16,
                    itemWidth: 18,
                    itemHeight: 10,
                    data: [{
                        name: '当年累计使用量',
                        //icon:'image://../wwwroot/js/url2.png', //路径
                    },
                    {
                        name: '本月使用量',
                    }],
                    textStyle: {
                        color: '#fff',
                        fontStyle: 'normal',
                        fontFamily: '微软雅黑',
                        fontSize: 16,
                    }
                },
                xAxis: [
                    {
                        type: 'category',
                        //	boundaryGap: true,//坐标轴两边留白
                        data:["氧气O₂", "氮气N₂", "氦气He", "氢气H₂", "二氧化碳CO₂", "一氧化碳CO", "氨气NH₃", "氩气Ar"],
                        axisLabel: { //坐标轴刻度标签的相关设置。
                            //		interval: 0,//设置为 1，表示『隔一个标签显示一个标签』
                            //	margin:15,
                            textStyle: {
                                color: '#fff',
                                fontStyle: 'normal',
                                fontFamily: '微软雅黑',
                                fontSize: 16,
                            },
                            rotate: 50,
                        },
                        axisTick: {//坐标轴刻度相关设置。
                            show: false,
                        },
                        axisLine: {//坐标轴轴线相关设置
                            lineStyle: {
                                color: '#fff',
                                opacity: 0.2
                            }
                        },
                        splitLine: { //坐标轴在 grid 区域中的分隔线。
                            show: false,
                        }
                    }
                ],
                yAxis: [
                    {
                        type: 'value',
                        splitNumber: 5,
                        axisLabel: {
                            textStyle: {
                                color: '#fff',
                                fontStyle: 'normal',
                                fontFamily: '微软雅黑',
                                fontSize: 16,
                            }
                        },
                        axisLine: {
                            show: false
                        },
                        axisTick: {
                            show: false
                        },
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: ['#fff'],
                                opacity: 0.06
                            }
                        }

                    }
                ],
                series: [
                    {
                        name: '当年累计使用量',
                        type: 'bar',
                        data: [1230, 1115, 1030, 1245, 1055, 760, 1262, 1580, ],
                        barWidth: 20,
                        barGap: 0,//柱间距离
                        // label: {//图形上的文本标签
                        //     normal: {
                        //       show: true,
                        //       position: 'top',
                        //       textStyle: {
                        //           color: '#a8aab0',
                        //           fontStyle: 'normal',
                        //           fontFamily: '微软雅黑',
                        //           fontSize: 12,   
                        //       },
                        //     },
                        // },
                        itemStyle: {
                            normal: {
                                show: true,
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                    offset: 0,
                                    color: '#5768EF'
                                }, {
                                    offset: 1,
                                    color: '#5768EF'
                                }]),
                                barBorderRadius: 50,
                                borderWidth: 0,
                            }
                        },
                    },
                    {
                        name: '本月使用量',
                        type: 'bar',
                        data: [92, 317, 211, 148, 262, 74, 115, 190,],
                        barWidth: 20,
                        barGap: 0,//柱间距离
                        // label: {//图形上的文本标签
                        //     normal: {
                        //       show: true,
                        //       position: 'top',
                        //       textStyle: {
                        //           color: '#a8aab0',
                        //           fontStyle: 'normal',
                        //           fontFamily: '微软雅黑',
                        //           fontSize: 12,   
                        //       },
                        //     },
                        // },
                        itemStyle: {
                            normal: {
                                show: true,
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                    offset: 0,
                                    color: '#69CBF2'
                                }, {
                                    offset: 1,
                                    color: '#69CBF2'
                                }]),
                                barBorderRadius: 50,
                                borderWidth: 0,
                            }
                        },
                    }
                ]
            };

            myChart.setOption(option);
        },
    },
};
</script>
  
<style lang="less" scoped>
.echart {
    width: 100%;
    height: 780px;
}
</style>
  