import { createRouter, createWebHashHistory } from "vue-router";
import Home from "../views/Home.vue";
import Login from "../views/Login.vue";

const routes = [
  {
    path: "/",
    name: "Login",
    component: Login,
  },
  {
    path: "/home",
    name: "Home",
    component: Home,
    meta: { requiresAuth: true },
    // 添加 props 配置来处理查询参数
    props: (route) => ({ ...route.query }),
  },
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

// 检查token是否过期
const isTokenExpired = (token) => {
  if (!token) return true;
  
  try {
    // JWT token 格式: header.payload.signature
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const payload = JSON.parse(window.atob(base64));
    
    // 检查过期时间
    const exp = payload.exp * 1000; // 转换为毫秒
    return Date.now() >= exp;
  } catch (error) {
    console.error("Token parsing error:", error);
    return true; // 解析出错，视为过期
  }
};

// 修改路由守卫，检查token有效性
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem("token");
  const tokenExpired = isTokenExpired(token);
  
  // 如果token过期，清除它
  if (token && tokenExpired) {
    localStorage.removeItem("token");
  }
  
  if (to.path === "/") {
    // 前往登录页
    if (token && !tokenExpired) {
      // 有效token，跳转到首页
      next({ 
        path: "/home",
        query: to.query // 保留原有的查询参数
      });
    } else {
      // 无token或token过期，正常进入登录页
      next();
    }
  } else if (to.meta.requiresAuth) {
    // 需要认证的页面
    if (token && !tokenExpired) {
      // 有效token，允许访问
      next();
    } else {
      // 无token或token过期，跳转到登录页
      next({ 
        path: "/",
        query: to.query // 保留原有的查询参数
      });
    }
  } else {
    // 不需要认证的页面，正常访问
    next();
  }
});

export default router;