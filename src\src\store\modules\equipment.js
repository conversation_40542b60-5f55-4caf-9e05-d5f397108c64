import axios from "axios";

// Vuex 状态管理
const state = () => ({
  equipmentRank: { yAxisdata: [], xAxisdata1: [] }, // 仪器使用排行
  yiqiztlist: [], // 仪器实时状态
  ryfblist: [], // 人员分布
  ktcslist: [], // 课题测试统计
  ktzlist: [], // 课题组使用统计
  equipmentTags: [], //仪器分类列表
});

// Mutation 定义
const mutations = {
  SET_EQUIPMENT_TAGS(state, tags) {
    console.log("更新仪器分类列表:", tags);
    state.equipmentTags = tags;
  },
  SET_EQUIPMENT_RANK(state, data) {
    console.log("更新仪器使用排行:", data);
    state.equipmentRank = data;
  },
  SET_YIQI_STATUS(state, data) {
    console.log("更新仪器实时状态:", data);
    state.yiqiztlist = data;
  },
  SET_USER_DISTRIBUTION(state, data) {
    console.log("更新人员分布:", data);
    state.ryfblist = data;
  },
  SET_TEST_STATISTICS(state, data) {
    console.log("更新课题测试统计:", data);
    state.ktcslist = data;
  },
  SET_TOP_USERS(state, data) {
    console.log("更新用户排行:", data);
    state.ktzlist = data;
  },
};

// API 配置
const baseURL = process.env.VUE_APP_BASE_API || "/lims/api";
const api = axios.create({ baseURL });
const headers = {
  clientid: "5a298e93-158d-4e22-83cf-6ceb62e9b4f1",
  clientsecret: "2c8ec39e-9887-482a-b28b-e64c496b601c",
};

// 通用请求函数
const apiRequest = async (
  method,
  params = {},
  commit,
  mutation,
  processData
) => {
  try {
    const response = await api.post("", { method, params }, { headers });
    if (response.data) {
      let data = response.data.response;
      if (processData) data = processData(data); // 如果需要处理数据，调用处理函数
      console.log(`${method} 返回的数据:`, data);
      commit(mutation, data);
    }
  } catch (error) {
    console.error(`${method} 请求失败:`, error);
  }
};

// Action 定义
const actions = {
  async fetchEquipmentTags({ commit }) {
    // 递归函数，将数据格式化
    // function formatData(data) {
    //   return Object.keys(data).map((key) => {
    //     const item = data[key];
    //     return {
    //       value: key,
    //       label: item.name,
    //       children:
    //         item.children && Object.keys(item.children).length > 0
    //           ? formatData(item.children)
    //           : undefined,
    //     };
    //   });
    // }
    function formatData(data, isTopLevel = true) {
      // 使用 reduce 格式化数据
      const formattedData = Object.keys(data).reduce((acc, key) => {
        const item = data[key];
        const children =
          item.children && Object.keys(item.children).length > 0
            ? formatData(item.children, false) // 递归调用时将 isTopLevel 设为 false
            : undefined;

        // 仅在 children 存在时推入结果数组
        if (children || !item.children) {
          acc.push({
            value: key,
            label: item.name,
            ...(children ? { children } : {}),
          });
        }
        return acc;
      }, []);

      // 如果是顶层，则在开头添加“全部分类”项
      return isTopLevel
        ? [
            {
              value: "all", // 任意自定义值，用于标识“全部分类”
              label: "全部分类",
            },
            ...formattedData,
          ]
        : formattedData;
    }

    try {
      const response = await axios.post("http://yiqi.tju.edu.cn/lims/api", {
        method: "equipment/getEquipmentTags",
        params: {},
      });
      // 假设返回的数据在 response.data 里，且数据需要保存到 equipmentTags
      console.log(`列表返回的数据:`, formatData(response.data.response));
      commit("SET_EQUIPMENT_TAGS", formatData(response.data.response));
    } catch (error) {
      console.error("Error fetching equipment tags:", error);
    }
  },
  async fetchEquipmentRank({ commit }) {
    await apiRequest(
      "equipment/time_rank",
      { num: 10, start: 1704038400, end: 1735660800 },
      commit,
      "SET_EQUIPMENT_RANK",
      (data) => ({
        yAxisdata: data.map((item) => item.name).reverse(),
        xAxisdata1: data.map((item) => item.time).reverse(),
      })
    );
  },

  async getdata2({ commit }) {
    await apiRequest(
      "equipment/getSummaryInfo",
      {},
      commit,
      "SET_YIQI_STATUS",
      (data) => [
        { name: "正在使用", value: data.usingCount },
        { name: "待机中", value: data.unUsingCount },
        // 移除故障状态显示
        // { name: "故障", value: data.outServiceCount },
      ]
    );
  },

  async getdata3({ commit }) {
    await apiRequest(
      "summarize/userStatus",
      {},
      commit,
      "SET_USER_DISTRIBUTION",
      (data) => ({
        value: [data.outer, data.inner, data.incharge],
        legend: ["校外人员", "校内人员", "管理员"],
      })
    );
  },

  async getdata4({ commit }) {
    await apiRequest(
      "summarize/labStatus",
      {},
      commit,
      "SET_TEST_STATISTICS",
      (data) => [
        { name: "总课题数", value: data.project },
        { name: "课题数", value: data.lab },
        { name: "测试数", value: data.test },
      ]
    );
  },

  async getdata5({ commit }) {
    await apiRequest(
      "eq_reserv/getTopUsers",
      { num: 9, year: 2024 },
      commit,
      "SET_TOP_USERS",
      (data) => ({
        yAxisdata: data.map((item) => item.name).reverse(),
        xAxisdata1: data.map((item) => item.time).reverse(),
      })
    );
  },
};

// Getters 定义
const getters = {
  equipmentTags: (state) => state.equipmentTags,
  equipmentRank: (state) => state.equipmentRank,
  yiqiStatus: (state) => state.yiqiztlist,
  userDistribution: (state) => state.ryfblist,
  testStatistics: (state) => state.ktcslist,
  topUsers: (state) => state.ktzlist,
};

// 导出模块
export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
