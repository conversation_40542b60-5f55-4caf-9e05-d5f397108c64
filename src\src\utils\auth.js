import { apiLogin } from '@/api/admin';
import { encrypt } from '@/utils/jsencrypt';
import axios from 'axios';

class AuthService {
  constructor() {
    this.refreshTokenTimeout = null;
    this.username = null;
    this.password = null;
    this.publicKey = null;
  }

  // 解析JWT token获取过期时间
  parseToken(token) {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const payload = JSON.parse(window.atob(base64));
      return {
        exp: payload.exp * 1000, // 转换为毫秒
        iat: payload.iat * 1000, // 签发时间
        ...payload
      };
    } catch (error) {
      console.error("Token parsing error:", error);
      return null;
    }
  }

  // 检查token是否即将过期（默认提前5分钟刷新）
  isTokenExpiringSoon(token, timeBuffer = 5 * 60 * 1000) {
    if (!token) return true;
    
    const tokenData = this.parseToken(token);
    if (!tokenData) return true;
    
    // 如果token在5分钟内过期，返回true
    return Date.now() + timeBuffer >= tokenData.exp;
  }

  // 获取token的剩余有效时间（毫秒）
  getTokenRemainingTime(token) {
    if (!token) return 0;
    
    const tokenData = this.parseToken(token);
    if (!tokenData) return 0;
    
    const remainingTime = tokenData.exp - Date.now();
    return remainingTime > 0 ? remainingTime : 0;
  }

  // 启动token自动刷新
  startTokenRefresh(username, password, publicKey) {
    // 保存凭据用于刷新
    this.username = username;
    this.password = password;
    this.publicKey = publicKey;
    
    // 清除之前的定时器
    if (this.refreshTokenTimeout) {
      clearTimeout(this.refreshTokenTimeout);
    }
    
    // 获取当前token
    const token = localStorage.getItem("token");
    if (!token) return;
    
    // 计算下次刷新时间（token有效期的80%时刷新）
    const remainingTime = this.getTokenRemainingTime(token);
    const refreshTime = remainingTime * 0.8;
    
    if (refreshTime <= 0) {
      // token已过期，立即刷新
      this.refreshToken();
    } else {
      // 设置定时器在token即将过期前刷新
      this.refreshTokenTimeout = setTimeout(() => {
        this.refreshToken();
      }, refreshTime);
      
      console.log(`Token will be refreshed in ${Math.round(refreshTime / 1000 / 60)} minutes`);
    }
  }

  // 刷新token
  async refreshToken() {
    try {
      if (!this.username || !this.password || !this.publicKey) {
        console.error("Missing credentials for token refresh");
        return;
      }
      
      const encryptedPassword = encrypt(this.password, this.publicKey);
      const response = await apiLogin({
        username: this.username,
        password: encryptedPassword,
        code: 123,
        uuid: 123,
      });
      
      if (response && response.token) {
        // 保存新token
        localStorage.setItem("token", response.token);
        console.log("Token refreshed successfully");
        
        // 重新启动刷新计时器
        this.startTokenRefresh(this.username, this.password, this.publicKey);
      }
    } catch (error) {
      console.error("Failed to refresh token:", error);
      // 如果刷新失败，尝试在1分钟后再次刷新
      this.refreshTokenTimeout = setTimeout(() => {
        this.refreshToken();
      }, 60000);
    }
  }

  // 手动检查并刷新token（可以在关键操作前调用）
  async checkAndRefreshToken() {
    const token = localStorage.getItem("token");
    if (!token) return false;
    
    // 如果token即将过期（5分钟内），刷新它
    if (this.isTokenExpiringSoon(token)) {
      await this.refreshToken();
      return true;
    }
    
    return false;
  }

  // 清除token和刷新定时器
  logout() {
    localStorage.removeItem("token");
    if (this.refreshTokenTimeout) {
      clearTimeout(this.refreshTokenTimeout);
    }
    this.username = null;
    this.password = null;
    this.publicKey = null;
  }
}

export default new AuthService(); 