import axios from "axios";
import router from "../router";
import authService from "./auth";

// 创建axios实例
const request = axios.create({
  baseURL: baseURL+'/api', // 基础URL
  timeout: 10000, // 请求超时时间
});

// 检查token是否过期
const isTokenExpired = (token) => {
  if (!token) return true;
  
  try {
    // JWT token 格式: header.payload.signature
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const payload = JSON.parse(window.atob(base64));
    
    // 检查过期时间
    const exp = payload.exp * 1000; // 转换为毫秒
    return Date.now() >= exp;
  } catch (error) {
    console.error("Token parsing error:", error);
    return true; // 解析出错，视为过期
  }
};

// 请求队列，用于存储等待token刷新的请求
let isRefreshing = false;
let requestsQueue = [];

// 执行队列中的请求
const processQueue = (error = null) => {
  requestsQueue.forEach(promise => {
    if (error) {
      promise.reject(error);
    } else {
      promise.resolve();
    }
  });
  
  // 清空队列
  requestsQueue = [];
};

// 请求拦截器
request.interceptors.request.use(
  async (config) => {
    const token = localStorage.getItem("token");
    
    // 检查token是否存在
    if (token) {
      // 如果token已过期，清除并跳转到登录页
      if (isTokenExpired(token)) {
        localStorage.removeItem("token");
        router.push("/");
        return Promise.reject(new Error("Token expired"));
      }
      
      // 如果token即将过期（5分钟内），尝试刷新
      if (authService.isTokenExpiringSoon(token)) {
        // 如果已经在刷新中，将请求加入队列
        if (isRefreshing) {
          return new Promise((resolve, reject) => {
            requestsQueue.push({ resolve: () => resolve(config), reject });
          });
        }
        
        isRefreshing = true;
        
        try {
          // 尝试刷新token
          await authService.refreshToken();
          
          // 刷新成功，更新当前请求的token
          const newToken = localStorage.getItem("token");
          if (newToken && config.headers.isToken !== false) {
            config.headers.Authorization = `Bearer ${newToken}`;
          }
          
          // 处理队列中的请求
          processQueue();
        } catch (error) {
          // 刷新失败，拒绝所有队列中的请求
          processQueue(error);
          return Promise.reject(error);
        } finally {
          isRefreshing = false;
        }
      }
      
      // 设置Authorization头
      if (config.headers.isToken !== false) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    if (error.response) {
      switch (error.response.status) {
        case 401: // 未授权
          console.error("Authentication failed, redirecting to login page");
          localStorage.removeItem("token"); // 清除token
          router.push("/"); // 跳转到登录页
          break;
        case 403: // 禁止访问
          console.error("Access forbidden");
          break;
        default:
          console.error("Request error:", error.response.data);
      }
    }
    return Promise.reject(error);
  }
);

const http = {
  get(url, params) {
    return request({
      method: "get",
      url: url,
      params,
    });
  },
  post(url, payload = undefined, quest) {
    return request({
      method: "post",
      url: url,
      data: payload,
      params: quest,
    });
  },
  delete(url, payload = undefined) {
    return request({
      method: "delete",
      url: url,
      data: payload,
    });
  },
};

export default http;
