<template>
  <div>
    <component :is="componentTag" @fatherMethoddd="fatherMethoddd" v-if="isshowwhat"></component>
    <div class="botbtn">
      <div v-for="(item, index) in changeTitle" :key="index" :class="titactive == index ? 'btt1' : 'btt'"
        @click="changetit(index)">
        {{ item }}
      </div>
    </div>
    <div class="zichanbeijin" v-if="!isshowwhat">
      <div class="title">
        <div>异常跟踪处理</div>
        <img class="img1" @click="anniu()" src="../assets/image/table-x.png" alt="" />
      </div>
      <hr />
      <div class="titlecontent">
        <div class="xuan">
          <el-select v-model="value" placeholder="请选择报警类型">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </div>
        <!-- <div class="xuan">
          <el-input placeholder="请输入设备名称" v-model="input4">
            <i slot="prefix" class="el-input__icon el-icon-search"></i>
          </el-input>
        </div> -->
        <div class="xiang">
          <div class="item" @click="handleSearch">
            <img src="../assets/image/xiaoiconshousuo.png" alt="" />
            <div>搜索</div>
          </div>
          <div class="item" @click="handleReset">
            <img src="../assets/image/xiaoiconzz.png" alt="" />
            <div>重置</div>
          </div>
          <!-- <div class="item item1">
            <img src="../assets/image/xiaoiconschuan.png" alt="" />
            <div style="color: #ffaa3f">导出</div>
          </div> -->
        </div>
      </div>
      <div class="table-container">
        <div class="table-header">
          <div class="table-rowtitle">
            <div class="table-cell">设备名称</div>
            <div class="table-cell">报警详情</div>
            <div class="table-cell">报警类型</div>
            <div class="table-cell">报警时间</div>
            <div class="table-cell">修复时间</div>
            <div class="table-cell">状态</div>
            <!-- <div class="table-cell">责任人</div>
            <div class="table-cell">操作</div> -->
          </div>
        </div>
        <div class="table-body">
          <div v-for="(item, index) in tableData" :key="index" class="table-row">
            <div class="table-cell">{{ item.monitorContent }}</div>
            <div class="table-cell">{{ item.description }}</div>
            <div class="table-cell">{{ item.function }}</div>
            <div class="table-cell">{{ item.createdAt }}</div>
            <div class="table-cell">{{ item.updatedAt || "-" }}</div>
            <div class="table-cell" :style="{
              color: item.status === '已修复' ? '#64f8bb' : '#b93851',
            }">
              {{ item.status }}
            </div>
            <!-- <div class="table-cell">{{ item.responsible }}</div>
            <div class="table-cell">
              <img src="../assets/image/zuochuoss.png" alt="" />
            </div> -->
          </div>
        </div>
      </div>
      <el-pagination class="fenye" @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="currentPage4" :page-sizes="[10, 20, 50, 100]" :page-size="pageSize"
        layout="total, sizes, next, pager, prev, jumper" :total="total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import component0 from "@/views/tongji/baojing.vue";
import { getDeviceWarningList } from "@/api/device.js";

export default {
  components: {
    component0,
  },
  data() {
    return {
      isshowwhat: true,
      componentTag: "component0",
      titactive: 0,
      changeTitle: ["数据统计", "数据列表"],
      ressss: true,
      tableData: [],
      value: "",
      input4: "",
      options: [
        {
          value: "",
          label: "全部",
        },
        {
          value: "N",
          label: "未修复",
        },
        {
          value: "Y",
          label: "已修复",
        },
      ],
      showdh: true,
      showdh1: false,
      currentPage4: 1,
      pageSize: 10,
      total: 0,
    };
  },
  methods: {
    changetit(index) {
      this.titactive = index;
      this.isshowwhat = !index;
      if (index == 1) {
        this.showdh = false;
        this.showdh1 = true;
        this.noAnimation = true;
        this.fetchWarningList();
      } else {
        this.showdh = true;
        this.showdh1 = false;
        this.noAnimation = false;
      }
    },
    anniu() {
      this.ressss = false;
    },
    async fetchWarningList() {
      try {
        const params = {
          pageNum: this.currentPage4,
          pageSize: this.pageSize,
          buildingId: 1,
          severitys: "一级,二级,三级",
        };

        if (this.value !== "") {
          params.hasFixed = this.value;
        }
        if (this.input4) {
          params.deviceName = this.input4;
        }

        console.log("请求参数:", params);
        const res = await getDeviceWarningList(params);
        console.log("响应数据:", res);

        if (res.code === 200) {
          this.tableData = (res.rows || []).map((item) => ({
            monitorContent: item.deviceName,
            description: item.errMsg,
            function: item.warningCategory,
            createdAt: this.formatDate(item.createdAt),
            updatedAt: item.updatedAt ? this.formatDate(item.updatedAt) : "-",
            status: item.hasFixed === "Y" ? "已修复" : "未修复",
            responsible: item.updateBy || "-",
          }));
          this.total = res.total || 0;
        }
      } catch (error) {
        console.error("获取报警数据失败:", error);
      }
    },
    formatDate(timestamp) {
      const date = new Date(timestamp);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
        2,
        "0"
      )}-${String(date.getDate()).padStart(2, "0")} ${String(
        date.getHours()
      ).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.fetchWarningList();
    },
    handleCurrentChange(val) {
      this.currentPage4 = val;
      this.fetchWarningList();
    },
    handleSearch() {
      this.currentPage4 = 1;
      this.fetchWarningList();
    },
    handleReset() {
      this.value = "";
      this.input4 = "";
      this.currentPage4 = 1;
      this.fetchWarningList();
    },
  },
  mounted() {
    if (!this.isshowwhat) {
      this.fetchWarningList();
    }
  },
};
</script>

<style lang="less" scoped>
.botbtn {
  position: fixed;
  top: 978px;
  // right: 441px;
  right: 60px;
  width: 200px;
  height: 43px;
  background: #022d56;
  border-radius: 8px;
  z-index: 20;
  display: flex;
  align-items: center;
  justify-content: center;

  .btt {
    color: #fff;
    font-size: 17px;
    line-height: 44px;
    width: 88px;
    text-align: center;
    height: 43px;
    cursor: pointer;
  }

  .btt1 {
    color: rgb(8, 207, 241);
    font-size: 17px;
    line-height: 44px;
    width: 88px;
    text-align: center;
    height: 43px;
    cursor: pointer;
  }
}

.zichanbeijin {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: url("../assets/image/zichanbeijin.png");
  width: 1863px;
  height: 868px;
  z-index: 99999;
  padding-left: 34px;
  padding-right: 22px;
  padding-top: 21px;

  .title {
    margin-bottom: 19px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-family: Source Han Sans SC;
    font-weight: bold;
    font-size: 20px;
    color: #ffffff;
  }

  .img1 {
    cursor: pointer;
    width: 15px;
    height: 15px;
  }

  hr {
    margin-bottom: 17px;
    border: none;
    border-top: 2px solid #466873;
    /* 设置边框颜色为红色 */
  }

  .titlecontent {
    width: 100%;
    display: flex;
    align-items: center;
    margin-bottom: 18px;

    .xuan {
      margin-right: 25px;
      width: 230px;
      height: 37px;
      color: #ffffff;

      ::v-deep .el-select__wrapper {
        background-color: rgba(28, 41, 50, 0.2);
        box-sizing: none;
        box-shadow: 0 0 0 1px #608e9a inset;
      }

      ::v-deep .el-input__wrapper {
        background-color: rgba(28, 41, 50, 0.2);
        box-sizing: none;
        box-shadow: 0 0 0 1px #608e9a inset;
      }
    }

    .xiang {
      width: 172px;
      height: 37px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .item {
        cursor: pointer;
        gap: 3px;
        width: 77px;
        height: 37px;
        border: 1px solid #537b86;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: Source Han Sans SC;
        font-weight: 400;
        font-size: 15px;
        color: #3cccf9;
        border-radius: 5px;
      }

      .item1 {
        cursor: pointer;
        border: 1px solid #d28e3c;
      }
    }
  }

  .table-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    color: #fff;
  }

  .table-body {
    font-size: 17px;
  }

  .table-row {
    color: #fff;
    display: flex;
    width: 100%;
    background-color: #1c2932;
    border: 1px solid #56808d;
    margin-bottom: -1px;
    height: 55px;
  }

  .table-header,
  .table-rowtitle {
    border: 1px solid #56808d;
    color: #fff;
    display: flex;
    width: 100%;
    background-color: #1c4064;
    margin-bottom: -1px;
    font-size: 18px;
    height: 50px;
    // margin-top: 3px;
  }

  .table-cell {
    flex: 1;
    padding: 10px;
    border-bottom: 1px solid #ccc;
    text-align: center;
  }

  .table-header .table-cell {
    font-weight: bold;
  }

  .table-row:nth-child(even) {
    color: #fff;
    display: flex;
    width: 100%;
    background-color: #1c4064;
    border: 1px solid #56808d;
    margin-bottom: -1px;
  }

  .fenye {
    margin-top: 23px;
    display: flex;
    flex-direction: row-reverse;

    ::v-deep .el-pager li {
      background: url("../assets/image/fenyebox.png");
      background-size: 100% 100%;
      color: #fff;
      margin: 3px !important;
      font-size: 14px;
    }
  }
}

::v-deep .el-pagination button {
  background: rgba(28, 41, 50, 0) !important;
  color: #fff !important;
}

::v-deep .el-pagination__jump {
  color: #fff !important;
}

::v-deep .el-pagination__total {
  color: #fff !important;
  margin-left: 10px !important;
}

::v-deep .el-select__wrapper {
  background-color: rgba(151, 173, 83, 0);
  box-sizing: none;
  box-shadow: 0 0 0 1px#694e31 inset;
  margin-right: 10px;
}

::v-deep .el-input__wrapper {
  background-color: rgba(28, 41, 50, 0);
  box-sizing: none;
  box-shadow: 0 0 0 1px #be8b34 inset;
}
</style>
