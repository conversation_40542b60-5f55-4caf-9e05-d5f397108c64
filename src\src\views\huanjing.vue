<template>
  <div>
    <component :is="componentTag" @fatherMethoddd="fatherMethoddd" v-if="isshowwhat"></component>
    <div class="botbtn">
      <div v-for="(item, index) in changeTitle" :key="index" :class="titactive == index ? 'btt1' : 'btt'"
        @click="changetit(index)">
        {{ item }}
      </div>
    </div>
    <tedai :ids="ids" :selectedItem="selectedItem" class="sbdetails" :zengtiimg="zengtiimg" v-if="false"
      @hidedetails="hidedetailsss"></tedai>
    <biaoGesss v-if="isshow" @hidedetails="hidedetails" :tableTitle="tableTitle" :tableDataItem="tableDataItem">
    </biaoGesss>
    <div class="container" v-if="!isshowwhat">
      <div class="left-panel" :class="{
        'left-panel-active': showdh,
        'no-animation': noAnimation,
        'left-panel-active1': showdh1,
      }">
        <Title2 class="ltitle1" tit="环境监管">
          <div class="box">
            <div>
              <el-input class="el-input" v-model="input" placeholder="请输入内容"></el-input>
            </div>
            <!-- <div class="xiaobox">
              <img class="siqiu" src="../assets/image/shixinqiu.png" alt="" />
              <div class="shuru">全部设备</div>
            </div> -->
            <div class="menu">
              <div v-for="(menu, index) in menus" :key="index" class="menu-group">
                <div :style="{ color: activeSubmenu == menu.id ? '#00ffc0' : '' }" class="menu-item"
                  @click="toggleSubMenu(menu.id, menu.title, index)">
                  {{ menu.title }}
                </div>
                <div v-show="activeSubmenu === menu.id" class="submenu">
                  <div v-for="(item, subIndex) in menu.submenu" :style="{
                    color: activeSubSubmenu === item.id ? '#00ffc0' : '',
                  }" :key="subIndex" class="submenu-items">
                    <div class="bq" @click="toggleSubSubMenu(item.id, index)">
                      {{ item.title }}
                    </div>
                    <div v-show="activeSubSubmenu === item.id" class="submenu">
                      <div v-for="(subItem, thirdIndex) in item.submenu" :key="thirdIndex" :style="{
                        color: selectedIndex == thirdIndex ? '#00ffc0' : '',
                      }" class="submenu-item" @click="setContent(subItem, thirdIndex)">
                        {{ subItem.title }}
                        <!-- <div class="listtype">使用中</div> -->
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- shebei12.png -->
        </Title2>
      </div>
      <!-- shebeibgc.png -->
      <!-- 右侧内容 -->
      <!-- 右侧内容 -->

      <div v-show="detalis && detalis.length" class="right-panel" :class="{
        'right-panel-active': showdh,
        'no-animation': noAnimation,
        'right-panel-active1': showdh1,
      }">
        <Title3 :tit="cgqname">
          <div class="box">
            <div class="xiaoboxs" v-for="item in detalis" :key="item">
              <img class="siqiu" src="../assets/image/kongxin.png" alt="" />
              <div class="nihaowo">{{ item.name }}</div>
              <div class="shuru">{{ item.value }}</div>
            </div>
          </div>
          <hr class="hr" />
          <echarts2 ref="echarts2" :chart-data="chartData" :unit="dataUnit" :title="cgqname" style="width: 100%">
          </echarts2>
          <echarts2 v-if="cgqname.includes('温湿度')" ref="echarts3" :chart-data="chartData" :unit="dataUnit"
            :title="cgqname" style="width: 100%">
          </echarts2>
          <hr class="hr" />
          <p class="local">
            功能介绍：<br />
            &nbsp;&nbsp;&nbsp;{{ yccontent }}
          </p>
          <hr class="hr" />
          <!-- <div class="local">
            <div style="display: flex">
              <div>维护人：</div>
              <div>王工</div>
            </div>
            <div style="display: flex; margin-top: 10px">
              <div>联系方式：</div>
              <div>173****5896</div>
            </div>
          </div> -->
        </Title3>
      </div>
    </div>
    <!-- <DeviceChart
      v-if="chartData.length"
      :chart-data="chartData"
      :unit="dataUnit"
      :title="cgqname"
    /> -->
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import component0 from "@/views/tongji/huanjing.vue";
import huanxing from "@/components/echarts/huanxing.vue";
import zhexian from "@/components/echarts/zhexian.vue";
import zhexian1 from "@/components/echarts/zhexian1.vue";
import SystemDete from "@/components/echarts/SystemDete.vue";
import echarts1 from "@/components/echarts/bingjifang/echarts3.vue";
import tedai from "@/components/common/tedais.vue";
import biaoGesss from "@/components/common/biaoGesss.vue";
import shuangxiang from "@/components/echarts/shuangxiang.vue";
import echarts2 from "@/components/echarts/bingjifang/echarts4.vue";
import axios from "axios";
import { getToken } from "@/utils/auth";
import DeviceChart from "@/components/echarts/DeviceChart.vue";
import { getDeviceData, getDevicedetails } from "@/api/device";

export default {
  // import引入的组件需要注入到对象中才能使用
  components: {
    component0,
    tedai,
    echarts2,
    huanxing,
    zhexian,
    zhexian1,
    SystemDete,
    echarts1,
    shuangxiang,
    biaoGesss,
    component0,
    DeviceChart,
  },
  props: ["tabledata", "zengtiimg"],
  //  cgqlistss: [
  //       { name: "湿度范围：", value: "5%RH~100%RH（无结露）" },
  //       { name: "湿度分辨率：", value: "0.04%" },
  //       { name: "IP地址：", value: "±3%RH（20%RH~80%RH）" },
  //       { name: "湿度精度：", value: "-40℃~+125℃" },
  //       { name: "温度分辨率：", value: "0.01℃" },
  //       { name: "温度精度：", value: "±0.5℃（10℃~60℃）" },
  //     ],

  data() {
    // 这里存放数据
    return {
      isshowwhat: false,
      isshowsss: false,
      titactive: 0,
      changeTitle: ["数据列表", "数据统计"],
      isshowsss: false,
      activeyj: null, // 当前激活的一级菜单
      activeSubmenu: null, // 当前激活的二级菜单
      activeSubSubmenu: null, // 当前激活的三级菜单
      activeContent: null, // 当前显示的内容
      isshow: false,
      selectedIndex: null,
      selectedItem: null,
      xxxx: false,
      cgqlist: [
        { name: "温度", value: "20℃" },
        { name: "湿度", value: "12" },
        // { name: "品牌型号：", value: "海康/DS-2CD7205E-SH" },
        // { name: "IP地址：", value: "************" },
        // { name: "设备类型：", value: "高清半球型摄像机" },
      ],
      yccontent: "",
      menus: [
        {
          "id": "menu2F",
          "title": "2F",
          "submenu": [
            {
              "id": "submenu2F-上空",
              "title": "上空",
              "submenu": [
                {
                  "title": "温湿度传感器 - 上空",
                  "content": "温湿度传感器 - 上空",
                  "deviceid": "1611052",
                  "id": 500912
                },
                {
                  "title": "氧浓度传感器 - 上空",
                  "content": "氧浓度传感器 - 上空",
                  "deviceid": "1615031",
                  "id": 500958
                },
                {
                  "title": "压力传感器 - 上空",
                  "content": "压力传感器 - 上空",
                  "deviceid": "1616010",
                  "id": 500985
                }
              ]
            },
            {
              "id": "submenu2F-通用实验室 (204)",
              "title": "通用实验室 (204)",
              "submenu": [
                {
                  "title": "温湿度传感器 - 204",
                  "content": "温湿度传感器 - 204",
                  "deviceid": "1611053",
                  "id": 500913
                },
                {
                  "title": "氧浓度传感器 - 204",
                  "content": "氧浓度传感器 - 204",
                  "deviceid": "1615032",
                  "id": 500959
                }
              ]
            },
            {
              "id": "submenu2F-通用实验室 (205)",
              "title": "通用实验室 (205)",
              "submenu": [
                {
                  "title": "温湿度传感器 - 205",
                  "content": "温湿度传感器 - 205",
                  "deviceid": "1611054",
                  "id": 500914
                },
                {
                  "title": "氧浓度传感器 - 205",
                  "content": "氧浓度传感器 - 205",
                  "deviceid": "1615033",
                  "id": 500960
                }
              ]
            },
            {
              "id": "submenu2F-样品准备间 (218)",
              "title": "样品准备间 (218)",
              "submenu": [
                {
                  "title": "温湿度传感器 - 218",
                  "content": "温湿度传感器 - 218",
                  "deviceid": "1611055",
                  "id": 500915
                },
                {
                  "title": "氧浓度传感器 - 218",
                  "content": "氧浓度传感器 - 218",
                  "deviceid": "1615034",
                  "id": 500961
                }
              ]
            },
            {
              "id": "submenu2F-液相色谱 (220)",
              "title": "液相色谱 (220)",
              "submenu": [
                {
                  "title": "温湿度传感器 - 220",
                  "content": "温湿度传感器 - 220",
                  "deviceid": "1611056",
                  "id": 500916
                },
                {
                  "title": "温湿度传感器 - 220",
                  "content": "温湿度传感器 - 220",
                  "deviceid": "1611057",
                  "id": 500917
                },
                {
                  "title": "氧浓度传感器 - 220",
                  "content": "氧浓度传感器 - 220",
                  "deviceid": "1615036",
                  "id": 500963
                },
                {
                  "title": "氧浓度传感器 - 220",
                  "content": "氧浓度传感器 - 220",
                  "deviceid": "1615037",
                  "id": 500964
                },
                {
                  "title": "压力传感器 - 220",
                  "content": "压力传感器 - 220",
                  "deviceid": "1616011",
                  "id": 500986
                }
              ]
            },
            {
              "id": "submenu2F-气相色谱 (223)",
              "title": "气相色谱 (223)",
              "submenu": [
                {
                  "title": "温湿度传感器 - 223",
                  "content": "温湿度传感器 - 223",
                  "deviceid": "1611058",
                  "id": 500918
                },
                {
                  "title": "温湿度传感器 - 223",
                  "content": "温湿度传感器 - 223",
                  "deviceid": "1611059",
                  "id": 500919
                },
                {
                  "title": "氧浓度传感器 - 223",
                  "content": "氧浓度传感器 - 223",
                  "deviceid": "1615039",
                  "id": 500966
                },
                {
                  "title": "氧浓度传感器 - 223",
                  "content": "氧浓度传感器 - 223",
                  "deviceid": "1615040",
                  "id": 500967
                },
                {
                  "title": "压力传感器 - 223",
                  "content": "压力传感器 - 223",
                  "deviceid": "1616013",
                  "id": 500988
                }
              ]
            },
            {
              "id": "submenu2F-光谱仪室 (225)",
              "title": "光谱仪室 (225)",
              "submenu": [
                {
                  "title": "温湿度传感器 - 225",
                  "content": "温湿度传感器 - 225",
                  "deviceid": "1611060",
                  "id": 500920
                },
                {
                  "title": "温湿度传感器 - 225",
                  "content": "温湿度传感器 - 225",
                  "deviceid": "1611061",
                  "id": 500921
                },
                {
                  "title": "氧浓度传感器 - 225",
                  "content": "氧浓度传感器 - 225",
                  "deviceid": "1615041",
                  "id": 500968
                },
                {
                  "title": "氧浓度传感器 - 225",
                  "content": "氧浓度传感器 - 225",
                  "deviceid": "1615042",
                  "id": 500969
                }
              ]
            },
            {
              "id": "submenu2F-光谱仪室 (226)",
              "title": "光谱仪室 (226)",
              "submenu": [
                {
                  "title": "温湿度传感器 - 226",
                  "content": "温湿度传感器 - 226",
                  "deviceid": "1611062",
                  "id": 500922
                },
                {
                  "title": "氧浓度传感器 - 226",
                  "content": "氧浓度传感器 - 226",
                  "deviceid": "1615043",
                  "id": 500970
                }
              ]
            },
            {
              "id": "submenu2F-样品处理间 (228)",
              "title": "样品处理间 (228)",
              "submenu": [
                {
                  "title": "温湿度传感器 - 228",
                  "content": "温湿度传感器 - 228",
                  "deviceid": "1611063",
                  "id": 500923
                },
                {
                  "title": "氧浓度传感器 - 228",
                  "content": "氧浓度传感器 - 228",
                  "deviceid": "1615044",
                  "id": 500971
                },
                {
                  "title": "压力传感器 - 228",
                  "content": "压力传感器 - 228",
                  "deviceid": "1616014",
                  "id": 500989
                }
              ]
            },
            {
              "id": "submenu2F-光谱仪室2 (231)",
              "title": "光谱仪室2 (231)",
              "submenu": [
                {
                  "title": "温湿度传感器 - 231",
                  "content": "温湿度传感器 - 231",
                  "deviceid": "1611064",
                  "id": 500924
                },
                {
                  "title": "温湿度传感器 - 231",
                  "content": "温湿度传感器 - 231",
                  "deviceid": "1611065",
                  "id": 500925
                },
                {
                  "title": "温湿度传感器 - 231",
                  "content": "温湿度传感器 - 231",
                  "deviceid": "1611066",
                  "id": 500926
                },
                {
                  "title": "温湿度传感器 - 231",
                  "content": "温湿度传感器 - 231",
                  "deviceid": "1611067",
                  "id": 500927
                },
                {
                  "title": "氧浓度传感器 - 231",
                  "content": "氧浓度传感器 - 231",
                  "deviceid": "1615045",
                  "id": 500972
                },
                {
                  "title": "氧浓度传感器 - 231",
                  "content": "氧浓度传感器 - 231",
                  "deviceid": "1615046",
                  "id": 500973
                },
                {
                  "title": "氧浓度传感器 - 231",
                  "content": "氧浓度传感器 - 231",
                  "deviceid": "1615047",
                  "id": 500974
                },
                {
                  "title": "氧浓度传感器 - 231",
                  "content": "氧浓度传感器 - 231",
                  "deviceid": "1615048",
                  "id": 500975
                }
              ]
            },
            {
              "id": "submenu2F-汇流排间 (219)",
              "title": "汇流排间 (219)",
              "submenu": [
                {
                  "title": "氧浓度传感器 - 219",
                  "content": "氧浓度传感器 - 219",
                  "deviceid": "1615035",
                  "id": 500962
                }
              ]
            },
            {
              "id": "submenu2F-样品处理室 (222)",
              "title": "样品处理室 (222)",
              "submenu": [
                {
                  "title": "氧浓度传感器 - 222",
                  "content": "氧浓度传感器 - 222",
                  "deviceid": "1615038",
                  "id": 500965
                },
                {
                  "title": "压力传感器 - 222",
                  "content": "压力传感器 - 222",
                  "deviceid": "1616012",
                  "id": 500987
                }
              ]
            }
          ]
        },
        {
          "id": "menu1F",
          "title": "1F",
          "submenu": [
            {
              "id": "submenu1F-核磁主机房 (119)",
              "title": "核磁主机房 (119)",
              "submenu": [
                {
                  "title": "温湿度传感器 - 119",
                  "content": "温湿度传感器 - 119",
                  "deviceid": "1611032",
                  "id": 500892
                },
                {
                  "title": "温湿度传感器 - 119",
                  "content": "温湿度传感器 - 119",
                  "deviceid": "1611033",
                  "id": 500893
                }
              ]
            },
            {
              "id": "submenu1F-核磁主机房 (118)",
              "title": "核磁主机房 (118)",
              "submenu": [
                {
                  "title": "温湿度传感器 - 118",
                  "content": "温湿度传感器 - 118",
                  "deviceid": "1611034",
                  "id": 500894
                },
                {
                  "title": "温湿度传感器 - 118",
                  "content": "温湿度传感器 - 118",
                  "deviceid": "1611035",
                  "id": 500895
                },
                {
                  "title": "温湿度传感器 - 118",
                  "content": "温湿度传感器 - 118",
                  "deviceid": "1611036",
                  "id": 500896
                }
              ]
            },
            {
              "id": "submenu1F-核磁中心 (127)",
              "title": "核磁中心 (127)",
              "submenu": [
                {
                  "title": "温湿度传感器 - 127",
                  "content": "温湿度传感器 - 127",
                  "deviceid": "1611037",
                  "id": 500897
                },
                {
                  "title": "氧浓度传感器 - 127",
                  "content": "氧浓度传感器 - 127",
                  "deviceid": "1615018",
                  "id": 500945
                },
                {
                  "title": "压力传感器 - 127",
                  "content": "压力传感器 - 127",
                  "deviceid": "1616004",
                  "id": 500979
                }
              ]
            },
            {
              "id": "submenu1F-核磁主机房 (114)",
              "title": "核磁主机房 (114)",
              "submenu": [
                {
                  "title": "温湿度传感器 - 124",
                  "content": "温湿度传感器 - 124",
                  "deviceid": "1611038",
                  "id": 500898
                },
                {
                  "title": "温湿度传感器 - 124",
                  "content": "温湿度传感器 - 124",
                  "deviceid": "1611039",
                  "id": 500899
                },
                {
                  "title": "温湿度传感器 - 124",
                  "content": "温湿度传感器 - 124",
                  "deviceid": "1611040",
                  "id": 500900
                }
              ]
            },
            {
              "id": "submenu1F-空压机房 (123)",
              "title": "空压机房 (123)",
              "submenu": [
                {
                  "title": "温湿度传感器 - 123",
                  "content": "温湿度传感器 - 123",
                  "deviceid": "1611041",
                  "id": 500901
                },
                {
                  "title": "压力传感器 - 123",
                  "content": "压力传感器 - 123",
                  "deviceid": "1616006",
                  "id": 500981
                }
              ]
            },
            {
              "id": "submenu1F-能谱仪室 (110)",
              "title": "能谱仪室 (110)",
              "submenu": [
                {
                  "title": "温湿度传感器 - 110",
                  "content": "温湿度传感器 - 110",
                  "deviceid": "1611042",
                  "id": 500902
                },
                {
                  "title": "温湿度传感器 - 110",
                  "content": "温湿度传感器 - 110",
                  "deviceid": "1611043",
                  "id": 500903
                },
                {
                  "title": "温湿度传感器 - 110",
                  "content": "温湿度传感器 - 110",
                  "deviceid": "1611044",
                  "id": 500904
                },
                {
                  "title": "氧浓度传感器 - 110",
                  "content": "氧浓度传感器 - 110",
                  "deviceid": "1615019",
                  "id": 500946
                },
                {
                  "title": "氧浓度传感器 - 110",
                  "content": "氧浓度传感器 - 110",
                  "deviceid": "1615020",
                  "id": 500947
                },
                {
                  "title": "氧浓度传感器 - 110",
                  "content": "氧浓度传感器 - 110",
                  "deviceid": "1615021",
                  "id": 500948
                }
              ]
            },
            {
              "id": "submenu1F-中控室 (103)",
              "title": "中控室 (103)",
              "submenu": [
                {
                  "title": "温湿度传感器 - 103",
                  "content": "温湿度传感器 - 103",
                  "deviceid": "1611045",
                  "id": 500905
                },
                {
                  "title": "氧浓度传感器 - 103",
                  "content": "氧浓度传感器 - 103",
                  "deviceid": "1615024",
                  "id": 500951
                }
              ]
            },
            {
              "id": "submenu1F-D射线衍射仪 (104)",
              "title": "D射线衍射仪 (104)",
              "submenu": [
                {
                  "title": "温湿度传感器 - 104",
                  "content": "温湿度传感器 - 104",
                  "deviceid": "1611046",
                  "id": 500906
                },
                {
                  "title": "温湿度传感器 - 104",
                  "content": "温湿度传感器 - 104",
                  "deviceid": "1611047",
                  "id": 500907
                },
                {
                  "title": "温湿度传感器 - 104",
                  "content": "温湿度传感器 - 104",
                  "deviceid": "1611048",
                  "id": 500908
                },
                {
                  "title": "氧浓度传感器 - 104",
                  "content": "氧浓度传感器 - 104",
                  "deviceid": "1615025",
                  "id": 500952
                },
                {
                  "title": "氧浓度传感器 - 104",
                  "content": "氧浓度传感器 - 104",
                  "deviceid": "1615026",
                  "id": 500953
                },
                {
                  "title": "氧浓度传感器 - 104",
                  "content": "氧浓度传感器 - 104",
                  "deviceid": "1615027",
                  "id": 500954
                }
              ]
            },
            {
              "id": "submenu1F-D射线衍射仪2 (109)",
              "title": "D射线衍射仪2 (109)",
              "submenu": [
                {
                  "title": "温湿度传感器 - 109",
                  "content": "温湿度传感器 - 109",
                  "deviceid": "1611049",
                  "id": 500909
                },
                {
                  "title": "温湿度传感器 - 109",
                  "content": "温湿度传感器 - 109",
                  "deviceid": "1611050",
                  "id": 500910
                },
                {
                  "title": "温湿度传感器 - 109",
                  "content": "温湿度传感器 - 109",
                  "deviceid": "1611051",
                  "id": 500911
                },
                {
                  "title": "氧浓度传感器 - 109",
                  "content": "氧浓度传感器 - 109",
                  "deviceid": "1615028",
                  "id": 500955
                },
                {
                  "title": "氧浓度传感器 - 109",
                  "content": "氧浓度传感器 - 109",
                  "deviceid": "1615029",
                  "id": 500956
                },
                {
                  "title": "氧浓度传感器 - 109",
                  "content": "氧浓度传感器 - 109",
                  "deviceid": "1615030",
                  "id": 500957
                }
              ]
            },
            {
              "id": "submenu1F-汇流排间",
              "title": "汇流排间",
              "submenu": [
                {
                  "title": "氧浓度传感器 - 汇流排间",
                  "content": "氧浓度传感器 - 汇流排间",
                  "deviceid": "1615022",
                  "id": 500949
                }
              ]
            },
            {
              "id": "submenu1F-仪器室 (116)",
              "title": "仪器室 (116)",
              "submenu": [
                {
                  "title": "氧浓度传感器 - 116",
                  "content": "氧浓度传感器 - 116",
                  "deviceid": "1615023",
                  "id": 500950
                },
                {
                  "title": "压力传感器 - 116",
                  "content": "压力传感器 - 116",
                  "deviceid": "1616008",
                  "id": 500983
                }
              ]
            },
            {
              "id": "submenu1F-辅助机房 (120)",
              "title": "辅助机房 (120)",
              "submenu": [
                {
                  "title": "压力传感器 - 120",
                  "content": "压力传感器 - 120",
                  "deviceid": "1616003",
                  "id": 500978
                }
              ]
            },
            {
              "id": "submenu1F-办公室 (125)",
              "title": "办公室 (125)",
              "submenu": [
                {
                  "title": "压力传感器 - 125",
                  "content": "压力传感器 - 125",
                  "deviceid": "1616005",
                  "id": 500980
                }
              ]
            },
            {
              "id": "submenu1F-样品处理间 (115)",
              "title": "样品处理间 (115)",
              "submenu": [
                {
                  "title": "压力传感器 - 115",
                  "content": "压力传感器 - 115",
                  "deviceid": "1616007",
                  "id": 500982
                }
              ]
            },
            {
              "id": "submenu1F-样品处理间 (105)",
              "title": "样品处理间 (105)",
              "submenu": [
                {
                  "title": "压力传感器 - 105",
                  "content": "压力传感器 - 105",
                  "deviceid": "1616009",
                  "id": 500984
                }
              ]
            }
          ]
        },
        {
          "id": "menuB1F",
          "title": "B1F",
          "submenu": [
            {
              "id": "submenuB1F-电镜辅助间 (B106)",
              "title": "电镜辅助间 (B106)",
              "submenu": [
                {
                  "title": "温湿度传感器 - B106",
                  "content": "温湿度传感器 - B106",
                  "deviceid": "1611001",
                  "id": 500861
                }
              ]
            },
            {
              "id": "submenuB1F-透射电镜室1 (B154)",
              "title": "透射电镜室1 (B154)",
              "submenu": [
                {
                  "title": "温湿度传感器 - B154",
                  "content": "温湿度传感器 - B154",
                  "deviceid": "1611002",
                  "id": 500862
                },
                {
                  "title": "氧浓度传感器 - B154",
                  "content": "氧浓度传感器 - B154",
                  "deviceid": "1615001",
                  "id": 500928
                }
              ]
            },
            {
              "id": "submenuB1F-电镜辅助间 (B144)",
              "title": "电镜辅助间 (B144)",
              "submenu": [
                {
                  "title": "温湿度传感器 - B144",
                  "content": "温湿度传感器 - B144",
                  "deviceid": "1611004",
                  "id": 500864
                }
              ]
            },
            {
              "id": "submenuB1F-电镜辅助间 (B109)",
              "title": "电镜辅助间 (B109)",
              "submenu": [
                {
                  "title": "温湿度传感器 - B109",
                  "content": "温湿度传感器 - B109",
                  "deviceid": "1611005",
                  "id": 500865
                }
              ]
            },
            {
              "id": "submenuB1F-透射电镜室3 (B156)",
              "title": "透射电镜室3 (B156)",
              "submenu": [
                {
                  "title": "温湿度传感器 - B156",
                  "content": "温湿度传感器 - B156",
                  "deviceid": "1611006",
                  "id": 500866
                },
                {
                  "title": "氧浓度传感器 - B156",
                  "content": "氧浓度传感器 - B156",
                  "deviceid": "1615003",
                  "id": 500930
                }
              ]
            },
            {
              "id": "submenuB1F-透射电镜室4 (B155)",
              "title": "透射电镜室4 (B155)",
              "submenu": [
                {
                  "title": "温湿度传感器 - B155",
                  "content": "温湿度传感器 - B155",
                  "deviceid": "1611007",
                  "id": 500867
                },
                {
                  "title": "氧浓度传感器 - B155",
                  "content": "氧浓度传感器 - B155",
                  "deviceid": "1615004",
                  "id": 500931
                }
              ]
            },
            {
              "id": "submenuB1F-电镜辅助间 (B141)",
              "title": "电镜辅助间 (B141)",
              "submenu": [
                {
                  "title": "温湿度传感器 - B141",
                  "content": "温湿度传感器 - B141",
                  "deviceid": "1611008",
                  "id": 500868
                }
              ]
            },
            {
              "id": "submenuB1F-电镜辅助间 (B110)",
              "title": "电镜辅助间 (B110)",
              "submenu": [
                {
                  "title": "温湿度传感器 - B110",
                  "content": "温湿度传感器 - B110",
                  "deviceid": "1611009",
                  "id": 500869
                }
              ]
            },
            {
              "id": "submenuB1F-透射电镜室5 (B138)",
              "title": "透射电镜室5 (B138)",
              "submenu": [
                {
                  "title": "温湿度传感器 - B138",
                  "content": "温湿度传感器 - B138",
                  "deviceid": "1611010",
                  "id": 500870
                },
                {
                  "title": "氧浓度传感器 - B138",
                  "content": "氧浓度传感器 - B138",
                  "deviceid": "1615005",
                  "id": 500932
                }
              ]
            },
            {
              "id": "submenuB1F-透射电镜室6 (B137)",
              "title": "透射电镜室6 (B137)",
              "submenu": [
                {
                  "title": "温湿度传感器 - B137",
                  "content": "温湿度传感器 - B137",
                  "deviceid": "1611011",
                  "id": 500871
                },
                {
                  "title": "氧浓度传感器 - B137",
                  "content": "氧浓度传感器 - B137",
                  "deviceid": "1615006",
                  "id": 500933
                }
              ]
            },
            {
              "id": "submenuB1F-电镜辅助间 (B140)",
              "title": "电镜辅助间 (B140)",
              "submenu": [
                {
                  "title": "温湿度传感器 - B140",
                  "content": "温湿度传感器 - B140",
                  "deviceid": "1611012",
                  "id": 500872
                }
              ]
            },
            {
              "id": "submenuB1F-电镜制样间1 (B113)",
              "title": "电镜制样间1 (B113)",
              "submenu": [
                {
                  "title": "温湿度传感器 - B113",
                  "content": "温湿度传感器 - B113",
                  "deviceid": "1611013",
                  "id": 500873
                }
              ]
            },
            {
              "id": "submenuB1F-电镜制样间 (B132)",
              "title": "电镜制样间 (B132)",
              "submenu": [
                {
                  "title": "温湿度传感器 - B132",
                  "content": "温湿度传感器 - B132",
                  "deviceid": "1611014",
                  "id": 500874
                }
              ]
            },
            {
              "id": "submenuB1F-电镜制样间 (B130)",
              "title": "电镜制样间 (B130)",
              "submenu": [
                {
                  "title": "温湿度传感器 - B130",
                  "content": "温湿度传感器 - B130",
                  "deviceid": "1611015",
                  "id": 500875
                },
                {
                  "title": "氧浓度传感器 - B130",
                  "content": "氧浓度传感器 - B130",
                  "deviceid": "1615007",
                  "id": 500934
                }
              ]
            },
            {
              "id": "submenuB1F-扫描电镜5 (B117)",
              "title": "扫描电镜5 (B117)",
              "submenu": [
                {
                  "title": "温湿度传感器 - B117",
                  "content": "温湿度传感器 - B117",
                  "deviceid": "1611016",
                  "id": 500876
                },
                {
                  "title": "氧浓度传感器 - B117",
                  "content": "氧浓度传感器 - B117",
                  "deviceid": "1615008",
                  "id": 500935
                }
              ]
            },
            {
              "id": "submenuB1F-电镜辅助间 (B118)",
              "title": "电镜辅助间 (B118)",
              "submenu": [
                {
                  "title": "温湿度传感器 - B118",
                  "content": "温湿度传感器 - B118",
                  "deviceid": "1611017",
                  "id": 500877
                }
              ]
            },
            {
              "id": "submenuB1F-扫面电镜6 (B119)",
              "title": "扫面电镜6 (B119)",
              "submenu": [
                {
                  "title": "温湿度传感器 - B119",
                  "content": "温湿度传感器 - B119",
                  "deviceid": "1611018",
                  "id": 500878
                },
                {
                  "title": "氧浓度传感器 - B119",
                  "content": "氧浓度传感器 - B119",
                  "deviceid": "1615009",
                  "id": 500936
                }
              ]
            },
            {
              "id": "submenuB1F-扫面电镜1 (B116)",
              "title": "扫面电镜1 (B116)",
              "submenu": [
                {
                  "title": "温湿度传感器 - B116",
                  "content": "温湿度传感器 - B116",
                  "deviceid": "1611019",
                  "id": 500879
                },
                {
                  "title": "氧浓度传感器 - B116",
                  "content": "氧浓度传感器 - B116",
                  "deviceid": "1615010",
                  "id": 500937
                }
              ]
            },
            {
              "id": "submenuB1F-电镜辅助间 (B120)",
              "title": "电镜辅助间 (B120)",
              "submenu": [
                {
                  "title": "温湿度传感器 - B120",
                  "content": "温湿度传感器 - B120",
                  "deviceid": "1611020",
                  "id": 500880
                }
              ]
            },
            {
              "id": "submenuB1F-扫面电镜3 (B122)",
              "title": "扫面电镜3 (B122)",
              "submenu": [
                {
                  "title": "温湿度传感器 - B122",
                  "content": "温湿度传感器 - B122",
                  "deviceid": "1611021",
                  "id": 500881
                },
                {
                  "title": "温湿度传感器 - B122",
                  "content": "温湿度传感器 - B122",
                  "deviceid": "1611022",
                  "id": 500882
                },
                {
                  "title": "氧浓度传感器 - B122",
                  "content": "氧浓度传感器 - B122",
                  "deviceid": "1615011",
                  "id": 500938
                },
                {
                  "title": "氧浓度传感器 - B122",
                  "content": "氧浓度传感器 - B122",
                  "deviceid": "1615012",
                  "id": 500939
                }
              ]
            },
            {
              "id": "submenuB1F-电镜辅助间 (B123)",
              "title": "电镜辅助间 (B123)",
              "submenu": [
                {
                  "title": "温湿度传感器 - B123",
                  "content": "温湿度传感器 - B123",
                  "deviceid": "1611023",
                  "id": 500883
                }
              ]
            },
            {
              "id": "submenuB1F-扫面电镜4 (B124)",
              "title": "扫面电镜4 (B124)",
              "submenu": [
                {
                  "title": "温湿度传感器 - B124",
                  "content": "温湿度传感器 - B124",
                  "deviceid": "1611024",
                  "id": 500884
                },
                {
                  "title": "氧浓度传感器 - B124",
                  "content": "氧浓度传感器 - B124",
                  "deviceid": "1615013",
                  "id": 500940
                }
              ]
            },
            {
              "id": "submenuB1F-电镜辅助间 (B145)",
              "title": "电镜辅助间 (B145)",
              "submenu": [
                {
                  "title": "温湿度传感器 - B145",
                  "content": "温湿度传感器 - B145",
                  "deviceid": "1611025",
                  "id": 500885
                }
              ]
            },
            {
              "id": "submenuB1F-扫面电镜7 (B146)",
              "title": "扫面电镜7 (B146)",
              "submenu": [
                {
                  "title": "温湿度传感器 - B146",
                  "content": "温湿度传感器 - B146",
                  "deviceid": "1611026",
                  "id": 500886
                },
                {
                  "title": "氧浓度传感器 - B146",
                  "content": "氧浓度传感器 - B146",
                  "deviceid": "1615014",
                  "id": 500941
                }
              ]
            },
            {
              "id": "submenuB1F-电镜辅助间 (B147)",
              "title": "电镜辅助间 (B147)",
              "submenu": [
                {
                  "title": "温湿度传感器 - B147",
                  "content": "温湿度传感器 - B147",
                  "deviceid": "1611027",
                  "id": 500887
                }
              ]
            },
            {
              "id": "submenuB1F-透射电镜室8 (B148)",
              "title": "透射电镜室8 (B148)",
              "submenu": [
                {
                  "title": "温湿度传感器 - B148",
                  "content": "温湿度传感器 - B148",
                  "deviceid": "1611028",
                  "id": 500888
                },
                {
                  "title": "氧浓度传感器 - B148",
                  "content": "氧浓度传感器 - B148",
                  "deviceid": "1615015",
                  "id": 500942
                }
              ]
            },
            {
              "id": "submenuB1F-透射电镜室7 (B150)",
              "title": "透射电镜室7 (B150)",
              "submenu": [
                {
                  "title": "温湿度传感器 - B150",
                  "content": "温湿度传感器 - B150",
                  "deviceid": "1611029",
                  "id": 500889
                },
                {
                  "title": "氧浓度传感器 - B150",
                  "content": "氧浓度传感器 - B150",
                  "deviceid": "1615016",
                  "id": 500943
                }
              ]
            },
            {
              "id": "submenuB1F-电镜辅助间 (B151)",
              "title": "电镜辅助间 (B151)",
              "submenu": [
                {
                  "title": "温湿度传感器 - B151",
                  "content": "温湿度传感器 - B151",
                  "deviceid": "1611030",
                  "id": 500890
                }
              ]
            },
            {
              "id": "submenuB1F-配电间外",
              "title": "配电间外",
              "submenu": [
                {
                  "title": "温湿度传感器 - 配电间外",
                  "content": "温湿度传感器 - 配电间外",
                  "deviceid": "1611031",
                  "id": 500891
                },
                {
                  "title": "氧浓度传感器 - 配电间外",
                  "content": "氧浓度传感器 - 配电间外",
                  "deviceid": "1615017",
                  "id": 500944
                }
              ]
            },
            {
              "id": "submenuB1F-透射电镜室2 (B153)",
              "title": "透射电镜室2 (B153)",
              "submenu": [
                {
                  "title": "氧浓度传感器 - B153",
                  "content": "氧浓度传感器 - B153",
                  "deviceid": "1615002",
                  "id": 500929
                }
              ]
            }
          ]
        }
      ],
      data: [
        {
          category: "仪器设备",
          items: [
            {
              number: "LAB001",
              nanme: "显微镜",
              pingpai: "品牌X",
              baozhuang: "3楼",
              xiaobaozhuang: "A305",

              qita: "",
            },
            {
              number: "LAB002",
              nanme: "离心机",
              pingpai: "品牌Y",
              baozhuang: "2楼",
              xiaobaozhuang: "B210",

              qita: "",
            },
            {
              number: "LAB003",
              nanme: "培养箱",
              pingpai: "品牌Z",
              baozhuang: "3楼",
              xiaobaozhuang: "A307",

              qita: "",
            },
            {
              number: "LAB004",
              nanme: "天平",
              pingpai: "品牌W",
              baozhuang: "2楼",
              xiaobaozhuang: "B209",

              qita: "",
            },
            {
              number: "LAB005",
              nanme: "烘箱",
              pingpai: "品牌V",
              baozhuang: "4楼",
              xiaobaozhuang: "C401",

              qita: "",
            },
          ],
        },
        {
          category: "计算机和信息化设备",
          items: [
            {
              number: "LAB001",
              nanme: "实验室电脑",
              pingpai: "品牌A",
              baozhuang: "3楼",
              xiaobaozhuang: "A308",

              qita: "",
            },
            {
              number: "LAB002",
              nanme: "数据采集设备",
              pingpai: "品牌B",
              baozhuang: "3楼",
              xiaobaozhuang: "A310",

              qita: "",
            },
            {
              number: "LAB003",
              nanme: "服务器",
              pingpai: "品牌C",
              baozhuang: "1楼",
              xiaobaozhuang: "机房",

              qita: "",
            },
          ],
        },
        {
          category: "办公设备",
          items: [
            {
              number: "LAB001",
              nanme: "打印机",
              pingpai: "品牌D",
              baozhuang: "2楼",
              xiaobaozhuang: "B205",

              qita: "",
            },
            {
              number: "LAB002",
              nanme: "复印机",
              pingpai: "品牌E",
              baozhuang: "2楼",
              xiaobaozhuang: "B206",

              qita: "",
            },
            {
              number: "LAB003",
              nanme: "投影仪",
              pingpai: "品牌F",
              baozhuang: "3楼",
              xiaobaozhuang: "A309",

              qita: "",
            },
          ],
        },
        {
          category: "基础设施",
          items: [
            {
              number: "LAB001",
              nanme: "实验台",
              pingpai: "品牌G",
              baozhuang: "4楼",
              xiaobaozhuang: "C402",

              qita: "",
            },
            {
              number: "LAB002",
              nanme: "通风系统",
              pingpai: "品牌H",
              baozhuang: "5楼",
              xiaobaozhuang: "D501",

              qita: "",
            },
            {
              number: "LAB003",
              nanme: "实验室椅子",
              pingpai: "品牌I",
              baozhuang: "3楼",
              xiaobaozhuang: "A306",

              qita: "",
            },
          ],
        },
      ],
      input: "",
      activeTab: "today",
      listst: [
        {
          name: "广东质检中诚认证有限公司到中广...",
        },
        { name: "材料科学、化学工程及医药研发成..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
        { name: "植酸检测方法及作用" },
        { name: "兔拉检测：行业中芯片造假技术..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
      ],
      showdh: true,
      showdh1: false,
      noAnimation: false,

      tableDataItem: [],
      tableTitle: [
        { key: "" },
        { key: "资产名称" },
        { key: "资产品牌" },
        { key: "楼层" },
        { key: "房间号" },

        { key: "其他说明" },
      ],
      ids: null,
      nhlist: [
        {
          title: "供气压力",
          status: "0.3Mpa",
          unit: "℃",
        },

        {
          title: "供气流量",
          status: "6M3/min",
          unit: "㎡",
        },
        {
          title: "露点温度",
          status: "6℃",
          unit: "℃",
        },
        {
          title: "含氧量",
          status: "6PPM",
          unit: "㎡",
        },
      ],
      warnlist1: [
        {
          type: 1,
          name: "检测到烟雾，可能有着火灾的发生...",
          value: "",
          time: "09:13:59  2023-06-07",
        },
        {
          type: 2,
          name: "检测到烟雾，可能预示着火灾的发生..",
          value: "",
          time: "10:13:18  2023-06-12",
        },
        {
          type: 3,
          name: "实验室内检测到漏水，可能来自冷凝水..",
          value: "",
          time: "10:13:18  2023-06-12",
        },
      ],
      isButton2Active: false,
      status: "巡检中",
      status1: "已完成",
      status2: "待巡检",

      componentTag: "component0",
      sbtitle: "",
      isFirstTime: true,
      cgqname: "传感器",
      detalis: [], // 用于标记是否是第一次调用
      chartData: [],
      dataUnit: "",
      chartOption: {
        title: {
          text: "",
          textStyle: {
            color: "#fff",
          },
        },
        tooltip: {
          trigger: "axis",
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: [],
          axisLabel: {
            color: "#fff",
          },
        },
        yAxis: {
          type: "value",
          name: "",
          axisLabel: {
            color: "#fff",
            formatter: "{value}",
          },
        },
        series: [
          {
            type: "line",
            data: [],
            smooth: true,
            showSymbol: false,
            lineStyle: {
              width: 2,
              color: "#37a2b6",
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "rgba(55, 162, 182, 0.5)",
                },
                {
                  offset: 1,
                  color: "rgba(55, 162, 182, 0.1)",
                },
              ]),
            },
          },
        ],
      },
      chartOption1: {
        title: {
          text: "",
          textStyle: {
            color: "#fff",
          },
        },
        tooltip: {
          trigger: "axis",
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: [],
          axisLabel: {
            color: "#fff",
          },
        },
        yAxis: {
          type: "value",
          name: "",
          axisLabel: {
            color: "#fff",
            formatter: "{value}",
          },
        },
        series: [
          {
            type: "line",
            data: [],
            smooth: true,
            showSymbol: false,
            lineStyle: {
              width: 2,
              color: "#37a2b6",
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "rgba(55, 162, 182, 0.5)",
                },
                {
                  offset: 1,
                  color: "rgba(55, 162, 182, 0.1)",
                },
              ]),
            },
          },
        ],
      },
    };
  },
  // 计算属性类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {},
  // 方法集合
  methods: {
    async fetchProjectSet(type, projectId, parkId, buildId, floorId) {
      console.log(this.getname, type, projectId, parkId, buildId, floorId);
      //从接口拿数据
      try {
        const response = await axios.get(
          "https://api-dh3d-public.3dzhanting.cn:8081/projectSet/all",
          {
            params: {
              type: type,
              projectId: projectId,
              deviceId: "", // 如果不需要 deviceId，可以将其删除或保留为空字符串
              parkId: parkId,
              buildId: buildId,
              loorId: "",
              name: "温湿度传感器,氧浓度传感器,压力传感器",
              roomId: "",
            },
          }
        );
        // this.sblist = response.data.data
        console.log(response.data.data, "处理好的数据");
        // 将楼层排序
        const floorOrder = ["5F", "4F", "3F", "2F", "1F", "B1F"]; // 定义排序顺序
        // 按楼层顺序排序
        const sortedData = response.data.data.sort(
          (a, b) =>
            floorOrder.indexOf(a.floorId) - floorOrder.indexOf(b.floorId)
        );
        const roomMap = {
          B106: "电镜辅助间 (B106)",
          B154: "透射电镜室1 (B154)",
          B153: "透射电镜室2 (B153)",
          B144: "电镜辅助间 (B144)",
          B109: "电镜辅助间 (B109)",
          B156: "透射电镜室3 (B156)",
          B155: "透射电镜室4 (B155)",
          B141: "电镜辅助间 (B141)",
          B110: "电镜辅助间 (B110)",
          B138: "透射电镜室5 (B138)",
          B137: "透射电镜室6 (B137)",
          B140: "电镜辅助间 (B140)",
          B113: "电镜制样间1 (B113)",
          B132: "电镜制样间 (B132)",
          B130: "电镜制样间 (B130)",
          B117: "扫描电镜5 (B117)",
          B118: "电镜辅助间 (B118)",
          B119: "扫面电镜6 (B119)",
          B116: "扫面电镜1 (B116)",
          B120: "电镜辅助间 (B120)",
          B122: "扫面电镜2 (B122)",
          B122: "扫面电镜3 (B122)",
          B123: "电镜辅助间 (B123)",
          B124: "扫面电镜4 (B124)",
          B145: "电镜辅助间 (B145)",
          B146: "扫面电镜7 (B146)",
          B147: "电镜辅助间 (B147)",
          B148: "透射电镜室8 (B148)",
          B150: "透射电镜室7 (B150)",
          B151: "电镜辅助间 (B151)",
          119: "核磁主机房 (119)",
          118: "核磁主机房 (118)",
          115: "样品处理间 (115)",
          116: "仪器室 (116)",
          120: "辅助机房 (120)",
          124: "核磁主机房 (114)",
          127: "核磁中心 (127)",
          125: "办公室 (125)",
          123: "空压机房 (123)",
          110: "能谱仪室 (110)",
          103: "中控室 (103)",
          104: "D射线衍射仪 (104)",
          105: "样品处理间 (105)",
          109: "D射线衍射仪2 (109)",
          204: "通用实验室 (204)",
          205: "通用实验室 (205)",
          218: "样品准备间 (218)",
          219: "汇流排间 (219)",
          220: "液相色谱 (220)",
          222: "样品处理室 (222)",
          223: "气相色谱 (223)",
          225: "光谱仪室 (225)",
          226: "光谱仪室 (226)",
          228: "样品处理间 (228)",
          231: "光谱仪室2 (231)",
          配电间外: "配电间外",
          上空: "上空",
          汇流排间: "汇流排间",
        };
        // 转换数据为所需格式
        const transformedData = sortedData.reduce((result, item) => {
          // 确保 roomId 存在且有效
          if (!item.roomId) return result; // 如果没有 roomId，跳过该条数据
          // 根据 roomId 获取区域（假设区域名在 roomId 的首字母）

          const area = roomMap[item.roomId] || item.roomId;

          // 查找当前楼层是否已存在
          let floorMenu = result.find((floor) => floor.title === item.floorId);

          // 如果楼层不存在，创建新的菜单项
          if (!floorMenu) {
            floorMenu = {
              id: `menu${item.floorId}`,
              title: item.floorId,
              submenu: [],
            };
            result.push(floorMenu);
          }
          // 查找当前楼层下是否已有对应的房间
          let roomSubmenu = floorMenu.submenu.find((sub) => sub.title === area);
          if (!roomSubmenu) {
            roomSubmenu = {
              id: `submenu${item.floorId}-${area}`,
              title: area,
              submenu: [],
            };
            floorMenu.submenu.push(roomSubmenu);
          }

          // 将设备信息添加到对应的房间下
          roomSubmenu.submenu.push({
            title: `${item.name} - ${item.roomId}`,
            content: `${item.name} - ${item.roomId}`,
            deviceid: item.deviceId,
            id: item.id,
          });

          return result;
        }, []);

        console.log(transformedData, "处理好的数据");
        this.menus = transformedData;
        return response.data;
      } catch (error) {
        console.error("Error fetching project set:", error);
        throw error; // 重新抛出错误以便调用者处理
      }

      //从config拿数据
      // 将 name 字符串按逗号分隔为数组
      // const nameArray = this.getname.split(',').map(item => item.trim());
      // console.log(this.alldeviceList, nameArray, parkId, buildId, floorId, 'alldeviceList');
      // // 过滤设备列表，返回符合条件的设备
      // this.sblist = this.alldeviceList.filter(device => {
      //   const buildMatch = buildId == '' || device.buildId == buildId;
      //   const floorMatch = floorId == '' || device.floorId == floorId;
      //   const nameMatch = nameArray.includes(device.name);
      //   return device.parkId == parkId && buildMatch && floorMatch && nameMatch;
      // });

      // console.log('Response data:', this.sblist);
      // this.sendToUE41('shebei', this.sblist);
    },
    changetit(index) {
      this.titactive = index;
      this.isshowwhat = index;
      if (index == 1) {
        this.showdh = false;
        this.showdh1 = true;
        this.noAnimation = true;
      } else {
        this.showdh = true;
        this.showdh1 = false;
        this.noAnimation = false;
      }
    },
    hidedetailsss() {
      this.isshowsss = false;
    },
    closeshow() {
      this.isshowsss = false;
    },
    async setContent(content, index) {
      console.log(content, index, "设置内容");
      this.isshowsss = true;
      this.selectedItem = content.content;
      this.cgqname = content.title;

      // 获取设备数据并更新图表
      try {
        const res = await getDeviceData(content.deviceid);
        if (content.title.includes("温湿度")) {
          const res2 = await getDeviceData(
            content.deviceid,
            content.deviceid + "2"
          );
          const chartData1 = res2.data.data || [];
          const times1 = chartData1.map((item) =>
            item.recordedAt.slice(11, 16)
          );
          const values1 = chartData1.map((item) => item.indication);
          this.chartOption1.xAxis.data = times1;
          this.chartOption1.series[0].data = values1;
          // this.chartOption.yAxis.name = res.data.dataUnit;
          this.chartOption1.title.text = res2.data.name + res2.data.dataUnit;
          if (this.$refs.echarts3) {
            this.$refs.echarts3.setOption(this.chartOption1);
          }
        }

        const res1 = await getDevicedetails(content.deviceid);

        console.log(res1.data.deviceDataBase, "res11");
        if (res.code === 200 && res.data) {
          // 更新图表数据
          console.log(res, "res21");
          const chartData = res.data.data || [];

          const times = chartData.map((item) => item.recordedAt.slice(11, 16));

          const values = chartData.map((item) => item.indication);

          console.log(times, values, "times, values");

          // 更新图表配置
          this.chartOption.xAxis.data = times;
          this.chartOption.series[0].data = values;
          // this.chartOption.yAxis.name = res.data.dataUnit;
          this.chartOption.title.text = res.data.name + res.data.dataUnit;
          // 更新图表配置

          // 如果图表实例存在，更新图表
          if (this.$refs.echarts2) {
            this.$refs.echarts2.setOption(this.chartOption);
          }
        }

        // 设置设备详情信息
        if (content.title.includes("温湿度")) {
          this.detalis = [
            { name: "设备名称", value: content.title },
            ...res1.data.deviceDataBase.map((item) => ({
              name: item.dmName,
              value: `${item.dVal}${item.dDataUnit}`,
            })),
          ];
          this.yccontent =
            "传感器可以测量周围环境的温度，并将其转换为数字信号或模拟信号输出;在温度超过设定范围后触发报警或关闭设备，以保护应用的安全性。";
        } else if (content.title.includes("氧")) {
          this.detalis = [
            { name: "设备名称", value: content.title },
            ...res1.data.deviceDataBase.map((item) => ({
              name: item.dmName,
              value: `${item.dVal}${item.dDataUnit}`,
            })),
          ];
          this.yccontent =
            "氧气传感器可以测量氧气在空气中的浓度,并将数据传输到控制系统,以监测氧气供应是否充足,从而保证系统的正常运行。";
        } else {
          this.detalis = [
            { name: "设备名称", value: content.title },
            ...res1.data.deviceDataBase.map((item) => ({
              name: item.dmName,
              value: `${item.dVal}${item.dDataUnit}`,
            })),
          ];
          this.yccontent =
            "压差传感器是一种用于测量两个物理参量差异的传感器,它可以测量液体或气体流动中两个位置之间的压差。";
        }
      } catch (error) {
        console.error("获取设备数据失败:", error);
      }
      this.selectedIndex = index;
      this.isFirstTime = false;

      if (content.id) {
        this.$emit("seedid", content.id);
      }
    },

    // 切换二级菜单显示/隐藏
    toggleSubMenu(menuId, title, index) {
      this.activeSubmenu = this.activeSubmenu === menuId ? null : menuId;
      this.sbtitle = index;
      this.$emit("seedbuild", "实验楼"); // 触发父组件的事件并传递数据
      this.$emit(
        "seedfloor",
        title,
        "",
        "氧浓度传感器,温湿度传感器,压力传感器",
        true
      ); // 触发父组件的事件并传递数据
      this.$emit("changecheck");
    },
    // 切换三级菜单显示/隐藏
    toggleSubSubMenu(submenuId, index) {
      this.activeSubSubmenu =
        this.activeSubSubmenu === submenuId ? null : submenuId;
      this.selectedIndex = null;
      this.sbtitle1 = index;
    },

    showdetails(item) {
      console.log(item.items);

      this.isshow = true;
      this.tableDataItem = item.items;
    },
    hidedetails() {
      this.isshow = false;
    },
    oc(value) {
      console.log(value, "收到的值");
      this.showdh = value;
    },
  },
  // 生命周期 - 创建完成（可以访问当前this实例）
  created() { },
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.toggleSubMenu(this.menus[1].id, this.menus[1].title, 1)
    this.fetchProjectSet(1, "YIHuaomzuSKUtXFCRYbdqA==", 0, "实验楼", "");
    this.showdh1 = true;
    var that = this;
    // setTimeout(() => {
    //   this.showdh1 = false;
    //   this.noAnimation = false;
    // }, 1000); // 动画持续时间为1秒
    console.log(1222);
    window.addEventListener("message", function (event) {
      //event.data获取传过来的数据
      let sbname;
      // let name = event.data.name;
      console.log(event, 517);
      if (event.data.type == "shebei") {
        console.log(event.data.data.id, "shebeiid");
        console.log(that.menus[that.sbtitle].submenu, "shebei123");
        let result = [];
        let menu = that.menus[that.sbtitle];
        let deviceIdResult = null; // 用于存储设备ID
        let submenuIndexResult = null; // 用于存储submenu索引
        // console.log(menu, 'menu');
        // 遍历所有submenu
        for (let i = 0; i < menu.submenu.length; i++) {
          console.log(menu.submenu[i], 121);
          const submenu = menu.submenu[i];
          deviceIdResult = submenu.id;
          // 遍历submenu中的所有设备
          for (let j = 0; j < submenu.submenu.length; j++) {
            const device = submenu.submenu[j];
            // console.log(device.id, event.data.data.id, 2024);
            // 如果设备ID匹配
            if (device.id == event.data.data.id) {
              submenuIndexResult = j; // 获取submenu的索引
              let idid = device.deviceid;
              console.log(device);
              sbname = device.title;
              // let jk = that.jkdata.find(item => item.deviceid == idid)
              // that.detalis =
              //   [
              //     { name: "设备名称", value: jk.fid + '-摄像头-' + jk.number },
              //     // { name: "监视区域：", value: "3F通风实验室" },
              //     { name: "品牌型号：", value: "天地伟业" },
              //     { name: "IP地址：", value: jk.ip },
              //     { name: "设备类型：", value: "高清枪型摄像机" },
              //   ];
              // 获取设备ID

              break; // 找到后退出
            }
          }

          if (submenuIndexResult !== null) break;
          // 如果找到了设备ID，则退出外层循环
        }
        that.activeSubSubmenu = deviceIdResult;
        that.selectedIndex = submenuIndexResult;
        console.log("sy", deviceIdResult, submenuIndexResult);
        // let title=this.menus[]
        if (sbname.includes("温湿度")) {
          that.detalis = [
            { name: "设备名称", value: sbname },
            // { name: "监视区域：", value: "3F通风实验室" },
            { name: "温度", value: "21.5℃" },
            { name: "湿度", value: "53.8%" },
          ];
          that.yccontent =
            "传感器可以测量周围环境的温度，并将其转换为数字信号或模拟信号输出;在温度超过设定范围后触发报警或关闭设备，以保护应用的安全性。";
        } else if (sbname.includes("氧")) {
          that.detalis = [
            { name: "设备名称", value: sbname },
            // { name: "监视区域：", value: "3F通风实验室" },
            { name: "氧浓度", value: "1Pa" },
          ];
          that.yccontent =
            "氧气传感器可以测量氧气在空气中的浓度,并将数据传输到控制系统,以监测氧气供应是否充足,从而保证系统的正常运行。";
        } else {
          that.detalis = [
            { name: "设备名称", value: sbname },
            // { name: "监视区域：", value: "3F通风实验室" },
            { name: "压差", value: "20%" },
          ];
          that.yccontent =
            "压差传感器是一种用于测量两个物理参量差异的传感器,它可以测量液体或气体流动中两个位置之间的压差。";
        }
        // that.activeSubSubmenu=that.menus[that.sbtitle].submenu
        //           .flatMap(submenuGroup => submenuGroup.submenu)
        //           .find(submenu => submenu.id == event.data.data.id).
      }
    });
  },
  beforeCreate() { }, // 生命周期 - 创建之前
  beforeMount() { }, // 生命周期 - 挂载之前
  beforeUpdate() { }, // 生命周期 - 更新之前
  updated() { }, // 生命周期 - 更新之后
  beforeUnmount() {
    // 在组件销毁之前清除定时器
    console.log(1111);
  },

  unmounted() {
    console.log(2222);
  }, // 生命周期 - 销毁之前
  destroyed() {
    console.log(1221);
  }, // 生命周期 - 销毁完成
  activated() { }, // 如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="less" scoped>
.botbtn {
  position: fixed;
  top: 978px;
  // left:377px;
  left: 543px;
  width: 200px;
  height: 43px;
  background: #022d56;
  border-radius: 8px;
  z-index: 20;
  display: flex;
  align-items: center;
  justify-content: center;

  .btt {
    color: #fff;
    font-size: 17px;
    line-height: 44px;
    width: 88px;
    text-align: center;
    height: 43px;
    cursor: pointer;
  }

  .btt1 {
    color: rgb(8, 207, 241);
    font-size: 17px;
    line-height: 44px;
    width: 88px;
    text-align: center;
    height: 43px;
    cursor: pointer;
  }
}

.container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: stretch;
  height: 1080px;
  text-align: center;

  .sbdetails {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
  }

  .box {
    margin-top: 6px;
    padding-top: 5px;
    margin-bottom: 0.225rem;
    // height: 800px;
    width: 360px;
    // height: 800px;

    overflow-y: scroll;

    /* 设置垂直滚动条 */
    /* 设置滚动条的样式 */
    &::-webkit-scrollbar {
      width: 0.1px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条轨道的样式 */
    &::-webkit-scrollbar-track {
      background-color: #f1f1f1;
      /* 设置滚动条轨道的背景色 */
    }

    /* 设置滚动条滑块的样式 */
    &::-webkit-scrollbar-thumb {
      background-color: #334f6e;
      /* 设置滚动条滑块的背景色 */
    }

    /* 鼠标悬停在滚动条上时的样式 */
    &::-webkit-scrollbar-thumb:hover {
      background-color: #555;
      /* 设置鼠标悬停时滚动条滑块的背景色 */
    }

    .el-input {
      width: 335px;
      height: 39px;
      color: #fff !important;

      ::v-deep .el-input__wrapper {
        background: url("../assets/image/inputss.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        box-shadow: none !important;
      }
    }

    .suosuo {
      position: absolute;
      top: 66px;
      left: 285px;
    }

    .xiaobox {
      margin-top: 20px;
      display: flex;
      align-items: center;

      .siqiu {
        width: 16px;
        height: 16px;
      }

      .shuru {
        display: flex;
        align-items: center;
        padding-left: 10px;
        background: url("../assets/image/shebei12.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 261px;
        height: 32px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 17px;
        color: #ffffff;
      }
    }

    .xiaoboxs {
      cursor: pointer;
      margin-top: 14px;
      display: flex;
      margin-left: 5px;
      align-items: center;

      .nihaowo {
        width: 107px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 15px;
        color: #ffffff;
        display: flex;
        white-space: nowrap;
        /* 不换行 */
        overflow: hidden;
        /* 超出部分隐藏 */
        text-overflow: ellipsis;
        /* 超出部分显示省略号 */
      }

      .siqiu {
        width: 16px;
        height: 16px;
        margin-left: 10px;
        margin-right: 7px;
      }

      .shuru {
        display: flex;
        align-items: center;
        padding-left: 10px;
        background: url("../assets/image/shebei12.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 261px;
        height: 32px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 17px;
        color: #ffffff;
        white-space: nowrap;
        /* 不换行 */
        overflow: hidden;
        /* 超出部分隐藏 */
        text-overflow: ellipsis;
        /* 超出部分显示省略号 */
      }
    }
  }

  .left-panel {
    position: fixed;
    z-index: 1;
    top: 100px;
    left: 22px;
    width: 330px;
    height: 937px;

    background-size: 100% 100%;
    transform: translate(-122%);
    transition: transform 0.5s ease-in-out;
  }

  .left-panel-active {
    transform: translate(0%);
  }

  .left-panel-active1 {
    // transform: translate(0%);
    animation: slideOut 1s ease-in-out forwards;
  }

  @keyframes slideOut {
    100% {
      transform: translateX(0%);
    }

    // 85% {
    //   transform: translateX(-25%);
    // }

    // 65% {
    //   transform: translateX(-15%);
    // }

    // 40% {
    //   transform: translateX(-55%);
    // }

    // 30% {
    //   transform: translateX(-40%);
    // }

    0% {
      transform: translateX(-100%);
    }
  }

  .rtitle {
    margin-top: 16px;
  }

  @keyframes slideIn {
    0% {
      transform: translateX(100%);
    }

    // 30% {
    //   transform: translateX(65%);
    // }

    // 40% {
    //   transform: translateX(40%);
    // }

    // 65% {
    //   transform: translateX(15%);
    // }

    // 85% {
    //   transform: translateX(25%);
    // }

    100% {
      transform: translateX(0%);
    }
  }

  .right-panel {
    position: fixed;
    z-index: 1;
    right: 83px;
    width: 330px;
    top: 100px;
    height: 937px;

    background-size: 100% 100%;
    transform: translate(122%);
    transition: transform 0.5s ease-in-out;

    .jk {
      margin-top: 12px;
      width: 90%;
      height: 200px;
    }

    .box {
      margin-top: 6px;
      margin-bottom: 18px;
      // background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      width: 94%;
      // height: 428px;

      .loudong {
        margin-top: 21px;
        margin-bottom: 25px;
      }

      .wenzi {
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 10px;
        color: #bdecf9;
        text-align: left;
        margin-left: 20px;
        margin-right: 20px;

        .h2biaoti {
          margin-bottom: 15px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 12px;
          color: #00ffff;
        }
      }

      .p {
        text-indent: 2em;
        margin-bottom: 1em;
        letter-spacing: 0.05em;
      }
    }

    .boxxx {
      margin-top: 6px;
      margin-bottom: 18px;
      background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      width: 333px;
      height: 420px;
      overflow-y: scroll;
      /* 设置垂直滚动条 */
      // overflow: hidden;
      /* 设置滚动条的样式 */

      .zengti {
        margin: 10px 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 315px;
        height: 38px;
        gap: 5px;

        .left {
          display: flex;
          align-items: center;
          justify-content: space-evenly;
          width: 84px;
          height: 27px;

          .yuan {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #08f7f7;
          }

          .wenziss {
            display: flex;
            align-items: flex-start;
            flex-direction: column;

            .p1 {
              font-family: Source Han Sans SC;
              font-weight: 500;
              font-size: 10px;
              color: #ffffff;
            }

            .p2 {
              font-family: Source Han Sans SC;
              font-weight: 500;
              font-size: 10px;
              color: #55cff9;
            }
          }
        }

        .right {
          background: url("../assets/image/rightbeij.png");
          background-size: 100% 100%;
          background-repeat: no-repeat;

          width: 217px;
          height: 38px;
          font-family: Source Han Sans SC;
          font-weight: 500;
          font-size: 11px;
          color: #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    .boxxx::-webkit-scrollbar {
      width: 1px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条轨道的样式 */
    .boxxx::-webkit-scrollbar-track {
      background-color: #f1f1f1;
      /* 设置滚动条轨道的背景色 */
    }

    /* 设置滚动条滑块的样式 */
    .boxxx::-webkit-scrollbar-thumb {
      background-color: #013363;
      /* 设置滚动条滑块的背景色 */
    }
  }

  .no-animation {
    transition: none;
  }

  .right-panel-active {
    transform: translate(0%);
    // animation: slideIn 1s ease-in-out ;
  }

  .right-panel-active1 {
    // transform: translate(0%);
    animation: slideIn 1s ease-in-out forwards;
  }

  @keyframes slideIn {
    0% {
      transform: translateX(100%);
    }

    // 30% {
    //   transform: translateX(65%);
    // }

    // 40% {
    //   transform: translateX(40%);
    // }

    // 65% {
    //   transform: translateX(15%);
    // }

    // 85% {
    //   transform: translateX(25%);
    // }

    100% {
      transform: translateX(0%);
    }
  }

  .completed {
    background: #7ad0ff;
  }

  .incomplete {
    background: #ff6041;
  }

  .warning {
    background: #00ffc0;
  }

  .completeds {
    color: #7ad0ff;
  }

  .incompletes {
    color: #ff6041;
  }

  .warnings {
    color: #00ffc0;
  }
}

.ql-center {
  display: flex;
  margin-top: 20px;
  justify-content: space-around;
  margin-top: 14px;

  .ql-Box {
    width: 75px;
    height: 49px;
    border: 1px solid #7ad0ff;
    // opacity: 0.6;
    border-radius: 2px;

    .ql-box1 {
      font-family: Alibaba PuHuiTi;
      font-weight: bold;
      font-size: 16px;
      color: #7ad0ff;
      margin-top: -10px;
    }

    .ql-box {
      display: flex;
      padding-left: 8px;
      padding-right: 9px;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 34px;

      .left_ql {
        width: 49px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #ffffff;

        .yuan {
          width: 7px;
          height: 7px;
          border-radius: 50%;
        }

        .pp {
          color: #fff;
          font-size: 12px;
        }
      }

      img {
        height: 12px;
        width: 7px;
      }
    }
  }
}

.warn1 {
  background: url("../assets/image/warnred.png");
}

.warn2 {
  background: url("../assets/image/warnyellow.png");
}

.warn3 {
  background: url("../assets/image/warngreen.png");
}

.warning12 {
  background-size: 100% 100%;
  // width: 365px;
  height: 47px;

  .info {
    margin-top: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: left;

    margin-left: 50px;

    .info1 {
      display: flex;
      flex-direction: column;

      p:nth-of-type(1) {
        font-size: 13px;
        font-family: Alibaba PuHuiTi;
        font-weight: bold;
      }

      .red {
        color: #ff0000;
      }

      .green {
        color: #00ffcc;
      }

      .yellow {
        color: #ffff00;
      }

      p:nth-of-type(2) {
        font-size: 16px;
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        color: #ffffff;
      }
    }

    .info2 {
      margin-right: 10px;
      font-size: 15px;
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      color: #cffff8;
    }
  }
}

.xxxx {
  position: absolute;
  top: 1%;
  right: 1%;
  width: 25px;
  height: 25px;
  z-index: 99999;
}

/* 菜单容器 */
.menu-container {
  display: flex;
  gap: 20px;
}

.menu {
  height: 760px;
  width: 340px;
  // background-color: #fff;
  // border: 1px solid #ddd;
  border-radius: 4px;
  overflow-y: auto;
  margin-left: 13px;
}

/* 设置滚动条的样式 */
.menu::-webkit-scrollbar {
  width: 3px;
  /* 设置滚动条的宽度 */
}

/* 设置滚动条轨道的样式 */
.menu::-webkit-scrollbar-track {
  background-color: #f1f1f1;
  /* 设置滚动条轨道的背景色 */
}

/* 设置滚动条滑块的样式 */
.menu::-webkit-scrollbar-thumb {
  background-color: #163561;
  /* 设置滚动条滑块的背景色 */
}

/* 鼠标悬停在滚动条上时的样式 */
.menu::-webkit-scrollbar-thumb:hover {
  background-color: #555;
  /* 设置鼠标悬停时滚动条滑块的背景色 */
}

/* 菜单项样式 */
.menu-group {
  margin-top: 14px;
}

.menu-item {
  cursor: pointer;
  background: url("../assets/image/rightbeij.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;

  width: 100%;
  height: 36px;
  font-family: Source Han Sans SC;
  font-weight: 500;
  font-size: 20px;
  color: #ffffff;
  display: flex;
  align-items: center;
  padding-left: 10px;
  // justify-content: center;
}

.menu-item:hover {
  background-color: #f0f0f0 !important;
  color: #00ffc0;
}

.submenu {
  display: block;
  /* 确保子菜单是块级元素 */
  // background-color: #f9f9f9;
  padding-left: 8px;
}

.submenu-items:hover {
  color: #00ffc0;
}

.submenu-item:hover {
  color: #00ffc0;
}

.submenu-items {
  cursor: pointer;
  // background-color:#013363;
  width: 100%;

  font-family: Source Han Sans SC;
  font-weight: 500;
  font-size: 18px;
  color: #ffffff;
  /* 修改 display 属性 */
  display: flex;
  flex-direction: column;
  /* 垂直方向排列 */
  align-items: flex-start;
  padding-left: 10px;
  margin-top: 10px;
}

.submenu-item {
  cursor: pointer;
  background: url("../assets/image/rightbeij.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: 300px !important;
  /* 移除固定高度 */
  /* height: 30px; */
  font-family: Source Han Sans SC;
  font-weight: 500;
  font-size: 15px;
  color: #ffffff;
  /* 修改 display 属性 */
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding-left: 10px;

  margin-top: 10px;
}

.qiuqiu {
  display: flex;
  align-items: center;

  .siqiu {
    width: 16px;
    height: 16px;
    margin-left: 10px;
    margin-right: 7px;
  }
}

.listtype {
  background: url("../assets/image/xuanzhotiemmoon.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 80px;
  height: 26px;
  text-align: center;
  line-height: 26px;

  font-size: 13px;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  color: #fff;
}

.listtypes {
  background: url("../assets/image/xuanzhotiemmoon.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 100px;
  height: 36px;
  text-align: center;
  line-height: 36px;
  cursor: pointer;
  font-size: 13px;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  color: #fff;
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  font-size: 15px;
}

.listtypes {
  background: url("../assets/image/xuanzhotiemmoon.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 100px;
  height: 32px;
  text-align: center;
  line-height: 32px;
  cursor: pointer;
  font-size: 13px;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  color: #fff;
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 190px;
  /* 你可以根据需要调整宽度 */
  font-size: 15px;
}

.hr {
  margin-top: 24px;
  margin-bottom: 25px;
  width: 100%;
  background-color: rgba(36, 101, 138, 1);
  color: rgba(36, 101, 138, 1);
}

.local {
  text-align: left !important;

  font-family: Source Han Sans SC;
  font-weight: bold;
  font-size: 17px;
  color: #ffffff;

  // display: flex;
  // justify-content: space-between;
  .lianxi {
    margin-left: 118px;
  }
}
</style>
