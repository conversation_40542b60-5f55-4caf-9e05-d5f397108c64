<template>
  <div>
    <component :is="componentTag" :tabledata="tabledata" :zengtiimg="zengtiimg" @fatherMethoddd="fatherMethoddd"
      ref="child"></component>
    <div class="container" v-if="isshow">
      <div class="left-panel" :class="{
        'left-panel-active': showdh,
        'no-animation': noAnimation,
        'left-panel-active1': showdh1,
      }">
        <Title class="ltitle1" tit="平台介绍" :isshow="true">
          <div class="zonghe">
            <div class="boxsty" v-for="item in tablelist" :key="item">
              <div class="mianji">
                <img :src="item.img" class="img" alt="" />
                <div class="wenzi">
                  <div class="top">{{ item.name }}</div>
                  <div class="bottom">{{ item.value }}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="gongneng" v-for="item in wenzilist" :key="item">
            <div style="display: flex; align-items: center">
              <div class="yuan"></div>
              <div class="name">{{ item.name }}</div>
            </div>
            <div class="value">{{ item.value }}</div>
          </div>
        </Title>
        <Title class="ltitle1" tit="报警统计" :isshow="true">
          <div class="boxxx">
            <huanxing :warningData="warningStats"></huanxing>
          </div>
        </Title>
        <!-- <Title class="ltitle1" tit="能耗统计" :isshow="true">
          <div class="box">
            <div class="zongheqt">
              <div class="left1">
                <div class="mianji" v-for="item in dianlist" :key="item">
                  <img :src="item.img" class="img" alt="" />
                  <div class="wenzis">
                    <div class="top">12346</div>
                    <div class="bottom">
                      <div style="
                          font-family: Alibaba PuHuiTi;
                          font-weight: 400;
                          font-size: 13px;
                          color: #3ba1f4;
                        ">
                        本日
                      </div>
                      /Kwh
                    </div>
                  </div>
                </div>
              </div>
              <biao1></biao1>
            </div>
          </div>
        </Title> -->
      </div>

      <!-- 弹出层 -->
      <div class="popup-overlay" v-if="showPopup" @click="closePopup">
        <div class="popup-content" @click.stop>
          <div class="popup-header">
            <div class="popup-title">历史预约详情</div>
            <div class="close-btn" @click="closePopup">×</div>
          </div>
          <div class="popup-body" v-loading="isAllLoading" element-loading-text="加载中..."
            element-loading-background="rgba( 28, 37, 56, 0.8)">
            <div class="popup-table">
              <div class="table-header">
                <div class="col-2">仪器名</div>
                <div class="col-2">组织机构</div>
                <div class="col-2">仪器位置</div>
                <div class="col-2">预约时长</div>
                <div class="col-1">预约人</div>
                <div class="col-1">预约状态</div>
              </div>
              <template v-if="tableDatass && tableDatass.length > 0">
                <div class="table-row" v-for="item in tableDatass" :key="item">
                  <div class="col-2" :title="item.name">{{ item.name }}</div>
                  <div class="col-2" :title="item.equipment_group">
                    {{ item.equipment_group }}
                  </div>
                  <div class="col-2" :title="item.equipment_location">
                    {{ item.equipment_location }}
                  </div>
                  <div class="col-2">{{ item.duration1 }}</div>
                  <div class="col-1">{{ item.roomNumber }}</div>
                  <div class="col-1">{{ item.status }}</div>
                </div>
              </template>
              <template v-else>
                <div class="empty-message">暂无预约记录</div>
              </template>
            </div>
            <!-- 分页组件 -->
            <div class="pagination-container">
              <el-pagination :current-page="currentPage" :page-size="pageSize" :page-sizes="pageSizes" :total="total"
                @size-change="handleSizeChange" @current-change="handleCurrentChange"
                layout="total, sizes, prev, pager, next, jumper" background />
            </div>
          </div>
        </div>
      </div>
      <!-- 右侧内容 -->

      <div class="right-panel" :class="{
        'right-panel-active': showdh,
        'no-animation': noAnimation,
        'right-panel-active1': showdh1,
      }">
        <Title1 class="rtitle" tit="今日预约">
          <div class="boxswq" @click="showLargeTable">
            <div class="titleimgs">
              <div class="bgu">
                <div>预约总数</div>
                <div>{{ yytotal }}</div>
              </div>
              <div class="bgu1">
                <div>已完成</div>
                <div>{{ yytotal }}</div>
              </div>
            </div>
            <div class="titless">
              <div class="item1">仪器名</div>
              <div class="item1">预约时长</div>
              <div class="item">预约人</div>
              <div class="item1">预约状态</div>
            </div>
            <div class="titlesscontents" v-loading="isLoading" element-loading-text="加载中..."
              element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.8)">
              <div class="contents" v-for="item in todayReservations" :key="item">
                <div class="item1" :title="item.name">{{ item.name }}</div>
                <div class="item1">{{ item.duration }}</div>
                <div class="item">{{ item.roomNumber }}</div>
                <div class="item1">{{ item.status }}</div>
              </div>
              <div v-if="!todayReservations.length" class="empty-message">
                暂无预约记录
              </div>
            </div>
          </div>
        </Title1>

        <Title1 class="rtitle" tit="仪器状态">
          <div class="huangxing">
            <SystemDete></SystemDete>
            <!-- <SystemDete></SystemDete> -->
          </div>
        </Title1>
        <Title1 class="rtitle" tit="异常跟踪处理" :isshow="true">
          <div class="boxxxs" @click="openbj()">
            <div class="ql-center">
              <div class="ql-Box">
                <div class="ql-box">
                  <div class="left_ql">
                    <div class="yuan status" style="color: #5c9dee"></div>
                    <div class="pp">未修复</div>
                  </div>
                </div>
                <div class="ql-box1" style="color: #b93851">
                  {{ unfixedCount }}
                </div>
              </div>
              <div class="ql-Box">
                <div class="ql-box">
                  <div class="left_ql">
                    <div class="yuan1 status" style="color: #89f6c1"></div>
                    <div class="pp">已修复</div>
                  </div>
                </div>
                <div class="ql-box1 status" style="color: #89f6c1">
                  {{ fixedCount }}
                </div>
              </div>
            </div>
            <div class="unfixed-warnings">
              <!-- 未修复警告列表 -->
              <div v-for="(warning, index) in warningData.unfixed" :key="'unfixed-' + index" class="warning12">
                <div class="info">
                  <div>
                    <div class="zongduan">
                      <div class="yuan" style="background-color: #b93851"></div>
                      <div class="cjhulizhong" style="color: #b93851">
                        未修复
                      </div>
                    </div>
                    <p class="info2">{{ warning.warningCategory }}</p>
                  </div>

                  <div class="info1">
                    <p class="time">{{ formatDate(warning.createdAt) }}</p>
                    <p class="location">{{ warning.errMsg }}</p>
                  </div>
                  <p class="info2">
                    {{ warning.deviceName }}
                  </p>
                </div>
              </div>
            </div>
            <!-- 已修复警告列表 -->
            <!-- <div v-for="(warning, index) in warningData.fixed" :key="'fixed-'+index" class="warning12">
              <div class="info">
                <div class="zongduan">
                  <div class="yuan" style="background-color: #64f8bb"></div>
                  <div class="cjhulizhong" style="color: #64f8bb">已处理</div>
                </div>
                <div class="info1">
                  <p class="time">{{ formatDate(warning.createdAt) }}</p>
                  <p class="location">{{ warning.errMsg }}</p>
                </div>
                <p class="info2" style="color: #64f8bb" @click="openbj()">{{ warning.deviceName }}</p>
              </div>
            </div> -->
          </div>
        </Title1>
      </div>
    </div>
    <table2 @close="closetan" v-if="opentable2"></table2>
    <!-- <table-2 class="table2" @close="closetan" v-if="opentable2"></table-2> -->
    <!-- <div
      class="center_container"
      :class="{
        'right-panel-active11': showdh,
        'no-animation': noAnimation,
        'right-panel-active12': showdh1,
      }"
    >
      <img
        class="btn"
        src="../assets/image/shang.png"
        @click="scrollUp"
        alt="向上"
      />
      <div class="content" ref="content">
        <div
          :class="activef == index ? 'itema' : 'item'"
          v-for="(item, index) in resItems"
          :key="index"
          @click="switchactivef(item, index)"
          @mouseover="hoveredRoom = item"
          @mouseleave="hoveredRoom = null"
        >
          {{
            index === 0
              ? title + "F-" + "整体"
              : title + "F-" + (index < 10 ? "10" + index : "1" + index)
          }}

          <div class="tooltip" v-if="hoveredRoom === item">{{ item.name }}</div>
        </div>
      </div>
      <img
        class="btn"
        src="../assets/image/xia.png"
        @click="scrollDown"
        alt="向下"
      />
    </div>
    <div
      @click="returnhome()"
      class="return"
      :class="{
        'right-panel-active11': showdh,
        'no-animation': noAnimation,
        'right-panel-active12': showdh1,
      }"
    >
      返回
    </div> -->
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import huanxing from "@/components/echarts/huanxing.vue";
import zhexian from "@/components/echarts/zhexian.vue";
import zhexian1 from "@/components/echarts/zhexian1.vue";
import SystemDete from "@/components/echarts/SystemDete.vue";
import echarts1 from "@/components/echarts/bingjifang/echarts3.vue";
import echarts2 from "@/components/echarts/bingjifang/echarts5.vue";
import table2 from "@/components/common/table2.vue";
import shuangxiang from "@/components/echarts/shuangxiang.vue";
import shebei from "@/views/shebei.vue";
import { resourceDeviceList } from "@/api/admin.js";
import biao1 from "../components/echarts/biao1.vue";
import biao1ss from "../components/echarts/biao1ss.vue";
import axios from "axios";
import {
  getDeviceData,
  getDevicedetails,
  getDeviceWarningList,
} from "@/api/device.js";

// resourceDeviceList
export default {
  // import引入的组件需要注入到对象中才能使用
  components: {
    table2,
    huanxing,
    zhexian,
    zhexian1,
    SystemDete,
    echarts1,
    echarts2,
    shuangxiang,
    shebei,
    biao1ss,
    biao1,
  },
  props: ["title", "resItems"],
  data() {
    // 这里存放数据
    return {
      jlURL,
      dstime,
      responseData: null, // 存储返回的数据
      error: null, // 存储错误信息
      todayReservations: [], // 今日预约数据
      allReservations: [], // 所有预约数据
      allTableData: [], // 存储所有数据
      allTableData1: [], // 存储所有数据
      currentPage: 1, // 当前页码
      pageSize: 10, // 每页显示条数
      total: 0, // 总数据条数
      yytotal: 0,
      pageSizes: [10, 20, 50, 100], // 每页显示条数选项
      opentable2: false,
      hoveredRoom: null,
      scrollPosition: 0,
      flag: true,
      localtitle: "",
      dianlist: [
        {
          name: "总用地面积",
          value: "57874.1㎡",
          img: require("../assets/image/ri.png"),
        },
        {
          name: "总建筑面积",
          value: "7802.54㎡",
          img: require("../assets/image/zhou.png"),
        },
        {
          name: "地上建筑面积",
          value: "14085㎡",
          img: require("../assets/image/yue.png"),
        },
      ],
      tablelist: [
        {
          name: "总用地面积",
          value: "4423.8㎡",
          img: require("../assets/image/mianji1.png"),
        },
        {
          name: "总建筑面积",
          value: "16845㎡",
          img: require("../assets/image/mianji2.png"),
        },
        {
          name: "地上建筑面积",
          value: "14085㎡",
          img: require("../assets/image/mianji3.png"),
        },
        {
          name: "地下建筑面积",
          value: "2760㎡",
          img: require("../assets/image/mianji4.png"),
        },
      ],
      wenzilist: [
        {
          name: "平台概述",
          value: "天津大学大型仪器平台是天津大学批准设立的校级公共技术服务平台，聚焦兼顾多学科需求的保障学校基础能力，促进跨平台和交叉新兴学科能力的建设,以'专管共用'的管理模式，为科学研究提供高质量的开放式测试服务，开展仪器设备创新性功能开发与技术研发。",
        },
      ],

      activef: 0,
      isshow: true,
      isactive: 0,
      tabledata: [],
      zengtiimg: "",
      lrdata: [
        {
          title1: "温度",
          title2: "22℃",
          title3: "2022-04-01 12:00:00",
        },
      ],
      deviceTypes: "CQQ11",
      activeTab: "today",
      botlist: [
        { name: "总览", code: "" },
        { name: "设备列表", code: "" },
        {
          name: "环境温湿度",
          code: "CGQ11",
          img: "http://3d.dddtask.cn/engineer-qilei/fengxian/wensiduchuanganqi.png",
        },
        {
          name: "防爆温湿度",
          code: "CGQ10",
          img: "http://3d.dddtask.cn/engineer-qilei/fengxian/fangbao.png",
        },
        {
          name: "冰箱状态",
          code: "LRY193",
          img: "http://3d.dddtask.cn/engineer-qilei/fengxian/bingxiang.png",
        },
        {
          name: "培养箱状态",
          code: "CGQ13",
          img: "http://3d.dddtask.cn/engineer-qilei/fengxian/bingxiang.png",
        },
        {
          name: "乙炔气体",
          code: "CGQ7",
          img: "http://3d.dddtask.cn/engineer-qilei/fengxian/fangbao.png",
        },
        {
          name: "环境CO2",
          code: "CGQ9",
          img: "http://3d.dddtask.cn/engineer-qilei/fengxian/huanjingco2.png",
        },
        {
          name: "环境O2",
          code: "CGQ3",
          img: "http://3d.dddtask.cn/engineer-qilei/fengxian/huanjingco2.png",
        },
        {
          name: "甲烷气体",
          code: "CGQ8",
          img: "http://3d.dddtask.cn/engineer-qilei/fengxian/jiawan.png",
        },
        {
          name: "房间压差",
          code: "CGQ2",
          img: "http://3d.dddtask.cn/engineer-qilei/fengxian/kqyc.png",
        },
      ],
      listst: [
        {
          name: "广东质检中诚认证有限公司到中广...",
        },
        { name: "材料科学、化学工程及医药研发成..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
        { name: "植酸检测方法及作用" },
        { name: "兔拉检测：行业中芯片造假技术..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
      ],
      showdh: true,
      showdh1: false,
      noAnimation: false,
      localTitle: this.title, // 初始化本地数据属性
      nhlist: [
        {
          title: "供气压力",
          status: "0.3Mpa",
          unit: "℃",
        },

        {
          title: "供气流量",
          status: "6M3/min",
          unit: "㎡",
        },
        {
          title: "露点温度",
          status: "6℃",
          unit: "℃",
        },
        {
          title: "含氧量",
          status: "6PPM",
          unit: "㎡",
        },
      ],
      warnlist1: [
        {
          type: 1,
          time: "视频监控报警-3号楼-3F-101",
          value: "",
          name: "2024-06-16   12:34:09",
        },
        {
          type: 2,
          time: "视频监控报警-3号楼-3F-101",
          value: "",
          name: "2024-06-16   12:34:09",
        },
        {
          type: 3,
          name: "2024-06-16   12:34:09",
          value: "",
          time: "视频监控报警-3号楼-3F-101",
        },
      ],
      isButton2Active: false,
      status: "巡检中",
      status1: "已完成",
      status2: "待巡检",
      selectedIndex: 0,
      componentTag: "",
      dectid: "",
      showPopup: false,
      isLoading: false, // 今日预约加载状态
      isAllLoading: false, // 全部预约加载状态
      warningData: {
        unfixed: [],
        fixed: [],
        unfixedtotal: 0,
        fixedtotal: 0,
      },
      baseURL: "https://tjdx.yuankong.org.cn",
      token: localStorage.getItem("token") || "",
      warningStats: [],
    };
  },
  // 计算属性类似于data概念
  computed: {
    formattedTitle() {
      return {
        title: `${this.localTitle}F实验室介绍`,
        img: require(`../assets/img/floor/1Fbig.png`),
      };
    },
    formattedTitle1() {
      return `${this.localTitle}F实验室总览`;
    },
    formattedTitle2() {
      return `实验室${this.localTitle}F环境信息`;
    },
    formattedTitle3() {
      return `实验室${this.localTitle}F设备信息`;
    },
    formattedTitle4() {
      return `实验室${this.localTitle}F事件详情`;
    },
    formatted1Title() {
      return {
        title: `${this.localTitle}实验室介绍`,
        img: require(`../assets/img/floor/${this.title}Fbig.png`),
      };
    },
    formatted1Title1() {
      return `${this.localTitle}实验室总览`;
    },
    formatted1Title2() {
      return `${this.localTitle}环境信息`;
    },
    formatted1Title3() {
      return `${this.localTitle}设备信息`;
    },
    formatted1Title4() {
      return `${this.localTitle}事件详情`;
    },
    unfixedCount() {
      return this.warningData.unfixedtotal;
    },
    fixedCount() {
      return this.warningData.fixedtotal;
    },
  },
  // 监控data中的数据变化
  watch: {
    title(newVal) {
      this.localTitle = newVal;
    },
    resItems(newVal) {
      console.log(newVal);

      // this.resItems = newVal;
    },
  },
  // 方法集合
  methods: {
    // 获取当前日期的00:00:01的时间戳
    getStartOfDayTimestamp() {
      const now = new Date();
      now.setHours(0, 0, 1, 0); // 设置时间为当天的 00:00:01
      return Math.floor(now.getTime() / 1000); // 转换为 Unix 时间戳（秒）
    },

    // 获取当前日期的23:59:59的时间戳
    getEndOfDayTimestamp() {
      const now = new Date();
      now.setHours(23, 59, 59, 0);
      return Math.floor(now.getTime() / 1000);
    },

    // 格式化预约数据
    formatReservationData(item) {
      return {
        name: item.equipment_name,
        date: new Date(item.start * 1000).toLocaleDateString(),
        date1: new Date(item.start * 1000).toLocaleString(),
        duration: `${new Date(item.start * 1000).getHours()}:00-${new Date(
          item.end * 1000
        ).getHours()}:00`,
        duration1: `${new Date(
          item.start * 1000
        ).toLocaleDateString()} ${new Date(
          item.start * 1000
        ).getHours()}:00 - ${new Date(
          item.end * 1000
        ).toLocaleDateString()} ${new Date(item.end * 1000).getHours()}:00`,
        roomNumber: item.user_name,
        status: item.is_using === "1" ? "已预约" : "已预约",
        equipment_group: item.equipment_group || "未设置", // 添加组织机构字段
        equipment_location: item.equipment_location || "未设置", // 添加仪器位置字段
      };
    },

    // 获取今日预约数据
    async fetchTodayReservations() {
      this.isLoading = true;
      const headers = {
        clientid: "5a298e93-158d-4e22-83cf-6ceb62e9b4f1",
        clientsecret: "2c8ec39e-9887-482a-b28b-e64c496b601c",
      };

      const requestBody = {
        method: "gpui/eq_reserv/reservList",
        params: {
          dtstart: this.getStartOfDayTimestamp(),
          dtend: this.getEndOfDayTimestamp(),
          params: {
            location: "58",
            limit: [0, 10000],
          },
        },
      };

      try {
        const response = await axios.post(
          jlURL,
          requestBody,
          { headers }
        );

        this.todayReservations = response.data.response.map(
          this.formatReservationData
        );
        this.yytotal = this.todayReservations.length;
      } catch (err) {
        console.error("获取今日预约数据失败:", err);
        this.error = err;
      } finally {
        this.isLoading = false;
      }
    },

    // 获取所有预约数据
    async fetchAllReservations() {
      this.isAllLoading = true;
      const headers = {
        clientid: "5a298e93-158d-4e22-83cf-6ceb62e9b4f1",
        clientsecret: "2c8ec39e-9887-482a-b28b-e64c496b601c",
      };

      const requestBody = {
        method: "gpui/eq_reserv/reservList",
        params: {
          dtstart: 1704844800,
          dtend: this.getEndOfDayTimestamp(),
          params: {
            location: "58",
            limit: [0, 10000],
          },
        },
      };

      try {
        const response = await axios.post(
          jlURL,
          requestBody,
          { headers }
        );

        // 对数据进行时间倒序排序
        const sortedData = response.data.response.sort(
          (a, b) => b.start - a.start
        );
        this.allReservations = sortedData.map(this.formatReservationData);
        this.total = this.allReservations.length;
        this.handleCurrentChange(1);
      } catch (err) {
        console.error("获取所有预约数据失败:", err);
        this.error = err;
      } finally {
        this.isAllLoading = false;
      }
    },

    // 显示大表格
    async showLargeTable() {
      this.showPopup = true;
      // 只在没有数据或数据过期的情况下重新获取
      if (!this.allReservations.length) {
        await this.fetchAllReservations();
      } else {
        // 如果已有数据，直接更新分页
        this.handleCurrentChange(1);
      }
    },

    closetan() {
      this.opentable2 = false;
    },
    openbj() {
      this.opentable2 = true;
    },
    handleOpenDialog() {
      console.log(1111);
      this.$emit("open-bj");
    },
    scrollUp() {
      const content = this.$refs.content;
      content.scrollTop -= 38; // 每次向上滑动25px
    },
    scrollDown() {
      const content = this.$refs.content;
      content.scrollTop += 38; // 每次向下滑动25px
    },
    returnhome() {
      this.$emit("returnhome");
    },
    async switchactivef(item, index) {
      this.dectid = item.id;
      const res = await resourceDeviceList({
        resourceId: item.id,
        deviceTypes: this.deviceTypes,
      });

      this.tabledata = res.data;

      if (index) {
        this.flag = false;
        this.localTitle = item.roomid;
      } else {
        this.localTitle = this.title;
        this.flag = true;
      }
      console.log(item);
      this.activef = index;
      // this.$emit("childEvent", title, index);
    },
    slideUp() {
      const contentHeight = this.$refs.content.scrollHeight;
      if (this.position > -contentHeight + this.containerHeight) {
        this.position -= this.step;
        this.$refs.content.style.transform = `translateY(${this.position}px)`;
      }
    },
    slideDown() {
      if (this.position < 0) {
        this.position += this.step;
        this.$refs.content.style.transform = `translateY(${this.position}px)`;
      }
    },

    //  this.dectid = item.id;
    //     const res = await resourceDeviceList({
    //       resourceId: item.id,
    //       deviceTypes: this.deviceTypes,
    //     });
    //     console.log(res.data, "qilei");
    //     this.tabledata = res.data;

    async switchTab1(item, index) {
      console.log(item.img);
      this.zengtiimg = item.img;

      this.deviceTypes = item.code;
      const res = await resourceDeviceList({
        resourceId: this.dectid,
        deviceTypes: this.deviceTypes,
      });

      this.tabledata = res.data;

      // this.switchactivef(item, item.code);
      this.isactive = index;
      if (index) {
        this.componentTag = "shebei";
        this.isshow = false;
        this.showdh = true;
        this.showdh1 = false;
      } else {
        this.componentTag = "";
        this.isshow = true;
        this.showdh = false;
        this.showdh1 = true;
      }
    },
    switchTab(tab) {
      this.activeTab = tab;
    },
    qeihuan(index) {
      console.log(index, "123123");
    },

    qiehuanyans(index) {
      console.log(index, "123123");
      this.currentIndex = index;
    },
    getClassForStatus(status) {
      if (status === "告警总数") {
        return "completed";
      } else if (status === "处理完") {
        return "incomplete";
      } else if (status === "未处理") {
        return "warning";
      } else {
        return "default"; // 没有匹配的状态时可以添加一个默认类
      }
    },
    getClassForStatuss(status) {
      if (status === "告警总数") {
        return "completeds";
      } else if (status === "处理完") {
        return "incompletes";
      } else if (status === "未处理") {
        return "warnings";
      } else {
        return "default"; // 没有匹配的状态时可以添加一个默认类
      }
    },
    qiehuanyans(index) {
      console.log(index, "123123");
      this.currentIndex = index;
    },
    oc(value) {
      console.log(value, "floor收到的值");
      this.showdh = value;
    },
    getClassForStatus(status) {
      if (status === "巡检中") {
        return "completed";
      } else if (status === "待巡检") {
        return "incomplete";
      } else if (status === "已完成") {
        return "warning";
      } else {
        return "default"; // 没有匹配的状态时可以添加一个默认类
      }
    },
    getClassForStatuss(status) {
      if (status === "巡检中") {
        return "completeds";
      } else if (status === "待巡检") {
        return "incompletes";
      } else if (status === "已完成") {
        return "warnings";
      } else {
        return "default"; // 没有匹配的状态时可以添加一个默认类
      }
    },
    closePopup() {
      this.showPopup = false;
    },
    // 处理页码改变
    handleCurrentChange(page) {
      this.currentPage = page;
      const start = (page - 1) * this.pageSize;
      const end = start + this.pageSize;
      this.tableDatass = this.allReservations.slice(start, end);
      console.log(this.tableDatass, "tableDatass");
    },

    // 处理每页显示条数改变
    handleSizeChange(size) {
      this.pageSize = size;
      this.handleCurrentChange(1);
    },
    async getWarningList(hasFixed) {
      try {
        // 在关键请求前检查token是否需要刷新
        if (this.$auth && this.$auth.checkAndRefreshToken) {
          await this.$auth.checkAndRefreshToken();
        }

        const response = await getDeviceWarningList({
          hasFixed: hasFixed,
        });

        if (response.code === 200) {
          if (hasFixed === "N") {
            this.warningData.unfixed = response.rows;
            this.warningData.unfixedtotal = response.total;
          } else {
            this.warningData.fixed = response.rows;
            this.warningData.fixedtotal = response.total;
          }
        } else {
          console.error("获取警告数据失败:", response.msg);
          // 只有在明确的认证错误时才清除token并跳转
          if (response.code === 401) {
            localStorage.removeItem("token");
            this.$router.push("/");
          }
        }
      } catch (error) {
        console.error("请求警告数据出错:", error);
        // 请求错误时不要立即清除token，让拦截器处理
      }
    },
    async fetchAllWarningData() {
      await Promise.all([this.getWarningList("N"), this.getWarningList("Y")]);
    },
    formatDate(timestamp) {
      const date = new Date(timestamp);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
        2,
        "0"
      )}-${String(date.getDate()).padStart(2, "0")} ${String(
        date.getHours()
      ).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
    },
    async fetchWarningStats() {
      try {
        const res = await getDeviceWarningList({
          pageSize: 9999,
          currentPage: 1,
          hasFixed: "N",
        });

        if (res.code === 200 && res.rows) {
          // 定义所有可能的报警类型及其阈值（简化后的名称）
          const allWarningTypes = {
            压力: 14,
            氧气: 48,
            温度: 67,
            湿度: 67,
            // '气体泄漏': 50
          };

          // 统计各类型报警数量
          const stats = {};
          // 初始化所有报警类型的计数为0
          Object.keys(allWarningTypes).forEach((type) => {
            stats[type] = {
              total: 0,
              unresolved: 0,
            };
          });

          // 统计实际数据（使用包含匹配）
          res.rows.forEach((item) => {
            // 查找匹配的报警类型（只要包含关键字就匹配）
            const matchedType = Object.keys(allWarningTypes).find(type =>
              item.warningCategory && item.warningCategory.includes(type)
            );

            if (matchedType) {
              stats[matchedType].total++;
              if (item.status === "N") {
                stats[matchedType].unresolved++;
              }
            }
          });

          // 转换为图表所需格式
          this.warningStats = Object.entries(stats).map(
            ([category, count]) => ({
              label: `${category}(${count.total}/${allWarningTypes[category]})`,
              value: count.total,
            })
          );

          console.log(this.warningStats, "报警统计数据");
        }
      } catch (error) {
        console.error("获取报警统计数据失败:", error);
      }
    },
  },
  // 生命周期 - 创建完成（可以访问当前this实例）
  created() {
    this.fetchAllWarningData();
    // 每5分钟刷新一次数据
    setInterval(this.fetchAllWarningData, 5 * 60 * 1000);
  },
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.fetchTodayReservations();
    this.showdh1 = true;
    setTimeout(() => {
      this.showdh1 = false;
      this.noAnimation = false;
    }, 1000);

    // 定时刷新今日预约数据
    setInterval(() => {
      this.fetchTodayReservations();
    }, 1000 * this.dstime);
    ue.interface.setSliderValue = (value) => {
      console.log(value, "ue点击拿到的值");
      if (!isNaN(Number(value.data))) {
        // let did = value.data; // 如果是数字，则赋值
        // const result = this.sblist.filter(item => item.id == did);
        // this.deviceId = result[0].deviceId
        // console.log(this.deviceId, 'ue点击拿到的id');
      }
      // this.deid = JSON.parse(value.data) - 43846
      // console.log(this.deid);
      // if (!isNaN(parseInt(value.data, 10))) {
      //   var dtdata1 = JSON.parse(JSON.stringify(this.dtdata))
      //   console.log(dtdata1);
      //   this.showdet = false
      //   // this.did = dtdata1.find(item => item.id == value.data)?.deviceid;
      //   // console.log(this.did);
      //   var didata = JSON.parse(dtdata1.find(item => item.id == value.data).channelCode);
      //   let data1 = dtdata1.find(item => item.id == value.data)
      //   // this.details = didata
      //   this.bid = data1.bid
      //   this.fid = data1.fid
      //   // this.hlsurl
      //   // this.bm = data1.note
      //   console.log(data1, 1111111);
      //   // this.getCameraData(did)
      // }
    };
    this.fetchWarningStats();
    // 每30秒更新一次数据
    setInterval(() => {
      this.fetchWarningStats();
    }, 30000);
  },
  beforeCreate() { }, // 生命周期 - 创建之前
  beforeMount() { }, // 生命周期 - 挂载之前
  beforeUpdate() { }, // 生命周期 - 更新之前
  updated() { }, // 生命周期 - 更新之后
  beforeUnmount() {
    // 在组件销毁之前清除定时器
    console.log(1111);
  },

  unmounted() {
    console.log(2222);
  }, // 生命周期 - 销毁之前
  destroyed() {
    console.log(1221);
  }, // 生命周期 - 销毁完成
  activated() { }, // 如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="less" scoped>
.table2 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 99999;
}

.return {
  position: fixed;
  right: 373px;
  top: 100px;
  height: 44px;
  width: 46px;
  // overflow: hidden;
  transform: translate(720%);
  transition: transform 0.5s ease-in-out;

  z-index: 999;
  cursor: pointer;
  text-align: center;
  line-height: 67px;
  font-family: Source Han Sans SC;
  font-weight: 400;
  font-size: 11px;
  color: #ffffff;
  background: url("../assets/image/return.png");
  background-size: 100% 100%;
}

.center_container {
  position: fixed;
  right: 359px;
  top: 352px;
  height: 401px;
  width: 70px;
  // overflow: hidden;
  transform: translate(470%);
  transition: transform 0.5s ease-in-out;
  z-index: 999;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: url("../assets/image/louceng.png");
  background-size: 100% 100%;

  .content::-webkit-scrollbar {
    width: 0px;
    display: none;
    /* 设置滚动条的宽度 */
  }

  /* 设置滚动条轨道的样式 */
  .content::-webkit-scrollbar-track {
    background-color: #f1f1f1;
    /* 设置滚动条轨道的背景色 */
  }

  /* 设置滚动条滑块的样式 */
  .content::-webkit-scrollbar-thumb {
    background-color: #888;
    /* 设置滚动条滑块的背景色 */
  }

  /* 鼠标悬停在滚动条上时的样式 */
  .content::-webkit-scrollbar-thumb:hover {
    background-color: #555;
    /* 设置鼠标悬停时滚动条滑块的背景色 */
  }

  .content {
    height: 330px;
    /* 内容区的总高度，视实际内容而定 */
    transition: transform 0.5s ease;
    overflow-y: auto;
    text-align: center;

    /* 设置滚动条的样式 */

    .item {
      cursor: pointer;
      width: 75px;
      height: 25px;
      font-family: YouSheBiaoTiHei;
      font-weight: 400;
      font-size: 14px;
      color: #86a6b7;
      line-height: 25px;
      margin-top: 12px;
    }

    .itema {
      background: url("../assets/image/lcactive.png");
      background-size: 100% 100%;
      cursor: pointer;
      width: 66px;
      height: 25px;
      font-family: YouSheBiaoTiHei;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
      line-height: 25px;
      margin-top: 12px;
    }

    .tooltip {
      position: absolute;
      left: 80%;
      // top: 15px;
      background-color: #1a3867;
      border: 1px solid #7ba6eb;
      color: #fff;
      padding: 5px;
      z-index: 1;
      white-space: nowrap;
      font-size: 12px;
      visibility: hidden;

      opacity: 0;
      transition: opacity 0.5s, visibility 0.5s;
      z-index: 999;
      font-family: Source Han Sans SC;
    }

    .item:hover .tooltip {
      visibility: visible;
      /* 当鼠标悬停时显示 */
      opacity: 1;
    }

    .itema:hover .tooltip {
      visibility: visible;
      /* 当鼠标悬停时显示 */
      opacity: 1;
    }
  }
}

.btn {
  margin-top: 13px;
  width: 27px;
  height: 14px;
  cursor: pointer;
}

.echart2 {
  height: 180px;
}

.bott {
  position: fixed;
  z-index: 1;
  bottom: 4px;
  // left: 6px;
  width: 1920px;
  height: 50px;
  display: flex;
  flex-direction: row;
  cursor: pointer;
  text-align: center;

  .bottit {
    width: 153px;
    height: 45px;
    background: url("../assets/image/bot_b.png");
    background-size: 100% 100%;
    margin-left: 19.496px;
    font-family: Source Han Sans SC;
    font-weight: bold;
    font-size: 17px;
    color: #ffffff;
    line-height: 43px;
    cursor: pointer;
  }

  .bottit1 {
    width: 153px;
    height: 69px;
    background: url("../assets/image/bot_a.png");
    background-size: 100% 100%;
    margin-left: 19.496px;
    font-family: Source Han Sans SC;
    font-weight: bold;
    font-size: 17px;
    color: #ffffff;
    line-height: 87px;
    cursor: pointer;
    margin-top: -23px;
  }
}

.container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: stretch;
  height: 1080px;
  text-align: center;

  .left-panel {
    position: fixed;
    z-index: 1;
    top: 75px;
    left: 22px;
    width: 387px;
    height: 937px;
    background-size: 100% 100%;
    transform: translate(-122%);
    transition: transform 0.5s ease-in-out;

    .box {
      // margin-top: 6px;
      margin-bottom: 18px;
      // background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      // width: 330px;
      // height: 404px;

      .loudong {
        margin-top: 21px;
        margin-bottom: 25px;
        width: 296px;
        height: 178px;
      }

      .wenzi {
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 10px;
        color: #bdecf9;
        text-align: left;
        margin-left: 20px;
        margin-right: 20px;
      }

      .p {
        text-indent: 2em;
        margin-bottom: 1em;
        letter-spacing: 0.05em;
      }
    }
  }

  .left-panel-active {
    transform: translate(0%);
  }

  .left-panel-active1 {
    // transform: translate(0%);
    animation: slideOut 1s ease-in-out forwards;
  }

  @keyframes slideOut {
    100% {
      transform: translateX(0%);
    }

    // 85% {
    //   transform: translateX(-25%);
    // }

    // 65% {
    //   transform: translateX(-15%);
    // }

    // 40% {
    //   transform: translateX(-55%);
    // }

    // 30% {
    //   transform: translateX(-40%);
    // }

    0% {
      transform: translateX(-100%);
    }
  }

  .rtitle {
    margin-top: 16px;
  }

  .ltitle1 {
    margin-top: 16px;
  }

  .right-panel {
    position: fixed;
    z-index: 1;
    right: 22px;
    width: 387px;
    top: 75px;
    height: 937px;

    background-size: 100% 100%;
    transform: translate(122%);
    transition: transform 0.5s ease-in-out;

    .box {
      // margin-top: 6px;
      margin-bottom: 18px;
      // background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
      font-family: Source Han Sans SC;
      font-weight: 400;
      font-size: 12px;
      color: #ffffff;
      width: 330px;
      height: 224px;

      .titlest {
        display: flex;

        // shiyansimg.png
        .itm {
          cursor: pointer;
          margin: 16px 9px 0 10px;
          background: url("../assets/image/shiyansimg.png");
          background-size: 100% 100%;
          background-repeat: no-repeat;
          width: 100px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .itms {
          background: url("../assets/image/xuanzexuanzhong.png") !important;
          background-size: 100% 100%;
          background-repeat: no-repeat;
          width: 100px;
          height: 41px !important;
          padding-bottom: 10px;
        }
      }

      .contentss {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        justify-content: space-around;
        align-items: center;

        .itm {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 112px;
          height: 70px;
          background: url("../assets/image/wendupng.png");
          background-size: 100% 100%;
          background-repeat: no-repeat;
          font-family: DIN;
          font-weight: bold;
          font-size: 22px;
          color: #ffffff;

          .danwei {
            font-family: DIN;
            font-weight: bold;
            font-size: 12px;
            color: #ffffff;
          }
        }

        .wendyu {
          font-family: Source Han Sans SC;
          font-weight: 400;
          font-size: 13px;
          color: #ffffff;
          margin-top: -7px;
        }
      }

      .loudong {
        margin-top: 21px;
        margin-bottom: 25px;
        width: 296px;
        height: 178px;
      }
    }

    .boxxxs {
      margin-left: -10px;
      margin-top: 1px;
      margin-bottom: 18px;
      // background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      width: 366px;
      cursor: pointer;
      // height: 254px;
    }
  }

  .boxxx {
    // margin-top: 6px;
    margin-bottom: 18px;
    // background: url("../assets/image/zuoshang1.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;

    width: 350px;
    height: 284px;
  }

  .no-animation {
    transition: none;
  }

  .right-panel-active {
    transform: translate(0%);
    // animation: slideIn 1s ease-in-out ;
  }

  .right-panel-active1 {
    // transform: translate(0%);
    animation: slideIn 1s ease-in-out forwards;
  }

  .right-panel-active11 {
    transform: translate(0%) !important;
    // animation: slideIn 1s ease-in-out ;
  }

  .right-panel-active12 {
    // transform: translate(0%);
    animation: slideIn 1s ease-in-out forwards !important;
  }

  @keyframes slideIn {
    0% {
      transform: translateX(100%);
    }

    // 30% {
    //   transform: translateX(65%);
    // }

    // 40% {
    //   transform: translateX(40%);
    // }

    // 65% {
    //   transform: translateX(15%);
    // }

    // 85% {
    //   transform: translateX(25%);
    // }

    100% {
      transform: translateX(0%);
    }
  }

  .completed {
    background: #7ad0ff;
  }

  .incomplete {
    background: #ff6041;
  }

  .warning {
    background: #00ffc0;
  }

  .completeds {
    color: #7ad0ff;
  }

  .incompletes {
    color: #ff6041;
  }

  .warnings {
    color: #00ffc0;
  }
}

.ql-center {
  display: flex;
  // margin-top: 20px;
  justify-content: space-around;
  margin-top: 4px;
  margin-bottom: 4px;

  .ql-Box {
    width: 46%;
    height: 49px;
    border: 1px solid #7ad0ff;
    // opacity: 0.6;
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;

    .ql-box1 {
      font-family: Alibaba PuHuiTi;
      font-weight: bold;
      font-size: 19px;
      color: #7ad0ff;
    }

    .ql-box {
      display: flex;
      // padding-left: 23px;
      padding-right: 9px;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      // width: 100%;
      height: 24px;

      .left_ql {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #ffffff;

        .yuan {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: #b93851;
          margin-right: 5px;
        }

        .yuan1 {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: #84edc3;
          margin-right: 5px;
        }

        .pp {
          margin-left: 5px;
          color: #fff;
          font-size: 18px;
        }
      }

      img {
        height: 12px;
        width: 8px;
      }
    }
  }
}

.warn1 {
  // background: url("../assets/image/warnred.png");
}

.warn2 {
  // background: url("../assets/image/warnyellow.png");
}

.warn3 {
  // background: url("../assets/image/warngreen.png");
}

.unfixed-warnings {
  height: 180px;
  overflow-y: auto;
}

.warning12 {
  background-size: 100% 100%;
  height: 47px;
  margin-bottom: 8px;

  .info {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    text-align: left;
    font-size: 13px;
    padding: 8px 12px;
    background: rgba(25, 37, 60, 0.1);
    border-radius: 4px;

    .zongduan {
      display: flex;
      align-items: center;
      min-width: 80px;

      .yuan {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 6px;
      }

      .cjhulizhong {
        font-family: Microsoft YaHei;
        font-weight: bold;
        font-size: 14px;
      }
    }

    .info1 {
      flex: 1;
      // margin: 0 12px;

      .time {
        font-family: Microsoft YaHei;
        font-size: 14px;
        color: #ffffff;
        margin-bottom: 4px;
      }

      .location {
        font-family: Microsoft YaHei;
        font-size: 14px;
        color: #ffffff;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .info2 {
      cursor: pointer;
      font-size: 14px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #b93851;
      // white-space: nowrap;
      margin-left: 10px;
    }
  }
}

.zonghe {
  // margin-bottom: 10px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;

  .boxsty {
    width: 50%;
    margin-top: 12px;

    .mianji {
      display: flex;
      align-items: center;

      .img {
        width: 50px;
        height: 49px;
      }

      .wenzi {
        text-align: left;
        margin-left: 5px;

        .top {
          // margin-bottom: 9px;
          font-family: Alibaba PuHuiTi;
          font-weight: 500;
          font-size: 20px;
          color: #ffffff;
        }

        .bottom {
          font-family: Source Han Sans SC;
          font-weight: 500;
          font-size: 21px;
          color: #59ffc4;
        }
      }
    }
  }
}

.gongneng {
  margin-top: 12px;

  display: flex;
  flex-direction: column;
  // align-items: center;
  // font-family: Source Han Sans SC;
  font-family: Alibaba PuHuiTi;
  // font-weight: bold;
  font-size: 22px;
  color: #59ffc4;
  text-align: left;

  .yuan {
    margin-right: 7px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: #85fdca;
  }

  .value {
    font-family: Alibaba PuHuiTi;
    font-weight: 500;
    font-size: 20px;
    color: #fff;
    width: 100%;
    margin-right: 3px;
    text-indent: 40px;
  }

  .name {
    // width: 58px;
    font-size: 22px;
  }
}

.zongheqt {
  .left1 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-right: 20px;
    margin-top: 7px;

    .mianji {
      background: url("../assets/image/zengfangti.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      width: 106px;
      height: 58px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .img {
      width: 50px;
      height: 49px;
    }

    .wenzis {
      .top {
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        font-size: 18px;
        color: #ffffff;
      }

      .bottom {
        display: flex;
        align-items: flex-end;
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        font-size: 13px;
        color: #fff;
        margin-left: 7px;
      }
    }
  }
}

.boxswq {
  width: 365px;
  height: 242px;
}

.huangxing {
  width: 359px;
  height: 238px;
}

.cjhulizhong {
  font-family: Microsoft YaHei;
  font-weight: bold;
  font-size: 14px;
  color: #64f8bb;
  margin-left: 8px;
}

.yuan {
  width: 10px;
  height: 10px;
  background-color: #518acd;
  border-radius: 50%;
}

.zongduan {
  display: flex;
  align-items: center;
  font-size: 13px;
}

.titleimgs {
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-right: 10px;

  .bgu {
    background-color: #95871cbf !important;

    // background: url("../assets/image/titlessimg.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 171px;
    height: 38px;
    font-family: Alibaba PuHuiTi;
    font-weight: 400;
    font-size: 22px;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 12px 0 14px;
  }

  .bgu1 {
    background-color: rgb(28, 128, 149) !important;

    // background: url("../assets/image/titlessimg.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 171px;
    height: 38px;
    font-family: Alibaba PuHuiTi;
    font-weight: 400;
    font-size: 22px;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 12px 0 14px;
  }
}

.titlesscontents {
  overflow: auto;
  height: 154px;
}

/* 设置滚动条的样式 */
.titlesscontents::-webkit-scrollbar {
  width: 5px;
  /* 设置滚动条的宽度 */
}

/* 设置滚动条轨道的样式 */
.titlesscontents::-webkit-scrollbar-track {
  background-color: #454f5d;
  /* 设置滚动条轨道的背景色 */
}

/* 设置滚动条的样式 */
.unfixed-warnings::-webkit-scrollbar {
  width: 5px;
  /* 设置滚动条的宽度 */
}

/* 设置滚动条轨道的样式 */
.unfixed-warnings::-webkit-scrollbar-track {
  background-color: #454f5d;
  /* 设置滚动条轨道的背景色 */
}

/* 设置滚动条滑块的样式 */
.unfixed-warnings::-webkit-scrollbar-thumb {
  background-color: #f1f1f1;
  /* 设置滚动条滑块的背景色 */
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  padding: 10px;
}

:deep(.el-pagination) {

  .el-pagination__total,
  .el-pagination__sizes,
  .el-pagination__jump,
  .btn-prev,
  .btn-next,
  .el-pager li {
    background-color: transparent;
    color: #fff;
  }

  .el-pagination__total,
  .el-pagination__jump {
    color: #fff;
  }

  .el-select .el-input .el-input__inner {
    color: #fff;
    background-color: transparent;
  }

  .el-pager li.active {
    background-color: #409eff;
    color: #fff;
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
}

:deep(.el-loading-spinner) {
  .el-loading-text {
    color: #fff;
    margin: 3px 0;
    font-size: 14px;
  }

  .circular {
    .path {
      stroke: #3ba1f4;
    }
  }
}

/* 设置滚动条滑块的样式 */
.titlesscontents::-webkit-scrollbar-thumb {
  background-color: #f1f1f1;
  /* 设置滚动条滑块的背景色 */
}

.titless {
  margin-right: 10px;
  width: 96%;
  background: rgba(25, 37, 60, 0.5);
  height: 32px;
  margin-top: 8px;
  display: flex;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  font-size: 17px;
  color: #40d7ff;
  display: flex;
  align-items: center;
  justify-content: space-around;

  .item {
    width: 100%;
    flex: 1.1;
  }

  .item1 {
    width: 100%;
    flex: 1.9;
  }
}

.contents {
  border-bottom: 1px solid #3b5471;
  margin-right: 10px;
  width: 96%;
  background: rgba(45, 58, 79, 0.2);
  height: 32px;
  display: flex;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  font-size: 15px;
  color: #fff;
  display: flex;
  align-items: center;

  .item {
    width: 100%;
    flex: 1;
  }

  .item1 {
    width: 100%;
    flex: 1.9;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    position: relative;
    cursor: pointer;

    &:hover::after {
      content: attr(title);
      position: absolute;
      left: 0;
      top: 100%;
      background: rgba(0, 0, 0, 0.8);
      color: #fff;
      padding: 4px 8px;
      border-radius: 4px;
      z-index: 999;
      white-space: normal;
    }
  }
}

.contents:nth-child(odd) {
  background: rgba(46, 61, 83, 0.4);
}

.contents:nth-child(even) {
  background: rgba(37, 50, 69, 0.2);
}

.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.popup-content {
  background: rgba(25, 37, 60, 0.95);
  border: 1px solid #3ba1f4;
  border-radius: 8px;
  width: 80%;
  max-width: 1000px;
  max-height: 80vh;
  overflow-y: auto;
  padding: 20px;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #3ba1f4;
}

.popup-title {
  font-family: Alibaba PuHuiTi;
  font-size: 24px;
  color: #40d7ff;
}

.close-btn {
  font-size: 28px;
  color: #fff;
  cursor: pointer;
  padding: 0 10px;

  &:hover {
    color: #40d7ff;
  }
}

.popup-table {
  .table-header {
    display: flex;
    background: rgba(25, 37, 60, 0.8);
    padding: 12px;
    color: #40d7ff;
    font-family: Alibaba PuHuiTi;
    font-size: 20px;

    .col-1 {
      flex: 1;
      padding: 0 2px;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .col-2 {
      flex: 2.3;
      padding: 0 2px;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .table-row {
    display: flex;
    padding: 12px;
    font-size: 12px;
    border-bottom: 1px solid rgba(59, 161, 244, 0.2);
    color: #fff;
    font-family: Alibaba PuHuiTi;

    &:hover {
      background: rgba(59, 161, 244, 0.1);
    }

    .col-1 {
      flex: 1;
      padding: 0 2px;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .col-2 {
      flex: 2.3;
      padding: 0 2px;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  padding: 10px;
}

:deep(.el-pagination) {

  .el-pagination__total,
  .el-pagination__sizes,
  .el-pagination__jump {
    color: #fff !important;
  }

  &.is-background {

    .btn-prev,
    .btn-next,
    .el-pager li {
      background-color: rgba(25, 37, 60, 0.8) !important;
      color: #fff !important;
      border: 1px solid #3ba1f4;
      margin: 0 3px;

      &:hover {
        color: #409eff !important;
        background-color: rgba(37, 50, 69, 0.4) !important;
      }

      &.is-active {
        background-color: #409eff !important;
        color: #fff !important;
        border-color: #409eff;
      }

      &:disabled {
        background-color: rgba(25, 37, 60, 0.4) !important;
        color: #606266 !important;
      }
    }
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
}

:deep(.el-loading-spinner) {
  .el-loading-text {
    color: #fff;
    margin: 3px 0;
    font-size: 14px;
  }

  .circular {
    .path {
      stroke: #3ba1f4;
    }
  }
}
</style>
