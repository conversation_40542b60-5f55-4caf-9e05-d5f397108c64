<template>
  <div>
    <!-- <component :is="componentTag" @fatherMethoddd="fatherMethoddd"></component> -->
    <component :is="componentTag" @fatherMethoddd="fatherMethoddd" v-if="isshowwhat"></component>
    <div class="botbtn">
      <div v-for="(item, index) in changeTitle" :key="index" :class="titactive == index ? 'btt1' : 'btt'"
        @click="changetit(index)">
        {{ item }}
      </div>
    </div>
    <tedai :ids="ids" :selectedItem="selectedItem" class="sbdetails" :zengtiimg="zengtiimg" v-if="false"
      @hidedetails="hidedetailsss"></tedai>
    <biaoGesss v-if="isshow" @hidedetails="hidedetails" :tableTitle="tableTitle" :tableDataItem="tableDataItem">
    </biaoGesss>
    <div class="container" v-if="!isshowwhat">
      <div class="left-panel" :class="{
        'left-panel-active': showdh,
        'no-animation': noAnimation,
        'left-panel-active1': showdh1,
      }">
        <Title2 class="ltitle1" tit="视频监控">
          <div class="box">
            <div>
              <el-input class="el-input" v-model="input" placeholder="请输入内容"></el-input>
            </div>
            <!-- <div class="xiaobox">
              <img class="siqiu" src="../assets/image/shixinqiu.png" alt="" />
              <div class="shuru">全部设备</div>
            </div> -->
            <div class="menu">
              <div v-for="(menu, index) in menus" :key="index" class="menu-group">
                <div :style="{ color: activeSubmenu == menu.id ? '#00ffc0' : '' }" class="menu-item"
                  @click="toggleSubMenu(menu.id, menu.title, index)">
                  {{ menu.title }}
                </div>
                <div v-show="activeSubmenu === menu.id" class="submenu">
                  <div v-for="(item, subIndex) in menu.submenu" :style="{
                    color: activeSubSubmenu === item.id ? '#00ffc0' : '',
                  }" :key="subIndex" class="submenu-items">
                    <div class="bq" @click="toggleSubSubMenu(item.id, index)">
                      {{ item.title }}
                    </div>
                    <div v-show="activeSubSubmenu === item.id" class="submenu">
                      <div v-for="(subItem, thirdIndex) in item.submenu" :key="thirdIndex" :style="{
                        color: selectedIndex == thirdIndex ? '#00ffc0' : '',
                      }" class="submenu-item" @click="setContent(subItem, thirdIndex)">
                        {{ subItem.title }}
                        <!-- <div class="listtype">使用中</div> -->
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- shebei12.png -->
        </Title2>
      </div>
      <!-- shebeibgc.png -->
      <!-- 右侧内容 -->
      <!-- 右侧内容 -->

      <div v-show="isshowsss && detalis.length > 0" class="right-panel" :class="{
        'right-panel-active': showdh,
        'no-animation': noAnimation,
        'right-panel-active1': showdh1,
      }">
        <Title3 tit="视频监控详情">
          <div class="box">
            <div class="xiaoboxs" v-for="item in detalis" :key="item">
              <img class="siqiu" src="../assets/image/kongxin.png" alt="" />
              <div class="nihaowo">{{ item.name }}</div>
              <div class="shuru">{{ item.value }}</div>
            </div>
          </div>
          <!-- <img  src="../assets/jiankong.png" alt="" /> -->
          <div class="jk-container" @click="showLargeIframe">
            <!-- {{ jkindex }} -->
            <iframe class="jk" :key="jkindex" :src="`http://*************:9082/?s=${jkindex}&m=TD`"
              frameborder="0"></iframe>
          </div>
        </Title3>
      </div>
    </div>

    <!-- Large iframe overlay -->
    <div class="large-iframe-overlay" v-if="showIframe" @click="hideLargeIframe">
      <div class="large-iframe-container" @click.stop>
        <iframe :key="jkindex" class="large-iframe" :src="`http://*************:9082/?s=${jkindex}&m=TD`"
          frameborder="0"></iframe>
        <div class="close-button" @click="hideLargeIframe">×</div>
      </div>
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import huanxing from "@/components/echarts/huanxing.vue";
import zhexian from "@/components/echarts/zhexian.vue";
import zhexian1 from "@/components/echarts/zhexian1.vue";
import SystemDete from "@/components/echarts/SystemDete.vue";
import echarts1 from "@/components/echarts/bingjifang/echarts3.vue";
import tedai from "@/components/common/tedais.vue";
import biaoGesss from "@/components/common/biaoGesss.vue";
import shuangxiang from "@/components/echarts/shuangxiang.vue";
import component0 from "@/views/tongji/shipin.vue";
import component2 from "@/views/tongji/tdjk.vue";
import component3 from "@/views/tongji/hkjk.vue";
import VideoPlayer from "@/components/VideoPlayer.vue";
import axios from "axios";
// import 'video.js/dist/video-js.css';
// import videojs from 'video.js';
// import 'videojs-contrib-hls';
// import details from "@/components/common/details.vue";
export default {
  // import引入的组件需要注入到对象中才能使用
  components: {
    VideoPlayer,
    tedai,
    huanxing,
    zhexian,
    zhexian1,
    SystemDete,
    echarts1,
    shuangxiang,
    biaoGesss,
    component0,
    component2,
    component3,
  },
  props: ["tabledata", "zengtiimg"],
  //  cgqlistss: [
  //       { name: "湿度范围：", value: "5%RH~100%RH（无结露）" },
  //       { name: "湿度分辨率：", value: "0.04%" },
  //       { name: "IP地址：", value: "±3%RH（20%RH~80%RH）" },
  //       { name: "湿度精度：", value: "-40℃~+125℃" },
  //       { name: "温度分辨率：", value: "0.01℃" },
  //       { name: "温度精度：", value: "±0.5℃（10℃~60℃）" },
  //     ],

  data() {
    // 这里存放数据
    return {
      isshowwhat: true,
      isshowwhat1: true,
      isshowsss: false,
      titactive: 0,
      changeTitle: ["数据统计", "数据列表"],
      Title: "新风监控",
      isshowsss: false,
      activeSubmenu: null, // 当前激活的二级菜单
      activeyj: null, // 当前激活的一级菜单
      activeSubSubmenu: null, // 当前激活的三级菜单
      activeContent: null, // 当前显示的内容
      isshow: false,
      selectedIndex: null,
      selectedItem: null,
      xxxx: false,
      cgqlist: [
        { name: "设备名称", value: "高清半球型摄像机C101" },
        { name: "监视区域：", value: "1#行政楼走廊" },
        { name: "品牌型号：", value: "海康/DS-2CD7205E-SH" },
        { name: "IP地址：", value: "************" },
        { name: "设备类型：", value: "高清半球型摄像机" },
      ],
      menus: [
        {
          "id": "menu5F",
          "title": "5F",
          "submenu": [
            {
              "id": "submenu5F-A",
              "title": "A区",
              "submenu": [
                {
                  "title": "摄像头-A5-JK01",
                  "content": "摄像头-A5-JK01",
                  "deviceid": "3550152",
                  "id": "500745"
                },
                {
                  "title": "摄像头-A5-JK02",
                  "content": "摄像头-A5-JK02",
                  "deviceid": "3550153",
                  "id": "500746"
                },
                {
                  "title": "摄像头-A5-JK03",
                  "content": "摄像头-A5-JK03",
                  "deviceid": "3550154",
                  "id": "500747"
                },
                {
                  "title": "摄像头-A5-JK04",
                  "content": "摄像头-A5-JK04",
                  "deviceid": "3550155",
                  "id": "500748"
                },
                {
                  "title": "摄像头-A5-JK05",
                  "content": "摄像头-A5-JK05",
                  "deviceid": "3550156",
                  "id": "500749"
                },
                {
                  "title": "摄像头-A5-JK06",
                  "content": "摄像头-A5-JK06",
                  "deviceid": "3550157",
                  "id": "500750"
                }
              ]
            },
            {
              "id": "submenu5F-C",
              "title": "C区",
              "submenu": [
                {
                  "title": "摄像头-C5-JK01",
                  "content": "摄像头-C5-JK01",
                  "deviceid": "3550158",
                  "id": "500751"
                },
                {
                  "title": "摄像头-C5-JK02",
                  "content": "摄像头-C5-JK02",
                  "deviceid": "3550159",
                  "id": "500752"
                }
              ]
            },
            {
              "id": "submenu5F-D",
              "title": "D区",
              "submenu": [
                {
                  "title": "摄像头-DTJK01",
                  "content": "摄像头-DTJK01",
                  "deviceid": "3550160",
                  "id": "500753"
                },
                {
                  "title": "摄像头-DTJK02",
                  "content": "摄像头-DTJK02",
                  "deviceid": "3550161",
                  "id": "500754"
                }
              ]
            },
            {
              "id": "submenu5F-E",
              "title": "E区",
              "submenu": [
                {
                  "title": "摄像头-E5-JK01",
                  "content": "摄像头-E5-JK01",
                  "deviceid": "3550162",
                  "id": "500755"
                },
                {
                  "title": "摄像头-E5-JK02",
                  "content": "摄像头-E5-JK02",
                  "deviceid": "3550163",
                  "id": "500756"
                },
                {
                  "title": "摄像头-E5-JK03",
                  "content": "摄像头-E5-JK03",
                  "deviceid": "3550164",
                  "id": "500757"
                },
                {
                  "title": "摄像头-E5-JK04",
                  "content": "摄像头-E5-JK04",
                  "deviceid": "3550165",
                  "id": "500758"
                }
              ]
            }
          ]
        },
        {
          "id": "menu4F",
          "title": "4F",
          "submenu": [
            {
              "id": "submenu4F-A",
              "title": "A区",
              "submenu": [
                {
                  "title": "摄像头-A4-JK01",
                  "content": "摄像头-A4-JK01",
                  "deviceid": "3550133",
                  "id": "500726"
                },
                {
                  "title": "摄像头-A4-JK02",
                  "content": "摄像头-A4-JK02",
                  "deviceid": "3550134",
                  "id": "500727"
                },
                {
                  "title": "摄像头-A4-JK03",
                  "content": "摄像头-A4-JK03",
                  "deviceid": "3550135",
                  "id": "500728"
                },
                {
                  "title": "摄像头-A4-JK04",
                  "content": "摄像头-A4-JK04",
                  "deviceid": "3550136",
                  "id": "500729"
                },
                {
                  "title": "摄像头-A4-JK05",
                  "content": "摄像头-A4-JK05",
                  "deviceid": "3550137",
                  "id": "500730"
                },
                {
                  "title": "摄像头-A4-JK06",
                  "content": "摄像头-A4-JK06",
                  "deviceid": "3550138",
                  "id": "500731"
                },
                {
                  "title": "摄像头-A4-JK07",
                  "content": "摄像头-A4-JK07",
                  "deviceid": "3550139",
                  "id": "500732"
                }
              ]
            },
            {
              "id": "submenu4F-C",
              "title": "C区",
              "submenu": [
                {
                  "title": "摄像头-C4-JK01",
                  "content": "摄像头-C4-JK01",
                  "deviceid": "3550140",
                  "id": "500733"
                },
                {
                  "title": "摄像头-C4-JK02",
                  "content": "摄像头-C4-JK02",
                  "deviceid": "3550141",
                  "id": "500734"
                },
                {
                  "title": "摄像头-C4-JK03",
                  "content": "摄像头-C4-JK03",
                  "deviceid": "3550142",
                  "id": "500735"
                },
                {
                  "title": "摄像头-C4-JK04",
                  "content": "摄像头-C4-JK04",
                  "deviceid": "3550143",
                  "id": "500736"
                },
                {
                  "title": "摄像头-C4-JK05",
                  "content": "摄像头-C4-JK05",
                  "deviceid": "3550144",
                  "id": "500737"
                },
                {
                  "title": "摄像头-C4-JK06",
                  "content": "摄像头-C4-JK06",
                  "deviceid": "3550145",
                  "id": "500738"
                },
                {
                  "title": "摄像头-C4-JK07",
                  "content": "摄像头-C4-JK07",
                  "deviceid": "3550146",
                  "id": "500739"
                }
              ]
            },
            {
              "id": "submenu4F-E",
              "title": "E区",
              "submenu": [
                {
                  "title": "摄像头-E4-JK01",
                  "content": "摄像头-E4-JK01",
                  "deviceid": "3550147",
                  "id": "500740"
                },
                {
                  "title": "摄像头-E4-JK02",
                  "content": "摄像头-E4-JK02",
                  "deviceid": "3550148",
                  "id": "500741"
                },
                {
                  "title": "摄像头-E4-JK03",
                  "content": "摄像头-E4-JK03",
                  "deviceid": "3550149",
                  "id": "500742"
                },
                {
                  "title": "摄像头-E4-JK04",
                  "content": "摄像头-E4-JK04",
                  "deviceid": "3550150",
                  "id": "500743"
                },
                {
                  "title": "摄像头-E4-JK05",
                  "content": "摄像头-E4-JK05",
                  "deviceid": "3550151",
                  "id": "500744"
                }
              ]
            }
          ]
        },
        {
          "id": "menu3F",
          "title": "3F",
          "submenu": [
            {
              "id": "submenu3F-A",
              "title": "A区",
              "submenu": [
                {
                  "title": "摄像头-A3-JK02",
                  "content": "摄像头-A3-JK02",
                  "deviceid": "3550116",
                  "id": "500709"
                },
                {
                  "title": "摄像头-A3-JK03",
                  "content": "摄像头-A3-JK03",
                  "deviceid": "3550117",
                  "id": "500710"
                },
                {
                  "title": "摄像头-A3-JK04",
                  "content": "摄像头-A3-JK04",
                  "deviceid": "3550118",
                  "id": "500711"
                },
                {
                  "title": "摄像头-A3-JK05",
                  "content": "摄像头-A3-JK05",
                  "deviceid": "3550119",
                  "id": "500712"
                },
                {
                  "title": "摄像头-A3-JK06",
                  "content": "摄像头-A3-JK06",
                  "deviceid": "3550120",
                  "id": "500713"
                }
              ]
            },
            {
              "id": "submenu3F-C",
              "title": "C区",
              "submenu": [
                {
                  "title": "摄像头-C3-JK02",
                  "content": "摄像头-C3-JK02",
                  "deviceid": "3550122",
                  "id": "500715"
                },
                {
                  "title": "摄像头-C3-JK03",
                  "content": "摄像头-C3-JK03",
                  "deviceid": "3550123",
                  "id": "500716"
                },
                {
                  "title": "摄像头-C3-JK04",
                  "content": "摄像头-C3-JK04",
                  "deviceid": "3550124",
                  "id": "500717"
                },
                {
                  "title": "摄像头-C3-JK05",
                  "content": "摄像头-C3-JK05",
                  "deviceid": "3550125",
                  "id": "500718"
                },
                {
                  "title": "摄像头-C3-JK06",
                  "content": "摄像头-C3-JK06",
                  "deviceid": "3550126",
                  "id": "500719"
                },
                {
                  "title": "摄像头-C3-JK07",
                  "content": "摄像头-C3-JK07",
                  "deviceid": "3550127",
                  "id": "500720"
                }
              ]
            },
            {
              "id": "submenu3F-E",
              "title": "E区",
              "submenu": [
                {
                  "title": "摄像头-E3-JK01",
                  "content": "摄像头-E3-JK01",
                  "deviceid": "3550128",
                  "id": "500721"
                },
                {
                  "title": "摄像头-E3-JK02",
                  "content": "摄像头-E3-JK02",
                  "deviceid": "3550129",
                  "id": "500722"
                },
                {
                  "title": "摄像头-E3-JK03",
                  "content": "摄像头-E3-JK03",
                  "deviceid": "3550130",
                  "id": "500723"
                },
                {
                  "title": "摄像头-E3-JK04",
                  "content": "摄像头-E3-JK04",
                  "deviceid": "3550131",
                  "id": "500724"
                },
                {
                  "title": "摄像头-E3-JK05",
                  "content": "摄像头-E3-JK05",
                  "deviceid": "3550132",
                  "id": "500725"
                }
              ]
            }
          ]
        },
        {
          "id": "menu2F",
          "title": "2F",
          "submenu": [
            {
              "id": "submenu2F-A",
              "title": "A区",
              "submenu": [
                {
                  "title": "摄像头-A2-JK01",
                  "content": "摄像头-A2-JK01",
                  "deviceid": "3550083",
                  "id": "500676"
                },
                {
                  "title": "摄像头-A2-JK10",
                  "content": "摄像头-A2-JK10",
                  "deviceid": "3550092",
                  "id": "500685"
                },
                {
                  "title": "摄像头-A2-JK11",
                  "content": "摄像头-A2-JK11",
                  "deviceid": "3550093",
                  "id": "500686"
                },
                {
                  "title": "摄像头-A2-JK12",
                  "content": "摄像头-A2-JK12",
                  "deviceid": "3550094",
                  "id": "500687"
                },
                {
                  "title": "摄像头-A2-JK13",
                  "content": "摄像头-A2-JK13",
                  "deviceid": "3550095",
                  "id": "500688"
                },
                {
                  "title": "摄像头-A2-JK14",
                  "content": "摄像头-A2-JK14",
                  "deviceid": "3550096",
                  "id": "500689"
                }
              ]
            },
            {
              "id": "submenu2F-C",
              "title": "C区",
              "submenu": [
                {
                  "title": "摄像头-C2-JK01",
                  "content": "摄像头-C2-JK01",
                  "deviceid": "3550097",
                  "id": "500690"
                },
                {
                  "title": "摄像头-C2-JK04",
                  "content": "摄像头-C2-JK04",
                  "deviceid": "3550100",
                  "id": "500693"
                },
                {
                  "title": "摄像头-C2-JK05",
                  "content": "摄像头-C2-JK05",
                  "deviceid": "3550101",
                  "id": "500694"
                },
                {
                  "title": "摄像头-C2-JK06",
                  "content": "摄像头-C2-JK06",
                  "deviceid": "3550102",
                  "id": "500695"
                },
                {
                  "title": "摄像头-C2-JK07",
                  "content": "摄像头-C2-JK07",
                  "deviceid": "3550103",
                  "id": "500696"
                },
                {
                  "title": "摄像头-C2-JK08",
                  "content": "摄像头-C2-JK08",
                  "deviceid": "3550104",
                  "id": "500697"
                },
                {
                  "title": "摄像头-C2-JK09",
                  "content": "摄像头-C2-JK09",
                  "deviceid": "3550105",
                  "id": "500698"
                },
                {
                  "title": "摄像头-C2-JK10",
                  "content": "摄像头-C2-JK10",
                  "deviceid": "3550106",
                  "id": "500699"
                },
                {
                  "title": "摄像头-C2-JK11",
                  "content": "摄像头-C2-JK11",
                  "deviceid": "3550107",
                  "id": "500700"
                },
                {
                  "title": "摄像头-C2-JK12",
                  "content": "摄像头-C2-JK12",
                  "deviceid": "3550108",
                  "id": "500701"
                },
                {
                  "title": "摄像头-C2-JK13",
                  "content": "摄像头-C2-JK13",
                  "deviceid": "3550109",
                  "id": "500702"
                }
              ]
            },
            {
              "id": "submenu2F-E",
              "title": "E区",
              "submenu": [
                {
                  "title": "摄像头-E2-JK01",
                  "content": "摄像头-E2-JK01",
                  "deviceid": "3550110",
                  "id": "500703"
                },
                {
                  "title": "摄像头-E2-JK02",
                  "content": "摄像头-E2-JK02",
                  "deviceid": "3550111",
                  "id": "500704"
                },
                {
                  "title": "摄像头-E2-JK03",
                  "content": "摄像头-E2-JK03",
                  "deviceid": "3550112",
                  "id": "500705"
                },
                {
                  "title": "摄像头-E2-JK04",
                  "content": "摄像头-E2-JK04",
                  "deviceid": "3550113",
                  "id": "500706"
                },
                {
                  "title": "摄像头-E2-JK05",
                  "content": "摄像头-E2-JK05",
                  "deviceid": "3550114",
                  "id": "500707"
                }
              ]
            }
          ]
        },
        {
          "id": "menu1F",
          "title": "1F",
          "submenu": [
            {
              "id": "submenu1F-A",
              "title": "A区",
              "submenu": [
                {
                  "title": "摄像头-A1-JK01",
                  "content": "摄像头-A1-JK01",
                  "deviceid": "3550037",
                  "id": "500630"
                },
                {
                  "title": "摄像头-A1-JK02",
                  "content": "摄像头-A1-JK02",
                  "deviceid": "3550038",
                  "id": "500631"
                },
                {
                  "title": "摄像头-A1-JK03",
                  "content": "摄像头-A1-JK03",
                  "deviceid": "3550039",
                  "id": "500632"
                },
                {
                  "title": "摄像头-A1-JK04",
                  "content": "摄像头-A1-JK04",
                  "deviceid": "3550040",
                  "id": "500633"
                },
                {
                  "title": "摄像头-A1-JK05",
                  "content": "摄像头-A1-JK05",
                  "deviceid": "3550041",
                  "id": "500634"
                },
                {
                  "title": "摄像头-A1-JK06",
                  "content": "摄像头-A1-JK06",
                  "deviceid": "3550042",
                  "id": "500635"
                },
                {
                  "title": "摄像头-A1-JK07",
                  "content": "摄像头-A1-JK07",
                  "deviceid": "3550043",
                  "id": "500636"
                },
                {
                  "title": "摄像头-A1-JK08",
                  "content": "摄像头-A1-JK08",
                  "deviceid": "3550044",
                  "id": "500637"
                },
                {
                  "title": "摄像头-A1-JK09",
                  "content": "摄像头-A1-JK09",
                  "deviceid": "3550045",
                  "id": "500638"
                },
                {
                  "title": "摄像头-A1-JK10",
                  "content": "摄像头-A1-JK10",
                  "deviceid": "3550046",
                  "id": "500639"
                },
                {
                  "title": "摄像头-A1-JK11",
                  "content": "摄像头-A1-JK11",
                  "deviceid": "3550047",
                  "id": "500640"
                },
                {
                  "title": "摄像头-A1-JK12",
                  "content": "摄像头-A1-JK12",
                  "deviceid": "3550048",
                  "id": "500641"
                },
                {
                  "title": "摄像头-A1-JK13",
                  "content": "摄像头-A1-JK13",
                  "deviceid": "3550049",
                  "id": "500642"
                },
                {
                  "title": "摄像头-A1-JK14",
                  "content": "摄像头-A1-JK14",
                  "deviceid": "3550050",
                  "id": "500643"
                },
                {
                  "title": "摄像头-A1-JK15",
                  "content": "摄像头-A1-JK15",
                  "deviceid": "3550051",
                  "id": "500644"
                },
                {
                  "title": "摄像头-A1-JK16",
                  "content": "摄像头-A1-JK16",
                  "deviceid": "3550052",
                  "id": "500645"
                },
                {
                  "title": "摄像头-A1-JK17",
                  "content": "摄像头-A1-JK17",
                  "deviceid": "3550053",
                  "id": "500646"
                },
                {
                  "title": "摄像头-A1-JK18",
                  "content": "摄像头-A1-JK18",
                  "deviceid": "3550054",
                  "id": "500647"
                },
                {
                  "title": "摄像头-A1-JK19",
                  "content": "摄像头-A1-JK19",
                  "deviceid": "3550055",
                  "id": "500648"
                },
                {
                  "title": "摄像头-A1-JK20",
                  "content": "摄像头-A1-JK20",
                  "deviceid": "3550056",
                  "id": "500649"
                },
                {
                  "title": "摄像头-A3-JK01",
                  "content": "摄像头-A3-JK01",
                  "deviceid": "3550115",
                  "id": "500708"
                }
              ]
            },
            {
              "id": "submenu1F-C",
              "title": "C区",
              "submenu": [
                {
                  "title": "摄像头-C1-JK01",
                  "content": "摄像头-C1-JK01",
                  "deviceid": "3550057",
                  "id": "500650"
                },
                {
                  "title": "摄像头-C1-JK02",
                  "content": "摄像头-C1-JK02",
                  "deviceid": "3550058",
                  "id": "500651"
                },
                {
                  "title": "摄像头-C1-JK03",
                  "content": "摄像头-C1-JK03",
                  "deviceid": "3550059",
                  "id": "500652"
                },
                {
                  "title": "摄像头-C1-JK04",
                  "content": "摄像头-C1-JK04",
                  "deviceid": "3550060",
                  "id": "500653"
                },
                {
                  "title": "摄像头-C1-JK05",
                  "content": "摄像头-C1-JK05",
                  "deviceid": "3550061",
                  "id": "500654"
                },
                {
                  "title": "摄像头-C1-JK06",
                  "content": "摄像头-C1-JK06",
                  "deviceid": "3550062",
                  "id": "500655"
                },
                {
                  "title": "摄像头-C1-JK07",
                  "content": "摄像头-C1-JK07",
                  "deviceid": "3550063",
                  "id": "500656"
                },
                {
                  "title": "摄像头-C1-JK08",
                  "content": "摄像头-C1-JK08",
                  "deviceid": "3550064",
                  "id": "500657"
                },
                {
                  "title": "摄像头-C1-JK09",
                  "content": "摄像头-C1-JK09",
                  "deviceid": "3550065",
                  "id": "500658"
                },
                {
                  "title": "摄像头-C1-JK10",
                  "content": "摄像头-C1-JK10",
                  "deviceid": "3550066",
                  "id": "500659"
                },
                {
                  "title": "摄像头-C1-JK11",
                  "content": "摄像头-C1-JK11",
                  "deviceid": "3550067",
                  "id": "500660"
                },
                {
                  "title": "摄像头-C3-JK01",
                  "content": "摄像头-C3-JK01",
                  "deviceid": "3550121",
                  "id": "500714"
                }
              ]
            },
            {
              "id": "submenu1F-E",
              "title": "E区",
              "submenu": [
                {
                  "title": "摄像头-E1-JK01",
                  "content": "摄像头-E1-JK01",
                  "deviceid": "3550068",
                  "id": "500661"
                },
                {
                  "title": "摄像头-E1-JK02",
                  "content": "摄像头-E1-JK02",
                  "deviceid": "3550069",
                  "id": "500662"
                },
                {
                  "title": "摄像头-E1-JK03",
                  "content": "摄像头-E1-JK03",
                  "deviceid": "3550070",
                  "id": "500663"
                },
                {
                  "title": "摄像头-E1-JK04",
                  "content": "摄像头-E1-JK04",
                  "deviceid": "3550071",
                  "id": "500664"
                },
                {
                  "title": "摄像头-E1-JK05",
                  "content": "摄像头-E1-JK05",
                  "deviceid": "3550072",
                  "id": "500665"
                },
                {
                  "title": "摄像头-E1-JK06",
                  "content": "摄像头-E1-JK06",
                  "deviceid": "3550073",
                  "id": "500666"
                },
                {
                  "title": "摄像头-E1-JK07",
                  "content": "摄像头-E1-JK07",
                  "deviceid": "3550074",
                  "id": "500667"
                },
                {
                  "title": "摄像头-E1-JK08",
                  "content": "摄像头-E1-JK08",
                  "deviceid": "3550075",
                  "id": "500668"
                },
                {
                  "title": "摄像头-E1-JK09",
                  "content": "摄像头-E1-JK09",
                  "deviceid": "3550076",
                  "id": "500669"
                },
                {
                  "title": "摄像头-E1-JK10",
                  "content": "摄像头-E1-JK10",
                  "deviceid": "3550077",
                  "id": "500670"
                },
                {
                  "title": "摄像头-E1-JK11",
                  "content": "摄像头-E1-JK11",
                  "deviceid": "3550078",
                  "id": "500671"
                },
                {
                  "title": "摄像头-E1-JK12",
                  "content": "摄像头-E1-JK12",
                  "deviceid": "3550079",
                  "id": "500672"
                },
                {
                  "title": "摄像头-E1-JK13",
                  "content": "摄像头-E1-JK13",
                  "deviceid": "3550080",
                  "id": "500673"
                },
                {
                  "title": "摄像头-E1-JK14",
                  "content": "摄像头-E1-JK14",
                  "deviceid": "3550081",
                  "id": "500674"
                },
                {
                  "title": "摄像头-E1-JK15",
                  "content": "摄像头-E1-JK15",
                  "deviceid": "3550082",
                  "id": "500675"
                }
              ]
            }
          ]
        },
        {
          "id": "menuB1F",
          "title": "B1F",
          "submenu": [
            {
              "id": "submenuB1F-A",
              "title": "A区",
              "submenu": [
                {
                  "title": "摄像头-AB1-JK01",
                  "content": "摄像头-AB1-JK01",
                  "deviceid": "3550001",
                  "id": "500594"
                },
                {
                  "title": "摄像头-AB1-JK05",
                  "content": "摄像头-AB1-JK05",
                  "deviceid": "3550005",
                  "id": "500598"
                },
                {
                  "title": "摄像头-AB1-JK07",
                  "content": "摄像头-AB1-JK07",
                  "deviceid": "3550007",
                  "id": "500600"
                },
                {
                  "title": "摄像头-AB1-JK09",
                  "content": "摄像头-AB1-JK09",
                  "deviceid": "3550009",
                  "id": "500602"
                },
                {
                  "title": "摄像头-AB1-JK10",
                  "content": "摄像头-AB1-JK10",
                  "deviceid": "3550010",
                  "id": "500603"
                },
                {
                  "title": "摄像头-AB1-JK11",
                  "content": "摄像头-AB1-JK11",
                  "deviceid": "3550011",
                  "id": "500604"
                },
                {
                  "title": "摄像头-AB1-JK12",
                  "content": "摄像头-AB1-JK12",
                  "deviceid": "3550012",
                  "id": "500605"
                },
                {
                  "title": "摄像头-AB1-JK13",
                  "content": "摄像头-AB1-JK13",
                  "deviceid": "3550013",
                  "id": "500606"
                },
                {
                  "title": "摄像头-AB1-JK14",
                  "content": "摄像头-AB1-JK14",
                  "deviceid": "3550014",
                  "id": "500607"
                },
                {
                  "title": "摄像头-AB1-JK15",
                  "content": "摄像头-AB1-JK15",
                  "deviceid": "3550015",
                  "id": "500608"
                },
                {
                  "title": "摄像头-AB1-JK16",
                  "content": "摄像头-AB1-JK16",
                  "deviceid": "3550016",
                  "id": "500609"
                },
                {
                  "title": "摄像头-AB1-JK17",
                  "content": "摄像头-AB1-JK17",
                  "deviceid": "3550017",
                  "id": "500610"
                },
                {
                  "title": "摄像头-AB1-JK18",
                  "content": "摄像头-AB1-JK18",
                  "deviceid": "3550018",
                  "id": "500611"
                }
              ]
            },
            {
              "id": "submenuB1F-C",
              "title": "C区",
              "submenu": [
                {
                  "title": "摄像头-CB1-JK01",
                  "content": "摄像头-CB1-JK01",
                  "deviceid": "3550019",
                  "id": "500612"
                },
                {
                  "title": "摄像头-CB1-JK02",
                  "content": "摄像头-CB1-JK02",
                  "deviceid": "3550020",
                  "id": "500613"
                },
                {
                  "title": "摄像头-CB1-JK03",
                  "content": "摄像头-CB1-JK03",
                  "deviceid": "3550021",
                  "id": "500614"
                },
                {
                  "title": "摄像头-CB1-JK04",
                  "content": "摄像头-CB1-JK04",
                  "deviceid": "3550022",
                  "id": "500615"
                },
                {
                  "title": "摄像头-CB1-JK05",
                  "content": "摄像头-CB1-JK05",
                  "deviceid": "3550023",
                  "id": "500616"
                },
                {
                  "title": "摄像头-CB1-JK06",
                  "content": "摄像头-CB1-JK06",
                  "deviceid": "3550024",
                  "id": "500617"
                },
                {
                  "title": "摄像头-CB1-JK07",
                  "content": "摄像头-CB1-JK07",
                  "deviceid": "3550025",
                  "id": "500618"
                },
                {
                  "title": "摄像头-CB1-JK08",
                  "content": "摄像头-CB1-JK08",
                  "deviceid": "3550026",
                  "id": "500619"
                },
                {
                  "title": "摄像头-CB1-JK09",
                  "content": "摄像头-CB1-JK09",
                  "deviceid": "3550027",
                  "id": "500620"
                },
                {
                  "title": "摄像头-CB1-JK10",
                  "content": "摄像头-CB1-JK10",
                  "deviceid": "3550028",
                  "id": "500621"
                },
                {
                  "title": "摄像头-CB1-JK11",
                  "content": "摄像头-CB1-JK11",
                  "deviceid": "3550029",
                  "id": "500622"
                },
                {
                  "title": "摄像头-CB1-JK12",
                  "content": "摄像头-CB1-JK12",
                  "deviceid": "3550030",
                  "id": "500623"
                },
                {
                  "title": "摄像头-CB1-JK13",
                  "content": "摄像头-CB1-JK13",
                  "deviceid": "3550031",
                  "id": "500624"
                },
                {
                  "title": "摄像头-CB1-JK14",
                  "content": "摄像头-CB1-JK14",
                  "deviceid": "3550032",
                  "id": "500625"
                },
                {
                  "title": "摄像头-CB1-JK15",
                  "content": "摄像头-CB1-JK15",
                  "deviceid": "3550033",
                  "id": "500626"
                },
                {
                  "title": "摄像头-CB1-JK16",
                  "content": "摄像头-CB1-JK16",
                  "deviceid": "3550034",
                  "id": "500627"
                },
                {
                  "title": "摄像头-CB1-JK17",
                  "content": "摄像头-CB1-JK17",
                  "deviceid": "3550035",
                  "id": "500628"
                },
                {
                  "title": "摄像头-CB1-JK18",
                  "content": "摄像头-CB1-JK18",
                  "deviceid": "3550036",
                  "id": "500629"
                }
              ]
            }
          ]
        }
      ],
      data: [
        {
          category: "仪器设备",
          items: [
            {
              number: "LAB001",
              nanme: "显微镜",
              pingpai: "品牌X",
              baozhuang: "3楼",
              xiaobaozhuang: "A305",

              qita: "",
            },
            {
              number: "LAB002",
              nanme: "离心机",
              pingpai: "品牌Y",
              baozhuang: "2楼",
              xiaobaozhuang: "B210",

              qita: "",
            },
            {
              number: "LAB003",
              nanme: "培养箱",
              pingpai: "品牌Z",
              baozhuang: "3楼",
              xiaobaozhuang: "A307",

              qita: "",
            },
            {
              number: "LAB004",
              nanme: "天平",
              pingpai: "品牌W",
              baozhuang: "2楼",
              xiaobaozhuang: "B209",

              qita: "",
            },
            {
              number: "LAB005",
              nanme: "烘箱",
              pingpai: "品牌V",
              baozhuang: "4楼",
              xiaobaozhuang: "C401",

              qita: "",
            },
          ],
        },
        {
          category: "计算机和信息化设备",
          items: [
            {
              number: "LAB001",
              nanme: "实验室电脑",
              pingpai: "品牌A",
              baozhuang: "3楼",
              xiaobaozhuang: "A308",

              qita: "",
            },
            {
              number: "LAB002",
              nanme: "数据采集设备",
              pingpai: "品牌B",
              baozhuang: "3楼",
              xiaobaozhuang: "A310",

              qita: "",
            },
            {
              number: "LAB003",
              nanme: "服务器",
              pingpai: "品牌C",
              baozhuang: "1楼",
              xiaobaozhuang: "机房",

              qita: "",
            },
          ],
        },
        {
          category: "办公设备",
          items: [
            {
              number: "LAB001",
              nanme: "打印机",
              pingpai: "品牌D",
              baozhuang: "2楼",
              xiaobaozhuang: "B205",

              qita: "",
            },
            {
              number: "LAB002",
              nanme: "复印机",
              pingpai: "品牌E",
              baozhuang: "2楼",
              xiaobaozhuang: "B206",

              qita: "",
            },
            {
              number: "LAB003",
              nanme: "投影仪",
              pingpai: "品牌F",
              baozhuang: "3楼",
              xiaobaozhuang: "A309",

              qita: "",
            },
          ],
        },
        {
          category: "基础设施",
          items: [
            {
              number: "LAB001",
              nanme: "实验台",
              pingpai: "品牌G",
              baozhuang: "4楼",
              xiaobaozhuang: "C402",

              qita: "",
            },
            {
              number: "LAB002",
              nanme: "通风系统",
              pingpai: "品牌H",
              baozhuang: "5楼",
              xiaobaozhuang: "D501",

              qita: "",
            },
            {
              number: "LAB003",
              nanme: "实验室椅子",
              pingpai: "品牌I",
              baozhuang: "3楼",
              xiaobaozhuang: "A306",

              qita: "",
            },
          ],
        },
      ],
      jkdata: [
        {
          fid: "A区B1F",
          code: "A区B1F3550001",
          deviceid: 3550001,
          number: "AB1-JK01",
          ip: "************",
          url: "http://*************:9080/TIANDY/1/hls.m3u8",
        },
        {
          fid: "A区B1F",
          code: "A区B1F3550002",
          deviceid: 3550002,
          number: "AB1-JK02",
          ip: "************",
          url: "http://*************:9080/TIANDY/2/hls.m3u8",
        },
        {
          fid: "A区B1F",
          code: "A区B1F3550003",
          deviceid: 3550003,
          number: "AB1-JK03",
          ip: "************",
          url: "http://*************:9080/TIANDY/3/hls.m3u8",
        },
        {
          fid: "A区B1F",
          code: "A区B1F3550004",
          deviceid: 3550004,
          number: "AB1-JK04",
          ip: "************",
          url: "http://*************:9080/TIANDY/4/hls.m3u8",
        },
        {
          fid: "A区B1F",
          code: "A区B1F3550005",
          deviceid: 3550005,
          number: "AB1-JK05",
          ip: "************",
          url: "http://*************:9080/TIANDY/5/hls.m3u8",
        },
        {
          fid: "A区B1F",
          code: "A区B1F3550006",
          deviceid: 3550006,
          number: "AB1-JK06",
          ip: "************",
        },
        {
          fid: "A区B1F",
          code: "A区B1F3550007",
          deviceid: 3550007,
          number: "AB1-JK07",
          ip: "************",
          url: "http://*************:9080/TIANDY/7/hls.m3u8",
        },
        {
          fid: "A区B1F",
          code: "A区B1F3550008",
          deviceid: 3550008,
          number: "AB1-JK08",
          ip: "************",
          url: "http://*************:9080/TIANDY/8/hls.m3u8",
        },
        {
          fid: "A区B1F",
          code: "A区B1F3550009",
          deviceid: 3550009,
          number: "AB1-JK09",
          ip: "************",
          url: "http://*************:9080/TIANDY/9/hls.m3u8",
        },
        {
          fid: "A区B1F",
          code: "A区B1F3550010",
          deviceid: 3550010,
          number: "AB1-JK10",
          ip: "************",
          url: "http://*************:9080/TIANDY/10/hls.m3u8",
        },
        {
          fid: "A区B1F",
          code: "A区B1F3550011",
          deviceid: 3550011,
          number: "AB1-JK11",
          ip: "************",
          url: "http://*************:9080/TIANDY/11/hls.m3u8",
        },
        {
          fid: "A区B1F",
          code: "A区B1F3550012",
          deviceid: 3550012,
          number: "AB1-JK12",
          ip: "************",
        },
        {
          fid: "A区B1F",
          code: "A区B1F3550013",
          deviceid: 3550013,
          number: "AB1-JK13",
          ip: "************",
        },
        {
          fid: "A区B1F",
          code: "A区B1F3550014",
          deviceid: 3550014,
          number: "AB1-JK14",
          ip: "************",
        },
        {
          fid: "A区B1F",
          code: "A区B1F3550015",
          deviceid: 3550015,
          number: "AB1-JK15",
          ip: "************",
        },
        {
          fid: "A区B1F",
          code: "A区B1F3550016",
          deviceid: 3550016,
          number: "AB1-JK16",
          ip: "************",
        },
        {
          fid: "A区B1F",
          code: "A区B1F3550017",
          deviceid: 3550017,
          number: "AB1-JK17",
          ip: "************",
        },
        {
          fid: "A区B1F",
          code: "A区B1F3550018",
          deviceid: 3550018,
          number: "AB1-JK18",
          ip: "************",
        },
        {
          fid: "C区B1F",
          code: "C区B1F3550019",
          deviceid: 3550019,
          number: "CB1-JK01",
          ip: "************",
        },
        {
          fid: "C区B1F",
          code: "C区B1F3550020",
          deviceid: 3550020,
          number: "CB1-JK02",
          ip: "************",
        },
        {
          fid: "C区B1F",
          code: "C区B1F3550021",
          deviceid: 3550021,
          number: "CB1-JK03",
          ip: "************",
        },
        {
          fid: "C区B1F",
          code: "C区B1F3550022",
          deviceid: 3550022,
          number: "CB1-JK04",
          ip: "************",
        },
        {
          fid: "C区B1F",
          code: "C区B1F3550023",
          deviceid: 3550023,
          number: "CB1-JK05",
          ip: "************",
        },
        {
          fid: "C区B1F",
          code: "C区B1F3550024",
          deviceid: 3550024,
          number: "CB1-JK06",
          ip: "************",
        },
        {
          fid: "C区B1F",
          code: "C区B1F3550025",
          deviceid: 3550025,
          number: "CB1-JK07",
          ip: "************",
        },
        {
          fid: "C区B1F",
          code: "C区B1F3550026",
          deviceid: 3550026,
          number: "CB1-JK08",
          ip: "************",
        },
        {
          fid: "C区B1F",
          code: "C区B1F3550027",
          deviceid: 3550027,
          number: "CB1-JK09",
          ip: "************",
        },
        {
          fid: "C区B1F",
          code: "C区B1F3550028",
          deviceid: 3550028,
          number: "CB1-JK10",
          ip: "************",
        },
        {
          fid: "C区B1F",
          code: "C区B1F3550029",
          deviceid: 3550029,
          number: "CB1-JK11",
          ip: "************",
        },
        {
          fid: "C区B1F",
          code: "C区B1F3550030",
          deviceid: 3550030,
          number: "CB1-JK12",
          ip: "************",
        },
        {
          fid: "C区B1F",
          code: "C区B1F3550031",
          deviceid: 3550031,
          number: "CB1-JK13",
          ip: "************",
        },
        {
          fid: "C区B1F",
          code: "C区B1F3550032",
          deviceid: 3550032,
          number: "CB1-JK14",
          ip: "************",
        },
        {
          fid: "C区B1F",
          code: "C区B1F3550033",
          deviceid: 3550033,
          number: "CB1-JK15",
          ip: "************",
        },
        {
          fid: "C区B1F",
          code: "C区B1F3550034",
          deviceid: 3550034,
          number: "CB1-JK16",
          ip: "************",
        },
        {
          fid: "C区B1F",
          code: "C区B1F3550035",
          deviceid: 3550035,
          number: "CB1-JK17",
          ip: "************",
        },
        {
          fid: "C区B1F",
          code: "C区B1F3550036",
          deviceid: 3550036,
          number: "CB1-JK18",
          ip: "************",
        },
        {
          fid: "A区1F",
          code: "A区1F3550037",
          deviceid: 3550037,
          number: "A1-JK01",
          ip: "************",
        },
        {
          fid: "A区1F",
          code: "A区1F3550038",
          deviceid: 3550038,
          number: "A1-JK02",
          ip: "************",
        },
        {
          fid: "A区1F",
          code: "A区1F3550039",
          deviceid: 3550039,
          number: "A1-JK03",
          ip: "************",
        },
        {
          fid: "A区1F",
          code: "A区1F3550040",
          deviceid: 3550040,
          number: "A1-JK04",
          ip: "************",
        },
        {
          fid: "A区1F",
          code: "A区1F3550041",
          deviceid: 3550041,
          number: "A1-JK05",
          ip: "************",
        },
        {
          fid: "A区1F",
          code: "A区1F3550042",
          deviceid: 3550042,
          number: "A1-JK06",
          ip: "************",
        },
        {
          fid: "A区1F",
          code: "A区1F3550043",
          deviceid: 3550043,
          number: "A1-JK07",
          ip: "************",
        },
        {
          fid: "A区1F",
          code: "A区1F3550044",
          deviceid: 3550044,
          number: "A1-JK08",
          ip: "************",
        },
        {
          fid: "A区1F",
          code: "A区1F3550045",
          deviceid: 3550045,
          number: "A1-JK09",
          ip: "************",
        },
        {
          fid: "A区1F",
          code: "A区1F3550046",
          deviceid: 3550046,
          number: "A1-JK10",
          ip: "************",
        },
        {
          fid: "A区1F",
          code: "A区1F3550047",
          deviceid: 3550047,
          number: "A1-JK11",
          ip: "************",
        },
        {
          fid: "A区1F",
          code: "A区1F3550048",
          deviceid: 3550048,
          number: "A1-JK12",
          ip: "************",
        },
        {
          fid: "A区1F",
          code: "A区1F3550049",
          deviceid: 3550049,
          number: "A1-JK13",
          ip: "************",
        },
        {
          fid: "A区1F",
          code: "A区1F3550050",
          deviceid: 3550050,
          number: "A1-JK14",
          ip: "************",
        },
        {
          fid: "A区1F",
          code: "A区1F3550051",
          deviceid: 3550051,
          number: "A1-JK15",
          ip: "************",
        },
        {
          fid: "A区1F",
          code: "A区1F3550052",
          deviceid: 3550052,
          number: "A1-JK16",
          ip: "************",
        },
        {
          fid: "A区1F",
          code: "A区1F3550053",
          deviceid: 3550053,
          number: "A1-JK17",
          ip: "************",
        },
        {
          fid: "A区1F",
          code: "A区1F3550054",
          deviceid: 3550054,
          number: "A1-JK18",
          ip: "************",
        },
        {
          fid: "A区1F",
          code: "A区1F3550055",
          deviceid: 3550055,
          number: "A1-JK19",
          ip: "************",
        },
        {
          fid: "A区1F",
          code: "A区1F3550056",
          deviceid: 3550056,
          number: "A1-JK20",
          ip: "************",
        },
        {
          fid: "C区1F",
          code: "C区1F3550057",
          deviceid: 3550057,
          number: "C1-JK01",
          ip: "*************",
        },
        {
          fid: "C区1F",
          code: "C区1F3550058",
          deviceid: 3550058,
          number: "C1-JK02",
          ip: "*************",
        },
        {
          fid: "C区1F",
          code: "C区1F3550059",
          deviceid: 3550059,
          number: "C1-JK03",
          ip: "*************",
        },
        {
          fid: "C区1F",
          code: "C区1F3550060",
          deviceid: 3550060,
          number: "C1-JK04",
          ip: "*************",
        },
        {
          fid: "C区1F",
          code: "C区1F3550061",
          deviceid: 3550061,
          number: "C1-JK05",
          ip: "*************",
        },
        {
          fid: "C区1F",
          code: "C区1F3550062",
          deviceid: 3550062,
          number: "C1-JK06",
          ip: "*************",
        },
        {
          fid: "C区1F",
          code: "C区1F3550063",
          deviceid: 3550063,
          number: "C1-JK07",
          ip: "*************",
        },
        {
          fid: "C区1F",
          code: "C区1F3550064",
          deviceid: 3550064,
          number: "C1-JK08",
          ip: "*************",
        },
        {
          fid: "C区1F",
          code: "C区1F3550065",
          deviceid: 3550065,
          number: "C1-JK09",
          ip: "*************",
        },
        {
          fid: "C区1F",
          code: "C区1F3550066",
          deviceid: 3550066,
          number: "C1-JK10",
          ip: "*************",
        },
        {
          fid: "C区1F",
          code: "C区1F3550067",
          deviceid: 3550067,
          number: "C1-JK11",
          ip: "************0",
        },
        {
          fid: "E区1F",
          code: "E区1F3550068",
          deviceid: 3550068,
          number: "E1-JK01",
          ip: "************2",
        },
        {
          fid: "E区1F",
          code: "E区1F3550069",
          deviceid: 3550069,
          number: "E1-JK02",
          ip: "************3",
        },
        {
          fid: "E区1F",
          code: "E区1F3550070",
          deviceid: 3550070,
          number: "E1-JK03",
          ip: "************4",
        },
        {
          fid: "E区1F",
          code: "E区1F3550071",
          deviceid: 3550071,
          number: "E1-JK04",
          ip: "*************",
        },
        {
          fid: "E区1F",
          code: "E区1F3550072",
          deviceid: 3550072,
          number: "E1-JK05",
          ip: "************6",
        },
        {
          fid: "E区1F",
          code: "E区1F3550073",
          deviceid: 3550073,
          number: "E1-JK06",
          ip: "************7",
        },
        {
          fid: "E区1F",
          code: "E区1F3550074",
          deviceid: 3550074,
          number: "E1-JK07",
          ip: "************8",
        },
        {
          fid: "E区1F",
          code: "E区1F3550075",
          deviceid: 3550075,
          number: "E1-JK08",
          ip: "************9",
        },
        {
          fid: "E区1F",
          code: "E区1F3550076",
          deviceid: 3550076,
          number: "E1-JK09",
          ip: "*************",
        },
        {
          fid: "E区1F",
          code: "E区1F3550077",
          deviceid: 3550077,
          number: "E1-JK10",
          ip: "*************",
        },
        {
          fid: "E区1F",
          code: "E区1F3550078",
          deviceid: 3550078,
          number: "E1-JK11",
          ip: "*************",
        },
        {
          fid: "E区1F",
          code: "E区1F3550079",
          deviceid: 3550079,
          number: "E1-JK12",
          ip: "*************",
        },
        {
          fid: "E区1F",
          code: "E区1F3550080",
          deviceid: 3550080,
          number: "E1-JK13",
          ip: "*************",
        },
        {
          fid: "E区1F",
          code: "E区1F3550081",
          deviceid: 3550081,
          number: "E1-JK14",
          ip: "*************",
        },
        {
          fid: "E区1F",
          code: "E区1F3550082",
          deviceid: 3550082,
          number: "E1-JK15",
          ip: "************6",
        },
        {
          fid: "A区2F",
          code: "A区2F3550083",
          deviceid: 3550083,
          number: "A2-JK01",
          ip: "************",
        },
        {
          fid: "A区2F",
          code: "A区2F3550084",
          deviceid: 3550084,
          number: "A2-JK02",
          ip: "************",
        },
        {
          fid: "A区2F",
          code: "A区2F3550085",
          deviceid: 3550085,
          number: "A2-JK03",
          ip: "************",
        },
        {
          fid: "A区2F",
          code: "A区2F3550086",
          deviceid: 3550086,
          number: "A2-JK04",
          ip: "************",
        },
        {
          fid: "A区2F",
          code: "A区2F3550087",
          deviceid: 3550087,
          number: "A2-JK05",
          ip: "************",
        },
        {
          fid: "A区2F",
          code: "A区2F3550088",
          deviceid: 3550088,
          number: "A2-JK06",
          ip: "************",
        },
        {
          fid: "A区2F",
          code: "A区2F3550089",
          deviceid: 3550089,
          number: "A2-JK07",
          ip: "************",
        },
        {
          fid: "A区2F",
          code: "A区2F3550090",
          deviceid: 3550090,
          number: "A2-JK08",
          ip: "************",
        },
        {
          fid: "A区2F",
          code: "A区2F3550091",
          deviceid: 3550091,
          number: "A2-JK09",
          ip: "************",
        },
        {
          fid: "A区2F",
          code: "A区2F3550092",
          deviceid: 3550092,
          number: "A2-JK10",
          ip: "************",
        },
        {
          fid: "A区2F",
          code: "A区2F3550093",
          deviceid: 3550093,
          number: "A2-JK11",
          ip: "************",
        },
        {
          fid: "A区2F",
          code: "A区2F3550094",
          deviceid: 3550094,
          number: "A2-JK12",
          ip: "************",
        },
        {
          fid: "A区2F",
          code: "A区2F3550095",
          deviceid: 3550095,
          number: "A2-JK13",
          ip: "************",
        },
        {
          fid: "A区2F",
          code: "A区2F3550096",
          deviceid: 3550096,
          number: "A2-JK14",
          ip: "************",
        },
        {
          fid: "C区2F",
          code: "C区2F3550097",
          deviceid: 3550097,
          number: "C2-JK01",
          ip: "************1",
        },
        {
          fid: "C区2F",
          code: "C区2F3550098",
          deviceid: 3550098,
          number: "C2-JK02",
          ip: "************2",
        },
        {
          fid: "C区2F",
          code: "C区2F3550099",
          deviceid: 3550099,
          number: "C2-JK03",
          ip: "************3",
        },
        {
          fid: "C区2F",
          code: "C区2F3550100",
          deviceid: 3550100,
          number: "C2-JK04",
          ip: "************4",
        },
        {
          fid: "C区2F",
          code: "C区2F3550101",
          deviceid: 3550101,
          number: "C2-JK05",
          ip: "************5",
        },
        {
          fid: "C区2F",
          code: "C区2F3550102",
          deviceid: 3550102,
          number: "C2-JK06",
          ip: "************6",
        },
        {
          fid: "C区2F",
          code: "C区2F3550103",
          deviceid: 3550103,
          number: "C2-JK07",
          ip: "************7",
        },
        {
          fid: "C区2F",
          code: "C区2F3550104",
          deviceid: 3550104,
          number: "C2-JK08",
          ip: "************8",
        },
        {
          fid: "C区2F",
          code: "C区2F3550105",
          deviceid: 3550105,
          number: "C2-JK09",
          ip: "************9",
        },
        {
          fid: "C区2F",
          code: "C区2F3550106",
          deviceid: 3550106,
          number: "C2-JK10",
          ip: "************0",
        },
        {
          fid: "C区2F",
          code: "C区2F3550107",
          deviceid: 3550107,
          number: "C2-JK11",
          ip: "************1",
        },
        {
          fid: "C区2F",
          code: "C区2F3550108",
          deviceid: 3550108,
          number: "C2-JK12",
          ip: "************2",
        },
        {
          fid: "C区2F",
          code: "C区2F3550109",
          deviceid: 3550109,
          number: "C2-JK13",
          ip: "************3",
        },
        {
          fid: "E区2F",
          code: "E区2F3550110",
          deviceid: 3550110,
          number: "E2-JK01",
          ip: "************7",
        },
        {
          fid: "E区2F",
          code: "E区2F3550111",
          deviceid: 3550111,
          number: "E2-JK02",
          ip: "*************",
        },
        {
          fid: "E区2F",
          code: "E区2F3550112",
          deviceid: 3550112,
          number: "E2-JK03",
          ip: "************9",
        },
        {
          fid: "E区2F",
          code: "E区2F3550113",
          deviceid: 3550113,
          number: "E2-JK04",
          ip: "************0",
        },
        {
          fid: "E区2F",
          code: "E区2F3550114",
          deviceid: 3550114,
          number: "E2-JK05",
          ip: "************1",
        },
        {
          fid: "A区3F",
          code: "A区3F3550115",
          deviceid: 3550115,
          number: "A3-JK01",
          ip: "************",
        },
        {
          fid: "A区3F",
          code: "A区3F3550116",
          deviceid: 3550116,
          number: "A3-JK02",
          ip: "************",
        },
        {
          fid: "A区3F",
          code: "A区3F3550117",
          deviceid: 3550117,
          number: "A3-JK03",
          ip: "************",
        },
        {
          fid: "A区3F",
          code: "A区3F3550118",
          deviceid: 3550118,
          number: "A3-JK04",
          ip: "************",
        },
        {
          fid: "A区3F",
          code: "A区3F3550119",
          deviceid: 3550119,
          number: "A3-JK05",
          ip: "************",
        },
        {
          fid: "A区3F",
          code: "A区3F3550120",
          deviceid: 3550120,
          number: "A3-JK06",
          ip: "************",
        },
        {
          fid: "C区3F",
          code: "C区3F3550121",
          deviceid: 3550121,
          number: "C3-JK01",
          ip: "************4",
        },
        {
          fid: "C区3F",
          code: "C区3F3550122",
          deviceid: 3550122,
          number: "C3-JK02",
          ip: "************5",
        },
        {
          fid: "C区3F",
          code: "C区3F3550123",
          deviceid: 3550123,
          number: "C3-JK03",
          ip: "************6",
        },
        {
          fid: "C区3F",
          code: "C区3F3550124",
          deviceid: 3550124,
          number: "C3-JK04",
          ip: "************7",
        },
        {
          fid: "C区3F",
          code: "C区3F3550125",
          deviceid: 3550125,
          number: "C3-JK05",
          ip: "************8",
        },
        {
          fid: "C区3F",
          code: "C区3F3550126",
          deviceid: 3550126,
          number: "C3-JK06",
          ip: "************9",
        },
        {
          fid: "C区3F",
          code: "C区3F3550127",
          deviceid: 3550127,
          number: "C3-JK07",
          ip: "************0",
        },
        {
          fid: "E区3F",
          code: "E区3F3550128",
          deviceid: 3550128,
          number: "E3-JK01",
          ip: "************2",
        },
        {
          fid: "E区3F",
          code: "E区3F3550129",
          deviceid: 3550129,
          number: "E3-JK02",
          ip: "************3",
        },
        {
          fid: "E区3F",
          code: "E区3F3550130",
          deviceid: 3550130,
          number: "E3-JK03",
          ip: "************4",
        },
        {
          fid: "E区3F",
          code: "E区3F3550131",
          deviceid: 3550131,
          number: "E3-JK04",
          ip: "************5",
        },
        {
          fid: "E区3F",
          code: "E区3F3550132",
          deviceid: 3550132,
          number: "E3-JK05",
          ip: "************6",
        },
        {
          fid: "A区4F",
          code: "A区4F3550133",
          deviceid: 3550133,
          number: "A4-JK01",
          ip: "************",
        },
        {
          fid: "A区4F",
          code: "A区4F3550134",
          deviceid: 3550134,
          number: "A4-JK02",
          ip: "************",
        },
        {
          fid: "A区4F",
          code: "A区4F3550135",
          deviceid: 3550135,
          number: "A4-JK03",
          ip: "************",
        },
        {
          fid: "A区4F",
          code: "A区4F3550136",
          deviceid: 3550136,
          number: "A4-JK04",
          ip: "************",
        },
        {
          fid: "A区4F",
          code: "A区4F3550137",
          deviceid: 3550137,
          number: "A4-JK05",
          ip: "************",
        },
        {
          fid: "A区4F",
          code: "A区4F3550138",
          deviceid: 3550138,
          number: "A4-JK06",
          ip: "************",
        },
        {
          fid: "A区4F",
          code: "A区4F3550139",
          deviceid: 3550139,
          number: "A4-JK07",
          ip: "************",
        },
        {
          fid: "C区4F",
          code: "C区4F3550140",
          deviceid: 3550140,
          number: "C4-JK01",
          ip: "************1",
        },
        {
          fid: "C区4F",
          code: "C区4F3550141",
          deviceid: 3550141,
          number: "C4-JK02",
          ip: "************2",
        },
        {
          fid: "C区4F",
          code: "C区4F3550142",
          deviceid: 3550142,
          number: "C4-JK03",
          ip: "************3",
        },
        {
          fid: "C区4F",
          code: "C区4F3550143",
          deviceid: 3550143,
          number: "C4-JK04",
          ip: "************4",
        },
        {
          fid: "C区4F",
          code: "C区4F3550144",
          deviceid: 3550144,
          number: "C4-JK05",
          ip: "************5",
        },
        {
          fid: "C区4F",
          code: "C区4F3550145",
          deviceid: 3550145,
          number: "C4-JK06",
          ip: "************6",
        },
        {
          fid: "C区4F",
          code: "C区4F3550146",
          deviceid: 3550146,
          number: "C4-JK07",
          ip: "************7",
        },
        {
          fid: "E区4F",
          code: "E区4F3550147",
          deviceid: 3550147,
          number: "E4-JK01",
          ip: "************7",
        },
        {
          fid: "E区4F",
          code: "E区4F3550148",
          deviceid: 3550148,
          number: "E4-JK02",
          ip: "************8",
        },
        {
          fid: "E区4F",
          code: "E区4F3550149",
          deviceid: 3550149,
          number: "E4-JK03",
          ip: "************9",
        },
        {
          fid: "E区4F",
          code: "E区4F3550150",
          deviceid: 3550150,
          number: "E4-JK04",
          ip: "************0",
        },
        {
          fid: "E区4F",
          code: "E区4F3550151",
          deviceid: 3550151,
          number: "E4-JK05",
          ip: "************1",
        },
        {
          fid: "A区5F",
          code: "A区5F3550152",
          deviceid: 3550152,
          number: "A5-JK01",
          ip: "************",
        },
        {
          fid: "A区5F",
          code: "A区5F3550153",
          deviceid: 3550153,
          number: "A5-JK02",
          ip: "************",
        },
        {
          fid: "A区5F",
          code: "A区5F3550154",
          deviceid: 3550154,
          number: "A5-JK03",
          ip: "************",
        },
        {
          fid: "A区5F",
          code: "A区5F3550155",
          deviceid: 3550155,
          number: "A5-JK04",
          ip: "************",
        },
        {
          fid: "A区5F",
          code: "A区5F3550156",
          deviceid: 3550156,
          number: "A5-JK05",
          ip: "************",
        },
        {
          fid: "A区5F",
          code: "A区5F3550157",
          deviceid: 3550157,
          number: "A5-JK06",
          ip: "************",
        },
        {
          fid: "C区5F",
          code: "C区5F3550158",
          deviceid: 3550158,
          number: "C5-JK01",
          ip: "************8",
        },
        {
          fid: "C区5F",
          code: "C区5F3550159",
          deviceid: 3550159,
          number: "C5-JK02",
          ip: "************9",
        },
        {
          fid: "C区5F",
          code: "C区5F3550160",
          deviceid: 3550160,
          number: "DTJK01",
          ip: "************0",
        },
        {
          fid: "C区5F",
          code: "C区5F3550161",
          deviceid: 3550161,
          number: "DTJK02",
          ip: "************1",
        },
        {
          fid: "E区5F",
          code: "E区5F3550162",
          deviceid: 3550162,
          number: "E5-JK01",
          ip: "*************",
        },
        {
          fid: "E区5F",
          code: "E区5F3550163",
          deviceid: 3550163,
          number: "E5-JK02",
          ip: "*************",
        },
        {
          fid: "E区5F",
          code: "E区5F3550164",
          deviceid: 3550164,
          number: "E5-JK03",
          ip: "*************",
        },
        {
          fid: "E区5F",
          code: "E区5F3550165",
          deviceid: 3550165,
          number: "E5-JK04",
          ip: "*************",
        },
      ],
      index: "",
      input: "",
      activeTab: "today",
      listst: [
        {
          name: "广东质检中诚认证有限公司到中广...",
        },
        { name: "材料科学、化学工程及医药研发成..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
        { name: "植酸检测方法及作用" },
        { name: "兔拉检测：行业中芯片造假技术..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
      ],
      showdh: true,
      showdh1: false,
      noAnimation: false,

      tableDataItem: [],
      tableTitle: [
        { key: "" },
        { key: "资产名称" },
        { key: "资产品牌" },
        { key: "楼层" },
        { key: "房间号" },

        { key: "其他说明" },
      ],
      ids: null,

      nhlist: [
        {
          title: "供气压力",
          status: "0.3Mpa",
          unit: "℃",
        },

        {
          title: "供气流量",
          status: "6M3/min",
          unit: "㎡",
        },
        {
          title: "露点温度",
          status: "6℃",
          unit: "℃",
        },
        {
          title: "含氧量",
          status: "6PPM",
          unit: "㎡",
        },
      ],
      warnlist1: [
        {
          type: 1,
          name: "检测到烟雾，可能有着火灾的发生...",
          value: "",
          time: "09:13:59  2023-06-07",
        },
        {
          type: 2,
          name: "检测到烟雾，可能预示着火灾的发生..",
          value: "",
          time: "10:13:18  2023-06-12",
        },
        {
          type: 3,
          name: "实验室内检测到漏水，可能来自冷凝水..",
          value: "",
          time: "10:13:18  2023-06-12",
        },
      ],
      isButton2Active: false,
      status: "巡检中",
      status1: "已完成",
      status2: "待巡检",
      // selectedIndex: 0,
      componentTag: "component0",
      sbtitle: "",
      sbtitle1: "",
      isFirstTime: true,
      detalis: [], // 用于标记是否是第一次调用
      showIframe: false,
      jkindex: 0,
    };
  },
  // 计算属性类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {},
  // 方法集合
  methods: {
    async fetchProjectSet(type, projectId, parkId, buildId, floorId) {
      console.log(this.getname, type, projectId, parkId, buildId, floorId);
      //从接口拿数据
      try {
        const response = await axios.get(
          "https://api-dh3d-public.3dzhanting.cn:8081/projectSet/all",
          {
            params: {
              type: type,
              projectId: projectId,
              deviceId: "", // 如果不需要 deviceId，可以将其删除或保留为空字符串
              parkId: parkId,
              buildId: buildId,
              loorId: "",
              name: "摄像头",
              roomId: "",
            },
          }
        );
        // this.sblist = response.data.data
        console.log(response.data.data, "处理好的数据");
        // 将楼层排序
        const floorOrder = ["5F", "4F", "3F", "2F", "1F", "B1F"]; // 定义排序顺序
        // 按楼层顺序排序
        const sortedData = response.data.data.sort(
          (a, b) =>
            floorOrder.indexOf(a.floorId) - floorOrder.indexOf(b.floorId)
        );

        // 转换数据为所需格式
        const transformedData = sortedData.reduce((result, item) => {
          // 确保 roomId 存在且有效
          if (!item.roomId) return result; // 如果没有 roomId，跳过该条数据
          // 根据 roomId 获取区域（假设区域名在 roomId 的首字母）
          const area = item.roomId.charAt(0); // A区, B区, C区, D区等

          // 查找当前楼层是否已存在
          let floorMenu = result.find((floor) => floor.title === item.floorId);

          // 如果楼层不存在，创建新的菜单项
          if (!floorMenu) {
            floorMenu = {
              id: `menu${item.floorId}`,
              title: item.floorId,
              submenu: [],
            };
            result.push(floorMenu);
          }

          // 查找当前楼层下是否已有对应的区域
          let areaSubmenu = floorMenu.submenu.find(
            (sub) => sub.title === `${area}区`
          );

          // 如果区域不存在，创建新的区域菜单
          if (!areaSubmenu) {
            areaSubmenu = {
              id: `submenu${item.floorId}-${area}`,
              title: `${area}区`,
              submenu: [],
            };
            floorMenu.submenu.push(areaSubmenu);
          }

          // 将设备信息添加到对应的区域菜单中
          areaSubmenu.submenu.push({
            title: `摄像头-${item.roomId}`,
            content: `摄像头-${item.roomId}`,
            deviceid: `${item.deviceId}`,
            id: `${item.id}`,
          });

          return result;
        }, []);

        console.log(transformedData, "处理好的数据");
        this.menus = transformedData;
        return response.data;
      } catch (error) {
        console.error("Error fetching project set:", error);
        throw error; // 重新抛出错误以便调用者处理
      }

      //从config拿数据
      // 将 name 字符串按逗号分隔为数组
      // const nameArray = this.getname.split(',').map(item => item.trim());
      // console.log(this.alldeviceList, nameArray, parkId, buildId, floorId, 'alldeviceList');
      // // 过滤设备列表，返回符合条件的设备
      // this.sblist = this.alldeviceList.filter(device => {
      //   const buildMatch = buildId == '' || device.buildId == buildId;
      //   const floorMatch = floorId == '' || device.floorId == floorId;
      //   const nameMatch = nameArray.includes(device.name);
      //   return device.parkId == parkId && buildMatch && floorMatch && nameMatch;
      // });

      // console.log('Response data:', this.sblist);
      // this.sendToUE41('shebei', this.sblist);
    },
    changetit(index) {
      this.titactive = index;
      this.isshowwhat = !index;
      if (index == 0) {
        this.showdh = true;
        this.showdh1 = false;
        this.noAnimation = false;
        this.componentTag = "component0";
      } else if (index == 1) {
        this.showdh = false;
        this.showdh1 = true;
        this.noAnimation = true;
      } else {
        this.isshowwhat = true;
        this.componentTag = "";
        this.componentTag = "component" + index;
      }
      // if (index == 0 || index == 1) {
      //   this.isshowwhat1 = true;
      // } else {
      //   this.isshowwhat1 = false;
      // }
    },
    hidedetailsss() {
      this.isshowsss = false;
    },
    setContent(content, index) {
      console.log(content, index, "设置内容");
      this.isshowsss = true;
      this.selectedItem = content.content;
      let idid = content.deviceid;
      let jkIndex = this.jkdata.findIndex((item) => item.deviceid == idid);
      // console.log(jkIndex,this.jkdata,'数据摄像');

      let jk = this.jkdata[jkIndex];
      this.jkindex = jkIndex > 12 ? jkIndex + 11 : jkIndex + 1; // 存储找到的索引
      (this.detalis = [
        { name: "设备名称", value: jk.fid + "-摄像头-" + jk.number },
        // { name: "监视区域：", value: "3F通风实验室" },
        { name: "品牌型号：", value: "天地伟业" },
        { name: "IP地址：", value: jk.ip },
        { name: "设备类型：", value: "高清枪型摄像机" },
      ]),
        console.log(this.jkindex, "数据摄像");
      this.selectedIndex = index;

      // 保存旧的 sbtitle 值，用于与当前值比较
      const oldSbtitle = this.sbtitle;

      // 设置内容

      // 如果是第一次调用，或者 sbtitle 发生变化，则触发事件
      // if (this.isFirstTime || this.sbtitle !== oldSbtitle) {
      //   this.$emit('seedbuild', 1);  // 触发父组件的事件并传递数据
      //   this.$emit('seedfloor', this.sbtitle + 2);  // 触发父组件的事件并传递数据
      //   this.isFirstTime = false;  // 第一次调用后将标志置为 false
      // }

      this.isFirstTime = false; // 第一次调用后将标志置为 false
      // 延时 1 秒后触发 seedid 事件
      // setTimeout(() => {
      if (content.id) {
        this.$emit("seedid", content.id);
      }

      // }, 500);
    },

    // 切换二级菜单显示/隐藏
    toggleSubMenu(menuId, title, index) {
      this.activeSubmenu = this.activeSubmenu === menuId ? null : menuId;
      this.sbtitle = index;
      this.$emit("seedbuild", "实验楼"); // 触发父组件的事件并传递数据
      this.$emit("seedfloor", title, "", "摄像头", true); // 触发父组件的事件并传递数据
      this.$emit("changecheck");
    },
    // 切换三级菜单显示/隐藏
    toggleSubSubMenu(submenuId, index) {
      this.activeSubSubmenu =
        this.activeSubSubmenu === submenuId ? null : submenuId;
      this.selectedIndex = null;
      this.sbtitle1 = index;
    },

    showdetails(item) {
      console.log(item.items);

      this.isshow = true;
      this.tableDataItem = item.items;
    },
    hidedetails() {
      this.isshow = false;
    },
    oc(value) {
      console.log(value, "收到的值");
      this.showdh = value;
    },
    showLargeIframe() {
      console.log(1234567890);
      this.showIframe = true;
    },
    hideLargeIframe() {
      this.showIframe = false;
    },
  },
  // 生命周期 - 创建完成（可以访问当前this实例）
  created() { },
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.fetchProjectSet(1, "YIHuaomzuSKUtXFCRYbdqA==", 0, "实验楼", "");
    var that = this;
    this.showdh1 = true;
    setTimeout(() => { }, 1000);
    // setTimeout(() => {
    //   this.showdh1 = false;
    //   this.noAnimation = false;
    // }, 1000); // 动画持续时间为1秒
    console.log(1222);
    window.addEventListener("message", function (event) {
      //event.data获取传过来的数据

      // let name = event.data.name;
      console.log(event, 517);
      if (event.data.type == "shebei") {
        console.log(event.data.data.id, "shebeiid");
        console.log(that.menus[that.sbtitle].submenu, "shebei123");
        let result = [];
        let menu = that.menus[that.sbtitle];
        let deviceIdResult = null; // 用于存储设备ID
        let submenuIndexResult = null; // 用于存储submenu索引
        // console.log(menu, 'menu');
        // 遍历所有submenu
        for (let i = 0; i < menu.submenu.length; i++) {
          console.log(menu.submenu[i], 121);
          const submenu = menu.submenu[i];
          deviceIdResult = submenu.id;
          // 遍历submenu中的所有设备
          for (let j = 0; j < submenu.submenu.length; j++) {
            const device = submenu.submenu[j];
            // console.log(device.id, event.data.data.id, 2024);
            // 如果设备ID匹配
            if (device.id == event.data.data.id) {
              submenuIndexResult = j; // 获取submenu的索引
              let idid = device.deviceid;
              let jkIndex = that.jkdata.findIndex(
                (item) => item.deviceid == idid
              );
              let jk = that.jkdata[jkIndex];
              // that.jkindex = jkIndex; // 存储索引到 data 中
              that.jkindex = jkIndex > 12 ? jkIndex + 11 : jkIndex + 1; // 存储找到的索引
              that.detalis = [
                { name: "设备名称", value: jk.fid + "-摄像头-" + jk.number },
                // { name: "监视区域：", value: "3F通风实验室" },
                { name: "品牌型号：", value: "天地伟业" },
                { name: "IP地址：", value: jk.ip },
                { name: "设备类型：", value: "高清枪型摄像机" },
              ];
              // 获取设备ID

              break; // 找到后退出
            }
          }

          if (submenuIndexResult !== null) break;
          // 如果找到了设备ID，则退出外层循环
        }
        that.activeSubSubmenu = deviceIdResult;
        that.selectedIndex = submenuIndexResult;
        // console.log('sy', deviceIdResult, submenuIndexResult);
        // that.activeSubSubmenu=that.menus[that.sbtitle].submenu
        //           .flatMap(submenuGroup => submenuGroup.submenu)
        //           .find(submenu => submenu.id == event.data.data.id).
      }
    });
  },
  beforeCreate() { }, // 生命周期 - 创建之前
  beforeMount() { }, // 生命周期 - 挂载之前
  beforeUpdate() { }, // 生命周期 - 更新之前
  updated() { }, // 生命周期 - 更新之后
  beforeUnmount() {
    // 在组件销毁之前清除定时器
    console.log(1111);
  },

  unmounted() {
    console.log(2222);
  }, // 生命周期 - 销毁之前
  destroyed() {
    console.log(1221);
  }, // 生命周期 - 销毁完成
  activated() { }, // 如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="less" scoped>
.botbtn {
  position: fixed;
  top: 978px;
  // left: 528px;
  left: 728px;
  width: 200px;
  height: 43px;
  background: #022d56;
  border-radius: 8px;
  z-index: 20;
  display: flex;
  align-items: center;
  justify-content: center;

  .btt {
    color: #fff;
    font-size: 17px;
    line-height: 44px;
    // width: 88px;
    text-align: center;
    height: 43px;
    cursor: pointer;
    margin-left: 7px;
    margin-right: 7px;
  }

  .btt1 {
    color: rgb(8, 207, 241);
    font-size: 17px;
    line-height: 44px;
    // width: 88px;
    text-align: center;
    height: 43px;
    cursor: pointer;
    margin-left: 7px;
    margin-right: 7px;
  }
}

.container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: stretch;
  height: 1080px;
  text-align: center;

  .sbdetails {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
  }

  .box {
    margin-top: 6px;
    padding-top: 5px;
    margin-bottom: 0.225rem;
    // height: 800px;
    width: 360px;
    // height: 800px;

    overflow-y: scroll;

    /* 设置垂直滚动条 */
    /* 设置滚动条的样式 */
    &::-webkit-scrollbar {
      width: 0.1px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条轨道的样式 */
    &::-webkit-scrollbar-track {
      background-color: #f1f1f1;
      /* 设置滚动条轨道的背景色 */
    }

    /* 设置滚动条滑块的样式 */
    &::-webkit-scrollbar-thumb {
      background-color: #334f6e;
      /* 设置滚动条滑块的背景色 */
    }

    /* 鼠标悬停在滚动条上时的样式 */
    &::-webkit-scrollbar-thumb:hover {
      background-color: #555;
      /* 设置鼠标悬停时滚动条滑块的背景色 */
    }

    .el-input {
      width: 335px;
      height: 39px;
      color: #fff !important;

      ::v-deep .el-input__wrapper {
        background: url("../assets/image/inputss.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        box-shadow: none !important;
      }
    }

    .suosuo {
      position: absolute;
      top: 66px;
      left: 285px;
    }

    .xiaobox {
      margin-top: 20px;
      display: flex;
      align-items: center;

      .siqiu {
        width: 16px;
        height: 16px;
      }

      .shuru {
        display: flex;
        align-items: center;
        padding-left: 10px;
        background: url("../assets/image/shebei12.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 261px;
        height: 32px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 17px;
        color: #ffffff;
      }
    }

    .xiaoboxs {
      cursor: pointer;
      margin-top: 14px;
      display: flex;
      margin-left: 5px;
      align-items: center;

      .nihaowo {
        width: 78px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 15px;
        color: #ffffff;
        display: flex;
        white-space: nowrap;
        /* 不换行 */
        overflow: hidden;
        /* 超出部分隐藏 */
        text-overflow: ellipsis;
        /* 超出部分显示省略号 */
      }

      .siqiu {
        width: 16px;
        height: 16px;
        margin-left: 10px;
        margin-right: 7px;
      }

      .shuru {
        display: flex;
        align-items: center;
        padding-left: 10px;
        background: url("../assets/image/shebei12.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 261px;
        height: 32px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 17px;
        color: #ffffff;
        white-space: nowrap;
        /* 不换行 */
        overflow: hidden;
        /* 超出部分隐藏 */
        text-overflow: ellipsis;
        /* 超出部分显示省略号 */
      }
    }
  }

  .left-panel {
    position: fixed;
    z-index: 1;
    top: 100px;
    left: 22px;
    width: 330px;
    height: 937px;

    background-size: 100% 100%;
    transform: translate(-122%);
    transition: transform 0.5s ease-in-out;
  }

  .left-panel-active {
    transform: translate(0%);
  }

  .left-panel-active1 {
    // transform: translate(0%);
    animation: slideOut 1s ease-in-out forwards;
  }

  @keyframes slideOut {
    100% {
      transform: translateX(0%);
    }

    // 85% {
    //   transform: translateX(-25%);
    // }

    // 65% {
    //   transform: translateX(-15%);
    // }

    // 40% {
    //   transform: translateX(-55%);
    // }

    // 30% {
    //   transform: translateX(-40%);
    // }

    0% {
      transform: translateX(-100%);
    }
  }

  .rtitle {
    margin-top: 16px;
  }

  @keyframes slideIn {
    0% {
      transform: translateX(100%);
    }

    // 30% {
    //   transform: translateX(65%);
    // }

    // 40% {
    //   transform: translateX(40%);
    // }

    // 65% {
    //   transform: translateX(15%);
    // }

    // 85% {
    //   transform: translateX(25%);
    // }

    100% {
      transform: translateX(0%);
    }
  }

  .right-panel {
    position: fixed;
    z-index: 1;
    right: 83px;
    width: 330px;
    top: 100px;
    height: 937px;

    background-size: 100% 100%;
    transform: translate(122%);
    transition: transform 0.5s ease-in-out;

    .jk {
      margin-top: 12px;
      width: 100%;
      height: 210px;
    }

    .box {
      margin-top: 6px;
      margin-bottom: 18px;
      // background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      width: 94%;
      // height: 428px;

      .loudong {
        margin-top: 21px;
        margin-bottom: 25px;
      }

      .wenzi {
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 10px;
        color: #bdecf9;
        text-align: left;
        margin-left: 20px;
        margin-right: 20px;

        .h2biaoti {
          margin-bottom: 15px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 12px;
          color: #00ffff;
        }
      }

      .p {
        text-indent: 2em;
        margin-bottom: 1em;
        letter-spacing: 0.05em;
      }
    }

    .boxxx {
      margin-top: 6px;
      margin-bottom: 18px;
      background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      width: 333px;
      height: 420px;
      overflow-y: scroll;
      /* 设置垂直滚动条 */
      // overflow: hidden;
      /* 设置滚动条的样式 */

      .zengti {
        margin: 10px 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 315px;
        height: 38px;
        gap: 5px;

        .left {
          display: flex;
          align-items: center;
          justify-content: space-evenly;
          width: 84px;
          height: 27px;

          .yuan {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #08f7f7;
          }

          .wenziss {
            display: flex;
            align-items: flex-start;
            flex-direction: column;

            .p1 {
              font-family: Source Han Sans SC;
              font-weight: 500;
              font-size: 10px;
              color: #ffffff;
            }

            .p2 {
              font-family: Source Han Sans SC;
              font-weight: 500;
              font-size: 10px;
              color: #55cff9;
            }
          }
        }

        .right {
          background: url("../assets/image/rightbeij.png");
          background-size: 100% 100%;
          background-repeat: no-repeat;

          width: 217px;
          height: 38px;
          font-family: Source Han Sans SC;
          font-weight: 500;
          font-size: 11px;
          color: #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    .boxxx::-webkit-scrollbar {
      width: 1px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条轨道的样式 */
    .boxxx::-webkit-scrollbar-track {
      background-color: #f1f1f1;
      /* 设置滚动条轨道的背景色 */
    }

    /* 设置滚动条滑块的样式 */
    .boxxx::-webkit-scrollbar-thumb {
      background-color: #013363;
      /* 设置滚动条滑块的背景色 */
    }
  }

  .no-animation {
    transition: none;
  }

  .right-panel-active {
    transform: translate(0%);
    // animation: slideIn 1s ease-in-out ;
  }

  .right-panel-active1 {
    // transform: translate(0%);
    animation: slideIn 1s ease-in-out forwards;
  }

  @keyframes slideIn {
    0% {
      transform: translateX(100%);
    }

    // 30% {
    //   transform: translateX(65%);
    // }

    // 40% {
    //   transform: translateX(40%);
    // }

    // 65% {
    //   transform: translateX(15%);
    // }

    // 85% {
    //   transform: translateX(25%);
    // }

    100% {
      transform: translateX(0%);
    }
  }

  .completed {
    background: #7ad0ff;
  }

  .incomplete {
    background: #ff6041;
  }

  .warning {
    background: #00ffc0;
  }

  .completeds {
    color: #7ad0ff;
  }

  .incompletes {
    color: #ff6041;
  }

  .warnings {
    color: #00ffc0;
  }
}

.ql-center {
  display: flex;
  margin-top: 20px;
  justify-content: space-around;
  margin-top: 14px;

  .ql-Box {
    width: 75px;
    height: 49px;
    border: 1px solid #7ad0ff;
    // opacity: 0.6;
    border-radius: 2px;

    .ql-box1 {
      font-family: Alibaba PuHuiTi;
      font-weight: bold;
      font-size: 16px;
      color: #7ad0ff;
      margin-top: -10px;
    }

    .ql-box {
      display: flex;
      padding-left: 8px;
      padding-right: 9px;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 34px;

      .left_ql {
        width: 49px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #ffffff;

        .yuan {
          width: 7px;
          height: 7px;
          border-radius: 50%;
        }

        .pp {
          color: #fff;
          font-size: 12px;
        }
      }

      img {
        height: 12px;
        width: 7px;
      }
    }
  }
}

.warn1 {
  background: url("../assets/image/warnred.png");
}

.warn2 {
  background: url("../assets/image/warnyellow.png");
}

.warn3 {
  background: url("../assets/image/warngreen.png");
}

.warning12 {
  background-size: 100% 100%;
  // width: 365px;
  height: 47px;

  .info {
    margin-top: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: left;

    margin-left: 50px;

    .info1 {
      display: flex;
      flex-direction: column;

      p:nth-of-type(1) {
        font-size: 13px;
        font-family: Alibaba PuHuiTi;
        font-weight: bold;
      }

      .red {
        color: #ff0000;
      }

      .green {
        color: #00ffcc;
      }

      .yellow {
        color: #ffff00;
      }

      p:nth-of-type(2) {
        font-size: 16px;
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        color: #ffffff;
      }
    }

    .info2 {
      margin-right: 10px;
      font-size: 15px;
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      color: #cffff8;
    }
  }
}

.xxxx {
  position: absolute;
  top: 1%;
  right: 1%;
  width: 25px;
  height: 25px;
  z-index: 99999;
}

/* 菜单容器 */
.menu-container {
  display: flex;
  gap: 20px;
}

/* 菜单样式 */
.menu {
  height: 760px;
  width: 340px;
  // background-color: #fff;
  // border: 1px solid #ddd;
  border-radius: 4px;
  overflow-y: auto;
  margin-left: 13px;
}

/* 设置滚动条的样式 */
.menu::-webkit-scrollbar {
  width: 3px;
  /* 设置滚动条的宽度 */
}

/* 设置滚动条轨道的样式 */
.menu::-webkit-scrollbar-track {
  background-color: #f1f1f1;
  /* 设置滚动条轨道的背景色 */
}

/* 设置滚动条滑块的样式 */
.menu::-webkit-scrollbar-thumb {
  background-color: #163561;
  /* 设置滚动条滑块的背景色 */
}

/* 鼠标悬停在滚动条上时的样式 */
.menu::-webkit-scrollbar-thumb:hover {
  background-color: #555;
  /* 设置鼠标悬停时滚动条滑块的背景色 */
}

/* 菜单项样式 */
.menu-group {
  margin-top: 14px;
}

.menu-item {
  cursor: pointer;
  background: url("../assets/image/rightbeij.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;

  width: 100%;
  height: 36px;
  font-family: Source Han Sans SC;
  font-weight: 500;
  font-size: 20px;
  color: #ffffff;
  display: flex;
  align-items: center;
  padding-left: 10px;
  // justify-content: center;
}

.menu-item1 {
  color: #00ffc0 !important;
  // justify-content: center;
}

.menu-item:hover {
  background-color: #f0f0f0 !important;
  color: #00ffc0;
}

.submenu {
  display: block;
  /* 确保子菜单是块级元素 */
  // background-color: #f9f9f9;
  padding-left: 8px;
}

.submenu-items:hover {
  color: #00ffc0;
}

.bq {
  text-align: left;
  width: 100px;
}

.submenu-item:hover {
  color: #00ffc0;
}

.submenu-items {
  cursor: pointer;
  // background-color:#013363;
  width: 100%;

  font-family: Source Han Sans SC;
  font-weight: 500;
  font-size: 18px;
  color: #ffffff;
  /* 修改 display 属性 */
  display: flex;
  flex-direction: column;
  /* 垂直方向排列 */
  align-items: flex-start;
  padding-left: 10px;
  margin-top: 10px;
}

.submenu-item {
  cursor: pointer;
  background: url("../assets/image/rightbeij.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: 300px !important;
  /* 移除固定高度 */
  /* height: 30px; */
  font-family: Source Han Sans SC;
  font-weight: 500;
  font-size: 15px;
  color: #ffffff;
  /* 修改 display 属性 */
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding-left: 10px;

  margin-top: 10px;
}

.qiuqiu {
  display: flex;
  align-items: center;

  .siqiu {
    width: 16px;
    height: 16px;
    margin-left: 10px;
    margin-right: 7px;
  }
}

.listtype {
  background: url("../assets/image/xuanzhotiemmoon.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 80px;
  height: 26px;
  text-align: center;
  line-height: 26px;

  font-size: 13px;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  color: #fff;
}

.listtypes {
  background: url("../assets/image/xuanzhotiemmoon.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 100px;
  height: 36px;
  text-align: center;
  line-height: 36px;
  cursor: pointer;
  font-size: 13px;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  color: #fff;
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  font-size: 15px;
}

.jiankong {
  position: relative;
}

.jk {
  cursor: pointer;
}

.large-iframe-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.large-iframe-container {
  position: relative;
  width: 80%;
  height: 81%;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
}

.large-iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.close-button {
  position: absolute;
  top: 2%;
  right: 20px;
  width: 40px;
  height: 40px;
  background: rgba(5, 5, 5, 0.3);
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  font-size: 65px;
  transition: background 0.3s;
}

.close-button:hover {
  background: rgba(219, 218, 218, 0.7);
}

.jk-container {
  cursor: pointer;
  width: 95%;
  height: 200px;
  margin-top: 12px;
}

.jk {
  width: 100%;
  height: 100%;
  pointer-events: none;
  /* 关键样式：禁用 iframe 的鼠标事件 */
}
</style>
