<template>
  <div>
    <!-- <component :is="componentTag" @fatherMethoddd="fatherMethoddd"></component> -->
    <component :is="componentTag" @fatherMethoddd="fatherMethoddd" v-if="!isshowwhat"></component>
    <div class="botbtn">
      <div v-for="(item, index) in changeTitle" :key="index" :class="titactive == index ? 'btt1' : 'btt'"
        @click="changetit(index)">
        {{ item }}
      </div>
    </div>
    <!-- <tedai
      :ids="ids"
      :selectedItem="selectedItem"
      class="sbdetails"
      :zengtiimg="zengtiimg"
      v-if="isshow"
      @hidedetails="hidedetails"
    ></tedai> -->
    <tedai :ids="ids" :selectedItem="selectedItem" :tfdata="tfdata" :tfchart="tfchart" class="sbdetails"
      :zengtiimg="zengtiimg" v-if="isshowsss" @hidedetails="hidedetailsss"></tedai>
    <biaoGe @xuanze-dialog="xuanzedialog" v-if="isshow" @hidedetails="hidedetails" :tableTitle="tableTitle"
      :tableDataItem="data" :Title="Title"></biaoGe>
    <div class="container" v-if="isshowwhat">
      <div class="left-panel" :class="{
        'left-panel-active': showdh,
        'no-animation': noAnimation,
        'left-panel-active1': showdh1,
      }">
        <Title2 @open-dialog="opendialog" class="ltitle1" tit="通风柜监控" title="">
          <div class="box">
            <div>
              <el-input class="el-input" v-model="input" placeholder="请输入内容"></el-input>
            </div>
            <!-- <div class="xiaobox">
              <img class="siqiu" src="../assets/image/shixinqiu.png" alt="" />
              <div class="shuru">全部设备</div>
            </div> -->
            <div class="menu-container">
              <!-- 动态生成菜单 -->
              <div class="menu">
                <div v-for="(menu, index) in menus" :key="index" class="menu-group">
                  <div :style="{
                    color: activeSubmenu == menu.id ? '#00ffc0' : '',
                  }" class="menu-item" @click="toggleSubMenu(menu.id, menu.title, index)">
                    {{ menu.title }}
                  </div>
                  <div v-show="activeSubmenu === menu.id" class="submenu">
                    <div v-for="(item, subIndex) in menu.submenu" :style="{
                      color: activeSubSubmenu === item.id ? '#00ffc0' : '',
                    }" :key="subIndex" class="submenu-items">
                      <div class="bq" @click="toggleSubSubMenu(item.id, index)">
                        {{ item.title }}
                      </div>
                      <div v-show="activeSubSubmenu === item.id" class="submenu">
                        <div v-for="(subItem, thirdIndex) in item.submenu" :key="thirdIndex" :style="{
                          color: selectedIndex == thirdIndex ? '#00ffc0' : '',
                        }" class="submenu-item" @click="setContent(subItem, thirdIndex)">
                          {{ subItem.title }}
                          <!-- <div class="listtype">使用中</div> -->
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- <div class="menu">
                <div v-for="(menu, index) in data" :key="index" class="menu-group">
                  <div class="qiuqiu">
                    <img class="siqiu" src="../assets/image/shixinqiu.png" alt="" />
                    <div class="menu-item" @click="toggleSubMenu(menu)">
                      {{ menu.category }}
                    </div>
                  </div>

                  <div v-show="activeSubmenu === menu.id" class="submenu">
                    <div v-for="(item, subIndex) in menu.items" :key="subIndex" class="submenu-item"
                      @click="setContent(item)">
                      <p class="ellipsis" :title="item.name">{{ item.name }}</p>
                    </div>
                  </div>
                </div>
              </div> -->
            </div>
          </div>
          <!-- shebei12.png -->
        </Title2>
      </div>
      <!-- shebeibgc.png -->
      <!-- 右侧内容 -->
      <!-- 右侧内容 -->

      <div class="right-panel" :class="{
        'right-panel-active': showdh,
        'no-animation': noAnimation,
        'right-panel-active1': showdh1,
      }">
        <!-- <Title3 tit="通风柜监控详情">
          <div class="box">
            <div class="xiaoboxs" v-for="item in cgqlist" :key="item">
              <img class="siqiu" src="../assets/image/kongxin.png" alt="" />
              <div class="nihaowo">{{ item.name }}</div>
              <div class="shuru">{{ item.value }}</div>
            </div>
          </div>
         
        </Title3> -->
        <Title3 tit="异常跟踪处理" v-if="false">
          <div class="boxxxs">
            <div class="ql-center">
              <div class="ql-Box">
                <div class="ql-box">
                  <div class="left_ql">
                    <div class="yuan status" style="color: #5c9dee"></div>
                    <div class="pp">未处理</div>
                  </div>
                </div>
                <div class="ql-box1" style="color: #5c9dee">3</div>
              </div>
              <div class="ql-Box">
                <div class="ql-box">
                  <div class="left_ql">
                    <div class="yuan status" style="color: #89f6c1"></div>
                    <div class="pp">已完成</div>
                  </div>
                </div>
                <div class="ql-box1 status" style="color: #89f6c1">1</div>
              </div>
              <div class="ql-Box">
                <div class="ql-box">
                  <div class="left_ql">
                    <div class="yuan status"></div>
                    <div class="pp" style="color: #f1c274">处理中</div>
                  </div>
                </div>
                <div class="ql-box1" style="color: #f1c274">2</div>
              </div>
            </div>
            <div class="warning12">
              <div class="info">
                <div class="zongduan">
                  <div class="yuan"></div>
                  <div class="cjhulizhong" style="color: #3ba1f4">处理中</div>
                </div>
                <div class="info1">
                  <p>2024-06-16 12:34:09</p>
                  <p>58号楼-5F-501</p>
                </div>
                <p class="info2" @click="openbj()">501通风柜</p>
              </div>
            </div>
            <div class="warning12">
              <div class="info">
                <div class="zongduan">
                  <div class="yuan"></div>
                  <div class="cjhulizhong" style="color: #64f8bb">处理中</div>
                </div>
                <div class="info1">
                  <p>2024-06-16 12:34:09</p>
                  <p>58号楼-5F-502</p>
                </div>
                <p class="info2" style="color: #64f8bb" @click="openbj()">
                  502通风柜
                </p>
              </div>
            </div>
            <!-- <div class="warning12">
              <div class="info">
                <div class="zongduan">
                  <div class="yuan"></div>
                  <div class="cjhulizhong" style="color: #fabf69">处理中</div>
                </div>
                <div class="info1">
                  <p>2024-06-16 12:34:09</p>
                  <p>3号楼-4F-102</p>
                </div>
                <p class="info2" style="color: #fabf69" @click="openbj()">
                  传感器温度报警
                </p>
              </div>
            </div> -->
          </div>
        </Title3>
      </div>
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import huanxing from "@/components/echarts/huanxing.vue";
import zhexian from "@/components/echarts/zhexian.vue";
import zhexian1 from "@/components/echarts/zhexian1.vue";
import SystemDete from "@/components/echarts/SystemDete.vue";
import echarts1 from "@/components/echarts/bingjifang/echarts3.vue";
import tedai from "@/components/common/hj-tedais.vue";
import biaoGe from "@/components/common/biaoGes.vue";
import shuangxiang from "@/components/echarts/shuangxiang.vue";
import component0 from "@/views/tongji/tongfeng.vue";
import axios from "axios";
import { getDeviceData, getDevicedetails } from "@/api/device";
// import details from "@/components/common/details.vue";
export default {
  // import引入的组件需要注入到对象中才能使用
  components: {
    tedai,
    huanxing,
    zhexian,
    zhexian1,
    SystemDete,
    echarts1,
    shuangxiang,
    biaoGe,
    component0,
  },
  props: ["tabledata", "zengtiimg"],
  //  cgqlistss: [
  //       { name: "湿度范围：", value: "5%RH~100%RH（无结露）" },
  //       { name: "湿度分辨率：", value: "0.04%" },
  //       { name: "IP地址：", value: "±3%RH（20%RH~80%RH）" },
  //       { name: "湿度精度：", value: "-40℃~+125℃" },
  //       { name: "温度分辨率：", value: "0.01℃" },
  //       { name: "温度精度：", value: "±0.5℃（10℃~60℃）" },
  //     ],

  data() {
    // 这里存放数据
    return {
      isshowwhat: true,
      tfdata: [],
      tfchart: [],
      titactive: 0,
      changeTitle: ["数据列表", "数据统计"],

      activeSubmenu: null, // 当前激活的二级菜单
      activeSubSubmenu: null, // 当前激活的三级菜单
      activeContent: null, // 当前显示的内容
      Title: "通风监控",
      isshowsss: false,
      menus: [
    {
        "id": "menu2F",
        "title": "2F",
        "submenu": [
            {
                "id": "submenu2F-C220",
                "title": "C220",
                "submenu": [
                    {
                        "title": "通风柜 - C220",
                        "content": "通风柜 - C220",
                        "deviceid": "2011006",
                        "id": 500765
                    }
                ]
            },
            {
                "id": "submenu2F-E204",
                "title": "E204",
                "submenu": [
                    {
                        "title": "通风柜 - E204",
                        "content": "通风柜 - E204",
                        "deviceid": "2011010",
                        "id": 500767
                    },
                    {
                        "title": "通风柜 - E204",
                        "content": "通风柜 - E204",
                        "deviceid": "2011011",
                        "id": 500768
                    }
                ]
            },
            {
                "id": "submenu2F-E205",
                "title": "E205",
                "submenu": [
                    {
                        "title": "通风柜 - E205",
                        "content": "通风柜 - E205",
                        "deviceid": "2011012",
                        "id": 500769
                    },
                    {
                        "title": "通风柜 - E205",
                        "content": "通风柜 - E205",
                        "deviceid": "2011013",
                        "id": 500770
                    }
                ]
            },
            {
                "id": "submenu2F-A228",
                "title": "A228",
                "submenu": [
                    {
                        "title": "通风柜 - A228",
                        "content": "通风柜 - A228",
                        "deviceid": "2011012",
                        "id": 547363
                    }
                ]
            }
        ]
    },
    {
        "id": "menu1F",
        "title": "1F",
        "submenu": [
            {
                "id": "submenu1F-A105",
                "title": "A105",
                "submenu": [
                    {
                        "title": "通风柜 - A105",
                        "content": "通风柜 - A105",
                        "deviceid": "2011001",
                        "id": 500761
                    }
                ]
            },
            {
                "id": "submenu1F-C115",
                "title": "C115",
                "submenu": [
                    {
                        "title": "通风柜 - C115",
                        "content": "通风柜 - C115",
                        "deviceid": "2011004",
                        "id": 500763
                    }
                ]
            },
            {
                "id": "submenu1F-E125",
                "title": "E125",
                "submenu": [
                    {
                        "title": "通风柜 - E125",
                        "content": "通风柜 - E125",
                        "deviceid": "2011008",
                        "id": 500764
                    },
                    {
                        "title": "通风柜 - E125",
                        "content": "通风柜 - E125",
                        "deviceid": "2011007",
                        "id": 549458
                    }
                ]
            }
        ]
    },
    {
        "id": "menuB1F",
        "title": "B1F",
        "submenu": [
            {
                "id": "submenuB1F-电镜制样间1 (B113)",
                "title": "电镜制样间1 (B113)",
                "submenu": [
                    {
                        "title": "通风柜 - B113",
                        "content": "通风柜 - B113",
                        "deviceid": "2011002",
                        "id": 500759
                    },
                    {
                        "title": "通风柜 - B113",
                        "content": "通风柜 - B113",
                        "deviceid": "2011003",
                        "id": 500760
                    }
                ]
            }
        ]
    }
],
      activeSubmenu: null, // 当前激活的子菜单
      activeContent: null, // 当前显示的内容
      newArr: [],
      isshow: false,
      xxxx: false,
      cgqlist: [],
      listtable: [],
      data: [
        {
          id: "menu1",
          category: "通风柜",
          items: [
            {
              name: "5F",
              number: "暂未入账（2207000840）",
              room: "A1003",
              sbtypess: "预约中",
              loucheng: "1层",
              type: "预约中",
            },
            {
              name: "4F",
              number: "暂未入账（2207000840）",
              room: "A1003",
              sbtypess: "预约中",
              loucheng: "1层",
              type: "预约中",
            },
            {
              name: "3F",
              number: "暂未入账（2207000840）",
              room: "A1003",
              sbtypess: "预约中",
              loucheng: "1层",
              type: "预约中",
            },
          ],
        },
        {
          id: "menu2",
          category: "排风系统",
          items: [
            {
              name: "5F",
              number: "暂未入账（2207000840）",
              room: "A1003",
              sbtypess: "预约中",
              loucheng: "1层",
              type: "预约中",
            },
            {
              name: "302-排风柜",
              number: "暂未入账（2207000840）",
              room: "A1003",
              sbtypess: "预约中",
              loucheng: "1层",
              type: "预约中",
            },
            {
              name: "303-排风柜",
              number: "暂未入账（2207000840）",
              room: "A1003",
              sbtypess: "预约中",
              loucheng: "1层",
              type: "预约中",
            },
          ],
        },
      ],
      input: "",
      activeTab: "today",
      listst: [
        {
          name: "广东质检中诚认证有限公司到中广...",
        },
        { name: "材料科学、化学工程及医药研发成..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
        { name: "植酸检测方法及作用" },
        { name: "兔拉检测：行业中芯片造假技术..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
      ],
      showdh: true,
      showdh1: false,
      noAnimation: false,
      selectedItem: null,
      tableDataItem: [],

      tableTitle: [
        { key: "楼层" },
        { key: "设备编号" },
        { key: "设备名称" },
        { key: "房间号" },
        { key: "模型" },
        { key: "设备状态" },
        { key: "状态说明" },
      ],
      ids: null,
      nhlist: [
        {
          title: "供气压力",
          status: "0.3Mpa",
          unit: "℃",
        },

        {
          title: "供气流量",
          status: "6M3/min",
          unit: "㎡",
        },
        {
          title: "露点温度",
          status: "6℃",
          unit: "℃",
        },
        {
          title: "含氧量",
          status: "6PPM",
          unit: "㎡",
        },
      ],
      warnlist1: [
        {
          type: 1,
          name: "检测到烟雾，可能有着火灾的发生...",
          value: "",
          time: "09:13:59  2023-06-07",
        },
        {
          type: 2,
          name: "检测到烟雾，可能预示着火灾的发生..",
          value: "",
          time: "10:13:18  2023-06-12",
        },
        {
          type: 3,
          name: "实验内检测到漏水，可能来自冷凝水..",
          value: "",
          time: "10:13:18  2023-06-12",
        },
      ],
      isButton2Active: false,
      status: "巡检中",
      status1: "已完成",
      status2: "待巡检",
      selectedIndex: null,
      componentTag: "component0",
    };
  },
  // 计算属性类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {},
  // 方法集合
  methods: {
    async fetchProjectSet(type, projectId, parkId, buildId, floorId) {
      console.log(this.getname, type, projectId, parkId, buildId, floorId);
      //从接口拿数据
      try {
        const response = await axios.get(
          "https://api-dh3d-public.3dzhanting.cn:8081/projectSet/all",
          {
            params: {
              type: type,
              projectId: projectId,
              deviceId: "", // 如果不需要 deviceId，可以将其删除或保留为空字符串
              parkId: parkId,
              buildId: buildId,
              loorId: "",
              name: "通风柜",
              roomId: "",
            },
          }
        );
        // this.sblist = response.data.data
        console.log(response.data.data, "处理好的数据1");
        // 将楼层排序
        const floorOrder = ["5F", "4F", "3F", "2F", "1F", "B1F"]; // 定义排序顺序
        // 按楼层顺序排序
        const sortedData = response.data.data.sort(
          (a, b) =>
            floorOrder.indexOf(a.floorId) - floorOrder.indexOf(b.floorId)
        );
        const roomMap = {
          B106: "电镜辅助间 (B106)",
          B154: "透射电镜室1 (B154)",
          B153: "透射电镜室2 (B153)",
          B144: "电镜辅助间 (B144)",
          B109: "电镜辅助间 (B109)",
          B156: "透射电镜室3 (B156)",
          B155: "透射电镜室4 (B155)",
          B141: "电镜辅助间 (B141)",
          B110: "电镜辅助间 (B110)",
          B138: "透射电镜室5 (B138)",
          B137: "透射电镜室6 (B137)",
          B140: "电镜辅助间 (B140)",
          B113: "电镜制样间1 (B113)",
          B132: "电镜制样间 (B132)",
          B130: "电镜制样间 (B130)",
          B117: "扫描电镜5 (B117)",
          B118: "电镜辅助间 (B118)",
          B119: "扫面电镜6 (B119)",
          B116: "扫面电镜1 (B116)",
          B120: "电镜辅助间 (B120)",
          B122: "扫面电镜2 (B122)",
          B122: "扫面电镜3 (B122)",
          B123: "电镜辅助间 (B123)",
          B124: "扫面电镜4 (B124)",
          B145: "电镜辅助间 (B145)",
          B146: "扫面电镜7 (B146)",
          B147: "电镜辅助间 (B147)",
          B148: "透射电镜室8 (B148)",
          B150: "透射电镜室7 (B150)",
          B151: "电镜辅助间 (B151)",
          119: "核磁主机房 (119)",
          118: "核磁主机房 (118)",
          115: "样品处理间 (115)",
          116: "仪器室 (116)",
          120: "辅助机房 (120)",
          124: "核磁主机房 (114)",
          127: "核磁中心 (127)",
          125: "办公室 (125)",
          123: "空压机房 (123)",
          110: "能谱仪室 (110)",
          103: "中控室 (103)",
          104: "D射线衍射仪 (104)",
          105: "样品处理间 (105)",
          109: "D射线衍射仪2 (109)",
          204: "通用实验室 (204)",
          205: "通用实验室 (205)",
          218: "样品准备间 (218)",
          219: "汇流排间 (219)",
          220: "液相色谱 (220)",
          222: "样品处理室 (222)",
          223: "气相色谱 (223)",
          225: "光谱仪室 (225)",
          226: "光谱仪室 (226)",
          228: "样品处理间 (228)",
          231: "光谱仪室2 (231)",
          配电间外: "配电间外",
          上空: "上空",
          汇流排间: "汇流排间",
        };
        // 转换数据为所需格式
        const transformedData = sortedData.reduce((result, item) => {
          // 确保 roomId 存在且有效
          if (!item.roomId) return result; // 如果没有 roomId，跳过该条数据
          // 根据 roomId 获取区域（假设区域名在 roomId 的首字母）

          const area = roomMap[item.roomId] || item.roomId;

          // 查找当前楼层是否已存在
          let floorMenu = result.find((floor) => floor.title === item.floorId);

          // 如果楼层不存在，创建新的菜单项
          if (!floorMenu) {
            floorMenu = {
              id: `menu${item.floorId}`,
              title: item.floorId,
              submenu: [],
            };
            result.push(floorMenu);
          }
          // 查找当前楼层下是否已有对应的房间
          let roomSubmenu = floorMenu.submenu.find((sub) => sub.title === area);
          if (!roomSubmenu) {
            roomSubmenu = {
              id: `submenu${item.floorId}-${area}`,
              title: area,
              submenu: [],
            };
            floorMenu.submenu.push(roomSubmenu);
          }

          // 将设备信息添加到对应的房间下
          roomSubmenu.submenu.push({
            title: `${item.name} - ${item.roomId}`,
            content: `${item.name} - ${item.roomId}`,
            deviceid: item.deviceId,
            id: item.id,
          });

          return result;
        }, []);

        console.log(transformedData, "处理好的数据");
        this.menus = transformedData;
        return response.data;
      } catch (error) {
        console.error("Error fetching project set:", error);
        throw error; // 重新抛出错误以便调用者处理
      }

      //从config拿数据
      // 将 name 字符串按逗号分隔为数组
      // const nameArray = this.getname.split(',').map(item => item.trim());
      // console.log(this.alldeviceList, nameArray, parkId, buildId, floorId, 'alldeviceList');
      // // 过滤设备列表，返回符合条件的设备
      // this.sblist = this.alldeviceList.filter(device => {
      //   const buildMatch = buildId == '' || device.buildId == buildId;
      //   const floorMatch = floorId == '' || device.floorId == floorId;
      //   const nameMatch = nameArray.includes(device.name);
      //   return device.parkId == parkId && buildMatch && floorMatch && nameMatch;
      // });

      // console.log('Response data:', this.sblist);
      // this.sendToUE41('shebei', this.sblist);
    },
    changetit(index) {
      this.titactive = index;
      this.isshowwhat = !index;
      if (index == 1) {
        this.showdh = false;
        this.showdh1 = true;
        this.noAnimation = true;
      } else {
        this.showdh = true;
        this.showdh1 = false;
        this.noAnimation = false;
      }
    },
    hidedetailsss() {
      this.isshowsss = false;
    },
    opendialog(payload) {
      if (payload == 1) {
        this.isshow = true;

        this.data.forEach((item) => {
          if (item.category == "电子显微镜") {
            this.isshow = true;
            this.tableDataItem = item.items;
            console.log(this.tableDataItem);
          }
        });
      }

      // 在这里处理事件
    },
    toggleSubMenu(menuId, title, index) {
      this.activeSubmenu = this.activeSubmenu === menuId ? null : menuId;
      this.sbtitle = index;
      this.$emit("seedbuild", "实验楼"); // 触发父组件的事件并传递数据
      this.$emit("seedfloor", title, "", "通风柜", true); // 触发父组件的事件并传递数据
    },
    // 切换三级菜单显示/隐藏
    toggleSubSubMenu(submenuId) {
      this.activeSubSubmenu =
        this.activeSubSubmenu === submenuId ? null : submenuId;
      this.selectedIndex = null;
      // this.sbtitle1 = index;
    },
    // 设置内容
    async setContent(content, index) {
      console.log(content, "contentdetails")

      setTimeout(() => {
        this.isshowsss = true;
      }, 2000);
      this.selectedItem = content.content;
      this.detalis = content.cgqlist;
      console.log(this.selectedItem, "details");
      this.selectedIndex = index;

      this.activeContent = content;
      if (
        this.hasValidValues(content.pos) &&
        this.hasValidValues(content.tar)
      ) {
        this.$emit("seedfeiru", content.pos, content.tar); // 触发父组件的事件并传递数据
      }
      if (content.id) {
        this.$emit("seedid", content.id);
      }
      // 获取设备数据并更新图表
      try {
        const res = await getDeviceData(content.deviceid, content.deviceid + '3');
        const res1 = await getDevicedetails(content.deviceid);
        console.log(res, "res22");
        console.log(res1, "res11");
        if (res.code === 200 && res.data) {
          // 更新图表数据
          const chartData = res.data.data || [];
          const times = chartData.map((item) => item.recordedAt.slice(11, 16));
          const values = chartData.map((item) => item.indication);
          console.log(times, values, "times, values");
          this.tfdata = res1.data.deviceDataBase;
          this.tfchart = {
            xAxis: {
              data: times,
            },
            series: [{ data: values }],
          };
          // 更新图表配置
          // this.chartOption.xAxis.data = times;
          // this.chartOption.series[0].data = values;
          // // this.chartOption.yAxis.name = res.data.dataUnit;
          // this.chartOption.title.text = res.data.dataUnit;

          // 如果图表实例存在，更新图表
          if (this.$refs.echarts2) {
            this.$refs.echarts2.setOption(this.chartOption);
          }
        }
      } catch (error) {
        console.error("获取设备数据失败:", error);
      }
    },
    // 工具方法：检查对象是否有有效值
    hasValidValues(obj) {
      if (!obj || typeof obj !== "object") {
        return false;
      }
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          const value = obj[key];
          // 检查属性是否为数字且不是 NaN
          if (typeof value !== "number" || isNaN(value)) {
            return false;
          }
        }
      }
      return true;
    },
    showdetails(item) {
      // item.items.forEach((item) => {
      //   this.newArr.push({ name: item.name });
      // });
      // console.log(this.newArr);

      this.isshow = true;
      this.tableDataItem = item.items;
    },
    hidedetails() {
      this.isshow = false;
    },

    oc(value) {
      console.log(value, "收到的值");
      this.showdh = value;
    },
    xuanzedialog(value) {
      const optionMapping = {
        选项1: 0,
        选项2: 1,
        选项3: 2,
        选项4: 3,
        选项5: 4,
        选项6: 5,
        选项7: 6,
        选项8: 7,
        选项9: 8,
        选项10: 9,
        选项11: 10,
        选项12: 11,
        选项13: 12,
      };

      const index = optionMapping[value];
      if (index !== undefined) {
        this.tableDataItem = this.data[index].items;
      } else {
        console.error("无效的选项: ", value);
      }
    },
  },
  // 生命周期 - 创建完成（可以访问当前this实例）
  created() { },
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    var that = this;
    this.fetchProjectSet(1, "YIHuaomzuSKUtXFCRYbdqA==", 0, "实验楼", "");
    this.showdh1 = true;
    // setTimeout(() => {
    //   this.showdh1 = false;
    //   this.noAnimation = false;
    // }, 1000); // 动画持续时间为1秒
    window.addEventListener("message", function (event) {
      //event.data获取传过来的数据

      // let name = event.data.name;
      console.log(event, 517);
      if (event.data.type == "shebei") {
        console.log(event, "shebei");
        that.isshowsss = true;
        console.log(that.menus[that.sbtitle], "shebei123");

        let result = [];
        let menu = that.menus[that.sbtitle];

        let submenuIdResult = null; // 初始化submenu的id结果
        let deviceIndexResult = -1; // 初始化设备所在submenu中的索引

        let title = "";
        // 检查主菜单
        for (const submenu of that.menus[that.sbtitle].submenu) {
          if (submenu.id == event.data.data.id) {
            title = submenu.title; // 找到主菜单的title
          }
          // 检查子菜单
          for (const item of submenu.submenu) {
            if (item.id == event.data.data.id) {
              title = item.title; // 找到子菜单的title
              console.log(title, "sss");
            }
          }
        }
        that.selectedItem = title;
        // 遍历所有submenu
        // 遍历所有submenu
        for (let i = 0; i < menu.submenu.length; i++) {
          const submenu = menu.submenu[i];

          // 遍历submenu中的所有设备
          for (let j = 0; j < submenu.submenu.length; j++) {
            const device = submenu.submenu[j];

            // 如果设备ID匹配
            if (device.id == event.data.data.id) {
              submenuIdResult = submenu.id; // 获取当前submenu的id
              deviceIndexResult = j; // 获取设备在submenu中的索引
              break; // 找到后退出内层循环
            }
          }

          if (submenuIdResult) break; // 如果找到了设备ID，则退出外层循环
        }
        console.log("sy", submenuIdResult, deviceIndexResult);
        that.activeSubSubmenu = submenuIdResult;
        that.selectedIndex = deviceIndexResult;

        // that.activeSubSubmenu=that.menus[that.sbtitle].submenu
        //           .flatMap(submenuGroup => submenuGroup.submenu)
        //           .find(submenu => submenu.id == event.data.data.id).
      }
    });
  },
  beforeCreate() { }, // 生命周期 - 创建之前
  beforeMount() { }, // 生命周期 - 挂载之前
  beforeUpdate() { }, // 生命周期 - 更新之前
  updated() { }, // 生命周期 - 更新之后
  beforeUnmount() {
    // 在组件销毁之前清除定时器
    console.log(1111);
  },

  unmounted() {
    console.log(2222);
  }, // 生命周期 - 销毁之前
  destroyed() {
    console.log(1221);
  }, // 生命周期 - 销毁完成
  activated() { }, // 如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="less" scoped>
.botbtn {
  position: fixed;
  top: 978px;
  // left: 678px;
  left: 916px;
  width: 200px;
  height: 43px;
  background: #022d56;
  border-radius: 8px;
  z-index: 20;
  display: flex;
  align-items: center;
  justify-content: center;

  .btt {
    color: #fff;
    font-size: 17px;
    line-height: 44px;
    width: 88px;
    text-align: center;
    height: 43px;
    cursor: pointer;
  }

  .btt1 {
    color: rgb(8, 207, 241);
    font-size: 17px;
    line-height: 44px;
    width: 88px;
    text-align: center;
    height: 43px;
    cursor: pointer;
  }
}

.container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: stretch;
  height: 1080px;
  text-align: center;

  .sbdetails {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
  }

  .box {
    margin-top: 6px;
    padding-top: 5px;
    margin-bottom: 0.225rem;
    height: 800px;
    width: 330px;
    // height: 800px;

    overflow-y: scroll;

    /* 设置垂直滚动条 */
    /* 设置滚动条的样式 */
    &::-webkit-scrollbar {
      width: 0.1px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条轨道的样式 */
    &::-webkit-scrollbar-track {
      background-color: #f1f1f1;
      /* 设置滚动条轨道的背景色 */
    }

    /* 设置滚动条滑块的样式 */
    &::-webkit-scrollbar-thumb {
      background-color: #334f6e;
      /* 设置滚动条滑块的背景色 */
    }

    /* 鼠标悬停在滚动条上时的样式 */
    &::-webkit-scrollbar-thumb:hover {
      background-color: #555;
      /* 设置鼠标悬停时滚动条滑块的背景色 */
    }

    .el-input {
      width: 305px;
      height: 34px;
      color: #fff !important;

      ::v-deep .el-input__wrapper {
        background: url("../assets/image/inputss.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        box-shadow: none !important;
      }
    }

    .suosuo {
      position: absolute;
      top: 66px;
      left: 285px;
    }

    .xiaobox {
      margin-top: 20px;
      display: flex;
      align-items: center;

      .siqiu {
        width: 16px;
        height: 16px;
      }

      .shuru {
        display: flex;
        align-items: center;
        padding-left: 10px;
        background: url("../assets/image/shebei12.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 261px;
        height: 32px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 17px;
        color: #ffffff;
      }
    }

    .xiaoboxs {
      cursor: pointer;
      margin-top: 14px;
      display: flex;
      margin-left: 5px;
      align-items: center;

      .nihaowo {
        width: 78px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 10px;
        color: #ffffff;
        display: flex;
        white-space: nowrap;
        /* 不换行 */
        overflow: hidden;
        /* 超出部分隐藏 */
        text-overflow: ellipsis;
        /* 超出部分显示省略号 */
      }

      .siqiu {
        width: 16px;
        height: 16px;
        margin-left: 10px;
        margin-right: 7px;
      }

      .shuru {
        display: flex;
        align-items: center;
        padding-left: 10px;
        background: url("../assets/image/shebei12.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 261px;
        height: 32px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 17px;
        color: #ffffff;
        white-space: nowrap;
        /* 不换行 */
        overflow: hidden;
        /* 超出部分隐藏 */
        text-overflow: ellipsis;
        /* 超出部分显示省略号 */
      }
    }
  }

  .left-panel {
    position: fixed;
    z-index: 1;
    top: 100px;
    left: 22px;
    width: 330px;
    height: 937px;

    background-size: 100% 100%;
    transform: translate(-122%);
    transition: transform 0.5s ease-in-out;
  }

  .left-panel-active {
    transform: translate(0%);
  }

  .left-panel-active1 {
    // transform: translate(0%);
    animation: slideOut 1s ease-in-out forwards;
  }

  @keyframes slideOut {
    100% {
      transform: translateX(0%);
    }

    // 85% {
    //   transform: translateX(-25%);
    // }

    // 65% {
    //   transform: translateX(-15%);
    // }

    // 40% {
    //   transform: translateX(-55%);
    // }

    // 30% {
    //   transform: translateX(-40%);
    // }

    0% {
      transform: translateX(-100%);
    }
  }

  .rtitle {
    margin-top: 16px;
  }

  @keyframes slideIn {
    0% {
      transform: translateX(100%);
    }

    // 30% {
    //   transform: translateX(65%);
    // }

    // 40% {
    //   transform: translateX(40%);
    // }

    // 65% {
    //   transform: translateX(15%);
    // }

    // 85% {
    //   transform: translateX(25%);
    // }

    100% {
      transform: translateX(0%);
    }
  }

  .right-panel {
    position: fixed;
    z-index: 1;
    right: 83px;
    width: 330px;
    top: 100px;
    height: 937px;

    background-size: 100% 100%;
    transform: translate(122%);
    transition: transform 0.5s ease-in-out;

    .jk {
      margin-top: 12px;
      width: 90%;
      height: 200px;
    }

    .box {
      margin-top: 6px;
      margin-bottom: 18px;
      // background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      width: 94%;
      // height: 428px;

      .loudong {
        margin-top: 21px;
        margin-bottom: 25px;
      }

      .wenzi {
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 10px;
        color: #bdecf9;
        text-align: left;
        margin-left: 20px;
        margin-right: 20px;

        .h2biaoti {
          margin-bottom: 15px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 12px;
          color: #00ffff;
        }
      }

      .p {
        text-indent: 2em;
        margin-bottom: 1em;
        letter-spacing: 0.05em;
      }
    }

    .boxxx {
      margin-top: 6px;
      margin-bottom: 18px;
      background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      width: 333px;
      height: 420px;
      overflow-y: scroll;
      /* 设置垂直滚动条 */
      // overflow: hidden;
      /* 设置滚动条的样式 */

      .zengti {
        margin: 10px 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 315px;
        height: 38px;
        gap: 5px;

        .left {
          display: flex;
          align-items: center;
          justify-content: space-evenly;
          width: 84px;
          height: 27px;

          .yuan {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #08f7f7;
          }

          .wenziss {
            display: flex;
            align-items: flex-start;
            flex-direction: column;

            .p1 {
              font-family: Source Han Sans SC;
              font-weight: 500;
              font-size: 10px;
              color: #ffffff;
            }

            .p2 {
              font-family: Source Han Sans SC;
              font-weight: 500;
              font-size: 10px;
              color: #55cff9;
            }
          }
        }

        .right {
          background: url("../assets/image/rightbeij.png");
          background-size: 100% 100%;
          background-repeat: no-repeat;

          width: 217px;
          height: 38px;
          font-family: Source Han Sans SC;
          font-weight: 500;
          font-size: 11px;
          color: #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    .boxxx::-webkit-scrollbar {
      width: 1px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条轨道的样式 */
    .boxxx::-webkit-scrollbar-track {
      background-color: #f1f1f1;
      /* 设置滚动条轨道的背景色 */
    }

    /* 设置滚动条滑块的样式 */
    .boxxx::-webkit-scrollbar-thumb {
      background-color: #013363;
      /* 设置滚动条滑块的背景色 */
    }
  }

  .no-animation {
    transition: none;
  }

  .right-panel-active {
    transform: translate(0%);
    // animation: slideIn 1s ease-in-out ;
  }

  .right-panel-active1 {
    // transform: translate(0%);
    animation: slideIn 1s ease-in-out forwards;
  }

  @keyframes slideIn {
    0% {
      transform: translateX(100%);
    }

    // 30% {
    //   transform: translateX(65%);
    // }

    // 40% {
    //   transform: translateX(40%);
    // }

    // 65% {
    //   transform: translateX(15%);
    // }

    // 85% {
    //   transform: translateX(25%);
    // }

    100% {
      transform: translateX(0%);
    }
  }

  .completed {
    background: #7ad0ff;
  }

  .incomplete {
    background: #ff6041;
  }

  .warning {
    background: #00ffc0;
  }

  .completeds {
    color: #7ad0ff;
  }

  .incompletes {
    color: #ff6041;
  }

  .warnings {
    color: #00ffc0;
  }
}

.warn1 {
  background: url("../assets/image/warnred.png");
}

.warn2 {
  background: url("../assets/image/warnyellow.png");
}

.warn3 {
  background: url("../assets/image/warngreen.png");
}

.xxxx {
  position: absolute;
  top: 1%;
  right: 1%;
  width: 25px;
  height: 25px;
  z-index: 99999;
}

/* 菜单容器 */
.menu-container {
  display: flex;
  gap: 20px;
}

/* 菜单样式 */
.menu {
  width: 100%;
  // background-color: #fff;
  // border: 1px solid #ddd;
  border-radius: 4px;
}

/* 菜单项样式 */
.menu-group {
  margin-top: 14px;
}

.menu-item {
  cursor: pointer;
  background: url("../assets/image/rightbeij.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;

  width: 100%;
  height: 36px;
  font-family: Source Han Sans SC;
  font-weight: 500;
  font-size: 20px;
  color: #ffffff;
  display: flex;
  align-items: center;
  padding-left: 10px;
  // justify-content: center;
}

.menu-item:hover {
  background-color: #f0f0f0 !important;
  color: #00ffc0;
}

.submenu {
  display: block;
  /* 确保子菜单是块级元素 */
  // background-color: #f9f9f9;
  padding-left: 8px;
}

.submenu-items:hover {
  color: #00ffc0;
}

.submenu-item:hover {
  color: #00ffc0;
}

.submenu-items {
  cursor: pointer;
  // background-color:#013363;
  width: 100%;

  font-family: Source Han Sans SC;
  font-weight: 500;
  font-size: 18px;
  color: #ffffff;
  /* 修改 display 属性 */
  display: flex;
  flex-direction: column;
  /* 垂直方向排列 */
  align-items: flex-start;
  padding-left: 10px;
  margin-top: 10px;
}

.submenu-item {
  cursor: pointer;
  background: url("../assets/image/rightbeij.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: 300px !important;
  /* 移除固定高度 */
  /* height: 30px; */
  font-family: Source Han Sans SC;
  font-weight: 500;
  font-size: 15px;
  color: #ffffff;
  /* 修改 display 属性 */
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding-left: 10px;

  margin-top: 10px;
}

.qiuqiu {
  display: flex;
  align-items: center;

  .siqiu {
    width: 16px;
    height: 16px;
    margin-left: 10px;
    margin-right: 7px;
  }
}

.listtype {
  background: url("../assets/image/xuanzhotiemmoon.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 80px;
  height: 26px;
  text-align: center;
  line-height: 26px;

  font-size: 13px;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  color: #fff;
}

.listtypes {
  background: url("../assets/image/xuanzhotiemmoon.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 100px;
  height: 36px;
  text-align: center;
  line-height: 36px;
  cursor: pointer;
  font-size: 13px;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  color: #fff;
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  font-size: 15px;
}

.yuan {
  width: 10px;
  height: 10px;
  background-color: #518acd;
  border-radius: 50%;
}

.cjhulizhong {
  font-family: Microsoft YaHei;
  font-weight: bold;
  font-size: 14px;
  color: #64f8bb;
  margin-left: 8px;
}

.boxxxs {
  margin-left: 8px;
  margin-top: 1px;
  margin-bottom: 18px;
  // background: url("../assets/image/zuoshang1.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;

  width: 330px;

  // height: 254px;
  .warning12 {
    background-size: 100% 100%;
    // width: 365px;
    height: 47px;

    .info {
      margin-top: 5px;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      text-align: left;

      .info1 {
        display: flex;
        flex-direction: column;

        p:nth-of-type(1) {
          margin-bottom: 10px;
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 13px;
          color: #ffffff;
        }

        .red {
          color: #ff0000;
        }

        .green {
          color: #00ffcc;
        }

        .yellow {
          color: #ffff00;
        }

        p:nth-of-type(2) {
          font-family: Alibaba PuHuiTi;
          font-weight: bold;
          font-size: 14px;
          color: #ffffff;
        }
      }

      .info2 {
        cursor: pointer;
        margin-right: 10px;
        font-size: 15px;
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        color: #cffff8;
      }
    }
  }

  .zongduan {
    display: flex;
    align-items: center;
  }

  .ql-center {
    display: flex;
    margin-top: 20px;
    justify-content: space-around;
    margin-top: 14px;
    padding-top: 10px;

    .ql-Box {
      width: 30%;
      height: 49px;
      border: 1px solid #7ad0ff;
      // opacity: 0.6;
      border-radius: 2px;

      .ql-box1 {
        font-family: Alibaba PuHuiTi;
        font-weight: bold;
        font-size: 19px;
        color: #7ad0ff;
      }

      .ql-box {
        display: flex;
        padding-left: 23px;
        padding-right: 9px;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 24px;

        .left_ql {
          display: flex;
          justify-content: space-between;
          align-items: center;
          color: #ffffff;

          .yuan {
            width: 7px;
            height: 7px;
            border-radius: 50%;
          }

          .pp {
            margin-left: 5px;
            color: #fff;
            font-size: 13px;
          }
        }

        img {
          height: 12px;
          width: 7px;
        }
      }
    }
  }
}
</style>
