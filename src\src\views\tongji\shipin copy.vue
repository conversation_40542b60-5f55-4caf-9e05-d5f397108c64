<template>
  <div class="contents" v-if="isshow">
    <div class="toubu">
      <div style="margin-left: 20px; display: flex; align-items: center" v-if="false">
        <div style="display: flex; width: 100%; align-items: center">
          <span class="sp">当前位置：</span>
          <el-select class="el-select" v-model="selectvalue1" placeholder="selectvalue1"
            style="width: 64px; height: 35.1px" @change="handleChange">
            <el-option v-for="item in options1" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-select class="el-select" v-model="selectvalue2" placeholder="selectvalue2"
            style="width: 64px; height: 35.1px" @change="handleChange">
            <el-option v-for="item in options2" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-select class="el-select" v-model="selectvalue3" placeholder="selectvalue3"
            style="width: 78px; height: 35.1px" @change="handleChange">
            <el-option v-for="item in options3" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <img v-if="isshow" class="img1sss" @click="anniu()" src="../../assets/image/table-x.png" alt="" />
      </div>

      <div class="all">
        <div class="all1">
          <Titles class="ltitle" tit="监控统计">
            <!-- <zhuzhuangtu class="zhuzhuangtu" :chartData="chartData1"></zhuzhuangtu> -->
            <!-- <div class="nenghao">累计总能耗:</div> -->
            <!-- <p class="nhp">92673.0 kwh</p> -->
            <div class="nh">
              <div class="nhimg">
                <img class="imgg" src="../../assets/image/jk.png" alt="" />
              </div>

              <div class="nhtit">
                <p class="p11">165</p>
                <p class="p2">总数</p>
              </div>
            </div>
            <div class="nh">
              <div class="nhimg">
                <img class="imgg" src="../../assets/image/jk.png" alt="" />
              </div>

              <div class="nhtit">
                <p class="p11">165</p>
                <p class="p2">在线</p>
              </div>
            </div>
            <div class="nh">
              <div class="nhimg">
                <img class="imgg" src="../../assets/image/jk.png" alt="" />
              </div>

              <div class="nhtit">
                <p class="p12">0</p>
                <p class="p2">离线</p>
              </div>
            </div>
          </Titles>
          <Titles class="ltitle11" tit="监控状态分析">
            <liti class="zhuzhuangtu" :echartData="optionData"></liti>
            <!-- <div class="shinei">
              <Electricity4></Electricity4>
            </div> -->
          </Titles>
        </div>
        <div class="line1"></div>
        <div class="all2">
          <Titles class="ltitle1" tit="核心区域实时监控">
            <div class="controls">
              <!-- <span class="page-info">第 {{ currentPage }}/{{ totalPages }} 页</span> -->
              <el-button class="control-btn" type="primary" size="mini" @click="prevPage"
                :disabled="currentPage === 1">上一页</el-button>
              <el-button class="control-btn" type="primary" size="mini" @click="nextPage"
                :disabled="currentPage === totalPages">下一页</el-button>
            </div>

            <div class="jkjkjk">
              <div v-for="(cameraId, index) in currentCameras" :key="index" class="jiank" @click="showBigImage(index)">
                <div :id="'player-' + index" class="video-container"></div>
              </div>
            </div>
          </Titles>
          <!-- <Titles class="ltitle" tit="办公设备使用情况">
              <div class="shinei">
                <Electricity8></Electricity8>
              </div>
            </Titles>
            <div>
              <Titles class="ltitle1" tit="设备购入时间">
                <div class="shinei">
                  <Electricity3 :chartData="chartData3"></Electricity3>
                </div>
              </Titles>
            </div> -->
        </div>
      </div>
      <!-- <img class="bigjk" src="../../assets/jk1.png" alt=""> -->
      <div v-if="showBig" class="bigjk">
        <div :id="'big-player'" class="bigjk1"></div>
        <img class="closeBigImage" @click="closeBigImage" src="../../assets/close.png" alt="" />
      </div>
    </div>
  </div>
</template>

<script>
import Electricity from "@/components/echarts/dianbiao/biao1.vue";
import biao1s from "@/components/echarts/dianbiao/biao1s.vue";
import biao1ss from "@/components/echarts/dianbiao/biao1ss.vue";
import Titles from "@/components/common/Titles.vue";

import Electricity2 from "@/components/echarts/dianbiao/Electricity2.vue";
import Electricity3 from "@/components/fuyong//zhexiantu.vue";
import Electricity4 from "@/components/echarts/dianbiao/Electricity4.vue";
import Electricity5 from "@/components/echarts/dianbiao/Electricity5.vue";
import Electricity6 from "@/components/fuyong/Electricity6.vue";
import Electricity7 from "@/components/fuyong/Electricity7.vue";
// import Electricity7 from "@/components/echarts/dianbiao/Electricity7.vue";
import Electricity8 from "@/components/fuyong/Electricity8.vue";
import huanxing from "@/components/fuyong/xiaobingtu.vue";
import zhuzhuangtu from "@/components/fuyong/zhuzhuangtu.vue";
import liti from "@/components/fuyong/huanxing.vue";
import flvjs from 'flv.js'

export default {
  components: {
    Titles,
    Electricity,
    Electricity2,
    Electricity3,
    Electricity4,
    Electricity5,
    Electricity6,
    Electricity7,
    Electricity8,
    huanxing,
    biao1s,
    biao1ss,
    zhuzhuangtu,
    liti,
  },
  data() {
    return {
      showBig: false, // 是否显示大图
      bigImage: "", // 放大的图片地址
      optionData: [
        {
          name: "在线数",
          value: 165,
          itemStyle: { color: "#00AA00", opacity: 0.9 },
        },
        {
          name: "离线数",
          value: 0,
          itemStyle: { color: "#EB6877", opacity: 0.9 },
        },
      ],
      chartData: {
        value: [112, 132],
        legend: ["仪器设备", "办公设备"],
      },
      chartData1: {
        title: ["已领用", "未领用"],
        xAxisdata: ["20日", "21日", "22日", "23日", "25日", "24日"],
        yAxisdata1: [720, 710, 730, 705, 715, 725],
        yAxisdata2: [480, 490, 470, 495, 485, 475],
      },
      chartData2: {
        title: [" ", "未领用"],
        xAxisdata: ["20日", "21日", "22日", "23日", "25日", "24日"],
        yAxisdata1: [600, 580, 620, 590, 610, 605],
        yAxisdata2: [300, 320, 280, 310, 290, 295],
      },
      chartData3: {
        title: ["仪器设备", "办公设备"],
        xAxisdata: [
          "10/16",
          "10/17",
          "10/18",
          "10/19",
          "10/20",
          "10/21",
          "10/22",
          "10/23",
          "10/24",
          "10/25",
          "10/26",
          "10/27",
          "10/28",
        ],
        yAxisdata1: [4, 17, 5, 9, 6, 5, 0, 0, 12, 0, 4, 0, 1],
        yAxisdata2: [14, 7, 2, 3, 16, 5, 0, 0, 2, 0, 13, 0, 0],
      },
      isshow: true,
      options: [
        {
          value: "总览",
          label: "总览",
        },
        {
          value: "能耗分析",
          label: "能耗分析",
        },
        {
          value: "能流分析",
          label: "能流分析",
        },
        {
          value: "设备状态",
          label: "设备状态",
        },
        {
          value: "一键抄表",
          label: "一键抄表",
        },
        {
          value: "费用管理",
          label: "费用管理",
        },
        {
          value: "碳排放管理",
          label: "碳排放管理",
        },
      ],
      selectvalue2: "B3",
      options2: [
        {
          value: "B3",
          label: "B3",
        },
      ],
      selectvalue3: "B3",
      options3: [
        {
          value: "B3",
          label: "B3",
        },
      ],
      selectvalue1: "B3",
      options1: [
        {
          value: "B3",
          label: "B3",
        },
      ],
      selectvalue3: "B1栋",
      options4: [
        {
          value: "B1栋",
          label: "B1栋",
        },
        {
          value: "B2栋",
          label: "B2栋",
        },
        {
          value: "B3栋",
          label: "B3栋",
        },
        {
          value: "B4栋",
          label: "B4栋",
        },
        {
          value: "W1栋",
          label: "W1栋",
        },
        {
          value: "W2栋",
          label: "W2栋",
        },
      ],
      selectvalue4: "B1栋",

      optionData1: [
        {
          name: "一级告警",
          value: 16,
          itemStyle: { color: "#EB6877", opacity: 0.9 },
        },
        {
          name: "二级告警",
          value: 27,
          itemStyle: { color: "#F8B551", opacity: 0.9 },
        },
        {
          name: "三级告警",
          value: 17,
          itemStyle: { color: "#B954E8", opacity: 0.9 },
        },
        {
          name: "四级告警",
          value: 40,
          itemStyle: { color: "#0284F0", opacity: 0.9 },
        },
      ],
      // 视频轮播相关数据
      currentPage: 1,
      pageSize: 9,
      allCameraIds: [], // 所有有效的摄像头ID
      currentCameras: [], // 当前页面显示的摄像头ID
      players: [], // 存储所有播放器实例
      currentBigPlayer: null, // 当前大屏播放器
      videoUrl: 'http://192.168.3.250:9080/TD/175.live.flv',

    };
  },
  computed: {
    totalPages() {
      return Math.ceil(this.allCameraIds.length / this.pageSize);
    }
  },
  created() {
    this.generateCameraIds();
    this.updateCurrentCameras();
  },
  methods: {
    generateCameraIds() {
      // 从1到175，排除13-22的ID
      for (let i = 1; i <= 175; i++) {
        if (!(i >= 13 && i <= 22&&i==170&&i==171)) {
          this.allCameraIds.push(i);
        }
      }
    },
    updateCurrentCameras() {
      this.destroyPlayers();
      const startIndex = (this.currentPage - 1) * this.pageSize;
      this.currentCameras = this.allCameraIds.slice(startIndex, startIndex + this.pageSize);
      this.$nextTick(() => {
        this.currentCameras.forEach((_, index) => {
          this.initPlayer(index);
        });
      });
    },
    prevPage() {
      if (this.currentPage > 1) {
        this.currentPage--;
        this.updateCurrentCameras();
      }
    },
    nextPage() {
      if (this.currentPage < this.totalPages) {
        this.currentPage++;
        this.updateCurrentCameras();
      }
    },
    getStreamUrl(cameraId) {
      return `http://192.168.3.250:9081/#/play/wasm/ws%3A%2F%2F192.168.3.250%3A9080%2FTD%2F${cameraId}.live.flv`;
      // http://192.168.3.250:9080/TD/175.live.flv
    },
    showBigImage(index) {
      this.showBig = true;
      this.$nextTick(() => {
        if (flvjs.isSupported()) {
          const videoElement = document.createElement('video');
          videoElement.style.width = '100%';
          videoElement.style.height = '100%';
          document.getElementById('big-player').appendChild(videoElement);

          const flvPlayer = flvjs.createPlayer({
            type: 'flv',
            url: this.videoUrl
          });
          flvPlayer.attachMediaElement(videoElement);
          flvPlayer.load();
          flvPlayer.play();

          this.currentBigPlayer = flvPlayer;
        }
      });
    },
    closeBigImage() {
      if (this.currentBigPlayer) {
        this.currentBigPlayer.destroy();
        this.currentBigPlayer = null;
      }
      const bigPlayer = document.getElementById('big-player');
      if (bigPlayer) {
        bigPlayer.innerHTML = '';
      }
      this.showBig = false;
    },
    destroyPlayers() {
      this.players.forEach(player => {
        if (player) {
          player.destroy();
        }
      });
      this.players = [];
    },
    initPlayer(index) {
      if (flvjs.isSupported()) {
        const videoElement = document.createElement('video');
        videoElement.style.width = '100%';
        videoElement.style.height = '100%';
        document.getElementById('player-' + index).appendChild(videoElement);

        const flvPlayer = flvjs.createPlayer({
          type: 'flv',
          url: this.videoUrl
        });
        flvPlayer.attachMediaElement(videoElement);
        flvPlayer.load();
        flvPlayer.play();

        this.players[index] = flvPlayer;
      }
    },
    anniu() {
      this.isshow = false;
    },
  },
  beforeDestroy() {
    this.destroyPlayers();
    if (this.currentBigPlayer) {
      this.currentBigPlayer.destroy();
    }
  },
};
</script>

<style lang="less" scoped>
.ltitle {
  margin-top: 10px;
}

.bigjk {
  position: fixed;
  z-index: 200;
  top: 0px;
  left: 180px;
  width: 81%;
  height: 867px;

  .bigjk1 {
    width: 100%;
    height: 867px;
  }

  .closeBigImage {
    position: absolute;
    z-index: 200;
    top: -0;
    right: 0px;
    cursor: pointer;
    width: 40px;
    height: 40px;
  }
}

.jkjkjk {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

.jiank {
  margin-top: 8px;
  margin-left: 5px;
  margin-right: 2px;
  width: 423px;
  height: 254px;
}

.zhuzhuangtu {
  margin-top: 20px;
  margin-bottom: 10px;
}

.all {
  display: flex;
  flex-direction: row;
  margin-top: 5px;

  .zong {
    display: flex;
    flex-direction: row;
    margin-top: 10px;

    .echart1,
    .echart2 {
      flex: 1;

      .center {
        margin-top: -24px;
        font-family: "Source Han Sans SC", sans-serif;
        font-weight: 400;
        font-size: 17px;
        color: #00ffb6;
        text-align: center;
        margin-bottom: 10px;
      }

      .btn {
        width: 133px;
        height: 31px;
        border: 1px solid #2d6cb0;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: "Source Han Sans SC", sans-serif;
        font-weight: bold;
        font-size: 15px;
        color: #ffffff;
        border-radius: 30px;
        margin-left: 7%;
      }
    }
  }

  .ltitle1 {
    margin-top: 10px;
  }

  .ltitle11 {
    margin-top: 30px;
  }

  .line1 {
    width: 2px;
    height: 823px;
    opacity: 0.64;
    background-color: #204964;
  }

  .all1 {
    flex: 1;

    .nenghao {
      width: 227px;
      height: 173px;
      background: url("../../assets/image/nenghao.png");
      background-size: 100% 100%;
      margin-left: 120px;
      margin-top: 21px;
      text-align: center;
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      font-size: 16px;
      color: #ffffff;
      line-height: 213px;
    }

    .nhp {
      text-align: center;
      font-family: Alibaba PuHuiTi;
      font-weight: 500;
      font-size: 30px;
      color: #2cc1ff;
      margin-top: 8px;
    }

    .nh {
      margin-left: 14px;
      margin-top: 5px;
      width: 423px;
      height: 93px;
      border: 1px solid #364d5a;
      background-size: 100% 100%;
      display: flex;
      flex-direction: row;
      align-items: center;
      padding-left: 42px;

      .nhimg {
        width: 80.6px;
        height: 80px;
        background: url("../../assets/image/jkbg.png");
        background-size: 100% 100%;
        display: flex;
        justify-content: center;
        align-items: center;

        .imgg {
          width: 40.6px;
          height: 35px;
        }
      }

      .nhtit {
        width: 178px;
        margin-left: 30px;
        margin-top: 10px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        .p11 {
          margin-left: 20px;
          font-family: PangMenZhengDao;
          font-weight: 400;
          font-size: 30px;
          color: #7acfff;
        }

        .p12 {
          font-family: PangMenZhengDao;
          font-weight: 400;
          font-size: 30px;
          color: #ffa170;
          margin-left: 30px;
        }

        .p2 {
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 28px;
          // margin-left: 20px;
          color: #ffffff;
        }
      }

      .nhtit1 {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-left: 35px;

        .nhimg1 {
          width: 16px;
          height: 20px;
        }

        .pp1 {
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 17px;

          color: #0df29b;
        }

        .pp2 {
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 17px;
          color: #ffa170;
        }
      }

      .nht {
        margin-top: 10px;
        display: flex;
        flex-direction: column;

        .pp {
          margin-left: 35px;
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 17px;

          color: #cccccc;
        }
      }
    }
  }

  .all2 {
    margin-left: 38px;
    flex: 2.8;
    display: flex;
    flex-direction: column;

    .shinei {
      .itemshei {
        display: flex;
        justify-content: space-around;

        .nenghaos {
          width: 227px;
          height: 173px;
          background: url("../../assets/image/nenghao.png");
          background-size: 100% 100%;
          text-align: center;
          margin-left: 10px;
          margin-top: 23px;
          text-align: center;
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 14px;
          color: #ffffff;
          line-height: 144px;
        }

        .nhps {
          text-align: center;
          font-family: Alibaba PuHuiTi;
          font-weight: 500;
          font-size: 21px;
          color: #2cc1ff;
          margin-top: 8px;
        }
      }
    }
  }

  .all3 {
    flex: 668;
    margin-left: 45px;
  }
}

.shinei {
  width: 100%;
  height: 100%;
}

.shuantitle {
  width: 100%;
  display: flex;
  margin-top: 10px;

  .title {
    width: 95%;
    background: url("../../assets/image/title.png");
    background-size: 100% 100%;

    height: 25px;
    font-family: Source Han Sans SC;
    font-weight: 400;
    font-size: 25px;
    color: #ffffff;
    text-shadow: 2px 2px 3px rgba(0, 0, 0, 0.47);
    font-style: italic;
    text-align: left;
    line-height: 4px;
    padding-left: 33px;
  }
}

.nenghao {
  width: 167px;
  height: 113px;
  background: url("../../assets/image/nenghao.png");
  background-size: 100% 100%;
  text-align: center;
  margin-left: 83px;
  margin-top: 63px;
  text-align: center;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  line-height: 144px;
}

.nhp {
  text-align: center;
  font-family: Alibaba PuHuiTi;
  font-weight: 500;
  font-size: 21px;
  color: #2cc1ff;
  margin-top: 8px;
}

.contents {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: url("../../assets/image/zichanbeijin.png");

  background-size: 100% 100%;
  width: 1863px;
  height: 868px;
  z-index: 99999;
  padding-left: 34px;
  padding-right: 22px;
  padding-top: 21px;
}

.toubu {
  width: 100%;

  position: relative;
}

.el-select {
  margin-top: -1px;
  margin-left: 10px;
  background: #00203d;
  border-radius: 3px;
  border: 1px solid #3e89db;

  /deep/.el-select__wrapper {
    background: #00203d !important;
    box-shadow: none;
  }

  /deep/.el-select__wrapper .is-hovering:not {
    box-shadow: none;
  }

  /deep/.el-select__wrapper:hover {
    box-shadow: none;
  }

  /deep/.el-select__placeholder.is-transparent {
    color: #2cc1ff;
  }

  /deep/.el-select__placeholder {
    color: #2cc1ff;
  }

  /deep/.el-select-dropdown__item.is-hovering {
    background-color: #2cc1ff !important;
  }
}

.sp {
  margin-top: -5px;
  margin-left: 12px;
  font-family: Alibaba PuHuiTi;
  font-weight: bold;
  font-size: 21px;
  color: #2cc1ff;
}

.img1sss {
  cursor: pointer;
  width: 15px;
  height: 15px;
}

.controls {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: -35px;
  margin-right: 10px;
}

.page-info {
  margin-right: 10px;
  color: #2cc1ff;
  font-size: 14px;
}

.control-btn {
  margin-left: 5px;
}

.video-container {
  width: 100%;
  height: 100%;
  background: #000;
}
</style>
