<template>
  <div class="contents" v-if="isshow">
    <div class="toubu">
      <div style="margin-left: 20px; display: flex; align-items: center" v-if="false">
        <div style="display: flex; width: 100%; align-items: center">
          <span class="sp">当前位置：</span>
          <el-select class="el-select" v-model="selectvalue1" placeholder="selectvalue1"
            style="width: 64px; height: 35.1px" @change="handleChange">
            <el-option v-for="item in options1" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-select class="el-select" v-model="selectvalue2" placeholder="selectvalue2"
            style="width: 64px; height: 35.1px" @change="handleChange">
            <el-option v-for="item in options2" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-select class="el-select" v-model="selectvalue3" placeholder="selectvalue3"
            style="width: 78px; height: 35.1px" @change="handleChange">
            <el-option v-for="item in options3" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <img v-if="isshow" class="img1sss" @click="anniu()" src="../../assets/image/table-x.png" alt="" />
      </div>

      <div class="all">
        <div class="all1">

        
          <Titles class="ltitle" tit="实时报警信息">
            <div class="shbei">
              <div class="item">
                <img src="../../assets/image/centerAcimg1.png" alt="" />
                <div class="numlist">
                  <div class="it1">{{deviceList.length}}</div>
                  <div class="it2">通风柜总数</div>
                </div>
              </div>
              <div class="item">
                <img src="../../assets/image/centerAcimg2.png" alt="" />
                <div class="numlist">
                  <div class="it1 it3">{{abnormalCount}}</div>
                  <div class="it2">异常总数</div>
                </div>
              </div>
              <div class="item">
                <img src="../../assets/image/centerAcimg2.png" alt="" />
                <div class="numlist">
                  <div class="it1 it3">{{faultCount}}</div>
                  <div class="it2">故障总数</div>
                </div>
              </div>
            </div>
            <echarts1 style="margin-top:20px"></echarts1>
          </Titles>
          <Titles class="ltitle1" :tit="title11">
            <div class="zong">
              <div class="echart1">
                <Electricity :echartDatas="echartDatas1"></Electricity>
                <div class="center">m/s</div>
                <div class="btn">面风速</div>
              </div>
              <div class="echart2">
                <Electricity :echartDatas="echartDatas2"></Electricity>
                <div class="center">m³/h</div>
                <div class="btn">排风量</div>
              </div>
            </div>
            <!-- <div class="shinei">
              <Electricity4></Electricity4>
            </div> -->
          </Titles>
        </div>
        <div class="line1"></div>
        <div class="all2">
          <Titles class="ltitle" :tit="title12">
            <div class="shinei">
              <Electricity5 :echartDatas="echartData2"></Electricity5>
            </div>
          </Titles>
          <Titles class="ltitle1" :tit="title13">
            <div class="shinei">
              <!-- <echarts3 class="echart3" :echartDatas="echartData2"></echarts3> -->
              <Electricity5 :echartDatas="echartData21"></Electricity5>
            </div>
          </Titles>

          <!-- <Titles class="ltitle1" tit="送风温度分析">
            <div class="shinei">
              <echarts3 class="echart3" :echartData="echartData2"></echarts3>
            </div>
          </Titles>
          <Titles class="ltitle" tit="送风湿度分析">
            <div class="shinei">
              <echarts3 class="echart3" :echartData="echartData3"></echarts3>
            </div>
          </Titles> -->
          <!-- <div>
            <Titles class="ltitle1" tit="设备购入时间">
              <div class="shinei">
                <Electricity3 :chartData="chartData3"></Electricity3>
              </div>
            </Titles>
          </div> -->
        </div>
        <div class="all3">
          <Titles class="ltitle" tit="故障统计">
            <div class="shinei">
              <Electricity4></Electricity4>
            </div>
          </Titles>
          <Titles class="ltitle1" tit="故障分析">
            <div class="shinei">
              <liti class="zhuzhuangtu" :echartData="optionData"></liti>
            </div>
          </Titles>
          <!-- <Titles class="ltitle1" tit="资产占比分析">
            <div class="shinei">
              <huanxing :chartData="chartData"></huanxing>
            </div>
          </Titles> -->
          <!-- <div class="shuantitle">
            <div style="width: 50%">
              <div class="title">实时负载率</div>
              <div class="nenghao">实时负载率:</div>
              <p class="nhp">30%</p>
            </div>
            <div style="width: 50%">
              <div class="title">实时总功率</div>
              <div class="nenghao">实时总功率:</div>
              <p class="nhp">200Kw</p>
            </div>
          </div>
      -->



        </div>
      </div>
    </div>
  </div>
</template> 

<script>
import Electricity from "@/components/echarts/kongya/biao1.vue";
import biao1s from "@/components/echarts/dianbiao/biao1s.vue";
import biao1ss from "@/components/echarts/dianbiao/biao1ss.vue";
import Titles from "@/components/common/Titles.vue";
import echarts1 from "@/components/fuyong/echarts1.vue";
import Electricity2 from "@/components/echarts/dianbiao/Electricity2.vue";
import Electricity3 from "@/components/fuyong//zhexiantu.vue";
import Electricity4 from "@/components/fuyong/Electricity41.vue";
import Electricity5 from "@/components/fuyong/Electricity5.vue";
import Electricity6 from "@/components/fuyong/Electricity6.vue";
import Electricity7 from "@/components/fuyong/Electricity7.vue";
// import Electricity7 from "@/components/echarts/dianbiao/Electricity7.vue";
import Electricity8 from "@/components/fuyong/Electricity8.vue";
import huanxing from "@/components/fuyong/xiaobingtu.vue";
import zhuzhuangtu from "@/components/fuyong/zhuzhuangtu.vue";
import echarts3 from "@/components/fuyong/shuangxiang.vue";
import liti from "@/components/fuyong/huanxing1.vue";
import { getDeviceData, getDevicedetails } from "@/api/device";
export default {
  components: {
    echarts1,
    echarts3,
    Titles,
    Electricity,
    Electricity2,
    Electricity3,
    Electricity4,
    Electricity5,
    Electricity6,
    Electricity7,
    Electricity8,
    huanxing,
    biao1s,
    biao1ss,
    zhuzhuangtu,
    liti
  },
  data() {
    return {
      deviceList: [
        { deviceid: 2011001, name: "1F-A区A103-01" },
        { deviceid: 2011002, name: "B1F电镜制样间1-01" },
        { deviceid: 2011003, name: "B1F电镜制样间1-02" },
        { deviceid: 2011004, name: "1F-C区C108-01" },
        { deviceid: 2011005, name: "2F-C区C208-01" },
        { deviceid: 2011006, name: "2F-C区C208-02" },
        { deviceid: 2011007, name: "1F-E区E108-01" },
        { deviceid: 2011008, name: "1F-E区E108-02" },
        { deviceid: 2011009, name: "2F-A区228" },
        { deviceid: 2011010, name: "2F-E区C204-01" },
        { deviceid: 2011011, name: "2F-E区C204-02" },
        { deviceid: 2011012, name: "2F-E区C205-01" },
        { deviceid: 2011013, name: "2F-E区C205-02" }
      ],
      currentDeviceIndex: 0,
      title11: '通风柜实时运行信息',
      title12: '面风速统计',
      title13: '排风量统计',
      abnormalCount: 0,
      faultCount: 0,
      echartDatas1: { min: 0, max: 10, text: 0 },
      echartDatas2: { min: 0, max: 1000, text: 0 },
      echartData2: {
        data1: [],
        data2: [],
        color: '#00FFFF',
        unit: 'm/s'
      },
      echartData21: {
        data1: [],
        data2: [],
        color: '#FFD52E',
        unit: 'm³/h'
      },
      optionData: [
        {
          name: "门控故障",
          value: 6,
          itemStyle: { color: "#00FFFF", opacity: 0.9 },
        },
        {
          name: "门位超限",
          value: 2,
          itemStyle: { color: "#FFFF00", opacity: 0.9 },
        },
        {
          name: "风速异常",
          value: 3,
          itemStyle: { color: "#E63F00", opacity: 0.9 },
        },
      ],
      isshow: true,
      selectvalue2: "B3",
      options2: [
        {
          value: "B3",
          label: "B3",
        },
      ],
      selectvalue3: "B3",
      options3: [
        {
          value: "B3",
          label: "B3",
        },
      ],
      selectvalue1: "B3",
      options1: [
        {
          value: "B3",
          label: "B3",
        },
      ],
    };
  },
  methods: {
    // 获取设备详细信息
    async getDeviceInfo(deviceId) {
      try {
        const res = await getDevicedetails(deviceId);
        if (res.code === 200) {
          const data = res.data;
          let mianfengsu = 0;
          let paifengfeng = 0;
          
          // 遍历设备数据获取面风速和排风量
          if (data.deviceDataBase && data.deviceDataBase.length > 0) {
            for (const item of data.deviceDataBase) {
              if (item.dmName === "面风速") {
                mianfengsu = parseFloat(item.dVal);
              } else if (item.dmName === "通风柜风量") {
                paifengfeng = parseFloat(item.dVal);
              }
            }
          }
          
          // 更新仪表盘数据
          this.echartDatas1.text = mianfengsu;
          this.echartDatas2.text = paifengfeng;
          
          // 更新标题
          const deviceName = this.deviceList.find(item => item.deviceid === deviceId)?.name || '';
          this.title11 = deviceName + '实时运行信息';
          this.title12 = deviceName + '面风速统计';
          this.title13 = deviceName + '排风量统计';
          
          return { mianfengsu, paifengfeng };
        }
      } catch (error) {
        console.error("获取设备详情失败", error);
      }
    },
    
    // 获取面风速历史数据
    async getMianfengsuHistory(deviceId) {
      try {
        const res = await getDeviceData(deviceId, deviceId + '5');
        if (res.code === 200 && res.data && res.data.data) {
          const historyData = res.data.data;
          const xAxis = [];
          const yAxis = [];
          
          // 处理历史数据
          historyData.forEach(item => {
            const time = item.recordedAt.split(' ')[1].substring(0, 5); // 提取时间部分 HH:MM
            xAxis.push(time);
            yAxis.push(parseFloat(item.indication));
          });
          
          // 更新图表数据
          this.echartData2.data1 = xAxis;
          this.echartData2.data2 = yAxis;
          
          return { xAxis, yAxis };
        }
      } catch (error) {
        console.error("获取面风速历史数据失败", error);
      }
    },
    
    // 获取排风量历史数据
    async getPaifengliangHistory(deviceId) {
      try {
        const res = await getDeviceData(deviceId, deviceId + '6');
        if (res.code === 200 && res.data && res.data.data) {
          const historyData = res.data.data;
          const xAxis = [];
          const yAxis = [];
          
          // 处理历史数据
          historyData.forEach(item => {
            const time = item.recordedAt.split(' ')[1].substring(0, 5); // 提取时间部分 HH:MM
            xAxis.push(time);
            yAxis.push(parseFloat(item.indication));
          });
          
          // 更新图表数据
          this.echartData21.data1 = xAxis;
          this.echartData21.data2 = yAxis;
          
          return { xAxis, yAxis };
        }
      } catch (error) {
        console.error("获取排风量历史数据失败", error);
      }
    },
    
    // 定时切换设备
    startDeviceRotation() {
      // 先加载第一个设备的数据
      this.loadDeviceData(this.deviceList[0].deviceid);
      
      // 设置定时器，每隔一段时间切换设备
      setInterval(() => {
        this.currentDeviceIndex = (this.currentDeviceIndex + 1) % this.deviceList.length;
        const currentDevice = this.deviceList[this.currentDeviceIndex];
        this.loadDeviceData(currentDevice.deviceid);
      }, 10000); // 每5秒切换一次
    },
    
    // 加载设备数据
    async loadDeviceData(deviceId) {
      // 先获取设备基本信息
      await this.getDeviceInfo(deviceId);
      
      // 获取历史数据
      await this.getMianfengsuHistory(deviceId);
      await this.getPaifengliangHistory(deviceId);
    },
    
    anniu() {
      this.isshow = false;
    },
  },
  mounted() {
    // 初始化统计数据
    this.abnormalCount = 0;
    this.faultCount = 0;
    
    // 启动设备轮换显示
    this.startDeviceRotation();
  }
};
</script>

<style lang="less" scoped>
.zhuzhuangtu {
  margin-top: 20px;
  margin-bottom: 10px;
}

.zong {
  width: 378px;
  display: flex;
  flex-direction: row;
  margin-top: 10px;
  margin-left: 35px;

  .echart1,
  .echart2 {
    flex: 1;
    margin-bottom: 20px;

    .center {
      margin-top: -23px;
      font-family: "Source Han Sans SC", sans-serif;
      font-weight: 400;
      font-size: 20px;
      color: #00ffb6;
      text-align: center;
      margin-bottom: 10px;
    }

    .btn {
      width: 133px;
      height: 31px;
      border: 1px solid #2d6cb0;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: "Source Han Sans SC", sans-serif;
      font-weight: bold;
      font-size: 15px;
      color: #ffffff;
      border-radius: 30px;
      margin-left: 25px;
    }
  }
}

.shbei {
  margin-top: 10px;
  margin-left: 10px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: 411px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: space-around;

  .item {
    display: flex;

    img {
      width: 58px;
      height: 56px;
    }

    .numlist {
      margin-left: 2px;

      .it1 {
        font-family: Alibaba PuHuiTi;
        font-weight: bold;
        font-size: 25px;
        color: #ffffff;
      }

      .it2 {
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        font-size: 16px;
        color: #00ffff;
      }

      .it3 {
        color: #00ffcc;
      }
    }
  }
}

.all {
  display: flex;
  flex-direction: row;
  margin-top: 5px;

  .zong {
    display: flex;
    flex-direction: row;
    margin-top: 10px;

    .echart1,
    .echart2 {
      flex: 1;

      .center {
        margin-top: -24px;
        font-family: "Source Han Sans SC", sans-serif;
        font-weight: 400;
        font-size: 17px;
        color: #00ffb6;
        text-align: center;
        margin-bottom: 10px;
      }

      .btn {
        width: 133px;
        height: 31px;
        border: 1px solid #2d6cb0;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: "Source Han Sans SC", sans-serif;
        font-weight: bold;
        font-size: 15px;
        color: #ffffff;
        border-radius: 30px;
        margin-left: 16%;
      }
    }
  }

  .ltitle {
    margin-top: 10px;
  }

  .ltitle1 {
    margin-top: 20px;
  }

  .ltitle11 {
    margin-top: 30px;
  }

  .line1 {
    width: 2px;
    height: 823px;
    opacity: 0.64;
    background-color: #204964;
  }

  .all1 {
    flex: 562;

    .nenghao {
      width: 227px;
      height: 173px;
      background: url("../../assets/image/nenghao.png");
      background-size: 100% 100%;
      margin-left: 120px;
      margin-top: 21px;
      text-align: center;
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      font-size: 16px;
      color: #ffffff;
      line-height: 213px;
    }

    .nhp {
      text-align: center;
      font-family: Alibaba PuHuiTi;
      font-weight: 500;
      font-size: 30px;
      color: #2cc1ff;
      margin-top: 8px;
    }

    .nh {
      margin-left: 24px;
      margin-top: 5px;
      width: 423px;
      height: 93px;
      border: 1px solid #364d5a;
      background-size: 100% 100%;
      display: flex;
      flex-direction: row;
      align-items: center;
      padding-left: 42px;

      .nhimg {
        width: 96.6px;
        height: 70px;
      }

      .nhtit {
        width: 148px;
        margin-left: 10px;
        margin-top: 10px;

        .p11 {
          font-family: PangMenZhengDao;
          font-weight: 400;
          font-size: 30px;
          color: #7acfff;
        }

        .p12 {
          font-family: PangMenZhengDao;
          font-weight: 400;
          font-size: 30px;
          color: #ffa170;
        }

        .p2 {
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 17px;
          color: #ffffff;
        }
      }

      .nhtit1 {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-left: 35px;

        .nhimg1 {
          width: 16px;
          height: 20px;
        }

        .pp1 {
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 17px;
          color: #0df29b;
        }

        .pp2 {
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 17px;
          color: #ffa170;
        }
      }

      .nht {
        margin-top: 10px;
        display: flex;
        flex-direction: column;

        .pp {
          margin-left: 35px;
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 17px;

          color: #cccccc;
        }
      }
    }
  }

  .all2 {
    margin-left: 38px;
    flex: 667;
    display: flex;
    flex-direction: column;

    .shinei {
      .itemshei {
        display: flex;
        justify-content: space-around;

        .nenghaos {
          width: 227px;
          height: 173px;
          background: url("../../assets/image/nenghao.png");
          background-size: 100% 100%;
          text-align: center;
          margin-left: 10px;
          margin-top: 23px;
          text-align: center;
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 14px;
          color: #ffffff;
          line-height: 144px;
        }

        .nhps {
          text-align: center;
          font-family: Alibaba PuHuiTi;
          font-weight: 500;
          font-size: 21px;
          color: #2cc1ff;
          margin-top: 8px;
        }
      }
    }
  }

  .all3 {
    flex: 668;
    margin-left: 45px;
  }
}

.shinei {
  width: 100%;
  height: 100%;
}

.shuantitle {
  width: 100%;
  display: flex;
  margin-top: 10px;

  .title {
    width: 95%;
    background: url("../../assets/image/title.png");
    background-size: 100% 100%;

    height: 25px;
    font-family: Source Han Sans SC;
    font-weight: 400;
    font-size: 25px;
    color: #ffffff;
    text-shadow: 2px 2px 3px rgba(0, 0, 0, 0.47);
    font-style: italic;
    text-align: left;
    line-height: 4px;
    padding-left: 33px;
  }
}

.nenghao {
  width: 167px;
  height: 113px;
  background: url("../../assets/image/nenghao.png");
  background-size: 100% 100%;
  text-align: center;
  margin-left: 83px;
  margin-top: 63px;
  text-align: center;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  line-height: 144px;
}

.nhp {
  text-align: center;
  font-family: Alibaba PuHuiTi;
  font-weight: 500;
  font-size: 21px;
  color: #2cc1ff;
  margin-top: 8px;
}

.contents {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: url("../../assets/image/zichanbeijin.png");
  background-size: 100% 100%;
  width: 1863px;
  height: 868px;
  z-index: 99999;
  padding-left: 34px;
  padding-right: 22px;
  padding-top: 21px;
}

.toubu {
  width: 100%;

  position: relative;
}

.el-select {
  margin-top: -1px;
  margin-left: 10px;
  background: #00203d;
  border-radius: 3px;
  border: 1px solid #3e89db;

  /deep/.el-select__wrapper {
    background: #00203d !important;
    box-shadow: none;
  }

  /deep/.el-select__wrapper .is-hovering:not {
    box-shadow: none;
  }

  /deep/.el-select__wrapper:hover {
    box-shadow: none;
  }

  /deep/.el-select__placeholder.is-transparent {
    color: #2cc1ff;
  }

  /deep/.el-select__placeholder {
    color: #2cc1ff;
  }

  /deep/.el-select-dropdown__item.is-hovering {
    background-color: #2cc1ff !important;
  }
}

.sp {
  margin-top: -5px;
  margin-left: 12px;
  font-family: Alibaba PuHuiTi;
  font-weight: bold;
  font-size: 21px;
  color: #2cc1ff;
}

.img1sss {
  cursor: pointer;
  width: 15px;
  height: 15px;
}
</style>