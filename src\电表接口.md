

 

 

 

 

 

 

目录

[**一、 通信协议****........................................................................................................................................................ 3**](#_Toc3636_WPSOffice_Level1)

[**二、 数据格式****........................................................................................................................................................ 3**](#_Toc16104_WPSOffice_Level1)

[**三、 接口列表****........................................................................................................................................................ 3**](#_Toc4417_WPSOffice_Level1)

[**四、 接口说明****........................................................................................................................................................ 4**](#_Toc10795_WPSOffice_Level1)

[1、 获取Token (get请求)...................................................................................................................... 4](#_Toc16104_WPSOffice_Level2)

[2、 获取用户roomTag............................................................................................................................ 5](#_Toc4417_WPSOffice_Level2)

[3、 获取当前账户信息............................................................................................................................... 7](#_Toc10795_WPSOffice_Level2)

[4、 单户采集电表表显数据...................................................................................................................... 9](#_Toc13951_WPSOffice_Level2)

[5、 获取电表通断状态............................................................................................................................. 11](#_Toc14207_WPSOffice_Level2)

[6、 电表通断控制...................................................................................................................................... 12](#_Toc248_WPSOffice_Level2)

[7、 获取电表能否售电状态.................................................................................................................... 13](#_Toc17360_WPSOffice_Level2)

[8、 充值购电（只针对预付费电能表）.............................................................................................. 14](#_Toc20332_WPSOffice_Level2)

[9、 批量采集抄电表记录（某时间段内）......................................................................................... 15](#_Toc23130_WPSOffice_Level2)

[10、 获取当前账户收支明细（某时间段内）.................................................................................. 18](#_Toc29116_WPSOffice_Level2)

[11、 获取账户欠费信息.......................................................................................................................... 20](#_Toc32453_WPSOffice_Level2)

[12、 获取账户缴费信息（某时间段内）........................................................................................... 22](#_Toc11021_WPSOffice_Level2)

[13、 获取账户缴费统计信息（某时间段内）.................................................................................. 24](#_Toc1336_WPSOffice_Level2)

[14、 获取用户用电量信息（某时间段内）....................................................................................... 27](#_Toc3616_WPSOffice_Level2)

 

[**五、 备注****............................................................................................................................................................... 2**](#_Toc13951_WPSOffice_Level1)**9**

 

 

 

一、 通信协议

采用HTTP传输，请求方式获取Token接口为get请求，其他接口均为POST请求。

二、 数据格式

返回数据均是JSON格式

三、 接口列表

| 序号 | 接口名称             | 说明                                                         |
| ---- | -------------------- | ------------------------------------------------------------ |
| 1    | 获取Token（get请求） | 获取tokenvalue，用于请求其他接口的用户凭证                   |
| 2    | 获取用户roomtag      | 获取用户标识码，代表电表或水表的唯一标识                     |
| 3    | 获取账号信息         | 根据roomtag，获取某账号的详细信息                            |
| 4    | 单户采集电表表显数据 | 实时抄表，返回当前表显数据，预付费表返回剩余电量，正计数表返回用当前电量 |
| 5    | 获取电表通断状态     | 实时获取电表进行通断状态                                     |
| 6    | 电表通断控制         | 单用户电表进行通电、断电操作                                 |
| 7    | 获取电表售电状态     | 购电前调用的接口，判断当前用户是否可以售电                   |
| 8    | 充值购电             | 缴费购电，购电前需要先验证售电状态                           |
| 9    | 批量采集抄电表记录   | 获取某用户时间段内抄表记录或最后一条抄表记录，获取所有用户时间段内抄表记录或最后一条抄表记录 |
| 10   | 获取当前账号收支明细 | 返回单用户某时间段内的收支记录                               |
| 11   | 获取账号欠费信息     | 获取到所有用户的欠费信息或某用户的欠费信息                   |
| 12   | 获取账号缴费信息     | 获取单用户某时间段内的缴费信息                               |
| 13   | 缴费统计信息         | 获取单用户或所有用户的缴费统计信息                           |
| 14   | 获取用电量信息       | 获取单用户或所有用户，指定时间段内的用电量,根据抄表记录计算获得，该时间段内无抄表记录无法获得用电量信息 |

 

四、 接口说明

**1、** **获取Token** (get请求)

Ø 请求地址

http:// 服务器地址:端口/api/ztwyPower/getToken?systemnum=qdhc001

Ø 返回



| 参数名      | 类型   | 说明           |
| ----------- | ------ | -------------- |
| errcode     | int    | 状态码 0为成功 |
| errmsg      | string | 状态描述       |
| records     | Int    | 返回记录数     |
| resultvalue |        |                |
| systemnum   | string | 请求用户编码   |
| tokenvalue  | string | token值        |
| expiretime  | string | token有效期    |

Ø 返回示例

{ 

"errcode": 0, 

"errmsg": "获取Token成功",

"records": 1,

"resultvalue": { 

"systemnum": "qdhc001",

"tokenvalue": "54a10915-97c9-44e4-b07f-178e95130b4f",

"expiretime": "2019-10-11 11:52:31"

} 

}



**2、** **获取用户roomTag**

Ø 请求地址

http:// 服务器地址:端口/api/ztwyPower/getRoomTags

Ø 请求参数

| 参数名     | 类型   | 必须 | 说明                                      |
| ---------- | ------ | ---- | ----------------------------------------- |
| tokenvalue | string | 是   | 用户凭证                                  |
| nodeid     | int    | 否   | 返回全部，  其他-返回对应节点下的用户档案 |

Ø 返回

| 参数名        | 类型   | 说明               |
| ------------- | ------ | ------------------ |
| errcode       | int    | 状态码 0为成功     |
| errmsg        | string | 状态描述           |
| records       | int    | 当前页所包含对象数 |
| resultvalue[] |        |                    |
| roomtag       | string | 房间标识           |
| zhaddress     | string | 住户地址           |
| dbxh          | string | 电表线号           |
| dbbh          | string | 电表表号           |

Ø 返回示例

{ 

"errcode": 0, 

"errmsg": "获取房间标识成功", 

"records": 3,

"resultvalue": [

{

"roomtag": "101640001",

"zhaddress": "东塔.1层.B118",

"dbxh": "1",

"dbbh": "4",

"sbbh": ""

},

{

"roomtag": "101640002",

"zhaddress": "东塔.1层.B109",

"dbxh": "4",

"dbbh": "4",

"sbbh": ""

},

{

"roomtag": "101640003",

"zhaddress": "东塔.1层.B117",

"dbxh": "5",

"dbbh": "4",

"sbbh": ""

}

]

}

**3、** **获取当前账户信息**

Ø 请求地址

http:// 服务器地址:端口/api/ztwyPower/getZhInfo

Ø 请求参数

| 参数名     | 类型   | 必须 | 说明     |
| ---------- | ------ | ---- | -------- |
| roomtag    | string | 是   | 房间标识 |
| tokenvalue | string | 是   | 用户凭证 |

Ø 返回

| 参数名      | 类型   | 说明                                             |
| ----------- | ------ | ------------------------------------------------ |
| errcode     | int    | 状态码 0为成功                                   |
| errmsg      | string | 状态描述                                         |
| records     | int    | 返回信息记录数目                                 |
| resultvalue | object | 结果集合                                         |
| roomtag     | string | 房间标识                                         |
| zhhh        | string | 住户户号                                         |
| zhxm        | string | 住户姓名                                         |
| zhaddress   | string | 住户地址                                         |
| ffstyle     | int    | 1-预付费  2-正计数                               |
| zhye        | string | 账户余额                                         |
| cbsj        | string | 抄表时间                                         |
| cbvalue     | string | 抄表值（ffstyle=1 剩余电量，ffstyle=2 当前电量） |
| qfje        | string | 欠费金额                                         |
| ljydl       | string | 累计用电量                                       |
| sdstate     | string | 售电状态 0-可以售电1-不可以售电                  |
| zhphone     | string | 住户电话                                         |

Ø 返回示例

{

"errcode": 0,

"errmsg": "获取账户信息成功",

"records": 1,

"resultvalue": {

"roomtag": "101640001",

"zhhh": "B118",

"zhxm": "",

"zhaddress": "东塔.1层.B118",

"ffstyle": 1,

"zhye": "100.00",

"cbsj": "2019/3/6 10:00:53",

"cbvalue": "2116.5",

"qfje": "1049.13",

"ljydl": "873.7",

"sdstate": "1",

"zhphone": ""

}

}

**4、** **单户采集电表表显数据**

Ø 请求地址

http:// 服务器地址:端口/api/ztwyPower/readDbValue

Ø 请求参数

| 参数名     | 类型   | 必须 | 说明     |
| ---------- | ------ | ---- | -------- |
| roomtag    | string | 是   | 房间标识 |
| tokenvalue | string | 是   | 用户凭证 |

Ø 返回

| 参数名      | 类型   | 说明              |
| ----------- | ------ | ----------------- |
| errcode     | int    | 状态码 0为成功    |
| errmsg      | string | 状态描述          |
| records     | Int    | 返回记录数        |
| resultvalue |        |                   |
| roomtag     | string | 房间标识          |
| zhaddress   | string | 住户地址          |
| readvalue   | string | 电表数值          |
| readtime    | string | 抄表时间          |
| onoffstate  | string | 通断状态（通/断） |
| ffstyle     | string | 预付费  正计数    |

Ø 返回示例

{

"errcode": 0,

"errmsg": "单表采集成功",

"records": 1,

"resultvalue": {

"roomtag": "101640001",

"zhaddress": "东塔.1层.B118",

"readvalue": "120.00",

"readtime": "2019-10-15 11:00:54",

"onoffstate": "通",

"ffstyle": "1"

}

}

**5、** **获取电表通断状态**

Ø 请求地址

http:// 服务器地址:端口/api/ztwyPower/getOnOffState

Ø 请求参数

| 参数名     | 类型   | 必须 | 说明     |
| ---------- | ------ | ---- | -------- |
| roomtag    | string | 是   | 房间标识 |
| tokenvalue | string | 是   | 用户凭证 |

Ø 返回

| 参数名      | 类型   | 说明           |
| ----------- | ------ | -------------- |
| errcode     | int    | 状态码 0为成功 |
| errmsg      | string | 状态描述       |
| records     | int    | 返回记录数     |
| resultvalue |        |                |
| roomtag     | string | 房间标识       |
| zhaddress   | string | 住户地址       |
| dbstate     | int    | 1-通 2-断      |
| message     | string | 说明信息       |

Ø 返回示例

{

"errcode": 0,

"errmsg": "获取电表断送电状态成功",

"records": 1,

"resultvalue": [

{

"roomtag": "101640001",

"zhaddress": "东塔.1层.B118",

"dbstate": 2,

"message": "欠费断电",

}

]

}

**6、** **电表通断控制**

Ø 请求地址

http:// 服务器地址:端口/api/ztwyPower/eleOnOff

Ø 请求参数

| 参数名     | 类型   | 必须 | 说明     |
| ---------- | ------ | ---- | -------- |
| roomtag    | string | 是   | 房间标识 |
| tag        | int    | 是   | 1通 2 断 |
| tokenvalue | string | 是   | 用户凭证 |

Ø 返回

| 参数名      | 类型   | 说明           |
| ----------- | ------ | -------------- |
| errcode     | int    | 状态码 0为成功 |
| errmsg      | string | 状态描述       |
| records     | int    | 返回记录数     |
| resultvalue |        |                |

Ø 返回示例

{ 

"errcode": 0, 

"errmsg": "送电成功,

"records": 0, 

"resultvalue":null

}

 

**7、** **获取电表能否售电状态**

Ø 请求地址

http:// 服务器地址:端口/api/ztwyPower/getGdState

Ø 请求参数

| 参数名     | 类型   | 必须 | 说明     |
| ---------- | ------ | ---- | -------- |
| roomtag    | string | 是   | 房间标识 |
| tokenvalue | string | 是   | 用户凭证 |

Ø 返回

| 参数名      | 类型   | 说明                                  |
| ----------- | ------ | ------------------------------------- |
| errcode     | int    | 状态码 0为成功                        |
| errmsg      | string | 状态描述                              |
| records     | Int    | 返回记录数                            |
| resultvalue |        |                                       |
| roomtag     | string | 房间标识                              |
| zhaddress   | string | 用户地址                              |
| dbstate     | int    | 可否购电状态 1-可以购电，2-不可以购电 |
| message     | string | 说明信息                              |

Ø 返回示例

{

"errcode": 0,

"errmsg": "获取售电状态成功",

"records": 1,

"resultvalue": {

"roomtag": "101640001",

"zhaddress": "东塔.1层.B118",

"dbstate": 2,

"message": "上次售电未成功，不允许售电"

}

}

**8、** **充值购电（只针对预付费电能表）**

Ø 请求地址

http:// 服务器地址:端口/api/ztwyPower/recharge

Ø 请求参数

| 参数名     | 类型   | 必须 | 说明           |
| ---------- | ------ | ---- | -------------- |
| roomtag    | string | 是   | 房间标识       |
| czje       | string | 是   | 充值金额       |
| strtrace   | string | 否   | 平台交易流水号 |
| tokenvalue | string | 是   | 用户凭证       |

Ø 返回

| 参数名      | 类型   | 说明           |
| ----------- | ------ | -------------- |
| errCode     | int    | 状态码 0为成功 |
| errMsg      | string | 状态描述       |
| records     | int    | 返回记录数     |
| resultvalue |        |                |

Ø 返回示例

{ 

"errCode": 0, 

"errMsg": "充值成功, 

"records": 0, 

"resultvalue":null

}

**9、** **批量采集抄电表记录（某时间段内）**

Ø 请求地址

http:// 服务器地址:端口/api/ztwyPower/getAllDbValue

Ø 请求参数

| 参数名     | 类型   | 必须 | 说明                                                         |
| ---------- | ------ | ---- | ------------------------------------------------------------ |
| roomtag    | string | 否   | 房间标识，当为非空时返回指定用户抄表记录，当为空时按nodeid参数说明返回抄表记录 |
| tokenvalue | string | 是   | 用户凭证                                                     |
| starttime  | string | 是   | 开始时间 （时间格式  (yyyy-MM-dd hh:mm:ss）                  |
| endtime    | string | 是   | 结束时间 （时间格式  (yyyy-MM-dd hh:mm:ss）                  |
| tag        | int    | 是   | 返回日期内所有抄表记录   0-返回最后一次抄表记录              |
| nodeid     | int    | 否   | 全部，其他是代表特定节点下  默认为0                          |

Ø 返回

| 参数名        | 类型   | 说明           |
| ------------- | ------ | -------------- |
| errcode       | int    | 状态码 0为成功 |
| errmsg        | string | 状态描述       |
| records       | int    | 返回记录数量   |
| resultvalue[] |        |                |
| roomtag       | string | 房间标识       |
| zhaddress     | string | 住户地址       |
| readtime      | string | 抄表值         |
| readtime      | string | 抄表时间       |

Ø 返回示例

{

"errcode": 0,

"errmsg": "批量采集抄表记录成功",

"records": 5,

"resultvalue": [

{

"roomtag": "101860001",

"zhaddress": "东塔.23层.B2302",

"readvalue": "899.90",

"readtime": "2019/3/6 10:00:53"

},

{

"roomtag": "101860002",

"zhaddress": "东塔.23层.B2309",

"readvalue": "962.70",

"readtime": "2019/3/6 10:00:53"

},

{

"roomtag": "*********",

"zhaddress": "东塔.23层.B2303",

"readvalue": "441.70",

"readtime": "2019/3/6 10:00:53"

},

{

"roomtag": "*********",

"zhaddress": "东塔.23层.B2301",

"readvalue": "81.40",

"readtime": "2019/3/6 10:00:53"

},

{

"roomtag": "*********",

"zhaddress": "东塔.23层.B2305",

"readvalue": "404.50",

"readtime": "2019/3/6 10:00:53"

}

]

}

**10、**    **获取当前账户收支明细（某时间段内）**

Ø 请求地址

http:// 服务器地址:端口/api/ztwyPower/getAccountInfos

Ø 请求参数

| 参数名     | 类型   | 必须 | 说明                                        |
| ---------- | ------ | ---- | ------------------------------------------- |
| roomtag    | string | 是   | 房间标识                                    |
| tokenvalue | string | 是   | 用户凭证                                    |
| starttime  | string | 是   | 开始时间 （时间格式  (yyyy-MM-dd hh:mm:ss） |
| endtime    | string | 是   | 结束时间 （时间格式  (yyyy-MM-dd hh:mm:ss） |

Ø 返回

| 参数名        | 类型   | 说明                  |
| ------------- | ------ | --------------------- |
| errcode       | int    | 状态码 0为成功        |
| errmsg        | string | 状态描述              |
| records       | int    | 记录数量              |
| resultvalue[] |        |                       |
| zhaddress     | string | 住户地址              |
| roomtag       | string | 房间标识              |
| time          | string | 交易时间              |
| je            | string | 发生金额              |
| type          | string | 交易类型（收入/支出） |
| remark        | string | 交易说明              |

Ø 返回示例

{

"errcode": 0,

"errmsg": "获取账户收支记录成功",

"records": 4,

"resultValue": [

{

"roomtag": "101660004",

"zhaddress": "东塔.3层.B325",

"time": "2019/4/2 12:58:26",

"je": "200.00",

"type": "收入",

"remark": "",

},

{

"roomtag": "101660004",

"zhaddress": "东塔.3层.B325",

"time": "2019/4/3 9:08:49",

"je": "200.00",

"type": "支出",

"remark": "",

},

{

"roomtag": "101660004",

"zhaddress": "东塔.3层.B325",

"time": "2019/4/3 9:08:49",

"je": "86.00",

"type": "收入",

"remark": "",

},

{

"roomtag": "101660004",

"zhaddress": "东塔.3层.B325",

"time": "2019/4/3 9:09:51",

"je": "86.00",

"type": "支出",

"remark": "",

}

]

}

**11、**    **获取账户欠费信息**

Ø 请求地址

http:// 服务器地址:端口/api/ztwyPower/getAllQfInfos

Ø 请求参数

| 参数名     | 类型   | 必须 | 说明                                                         |
| ---------- | ------ | ---- | ------------------------------------------------------------ |
| tokenvalue | string | 是   | 用户凭证                                                     |
| roomtag    | string | 是   | 房间标识，当为空时，返回所有欠费账户信息，当为非空时返回当前用户欠费信息 |
| nodeid     | int    | 否   |                                                              |

Ø 返回

| 参数名        | 类型   | 说明           |
| ------------- | ------ | -------------- |
| errcode       | int    | 状态码 0为成功 |
| errmsg        | string | 状态描述       |
| records       | int    | 记录数量       |
| resultvalue[] |        |                |
| roomtag       | string | 房间标识       |
| zhaddress     | string | 住户地址       |
| qfje          | string | 欠费金额       |
| qfmx          | string | 欠费明细       |

Ø 返回示例

获取单用户欠费信息

{

"errcode": 0,

"errmsg": "获取当前账户欠费信息成功",

"records": 1,

"resultvalue": [

{

"roomtag": "101640001",

"zhaddress": "东塔.1层.B118",

"qfje": "1049.13",

"qfmx": "项目:电费：1049.13 单价：1.00 金额:1049.13"

}

]

}

 

获取所有欠费用户信息

{

"errcode": 0,

"errmsg": "获取所有欠费账户信息成功",

"records": 2,

"resultValue": [

{

"roomtag": "101640001",

"zhaddress": "东塔.1层.B118",

"qfje": "1049.13",

"qfmx": "项目:电费：1049.13 单价：1.00 金额:1049.13"

},

{

"roomtag": "101640002",

"zhaddress": "东塔.1层.B109",

"qfje": "839.30",

"qfmx": "项目:电费：839.30单价：1.00 金额:839.30"

}

]

}

**12、**    **获取账户缴费信息（某时间段内）**

Ø 请求地址

http:// 服务器地址:端口/api/ztwyPower/getJfInfos

Ø 请求参数

| 参数名     | 类型   | 必须 | 说明                                        |
| ---------- | ------ | ---- | ------------------------------------------- |
| roomtag    | string | 是   | 房间标识                                    |
| tokenvalue | string | 是   | 用户凭证                                    |
| starttime  | string | 是   | 开始时间 （时间格式  (yyyy-MM-dd hh:mm:ss） |
| endtime    | string | 是   | 结束时间 （时间格式  (yyyy-MM-dd hh:mm:ss） |

Ø 返回

| 参数名        | 类型   | 说明           |
| ------------- | ------ | -------------- |
| errcode       | int    | 状态码 0为成功 |
| errmsg        | string | 状态描述       |
| records       | int    | 记录数量       |
| resultvalue[] |        |                |
| roomtag       | string | 房间标识       |
| zhaddress     | string | 住户地址       |
| zhxm          | string | 住户姓名       |
| jftime        | string | 缴费时间       |
| yjje          | string | 应缴金额       |
| scye          | string | 上次余额       |
| ssje          | string | 实收金额       |
| jfmx          | string | 缴费明细       |
| pzh           | string | 凭证号         |
| czy           | string | 操作员         |

Ø 返回示例

{

"errcode": 0,

"errmsg": 获取账户缴费信息成功,

"records": 2,

"resultvalue": [

{

"roomtag": "101640001",

"zhaddress": "东塔.1层.B118",

"zhxm": "",

"jftime": "2019/8/15 11:40:52",

"yjje": "200.00",

"scye": "0.00",

"ssje": "200.00",

"jfmx": "项目:电费 购(用)量：200.0 单价：1.00 金额:200.00",

"pzh": "1ecac7d4-7ae2-4b12-a721-8f2fec028a6c",

"czy": "Admin"

},

{

"roomtag": "101640001",

"zhaddress": "东塔.1层.B118",

"zhxm": "",

"jftime": "2019/10/7 14:00:34",

"yjje": "100.00",

"scye": "0.00",

"ssje": "100.00",

"jfmx": "项目:账户充值 购(用)量：0.0 单价： 金额:100.00",

"pzh": "",

"czy": "Admin"

}

]

}

**13、**    **获取账户缴费统计信息（某时间段内）**

Ø 请求地址

http:// 服务器地址:端口/api/ztwyPower/getJfAllInfos

Ø 请求参数

| 参数名     | 类型   | 必须 | 说明                                                         |
| ---------- | ------ | ---- | ------------------------------------------------------------ |
| roomtag    | string | 是   | 房间标识，当为空时，返回所有账户缴费统计信息，当为非空时返回当前用户缴费统计信息 |
| tokenvalue | string | 是   | 用户凭证                                                     |
| starttime  | string | 是   | 开始时间 （时间格式  (yyyy-MM-dd hh:mm:ss）                  |
| endtime    | string | 是   | 结束时间 （时间格式  (yyyy-MM-dd hh:mm:ss）                  |

Ø 返回

| 参数名        | 类型   | 说明           |
| ------------- | ------ | -------------- |
| errcode       | int    | 状态码 0为成功 |
| errmsg        | string | 状态描述       |
| records       | int    | 记录数量       |
| resultvalue[] |        |                |
| roomtag       | string | 房间标识       |
| zhaddress     | string | 住户地址       |
| zdf           | string | 总电费         |
| zsf           | string | 总水费         |
| zydl          | string | 总用电量       |
| zysl          | string | 总用水量       |

Ø 返回示例

单个用户：

{

"errcode": 0,

"errmsg": "获取账户缴费统计信息成功",

"records": 1,

"resultvalue": [

{

"roomtag": "101640001",

"zhaddress": "东塔.1层.B118",

"zdf": "200.00",

"zsf": "0.00",

"zydl": "882.00",

"zysl": "0"

}

]

}

所有用户：

{

"errcode": 0,

"errmsg": "获取账户缴费统计信息成功",

"records": 4,

"resultvalue": [

{

"roomtag": "101640001",

"zhaddress": "东塔.1层.B118",

"zdf": "200.00",

"zsf": "0.00",

"zydl": "882.00",

"zysl": "0"

},

{

"roomtag": "101640002",

"zhaddress": "东塔.1层.B109",

"zdf": "0.00",

"zsf": "0.00",

"zydl": "3.50",

"zysl": "0"

},

{

"roomtag": "101640003",

"zhaddress": "东塔.1层.B117",

"zdf": "0.00",

"zsf": "0.00",

"zydl": "534.00",

"zysl": "0"

},

{

"roomtag": "101640004",

"zhaddress": "东塔.1层.B113",

"zdf": "0.00",

"zsf": "0.00",

"zydl": "342.40",

"zysl": "0"

}

]

}

**14、**    **获取用户用电量信息（某时间段内）**

Ø 请求地址

http:// 服务器地址:端口/api/ztwyPower/getYdlByTime

Ø 请求参数

| 参数名     | 类型   | 必须 | 说明                                                         |
| ---------- | ------ | ---- | ------------------------------------------------------------ |
| roomtag    | string | 否   | 房间标识，空时按nodeid参数返回，为非空时返回指定用户时间段内用电量信息 |
| tokenvalue | string | 是   | 用户凭证                                                     |
| starttime  | string | 是   | 开始时间 （时间格式  (yyyy-MM-dd hh:mm:ss）                  |
| endtime    | string | 是   | 结束时间 （时间格式  (yyyy-MM-dd hh:mm:ss）                  |
| nodeid     | int    | 否   | 0-返回所有，其他-返回对应节点下用户时间段内的用电量；默认为0 |

Ø 返回

| 参数名        | 类型   | 说明           |
| ------------- | ------ | -------------- |
| errcode       | int    | 状态码 0为成功 |
| errmsg        | string | 状态描述       |
| records       | int    | 记录数量       |
| resultvalue[] |        |                |
| roomtag       | string | 房间标识       |
| zhaddress     | string | 住户地址       |
| zhxm          | string | 住户姓名       |
| starttime     | string | 起码日期       |
| endtime       | string | 止码日期       |
| startcode     | string | 起码           |
| endcode       | string | 止码           |
| jfmx          | string | 缴费明细       |
| glvalue       | string | 购量           |
| ylvalue       | string | 用电量         |

Ø 返回示例

{

"errcode": 0,

"errmsg": "获取用电量成功",

"records": 4,

"resultvalue": [

{

"roomtag": "101900001",

"zhaddress": "西塔.1层.A107",

"zhxm": "",

"stattime": "2019/1/17 14:13:16",

"endtime": "2019/3/6 10:00:53",

"startcode": "2968.3",

"endcode": "2445.0",

"glvalue": "0.00",

"ylvalue": "523.30"

},

{

"roomtag": "101900002",

"zhaddress": "西塔.1层.A108",

"zhxm": "",

"stattime": "2019/1/17 14:13:16",

"endtime": "2019/3/6 10:00:53",

"startcode": "2999.9",

"endcode": "2641.8",

"glvalue": "0.00",

"ylvalue": "358.10"

},

{

"roomtag": "101900003",

"zhaddress": "西塔.1层.A101",

"zhxm": "",

"stattime": "2019/1/17 14:13:16",

"endtime": "2019/3/6 10:00:53",

"startcode": "2884.7",

"endcode": "2436.0",

"glvalue": "0.00",

"ylvalue": "448.70"

},

{

"roomtag": "101900004",

"zhaddress": "西塔.1层.A106",

"zhxm": "",

"stattime": "2019/1/17 14:13:16",

"endtime": "2019/3/6 10:00:53",

"startcode": "2955.7",

"endcode": "2940.8",

"glvalue": "0.00",

"ylvalue": "14.90"

}

]

}

五、 备注

\1.   接口中的tokenvalue参数均通过获取Token接口获取。

\2.   接口中的roomtag参数是双方的一个对接字段,用来标识唯一住户标识。

 